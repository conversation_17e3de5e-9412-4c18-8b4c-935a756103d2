import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";
import { EmailTemplateEditor } from "@/components/ui/email-template-editor";
import { Loader2, Eye, Send } from "lucide-react";
import { useAuth } from "@/providers/AuthProvider";
import {
  collection,
  addDoc,
  query,
  orderBy,
  onSnapshot,
  Timestamp,
  doc,
  updateDoc,
} from "firebase/firestore";
import { firestore } from "@/config/firebase";
import { format } from "date-fns";

// Helper function to safely format dates
const formatFirestoreDate = (
  timestamp: Timestamp | Record<string, number> | number | string | undefined
): string => {
  try {
    // Check if timestamp has _seconds property (Firestore Timestamp from server)
    if (timestamp && typeof timestamp === "object" && "_seconds" in timestamp) {
      const date = new Date(timestamp._seconds * 1000);
      // Check if date is valid
      if (!isNaN(date.getTime())) {
        return format(date, "MMM d, yyyy");
      }
    }

    // Check if timestamp has seconds property (Firestore Timestamp)
    if (timestamp && typeof timestamp === "object" && "seconds" in timestamp) {
      const date = new Date(timestamp.seconds * 1000);
      // Check if date is valid
      if (!isNaN(date.getTime())) {
        return format(date, "MMM d, yyyy");
      }
    }

    // Check if timestamp is a string that might be a date
    if (timestamp && typeof timestamp === "string") {
      const date = new Date(timestamp);
      if (!isNaN(date.getTime())) {
        return format(date, "MMM d, yyyy");
      }

      // Special case for the timestamp format in the screenshot (May 10, 2025 at 4:24:21 AM UTC+4)
      if (timestamp.includes("at")) {
        try {
          // Extract the date part before "at"
          const datePart = timestamp.split("at")[0].trim();
          const date = new Date(datePart);
          if (!isNaN(date.getTime())) {
            return format(date, "MMM d, yyyy");
          }
        } catch {
          // Continue with other checks
        }
      }
    }

    // If timestamp is a number (unix timestamp)
    if (timestamp && typeof timestamp === "number") {
      const date = new Date(timestamp);
      if (!isNaN(date.getTime())) {
        return format(date, "MMM d, yyyy");
      }
    }

    return "Unknown date";
  } catch (error) {
    console.error("Error formatting date:", error, timestamp);
    return "Unknown date";
  }
};

// Email template interface
interface EmailTemplate {
  id: string;
  subject: string;
  content: string;
  createdAt: Timestamp;
  category?: string;
  description?: string;
  variables?: string[];
  lastUsed?: Timestamp;
  usageCount?: number;
}

// Subscriber interface
interface Subscriber {
  id: string;
  email: string;
  name?: string;
  subscribed: boolean;
  subscribedAt?: {
    seconds: number;
    nanoseconds: number;
  };
  createdAt?: Timestamp | Record<string, number> | number; // Could be Timestamp or other format
  timestamp?: string; // For the format "May 10, 2025 at 4:24:21 AM UTC+4"
}

// Predefined HTML templates
const predefinedTemplates = [
  {
    name: "Welcome Template",
    category: "Onboarding",
    subject: "Welcome to Our Newsletter!",
    content: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9f9f9;">
        <div style="background-color: #ffffff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
          <h1 style="color: #333333; text-align: center;">Welcome, {{name}}!</h1>
          <p style="color: #666666; line-height: 1.6;">
            Thank you for subscribing to our newsletter! We're excited to share the latest restaurant recommendations and food trends with you.
          </p>
          <p style="color: #666666; line-height: 1.6;">
            Stay tuned for delicious updates!
          </p>
          <div style="text-align: center; margin-top: 20px;">
            <a href="https://qonai.me" style="background-color: #ff6200; color: #ffffff; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
              Explore Now
            </a>
          </div>
          <p style="color: #999999; font-size: 12px; text-align: center; margin-top: 20px;">
            You received this email because you subscribed to our newsletter.
            <a href="{{unsubscribeLink}}" style="color: #999999;">Unsubscribe</a>
          </p>
        </div>
      </div>
    `,
  },
  {
    name: "Weekly Update Template",
    category: "Newsletter",
    subject: "Weekly Food Trends Update",
    content: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9f9f9;">
        <div style="background-color: #ffffff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
          <h1 style="color: #333333; text-align: center;">Weekly Food Trends</h1>
          <p style="color: #666666; line-height: 1.6;">
            Hi {{name}}, here are this week's top food trends and restaurant recommendations!
          </p>
          <ul style="color: #666666; line-height: 1.6;">
            <li>Trend 1: Plant-based dishes are on the rise!</li>
            <li>Trend 2: Fusion cuisine is making a comeback.</li>
          </ul>
          <div style="text-align: center; margin-top: 20px;">
            <a href="https://qonai.me" style="background-color: #ff6200; color: #ffffff; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
              Discover More
            </a>
          </div>
          <p style="color: #999999; font-size: 12px; text-align: center; margin-top: 20px;">
            <a href="{{unsubscribeLink}}" style="color: #999999;">Unsubscribe</a>
          </p>
        </div>
      </div>
    `,
  },
  {
    name: "Special Offer",
    category: "Promotions",
    subject: "Limited Time Offer Just For You!",
    content: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9f9f9;">
        <div style="background-color: #ffffff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
          <div style="text-align: center; margin-bottom: 20px;">
            <img src="https://qonai.me/images/special-offer.png" alt="Special Offer" style="max-width: 150px;">
          </div>
          <h1 style="color: #d23600; text-align: center; margin-bottom: 20px;">Special Offer Inside!</h1>
          <p style="color: #666666; line-height: 1.6;">
            Hello {{name}},
          </p>
          <p style="color: #666666; line-height: 1.6;">
            We're excited to offer you an exclusive deal at our partner restaurants this week!
          </p>
          <div style="background-color: #fff4e6; border-left: 4px solid #ff6200; padding: 15px; margin: 20px 0;">
            <h3 style="color: #333333; margin-top: 0;">{{offerTitle}}</h3>
            <p style="color: #666666; margin-bottom: 0;">{{offerDescription}}</p>
            <p style="font-weight: bold; color: #d23600; margin-top: 10px;">Valid until: {{offerExpiry}}</p>
          </div>
          <div style="text-align: center; margin-top: 25px;">
            <a href="{{offerLink}}" style="background-color: #ff6200; color: #ffffff; padding: 12px 25px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;">
              Claim Your Offer
            </a>
          </div>
          <p style="color: #999999; font-size: 12px; text-align: center; margin-top: 30px;">
            <a href="{{unsubscribeLink}}" style="color: #999999;">Unsubscribe</a> from promotional emails.
          </p>
        </div>
      </div>
    `,
  },
  {
    name: "New Restaurant Announcement",
    category: "Announcements",
    subject: "New Restaurant Alert: {{restaurantName}} Just Joined Qonai!",
    content: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9f9f9;">
        <div style="background-color: #ffffff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
          <h1 style="color: #333333; text-align: center;">New Restaurant Alert!</h1>
          <p style="color: #666666; line-height: 1.6;">
            Hello {{name}},
          </p>
          <p style="color: #666666; line-height: 1.6;">
            We're excited to announce that <strong>{{restaurantName}}</strong> has just joined the Qonai platform!
          </p>
          <div style="margin: 25px 0; text-align: center;">
            <img src="{{restaurantImage}}" alt="{{restaurantName}}" style="max-width: 100%; border-radius: 8px; max-height: 250px; object-fit: cover;">
          </div>
          <div style="background-color: #f5f5f5; padding: 15px; border-radius: 8px; margin: 20px 0;">
            <h3 style="color: #333333; margin-top: 0;">About {{restaurantName}}</h3>
            <p style="color: #666666; line-height: 1.6;">{{restaurantDescription}}</p>
            <p style="color: #666666;"><strong>Cuisine:</strong> {{restaurantCuisine}}</p>
            <p style="color: #666666;"><strong>Location:</strong> {{restaurantLocation}}</p>
          </div>
          <div style="text-align: center; margin-top: 25px;">
            <a href="{{restaurantLink}}" style="background-color: #ff6200; color: #ffffff; padding: 12px 25px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;">
              View Menu & Order Now
            </a>
          </div>
          <p style="color: #999999; font-size: 12px; text-align: center; margin-top: 30px;">
            <a href="{{unsubscribeLink}}" style="color: #999999;">Unsubscribe</a> from our newsletter.
          </p>
        </div>
      </div>
    `,
  },
  {
    name: "Monthly Digest",
    category: "Newsletter",
    subject: "Your Monthly Food Digest - {{month}} Highlights",
    content: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9f9f9;">
        <div style="background-color: #ffffff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
          <h1 style="color: #333333; text-align: center; margin-bottom: 5px;">{{month}} Food Digest</h1>
          <p style="color: #666666; text-align: center; margin-top: 0;">Your monthly roundup of food discoveries</p>

          <p style="color: #666666; line-height: 1.6; margin-top: 20px;">
            Hello {{name}},
          </p>
          <p style="color: #666666; line-height: 1.6;">
            Here's your personalized monthly digest of food trends, new restaurants, and special offers based on your preferences.
          </p>

          <div style="margin: 25px 0;">
            <h2 style="color: #ff6200; border-bottom: 2px solid #ff6200; padding-bottom: 8px;">Top Picks For You</h2>
            <div style="display: flex; gap: 15px; margin-top: 15px;">
              <div style="flex: 1; background-color: #f5f5f5; border-radius: 8px; padding: 15px; text-align: center;">
                <img src="{{topPick1Image}}" alt="{{topPick1Name}}" style="width: 100%; height: 120px; object-fit: cover; border-radius: 5px;">
                <h3 style="color: #333333; margin: 10px 0 5px;">{{topPick1Name}}</h3>
                <p style="color: #666666; font-size: 14px; margin: 0;">{{topPick1Description}}</p>
              </div>
              <div style="flex: 1; background-color: #f5f5f5; border-radius: 8px; padding: 15px; text-align: center;">
                <img src="{{topPick2Image}}" alt="{{topPick2Name}}" style="width: 100%; height: 120px; object-fit: cover; border-radius: 5px;">
                <h3 style="color: #333333; margin: 10px 0 5px;">{{topPick2Name}}</h3>
                <p style="color: #666666; font-size: 14px; margin: 0;">{{topPick2Description}}</p>
              </div>
            </div>
          </div>

          <div style="margin: 30px 0;">
            <h2 style="color: #ff6200; border-bottom: 2px solid #ff6200; padding-bottom: 8px;">This Month's Food Trend</h2>
            <div style="margin-top: 15px;">
              <h3 style="color: #333333; margin: 0 0 10px;">{{trendTitle}}</h3>
              <p style="color: #666666; line-height: 1.6;">{{trendDescription}}</p>
            </div>
          </div>

          <div style="text-align: center; margin-top: 30px;">
            <a href="https://qonai.me/explore" style="background-color: #ff6200; color: #ffffff; padding: 12px 25px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;">
              Explore More Restaurants
            </a>
          </div>

          <p style="color: #999999; font-size: 12px; text-align: center; margin-top: 30px;">
            <a href="{{unsubscribeLink}}" style="color: #999999;">Unsubscribe</a> from our newsletter.
          </p>
        </div>
      </div>
    `,
  },
  {
    name: "Loyalty Reward Notification",
    category: "Loyalty",
    subject: "You've Earned a New Reward! 🎁",
    content: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9f9f9;">
        <div style="background-color: #ffffff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
          <div style="text-align: center; margin-bottom: 20px;">
            <img src="https://qonai.me/images/reward-badge.png" alt="Reward Badge" style="max-width: 120px;">
          </div>
          <h1 style="color: #333333; text-align: center; margin-bottom: 20px;">Congratulations, {{name}}!</h1>

          <div style="background-color: #fff8e6; border-radius: 8px; padding: 20px; margin: 20px 0; text-align: center; border: 2px dashed #ffb74d;">
            <h2 style="color: #ff6200; margin-top: 0;">You've Earned a New Reward!</h2>
            <p style="color: #666666; font-size: 18px; margin-bottom: 5px;">{{rewardTitle}}</p>
            <p style="color: #666666; margin-bottom: 15px;">{{rewardDescription}}</p>
            <div style="background-color: #ffffff; display: inline-block; padding: 10px 20px; border-radius: 4px; font-size: 18px; font-weight: bold; letter-spacing: 2px; color: #333333; border: 1px solid #ddd;">{{rewardCode}}</div>
          </div>

          <div style="color: #666666; line-height: 1.6;">
            <p>Your loyalty has been rewarded! You've reached {{pointsEarned}} points and unlocked this special reward.</p>
            <p>Current loyalty level: <strong>{{loyaltyLevel}}</strong></p>
            <p>Total points balance: <strong>{{pointsBalance}}</strong></p>
          </div>

          <div style="margin: 25px 0; padding: 15px; background-color: #f5f5f5; border-radius: 8px;">
            <h3 style="color: #333333; margin-top: 0;">How to Redeem Your Reward:</h3>
            <ol style="color: #666666; padding-left: 20px;">
              <li>Visit any participating restaurant</li>
              <li>Show this email or enter your reward code at checkout</li>
              <li>Enjoy your reward!</li>
            </ol>
            <p style="color: #666666; font-style: italic; margin-bottom: 0;">Valid until: {{expiryDate}}</p>
          </div>

          <div style="text-align: center; margin-top: 25px;">
            <a href="https://qonai.me/rewards" style="background-color: #ff6200; color: #ffffff; padding: 12px 25px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;">
              View All Your Rewards
            </a>
          </div>

          <p style="color: #999999; font-size: 12px; text-align: center; margin-top: 30px;">
            <a href="{{unsubscribeLink}}" style="color: #999999;">Unsubscribe</a> from promotional emails.
          </p>
        </div>
      </div>
    `,
  },
];

export function EmailPanel() {
  const { user } = useAuth();
  const [subscribers, setSubscribers] = useState<Subscriber[]>([]);
  const [templates, setTemplates] = useState<EmailTemplate[]>([]);
  const [newTemplate, setNewTemplate] = useState({
    subject: "",
    content: "",
    category: "General",
    description: "",
    variables: [],
  });
  const [emailSubject, setEmailSubject] = useState("");
  const [emailContent, setEmailContent] = useState("");
  const [isSending, setIsSending] = useState(false);
  const [previewContent, setPreviewContent] = useState("");
  const [testEmail, setTestEmail] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("");

  useEffect(() => {
    if (!user) return;

    const fetchSubscribers = async () => {
      try {
        // Show loading toast

        const apiUrl =
          process.env.NODE_ENV === "production"
            ? "https://api.qonai.me/subscribers"
            : "http://localhost:3000/subscribers";

        const response = await fetch(apiUrl, {
          headers: {
            Accept: "application/json",
            "Content-Type": "application/json",
          },
          // Add credentials if your API requires authentication
          // credentials: 'include',
        });

        if (!response.ok) {
          throw new Error(
            `Failed to fetch subscribers: ${response.status} ${response.statusText}`
          );
        }

        const data = await response.json();

        setSubscribers(data);
      } catch (error) {
        console.error("Error fetching subscribers:", error);
        setSubscribers([]);
      }
    };

    fetchSubscribers();
    // Reduce polling frequency to avoid unnecessary API calls
    const intervalId = setInterval(fetchSubscribers, 300000); // 5 minutes
    return () => clearInterval(intervalId);
  }, [user]);

  useEffect(() => {
    if (!user) return;

    const templatesRef = collection(firestore, "emailTemplates");
    const q = query(templatesRef, orderBy("createdAt", "desc"));

    const unsubscribe = onSnapshot(q, (snapshot) => {
      const templatesList: EmailTemplate[] = [];
      snapshot.forEach((doc) =>
        templatesList.push({ id: doc.id, ...doc.data() } as EmailTemplate)
      );
      setTemplates(templatesList);
    });

    return () => unsubscribe();
  }, [user]);

  if (!user || user.email !== "<EMAIL>") {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center p-6 text-center">
        <h2 className="text-2xl font-bold text-gray-900">Access Denied</h2>
        <p className="mt-2 text-gray-600">
          You don't have permission to access this page.
        </p>
      </div>
    );
  }

  const saveTemplate = async () => {
    try {
      // Extract variables from content using regex
      const variableMatches =
        newTemplate.content.match(/\{\{([^}]+)\}\}/g) || [];
      const variables = variableMatches.map((match) =>
        match.replace(/\{\{|\}\}/g, "")
      );

      // Remove duplicates from variables array
      const uniqueVariables = [...new Set(variables)];

      const templatesRef = collection(firestore, "emailTemplates");
      await addDoc(templatesRef, {
        ...newTemplate,
        variables: uniqueVariables,
        createdAt: Timestamp.now(),
        usageCount: 0,
      });
      toast.success("Template saved successfully");
      setNewTemplate({
        subject: "",
        content: "",
        category: "General",
        description: "",
        variables: [],
      });
    } catch (error) {
      console.error("Error saving template:", error);
      toast.error("Failed to save template");
    }
  };

  const sendNewsletter = async () => {
    if (!emailSubject || !emailContent) {
      toast.error("Please fill in both subject and content");
      return;
    }

    // Check if we have subscribers
    if (subscribers.length === 0) {
      toast.error("No subscribers found to send newsletter to");
      return;
    }

    // Get active subscribers
    const activeSubscribers = subscribers.map((sub) => sub.email);

    if (activeSubscribers.length === 0) {
      toast.error("No active subscribers found");
      return;
    }

    setIsSending(true);
    try {
      // Try the newsletter API endpoint first
      const apiUrl =
        process.env.NODE_ENV === "production"
          ? "https://api.qonai.me/send-newsletter"
          : "http://localhost:3000/send-newsletter";

      // Log the request for debugging
      console.log("Sending newsletter to:", {
        subscriberCount: activeSubscribers.length,
        firstFew: activeSubscribers.slice(0, 3),
        subject:
          emailSubject.substring(0, 30) +
          (emailSubject.length > 30 ? "..." : ""),
      });

      // Prepare the request payload
      const requestData = {
        subject: emailSubject,
        content: emailContent,
        recipients: activeSubscribers, // Send the list of active subscribers
        emails: activeSubscribers, // Alternative field name
        to: activeSubscribers, // Another alternative field name
        subscribed: true, // Explicitly indicate these are active subscribers
      };

      const response = await fetch(apiUrl, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
        body: JSON.stringify(requestData),
      });

      // Handle non-OK responses
      if (!response.ok) {
        let errorMessage = `Server returned ${response.status}: ${response.statusText}`;

        try {
          const errorData = await response.json();
          errorMessage = errorData.error || errorMessage;
        } catch (jsonError) {
          // If we can't parse the error as JSON, use the status text
          console.error("Error parsing error response:", jsonError);
        }

        throw new Error(errorMessage);
      }

      // Parse the successful response
      let result;
      try {
        result = await response.json();
      } catch (jsonError) {
        // If we can't parse the response as JSON, create a default result
        console.warn("Could not parse response as JSON:", jsonError);
        result = { success: true, recipientCount: activeSubscribers.length };
      }

      // Show success message
      toast.success(
        `Newsletter sent to ${
          result.recipientCount || activeSubscribers.length
        } subscribers`
      );

      // Clear the form
      setEmailSubject("");
      setEmailContent("");
      setPreviewContent("");
    } catch (error) {
      console.error("Error sending newsletter:", error);

      // Try sending test email as fallback
      if (activeSubscribers.length > 0) {
        try {
          toast.info("Attempting to send to first subscriber as test...");
          await sendSingleEmail(activeSubscribers[0]);
        } catch (fallbackError) {
          console.error("Fallback also failed:", fallbackError);
        }
      }

      toast.error(
        error instanceof Error ? error.message : "Failed to send newsletter"
      );
    } finally {
      setIsSending(false);
    }
  };

  // Helper function to send to a single email
  const sendSingleEmail = async (email: string) => {
    const apiUrl =
      process.env.NODE_ENV === "production"
        ? "https://api.qonai.me/send-notification-email"
        : "http://localhost:3000/send-notification-email";

    // Find subscriber info if available
    const subscriber = subscribers.find((sub) => sub.email === email);
    const subscriberName = subscriber?.name || "there";

    // Replace template variables with actual values
    const personalizedContent = emailContent
      .replace(/\{\{name\}\}/g, subscriberName)
      .replace(/\{\{unsubscribeLink\}\}/g, "#");

    const response = await fetch(apiUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Accept: "application/json",
      },
      body: JSON.stringify({
        to: email,
        subject: emailSubject,
        body: personalizedContent,
        type: "marketing",
        recipientName: subscriberName,
      }),
    });

    if (!response.ok) {
      throw new Error(
        `Failed to send to ${email}: ${response.status} ${response.statusText}`
      );
    }

    toast.success(`Sent newsletter to ${email}`);
    return await response.json();
  };

  const sendTestEmail = async () => {
    if (!emailSubject || !emailContent || !testEmail) {
      toast.error("Please fill in subject, content, and test email address");
      return;
    }

    setIsSending(true);
    try {
      // First try the newsletter API with test flag
      try {
        const apiUrl =
          process.env.NODE_ENV === "production"
            ? "https://api.qonai.me/send-newsletter"
            : "http://localhost:3000/send-newsletter";

        const response = await fetch(apiUrl, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json",
          },
          body: JSON.stringify({
            subject: emailSubject,
            content: emailContent
              .replace(/\{\{name\}\}/g, "Test User")
              .replace(/\{\{unsubscribeLink\}\}/g, "#"),
            testEmail, // Test email parameter
            isTest: true, // Flag to indicate this is a test email
            recipients: [testEmail], // Also include in recipients array
            emails: [testEmail], // Alternative field name
            to: [testEmail], // Another alternative field name
            recipientName: "Test User", // Add recipient name for template variables
          }),
        });

        if (response.ok) {
          toast.success(`Test email sent to ${testEmail}`);
          return;
        }

        // If newsletter API fails, throw error to try fallback
        throw new Error(
          `Newsletter API returned ${response.status}: ${response.statusText}`
        );
      } catch (newsletterError) {
        console.warn("Newsletter API failed for test email:", newsletterError);

        // Fall back to notification email API
        await sendSingleEmail(testEmail);
      }
    } catch (error) {
      console.error("Error sending test email:", error);
      toast.error(
        error instanceof Error ? error.message : "Failed to send test email"
      );
    } finally {
      setIsSending(false);
    }
  };

  const handleTemplateSelect = (template: {
    name?: string;
    subject: string;
    content: string;
    category?: string;
    variables?: string[];
  }) => {
    setEmailSubject(template.subject);
    setEmailContent(template.content);

    // Create a preview with sample data for all variables
    let previewContent = template.content;

    // Replace common variables first
    previewContent = previewContent
      .replace(/\{\{name\}\}/g, "Test User")
      .replace(/\{\{unsubscribeLink\}\}/g, "#");

    // Replace any other variables with sample data
    const variableMatches = template.content.match(/\{\{([^}]+)\}\}/g) || [];
    variableMatches.forEach((variable) => {
      const variableName = variable.replace(/\{\{|\}\}/g, "");
      if (variableName !== "name" && variableName !== "unsubscribeLink") {
        // Generate sample data based on variable name
        let sampleValue = "Sample Value";

        // Special cases for common variable types
        if (
          variableName.toLowerCase().includes("date") ||
          variableName.toLowerCase().includes("expiry")
        ) {
          sampleValue = "June 30, 2024";
        } else if (
          variableName.toLowerCase().includes("price") ||
          variableName.toLowerCase().includes("amount")
        ) {
          sampleValue = "$49.99";
        } else if (
          variableName.toLowerCase().includes("percent") ||
          variableName.toLowerCase().includes("discount")
        ) {
          sampleValue = "25%";
        } else if (variableName.toLowerCase().includes("restaurant")) {
          sampleValue = "Delicious Bistro";
        } else if (
          variableName.toLowerCase().includes("image") ||
          variableName.toLowerCase().includes("img")
        ) {
          sampleValue = "https://via.placeholder.com/300x200";
        } else if (
          variableName.toLowerCase().includes("link") ||
          variableName.toLowerCase().includes("url")
        ) {
          sampleValue = "#";
        } else if (variableName.toLowerCase().includes("description")) {
          sampleValue =
            "This is a sample description text that would appear in your email.";
        } else if (variableName.toLowerCase().includes("title")) {
          sampleValue = "Sample Title";
        } else if (variableName.toLowerCase().includes("month")) {
          sampleValue = "June";
        }

        // Replace all occurrences of this variable
        const regex = new RegExp(`\\{\\{${variableName}\\}\\}`, "g");
        previewContent = previewContent.replace(regex, sampleValue);
      }
    });

    setPreviewContent(previewContent);
  };

  const handlePreview = () => {
    // Create a preview with sample data for all variables
    let previewContent = emailContent;

    // Replace common variables first
    previewContent = previewContent
      .replace(/\{\{name\}\}/g, "Test User")
      .replace(/\{\{unsubscribeLink\}\}/g, "#");

    // Replace any other variables with sample data
    const variableMatches = emailContent.match(/\{\{([^}]+)\}\}/g) || [];
    variableMatches.forEach((variable) => {
      const variableName = variable.replace(/\{\{|\}\}/g, "");
      if (variableName !== "name" && variableName !== "unsubscribeLink") {
        // Generate sample data based on variable name
        let sampleValue = "Sample Value";

        // Special cases for common variable types
        if (
          variableName.toLowerCase().includes("date") ||
          variableName.toLowerCase().includes("expiry")
        ) {
          sampleValue = "June 30, 2024";
        } else if (
          variableName.toLowerCase().includes("price") ||
          variableName.toLowerCase().includes("amount")
        ) {
          sampleValue = "$49.99";
        } else if (
          variableName.toLowerCase().includes("percent") ||
          variableName.toLowerCase().includes("discount")
        ) {
          sampleValue = "25%";
        } else if (variableName.toLowerCase().includes("restaurant")) {
          sampleValue = "Delicious Bistro";
        } else if (
          variableName.toLowerCase().includes("image") ||
          variableName.toLowerCase().includes("img")
        ) {
          sampleValue = "https://via.placeholder.com/300x200";
        } else if (
          variableName.toLowerCase().includes("link") ||
          variableName.toLowerCase().includes("url")
        ) {
          sampleValue = "#";
        } else if (variableName.toLowerCase().includes("description")) {
          sampleValue =
            "This is a sample description text that would appear in your email.";
        } else if (variableName.toLowerCase().includes("title")) {
          sampleValue = "Sample Title";
        } else if (variableName.toLowerCase().includes("month")) {
          sampleValue = "June";
        }

        // Replace all occurrences of this variable
        const regex = new RegExp(`\\{\\{${variableName}\\}\\}`, "g");
        previewContent = previewContent.replace(regex, sampleValue);
      }
    });

    setPreviewContent(previewContent);
  };

  return (
    <section className="min-h-screen bg-gradient-to-br from-orange-50 to-red-50 p-4 lg:p-8">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Email Campaign Manager
          </h1>
          <p className="text-gray-600">
            Create, manage, and send newsletters to your subscribers
          </p>
        </div>

        <Card className="shadow-xl border-0 overflow-hidden">
          <CardContent className="p-0">
            <Tabs defaultValue="compose" className="w-full">
              <div className="bg-white border-b border-gray-200 px-6 py-4">
                <TabsList className="grid w-full max-w-md grid-cols-3 bg-gray-100 rounded-lg p-1 h-12">
                  <TabsTrigger
                    value="compose"
                    className="rounded-md font-medium data-[state=active]:bg-orange-500 data-[state=active]:text-white"
                  >
                    Compose
                  </TabsTrigger>
                  <TabsTrigger
                    value="templates"
                    className="rounded-md font-medium data-[state=active]:bg-orange-500 data-[state=active]:text-white"
                  >
                    Templates
                  </TabsTrigger>
                  <TabsTrigger
                    value="subscribers"
                    className="rounded-md font-medium data-[state=active]:bg-orange-500 data-[state=active]:text-white"
                  >
                    Subscribers
                  </TabsTrigger>
                </TabsList>
              </div>

              <TabsContent value="compose" className="p-6">
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                  {/* Left Column - Form */}
                  <div className="lg:col-span-2 space-y-6">
                    {/* Subject Input */}
                    <div className="bg-white rounded-lg border border-gray-200 p-6 shadow-sm">
                      <Label
                        htmlFor="subject"
                        className="text-lg font-semibold text-gray-800 mb-3 block"
                      >
                        Email Subject
                      </Label>
                      <Input
                        id="subject"
                        value={emailSubject}
                        onChange={(e) => setEmailSubject(e.target.value)}
                        placeholder="Enter your newsletter subject line..."
                        className="h-12 text-base border-gray-300 focus:border-orange-500 focus:ring-orange-500"
                      />
                    </div>

                    {/* Content Editor */}
                    <div className="bg-white rounded-lg border border-gray-200 p-6 shadow-sm">
                      <Label
                        htmlFor="content"
                        className="text-lg font-semibold text-gray-800 mb-3 block"
                      >
                        Email Content
                      </Label>
                      <div className="bg-gray-50 rounded-lg p-6 min-h-[400px]">
                        <EmailTemplateEditor
                          initialHtml={emailContent}
                          onSave={(html) => {
                            setEmailContent(html);
                          }}
                          onPreview={(html) => {
                            setPreviewContent(html);
                          }}
                        />
                      </div>
                    </div>

                    {/* Action Buttons */}
                    <div className="bg-white rounded-lg border border-gray-200 p-6 shadow-sm">
                      <div className="flex flex-col sm:flex-row gap-4">
                        <Button
                          onClick={handlePreview}
                          variant="outline"
                          className="flex-1 h-12 text-base border-orange-500 text-orange-600 hover:bg-orange-50"
                        >
                          <Eye className="mr-2 h-5 w-5" />
                          Preview Email
                        </Button>
                        <Button
                          onClick={sendNewsletter}
                          disabled={isSending}
                          className="flex-1 h-12 text-base bg-orange-500 hover:bg-orange-600"
                        >
                          {isSending ? (
                            <>
                              <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                              Sending Newsletter...
                            </>
                          ) : (
                            <>
                              <Send className="mr-2 h-5 w-5" />
                              Send Newsletter
                            </>
                          )}
                        </Button>
                      </div>
                    </div>

                    {/* Test Email Section */}
                    <div className="bg-white rounded-lg border border-gray-200 p-4 shadow-sm">
                      <Label
                        htmlFor="testEmail"
                        className="text-md font-semibold text-gray-800 mb-2 block"
                      >
                        Test Email
                      </Label>
                      <div className="flex flex-col sm:flex-row gap-2">
                        <Input
                          id="testEmail"
                          value={testEmail}
                          onChange={(e) => setTestEmail(e.target.value)}
                          placeholder="Enter test email address"
                          className="flex-1 h-10 text-sm border-gray-300 focus:border-orange-500 focus:ring-orange-500"
                        />
                        <Button
                          onClick={sendTestEmail}
                          disabled={isSending || !testEmail}
                          variant="outline"
                          size="sm"
                          className="h-10 px-4 border-orange-500 text-orange-600 hover:bg-orange-50"
                        >
                          <Send className="mr-1 h-4 w-4" />
                          Send Test
                        </Button>
                      </div>
                    </div>

                    {/* Quick Templates */}
                    <div className="bg-white rounded-lg border border-gray-200 p-4 shadow-sm">
                      <Label className="text-md font-semibold text-gray-800 mb-2 block">
                        Quick Templates
                      </Label>
                      <div className="grid grid-cols-2 lg:grid-cols-3 gap-2">
                        {predefinedTemplates.map((template) => (
                          <Button
                            key={template.name}
                            onClick={() => handleTemplateSelect(template)}
                            variant="outline"
                            size="sm"
                            className="text-xs border-gray-300 hover:border-orange-500 hover:text-orange-600"
                          >
                            {template.name}
                          </Button>
                        ))}
                      </div>
                    </div>
                  </div>

                  {/* Right Column - Preview */}
                  <div className="lg:col-span-1 space-y-6">
                    {previewContent ? (
                      <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden sticky top-6">
                        <div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
                          <h3 className="text-md font-semibold text-gray-800 flex items-center">
                            <Eye className="mr-2 h-4 w-4 text-orange-500" />
                            Live Preview
                          </h3>
                        </div>
                        <div className="p-4">
                          <div
                            dangerouslySetInnerHTML={{ __html: previewContent }}
                            className="max-h-[600px] overflow-auto prose prose-sm max-w-none email-preview text-sm"
                          />
                        </div>
                      </div>
                    ) : (
                      <div className="bg-gradient-to-br from-gray-50 to-gray-100 rounded-lg border-2 border-dashed border-gray-300 p-8 text-center sticky top-6">
                        <Eye className="mx-auto h-8 w-8 text-gray-400 mb-3" />
                        <h3 className="text-md font-medium text-gray-600 mb-2">
                          Email Preview
                        </h3>
                        <p className="text-sm text-gray-500">
                          Add content and click "Preview Email" to see your
                          newsletter
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="templates" className="p-6">
                <div className="space-y-8">
                  {/* Create New Template Section */}
                  <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
                    <div className="bg-gradient-to-r from-orange-500 to-red-500 text-white p-6 rounded-t-lg">
                      <h3 className="text-xl font-bold mb-2">
                        Create New Template
                      </h3>
                      <p className="text-orange-100">
                        Design reusable email templates for your campaigns
                      </p>
                    </div>

                    <div className="p-6 space-y-6">
                      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div>
                          <Label
                            htmlFor="templateSubject"
                            className="text-sm font-semibold text-gray-800 mb-2 block"
                          >
                            Template Subject
                          </Label>
                          <Input
                            id="templateSubject"
                            value={newTemplate.subject}
                            onChange={(e) =>
                              setNewTemplate({
                                ...newTemplate,
                                subject: e.target.value,
                              })
                            }
                            placeholder="Enter template subject line..."
                            className="h-12 border-gray-300 focus:border-orange-500 focus:ring-orange-500"
                          />
                        </div>
                        <div>
                          <Label
                            htmlFor="templateCategory"
                            className="text-sm font-semibold text-gray-800 mb-2 block"
                          >
                            Category
                          </Label>
                          <select
                            id="templateCategory"
                            value={newTemplate.category}
                            onChange={(e) =>
                              setNewTemplate({
                                ...newTemplate,
                                category: e.target.value,
                              })
                            }
                            className="w-full h-12 px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                          >
                            <option value="General">General</option>
                            <option value="Onboarding">Onboarding</option>
                            <option value="Newsletter">Newsletter</option>
                            <option value="Promotions">Promotions</option>
                            <option value="Announcements">Announcements</option>
                            <option value="Transactional">Transactional</option>
                            <option value="Loyalty">Loyalty</option>
                          </select>
                        </div>
                      </div>

                      <div>
                        <Label
                          htmlFor="templateDescription"
                          className="text-sm font-semibold text-gray-800 mb-2 block"
                        >
                          Description
                        </Label>
                        <Input
                          id="templateDescription"
                          value={newTemplate.description}
                          onChange={(e) =>
                            setNewTemplate({
                              ...newTemplate,
                              description: e.target.value,
                            })
                          }
                          placeholder="Brief description of this template..."
                          className="h-12 border-gray-300 focus:border-orange-500 focus:ring-orange-500"
                        />
                      </div>

                      <div>
                        <Label
                          htmlFor="templateContent"
                          className="text-sm font-semibold text-gray-800 mb-2 block"
                        >
                          Template Content
                        </Label>
                        <div className="mb-3 p-3 bg-blue-50 rounded-lg border border-blue-200">
                          <p className="text-sm text-blue-800">
                            💡 <strong>Tip:</strong> Use variables like{" "}
                            <code className="bg-blue-100 px-1 rounded">
                              {"{{name}}"}
                            </code>
                            ,{" "}
                            <code className="bg-blue-100 px-1 rounded">
                              {"{{unsubscribeLink}}"}
                            </code>{" "}
                            to personalize emails
                          </p>
                        </div>
                        <div className="bg-gray-50 rounded-lg p-4">
                          <EmailTemplateEditor
                            initialHtml={newTemplate.content}
                            onSave={(html) => {
                              setNewTemplate({
                                ...newTemplate,
                                content: html,
                              });
                            }}
                            onPreview={(html) => {
                              setPreviewContent(html);
                              const composeTab =
                                document.querySelector('[value="compose"]');
                              if (composeTab) {
                                (composeTab as HTMLElement).click();
                              }
                            }}
                          />
                        </div>
                      </div>

                      <div className="flex flex-col sm:flex-row gap-4 pt-4 border-t border-gray-200">
                        <Button
                          onClick={() => {
                            let previewContent = newTemplate.content;
                            const variableMatches =
                              newTemplate.content.match(/\{\{([^}]+)\}\}/g) ||
                              [];
                            const variables = variableMatches.map((match) =>
                              match.replace(/\{\{|\}\}/g, "")
                            );

                            variables.forEach((variable) => {
                              let sampleValue = "Sample Value";

                              if (
                                variable.toLowerCase().includes("date") ||
                                variable.toLowerCase().includes("expiry")
                              ) {
                                sampleValue = "June 30, 2024";
                              } else if (
                                variable.toLowerCase().includes("price") ||
                                variable.toLowerCase().includes("amount")
                              ) {
                                sampleValue = "$49.99";
                              } else if (
                                variable.toLowerCase().includes("percent") ||
                                variable.toLowerCase().includes("discount")
                              ) {
                                sampleValue = "25%";
                              } else if (
                                variable.toLowerCase().includes("restaurant")
                              ) {
                                sampleValue = "Delicious Bistro";
                              } else if (
                                variable.toLowerCase().includes("image") ||
                                variable.toLowerCase().includes("img")
                              ) {
                                sampleValue =
                                  "https://via.placeholder.com/300x200";
                              } else if (
                                variable.toLowerCase().includes("link") ||
                                variable.toLowerCase().includes("url")
                              ) {
                                sampleValue = "#";
                              } else if (
                                variable.toLowerCase().includes("description")
                              ) {
                                sampleValue =
                                  "This is a sample description text that would appear in your email.";
                              } else if (
                                variable.toLowerCase().includes("title")
                              ) {
                                sampleValue = "Sample Title";
                              } else if (
                                variable.toLowerCase().includes("month")
                              ) {
                                sampleValue = "June";
                              }

                              const regex = new RegExp(
                                `\\{\\{${variable}\\}\\}`,
                                "g"
                              );
                              previewContent = previewContent.replace(
                                regex,
                                sampleValue
                              );
                            });

                            setPreviewContent(previewContent);

                            const composeTab =
                              document.querySelector('[value="compose"]');
                            if (composeTab) {
                              (composeTab as HTMLElement).click();
                            }
                          }}
                          variant="outline"
                          className="flex-1 h-12 border-orange-500 text-orange-600 hover:bg-orange-50"
                        >
                          <Eye className="mr-2 h-4 w-4" />
                          Preview Template
                        </Button>
                        <Button
                          onClick={saveTemplate}
                          disabled={
                            !newTemplate.subject || !newTemplate.content
                          }
                          className="flex-1 h-12 bg-orange-500 hover:bg-orange-600"
                        >
                          Save Template
                        </Button>
                      </div>
                    </div>
                  </div>

                  {/* Saved Templates Section */}
                  <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
                    <div className="bg-gray-50 px-6 py-4 border-b border-gray-200 rounded-t-lg">
                      <h3 className="text-xl font-semibold text-gray-800 mb-2">
                        Saved Templates ({templates.length})
                      </h3>
                      <p className="text-gray-600">
                        Manage and use your saved email templates
                      </p>
                    </div>

                    <div className="p-6">
                      {/* Category Filter */}
                      <div className="mb-6">
                        <Label className="text-sm font-semibold text-gray-800 mb-3 block">
                          Filter by Category
                        </Label>
                        <div className="flex flex-wrap gap-2">
                          <Button
                            variant={!selectedCategory ? "default" : "outline"}
                            size="sm"
                            onClick={() => setSelectedCategory("")}
                            className={
                              !selectedCategory
                                ? "bg-orange-500 hover:bg-orange-600 text-white"
                                : "border-gray-300 hover:border-orange-500 hover:text-orange-600"
                            }
                          >
                            All ({templates.length})
                          </Button>
                          {Array.from(
                            new Set(
                              templates.map((t) => t.category || "General")
                            )
                          ).map((category) => {
                            const count = templates.filter(
                              (t) => (t.category || "General") === category
                            ).length;
                            return (
                              <Button
                                key={category}
                                variant={
                                  selectedCategory === category
                                    ? "default"
                                    : "outline"
                                }
                                size="sm"
                                onClick={() => setSelectedCategory(category)}
                                className={
                                  selectedCategory === category
                                    ? "bg-orange-500 hover:bg-orange-600 text-white"
                                    : "border-gray-300 hover:border-orange-500 hover:text-orange-600"
                                }
                              >
                                {category} ({count})
                              </Button>
                            );
                          })}
                        </div>
                      </div>
                      {/* Templates Grid */}
                      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
                        {/* Templates Grid */}
                        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
                          {templates
                            .filter(
                              (template) =>
                                !selectedCategory ||
                                template.category === selectedCategory
                            )
                            .map((template) => (
                              <Card
                                key={template.id}
                                className="template-card-hover shadow-lg hover:shadow-xl transition-all duration-300 border-0 overflow-hidden group fade-in-up"
                              >
                                <CardContent className="p-0">
                                  {/* Card Header */}
                                  <div className="bg-gradient-to-r from-gray-50 to-gray-100 p-4 border-b border-gray-200">
                                    <div className="flex justify-between items-start mb-2">
                                      <h4 className="font-semibold text-gray-800 text-lg truncate pr-2">
                                        {template.subject}
                                      </h4>
                                      {template.category && (
                                        <span className="inline-block bg-orange-100 text-orange-600 text-xs px-2 py-1 rounded-full font-medium whitespace-nowrap">
                                          {template.category}
                                        </span>
                                      )}
                                    </div>

                                    {template.description && (
                                      <p className="text-sm text-gray-600 line-clamp-2">
                                        {template.description}
                                      </p>
                                    )}
                                  </div>

                                  {/* Template Variables */}
                                  {template.variables &&
                                    template.variables.length > 0 && (
                                      <div className="p-4 bg-blue-50 border-b border-blue-100">
                                        <p className="text-xs font-medium text-blue-800 mb-2">
                                          Template Variables:
                                        </p>
                                        <div className="flex flex-wrap gap-1">
                                          {template.variables
                                            .slice(0, 4)
                                            .map((variable) => (
                                              <span
                                                key={variable}
                                                className="inline-block bg-blue-100 text-blue-700 text-xs px-2 py-1 rounded-md font-mono"
                                              >
                                                {"{{"}
                                                {variable}
                                                {"}}"}
                                              </span>
                                            ))}
                                          {template.variables.length > 4 && (
                                            <span className="text-xs text-blue-600">
                                              +{template.variables.length - 4}{" "}
                                              more
                                            </span>
                                          )}
                                        </div>
                                      </div>
                                    )}

                                  {/* Content Preview */}
                                  <div className="p-4">
                                    <div
                                      className="text-xs text-gray-600 line-clamp-3 bg-gray-50 p-3 rounded-lg border"
                                      dangerouslySetInnerHTML={{
                                        __html:
                                          template.content.length > 150
                                            ? template.content.substring(
                                                0,
                                                150
                                              ) + "..."
                                            : template.content,
                                      }}
                                    />
                                  </div>

                                  {/* Action Buttons */}
                                  <div className="p-4 bg-gray-50 border-t border-gray-200">
                                    <div className="flex gap-2">
                                      <Button
                                        onClick={() => {
                                          let previewContent = template.content;
                                          previewContent = previewContent
                                            .replace(
                                              /\{\{name\}\}/g,
                                              "Test User"
                                            )
                                            .replace(
                                              /\{\{unsubscribeLink\}\}/g,
                                              "#"
                                            );

                                          const variableMatches =
                                            template.content.match(
                                              /\{\{([^}]+)\}\}/g
                                            ) || [];
                                          variableMatches.forEach(
                                            (variable) => {
                                              const variableName =
                                                variable.replace(
                                                  /\{\{|\}\}/g,
                                                  ""
                                                );
                                              if (
                                                variableName !== "name" &&
                                                variableName !==
                                                  "unsubscribeLink"
                                              ) {
                                                let sampleValue =
                                                  "Sample Value";

                                                if (
                                                  variableName
                                                    .toLowerCase()
                                                    .includes("date") ||
                                                  variableName
                                                    .toLowerCase()
                                                    .includes("expiry")
                                                ) {
                                                  sampleValue = "June 30, 2024";
                                                } else if (
                                                  variableName
                                                    .toLowerCase()
                                                    .includes("price") ||
                                                  variableName
                                                    .toLowerCase()
                                                    .includes("amount")
                                                ) {
                                                  sampleValue = "$49.99";
                                                } else if (
                                                  variableName
                                                    .toLowerCase()
                                                    .includes("percent") ||
                                                  variableName
                                                    .toLowerCase()
                                                    .includes("discount")
                                                ) {
                                                  sampleValue = "25%";
                                                } else if (
                                                  variableName
                                                    .toLowerCase()
                                                    .includes("restaurant")
                                                ) {
                                                  sampleValue =
                                                    "Delicious Bistro";
                                                } else if (
                                                  variableName
                                                    .toLowerCase()
                                                    .includes("image") ||
                                                  variableName
                                                    .toLowerCase()
                                                    .includes("img")
                                                ) {
                                                  sampleValue =
                                                    "https://via.placeholder.com/300x200";
                                                } else if (
                                                  variableName
                                                    .toLowerCase()
                                                    .includes("link") ||
                                                  variableName
                                                    .toLowerCase()
                                                    .includes("url")
                                                ) {
                                                  sampleValue = "#";
                                                } else if (
                                                  variableName
                                                    .toLowerCase()
                                                    .includes("description")
                                                ) {
                                                  sampleValue =
                                                    "This is a sample description text that would appear in your email.";
                                                } else if (
                                                  variableName
                                                    .toLowerCase()
                                                    .includes("title")
                                                ) {
                                                  sampleValue = "Sample Title";
                                                } else if (
                                                  variableName
                                                    .toLowerCase()
                                                    .includes("month")
                                                ) {
                                                  sampleValue = "June";
                                                }

                                                const regex = new RegExp(
                                                  `\\{\\{${variableName}\\}\\}`,
                                                  "g"
                                                );
                                                previewContent =
                                                  previewContent.replace(
                                                    regex,
                                                    sampleValue
                                                  );
                                              }
                                            }
                                          );

                                          setPreviewContent(previewContent);

                                          const composeTab =
                                            document.querySelector(
                                              '[value="compose"]'
                                            );
                                          if (composeTab) {
                                            (composeTab as HTMLElement).click();
                                          }
                                        }}
                                        variant="outline"
                                        size="sm"
                                        className="flex-1 border-gray-300 hover:border-orange-500 hover:text-orange-600"
                                      >
                                        <Eye className="mr-1 h-4 w-4" />
                                        Preview
                                      </Button>
                                      <Button
                                        onClick={() => {
                                          setEmailSubject(template.subject);
                                          setEmailContent(template.content);

                                          const templateRef = doc(
                                            firestore,
                                            "emailTemplates",
                                            template.id
                                          );
                                          updateDoc(templateRef, {
                                            lastUsed: Timestamp.now(),
                                            usageCount:
                                              (template.usageCount || 0) + 1,
                                          }).catch((error) => {
                                            console.error(
                                              "Error updating template usage:",
                                              error
                                            );
                                          });

                                          let previewContent = template.content;
                                          previewContent = previewContent
                                            .replace(
                                              /\{\{name\}\}/g,
                                              "Test User"
                                            )
                                            .replace(
                                              /\{\{unsubscribeLink\}\}/g,
                                              "#"
                                            );

                                          const variableMatches =
                                            template.content.match(
                                              /\{\{([^}]+)\}\}/g
                                            ) || [];
                                          variableMatches.forEach(
                                            (variable) => {
                                              const variableName =
                                                variable.replace(
                                                  /\{\{|\}\}/g,
                                                  ""
                                                );
                                              if (
                                                variableName !== "name" &&
                                                variableName !==
                                                  "unsubscribeLink"
                                              ) {
                                                let sampleValue =
                                                  "Sample Value";

                                                if (
                                                  variableName
                                                    .toLowerCase()
                                                    .includes("date") ||
                                                  variableName
                                                    .toLowerCase()
                                                    .includes("expiry")
                                                ) {
                                                  sampleValue = "June 30, 2024";
                                                } else if (
                                                  variableName
                                                    .toLowerCase()
                                                    .includes("price") ||
                                                  variableName
                                                    .toLowerCase()
                                                    .includes("amount")
                                                ) {
                                                  sampleValue = "$49.99";
                                                } else if (
                                                  variableName
                                                    .toLowerCase()
                                                    .includes("percent") ||
                                                  variableName
                                                    .toLowerCase()
                                                    .includes("discount")
                                                ) {
                                                  sampleValue = "25%";
                                                } else if (
                                                  variableName
                                                    .toLowerCase()
                                                    .includes("restaurant")
                                                ) {
                                                  sampleValue =
                                                    "Delicious Bistro";
                                                } else if (
                                                  variableName
                                                    .toLowerCase()
                                                    .includes("image") ||
                                                  variableName
                                                    .toLowerCase()
                                                    .includes("img")
                                                ) {
                                                  sampleValue =
                                                    "https://via.placeholder.com/300x200";
                                                } else if (
                                                  variableName
                                                    .toLowerCase()
                                                    .includes("link") ||
                                                  variableName
                                                    .toLowerCase()
                                                    .includes("url")
                                                ) {
                                                  sampleValue = "#";
                                                } else if (
                                                  variableName
                                                    .toLowerCase()
                                                    .includes("description")
                                                ) {
                                                  sampleValue =
                                                    "This is a sample description text that would appear in your email.";
                                                } else if (
                                                  variableName
                                                    .toLowerCase()
                                                    .includes("title")
                                                ) {
                                                  sampleValue = "Sample Title";
                                                } else if (
                                                  variableName
                                                    .toLowerCase()
                                                    .includes("month")
                                                ) {
                                                  sampleValue = "June";
                                                }

                                                const regex = new RegExp(
                                                  `\\{\\{${variableName}\\}\\}`,
                                                  "g"
                                                );
                                                previewContent =
                                                  previewContent.replace(
                                                    regex,
                                                    sampleValue
                                                  );
                                              }
                                            }
                                          );

                                          setPreviewContent(previewContent);

                                          const composeTab =
                                            document.querySelector(
                                              '[value="compose"]'
                                            );
                                          if (composeTab) {
                                            (composeTab as HTMLElement).click();
                                          }

                                          toast.success(
                                            "Template loaded successfully"
                                          );
                                        }}
                                        size="sm"
                                        className="flex-1 bg-orange-500 hover:bg-orange-600 text-white"
                                      >
                                        Use Template
                                      </Button>
                                    </div>
                                  </div>
                                </CardContent>
                              </Card>
                            ))}
                        </div>

                        {/* Empty States */}
                        {templates.length === 0 && (
                          <div className="text-center py-12 bg-gradient-to-br from-gray-50 to-gray-100 rounded-lg border-2 border-dashed border-gray-300">
                            <div className="mx-auto w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mb-4">
                              <svg
                                className="w-8 h-8 text-gray-400"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                                />
                              </svg>
                            </div>
                            <h4 className="text-lg font-medium text-gray-600 mb-2">
                              No templates found
                            </h4>
                            <p className="text-gray-500">
                              Create your first template to get started with
                              your email campaigns
                            </p>
                          </div>
                        )}

                        {templates.length > 0 &&
                          selectedCategory &&
                          templates.filter(
                            (t) => t.category === selectedCategory
                          ).length === 0 && (
                            <div className="text-center py-12 bg-gradient-to-br from-gray-50 to-gray-100 rounded-lg border-2 border-dashed border-gray-300">
                              <div className="mx-auto w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mb-4">
                                <svg
                                  className="w-8 h-8 text-gray-400"
                                  fill="none"
                                  stroke="currentColor"
                                  viewBox="0 0 24 24"
                                >
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                                  />
                                </svg>
                              </div>
                              <h4 className="text-lg font-medium text-gray-600 mb-2">
                                No templates in "{selectedCategory}" category
                              </h4>
                              <p className="text-gray-500">
                                Create a template in this category or select a
                                different filter
                              </p>
                            </div>
                          )}
                      </div>{" "}
                    </div>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="subscribers" className="p-6">
                <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
                  {/* Header */}
                  <div className="bg-gradient-to-r from-orange-500 to-red-500 text-white p-6 rounded-t-lg">
                    <div className="flex justify-between items-center">
                      <div>
                        <h3 className="text-xl font-bold mb-2">
                          Newsletter Subscribers
                        </h3>
                        <p className="text-orange-100">
                          Manage your newsletter subscriber list (
                          {subscribers.length} total subscribers)
                        </p>
                      </div>
                      <Button
                        variant="outline"
                        onClick={() => {
                          const loadingToast = toast.loading(
                            "Refreshing subscribers..."
                          );
                          const apiUrl =
                            process.env.NODE_ENV === "production"
                              ? "https://api.qonai.me/subscribers"
                              : "http://localhost:3000/subscribers";

                          fetch(apiUrl)
                            .then((res) => res.json())
                            .then((data) => {
                              setSubscribers(data);
                              toast.dismiss(loadingToast);
                              toast.success(
                                `Refreshed subscribers (${data.length})`
                              );
                            })
                            .catch((err) => {
                              toast.dismiss(loadingToast);
                              toast.error(`Failed to refresh: ${err.message}`);
                            });
                        }}
                        className="bg-white bg-opacity-20 border-white border-opacity-30 text-white hover:bg-white hover:bg-opacity-30 hover:border-white"
                      >
                        <svg
                          className="mr-2 h-4 w-4"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                          />
                        </svg>
                        Refresh List
                      </Button>
                    </div>
                  </div>

                  <div className="p-6">
                    {subscribers.length === 0 ? (
                      <div className="text-center py-12 bg-gradient-to-br from-gray-50 to-gray-100 rounded-lg border-2 border-dashed border-gray-300">
                        <div className="mx-auto w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mb-4">
                          <svg
                            className="w-8 h-8 text-gray-400"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
                            />
                          </svg>
                        </div>
                        <h4 className="text-lg font-medium text-gray-600 mb-2">
                          No subscribers yet
                        </h4>
                        <p className="text-gray-500 mb-4">
                          Your subscriber list is empty. Promote your newsletter
                          to start building your audience.
                        </p>
                        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 text-left max-w-md mx-auto">
                          <h5 className="font-medium text-blue-800 mb-2">
                            💡 Tips to grow your subscriber list:
                          </h5>
                          <ul className="text-sm text-blue-700 space-y-1">
                            <li>
                              • Add newsletter signup forms to your website
                            </li>
                            <li>• Share exclusive content with subscribers</li>
                            <li>• Promote on social media platforms</li>
                            <li>• Offer incentives for signing up</li>
                          </ul>
                        </div>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        {/* Stats Cards */}
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                            <div className="flex items-center justify-between">
                              <div>
                                <p className="text-sm font-medium text-blue-600">
                                  Total Subscribers
                                </p>
                                <p className="text-2xl font-bold text-blue-800">
                                  {subscribers.length}
                                </p>
                              </div>
                              <div className="w-8 h-8 bg-blue-200 rounded-full flex items-center justify-center">
                                <svg
                                  className="w-4 h-4 text-blue-600"
                                  fill="none"
                                  stroke="currentColor"
                                  viewBox="0 0 24 24"
                                >
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"
                                  />
                                </svg>
                              </div>
                            </div>
                          </div>

                          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                            <div className="flex items-center justify-between">
                              <div>
                                <p className="text-sm font-medium text-green-600">
                                  This Month
                                </p>
                                <p className="text-2xl font-bold text-green-800">
                                  {
                                    subscribers.filter((sub) => {
                                      try {
                                        let subDate: Date;
                                        if (
                                          sub.timestamp &&
                                          typeof sub.timestamp === "object" &&
                                          "_seconds" in sub.timestamp
                                        ) {
                                          subDate = new Date(
                                            (
                                              sub.timestamp as {
                                                _seconds: number;
                                              }
                                            )._seconds * 1000
                                          );
                                        } else if (
                                          sub.timestamp &&
                                          typeof sub.timestamp === "object" &&
                                          "seconds" in sub.timestamp
                                        ) {
                                          subDate = new Date(
                                            (
                                              sub.timestamp as {
                                                seconds: number;
                                              }
                                            ).seconds * 1000
                                          );
                                        } else if (sub.createdAt) {
                                          if (
                                            typeof sub.createdAt === "object" &&
                                            "seconds" in sub.createdAt
                                          ) {
                                            subDate = new Date(
                                              (
                                                sub.createdAt as {
                                                  seconds: number;
                                                }
                                              ).seconds * 1000
                                            );
                                          } else if (
                                            typeof sub.createdAt === "string" ||
                                            typeof sub.createdAt === "number"
                                          ) {
                                            subDate = new Date(sub.createdAt);
                                          } else {
                                            return false;
                                          }
                                        } else if (sub.subscribedAt) {
                                          if (
                                            typeof sub.subscribedAt ===
                                              "object" &&
                                            "seconds" in sub.subscribedAt
                                          ) {
                                            subDate = new Date(
                                              (
                                                sub.subscribedAt as {
                                                  seconds: number;
                                                }
                                              ).seconds * 1000
                                            );
                                          } else {
                                            return false;
                                          }
                                        } else {
                                          return false;
                                        }
                                        const now = new Date();
                                        return (
                                          subDate.getMonth() ===
                                            now.getMonth() &&
                                          subDate.getFullYear() ===
                                            now.getFullYear()
                                        );
                                      } catch {
                                        return false;
                                      }
                                    }).length
                                  }
                                </p>
                              </div>
                              <div className="w-8 h-8 bg-green-200 rounded-full flex items-center justify-center">
                                <svg
                                  className="w-4 h-4 text-green-600"
                                  fill="none"
                                  stroke="currentColor"
                                  viewBox="0 0 24 24"
                                >
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                                  />
                                </svg>
                              </div>
                            </div>
                          </div>

                          <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
                            <div className="flex items-center justify-between">
                              <div>
                                <p className="text-sm font-medium text-purple-600">
                                  Growth Rate
                                </p>
                                <p className="text-2xl font-bold text-purple-800">
                                  +12%
                                </p>
                              </div>
                              <div className="w-8 h-8 bg-purple-200 rounded-full flex items-center justify-center">
                                <svg
                                  className="w-4 h-4 text-purple-600"
                                  fill="none"
                                  stroke="currentColor"
                                  viewBox="0 0 24 24"
                                >
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"
                                  />
                                </svg>
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* Subscribers List */}
                        <div className="space-y-3">
                          <h4 className="text-lg font-semibold text-gray-800 mb-4">
                            Subscriber List
                          </h4>
                          {subscribers.map((subscriber) => (
                            <div
                              key={subscriber.id}
                              className="flex items-center justify-between p-4 bg-gray-50 rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow"
                            >
                              <div className="flex items-center space-x-4">
                                <div className="w-10 h-10 subscriber-avatar rounded-full flex items-center justify-center text-white font-semibold">
                                  {subscriber.name
                                    ? subscriber.name.charAt(0).toUpperCase()
                                    : subscriber.email.charAt(0).toUpperCase()}
                                </div>
                                <div className="flex flex-col">
                                  <span className="font-medium text-gray-800">
                                    {subscriber.email}
                                  </span>
                                  {subscriber.name && (
                                    <span className="text-sm text-gray-600">
                                      {subscriber.name}
                                    </span>
                                  )}
                                </div>
                              </div>

                              <div className="flex items-center space-x-4">
                                <div className="text-right">
                                  <span className="text-sm font-medium text-gray-700">
                                    {subscriber.timestamp
                                      ? formatFirestoreDate(
                                          subscriber.timestamp
                                        )
                                      : formatFirestoreDate(
                                          subscriber.createdAt ||
                                            subscriber.subscribedAt
                                        )}
                                  </span>
                                  <p className="text-xs text-gray-500">
                                    Subscribed
                                  </p>
                                </div>

                                <div className="flex items-center">
                                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    <span className="w-1.5 h-1.5 bg-green-400 rounded-full mr-1.5"></span>
                                    Active
                                  </span>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>

                        {/* Export Button */}
                        <div className="mt-8 pt-6 border-t border-gray-200">
                          <Button
                            onClick={() => {
                              // Create CSV content
                              const csvContent = [
                                ["Email", "Name", "Subscribed Date"].join(","),
                                ...subscribers.map((sub) =>
                                  [
                                    sub.email,
                                    sub.name || "",
                                    sub.timestamp
                                      ? formatFirestoreDate(sub.timestamp)
                                      : formatFirestoreDate(
                                          sub.createdAt || sub.subscribedAt
                                        ),
                                  ].join(",")
                                ),
                              ].join("\n");

                              // Create download link
                              const blob = new Blob([csvContent], {
                                type: "text/csv",
                              });
                              const url = URL.createObjectURL(blob);
                              const a = document.createElement("a");
                              a.href = url;
                              a.download = `subscribers-${
                                new Date().toISOString().split("T")[0]
                              }.csv`;
                              document.body.appendChild(a);
                              a.click();
                              document.body.removeChild(a);
                              URL.revokeObjectURL(url);

                              toast.success("Subscribers exported to CSV");
                            }}
                            variant="outline"
                            className="w-full md:w-auto border-orange-500 text-orange-600 hover:bg-orange-50"
                          >
                            <svg
                              className="mr-2 h-4 w-4"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                              />
                            </svg>
                            Export to CSV
                          </Button>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>
    </section>
  );
}
