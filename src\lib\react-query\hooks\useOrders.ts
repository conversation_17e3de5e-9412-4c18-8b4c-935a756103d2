import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import {
  collection,
  query,
  getDocs,
  doc,
  onSnapshot,
  orderBy,
  writeBatch,
  serverTimestamp,
  FirestoreError,
  Timestamp,
} from "firebase/firestore";
import { firestore } from "@/config/firebase";
import { Order } from "@/types/index";
import { queryKeys, CACHE_TIME, STALE_TIME } from "../index";
import { useEffect, useState } from "react";
import { toast } from "sonner";

/**
 * Hook to fetch orders for a restaurant with caching
 */
export function useRestaurantOrders(restaurantId: string | undefined) {
  return useQuery({
    queryKey: queryKeys.restaurants.orders(restaurantId || ""),
    queryFn: async () => {
      if (!restaurantId) {
        throw new Error("Restaurant ID is required");
      }

      const ordersRef = collection(
        doc(firestore, "restaurants", restaurantId),
        "orders"
      );
      const q = query(ordersRef, orderBy("orderDate", "desc"));
      const querySnapshot = await getDocs(q);

      const orders: Order[] = [];
      querySnapshot.forEach((doc) => {
        const data = doc.data();
        orders.push({
          orderId: doc.id,
          ...data,
          orderDate:
            data.orderDate instanceof Timestamp
              ? data.orderDate.toDate()
              : new Date(data.orderDate),
        } as Order);
      });

      return orders;
    },
    enabled: !!restaurantId,
    staleTime: STALE_TIME,
    gcTime: CACHE_TIME,
  });
}

/**
 * Hook to fetch orders for a client with caching
 */
export function useClientOrders(clientId: string | undefined) {
  return useQuery({
    queryKey: queryKeys.clients.orders(clientId || ""),
    queryFn: async () => {
      if (!clientId) {
        throw new Error("Client ID is required");
      }

      const ordersRef = collection(
        doc(firestore, "clients", clientId),
        "orders"
      );
      const q = query(ordersRef, orderBy("orderDate", "desc"));
      const querySnapshot = await getDocs(q);

      const orders: Order[] = [];
      querySnapshot.forEach((doc) => {
        const data = doc.data();
        orders.push({
          orderId: doc.id,
          ...data,
          orderDate:
            data.orderDate instanceof Timestamp
              ? data.orderDate.toDate()
              : new Date(data.orderDate),
        } as Order);
      });

      return orders;
    },
    enabled: !!clientId,
    staleTime: STALE_TIME,
    gcTime: CACHE_TIME,
  });
}

/**
 * Hook to fetch orders with real-time updates for a restaurant
 */
export function useRealtimeRestaurantOrders(restaurantId: string | undefined) {
  const [orders, setOrders] = useState<Order[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<FirestoreError | null>(null);

  useEffect(() => {
    if (!restaurantId) {
      setIsLoading(false);
      setOrders([]);
      setError(null);
      return;
    }

    setIsLoading(true);
    setError(null);

    const ordersRef = collection(
      doc(firestore, "restaurants", restaurantId),
      "orders"
    );
    const q = query(ordersRef, orderBy("orderDate", "desc"));

    const unsubscribe = onSnapshot(
      q,
      (snapshot) => {
        const fetchedOrders = snapshot.docs.map((doc) => {
          const data = doc.data();
          return {
            orderId: doc.id,
            ...data,
            orderDate:
              data.orderDate instanceof Timestamp
                ? data.orderDate.toDate()
                : new Date(data.orderDate),
          } as Order;
        });

        setOrders(fetchedOrders);
        setIsLoading(false);
      },
      (err: FirestoreError) => {
        console.error("Error fetching orders:", err);
        setError(err);
        toast.error("Failed to load orders");
        setIsLoading(false);
      }
    );

    return () => unsubscribe();
  }, [restaurantId]);

  return { orders, isLoading, error };
}

/**
 * Hook to fetch orders with real-time updates for a client
 */
export function useRealtimeClientOrders(clientId: string | undefined) {
  const [orders, setOrders] = useState<Order[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<FirestoreError | null>(null);

  useEffect(() => {
    if (!clientId) {
      setIsLoading(false);
      setOrders([]);
      setError(null);
      return;
    }

    setIsLoading(true);
    setError(null);

    const ordersRef = collection(doc(firestore, "clients", clientId), "orders");
    const q = query(ordersRef, orderBy("orderDate", "desc"));

    const unsubscribe = onSnapshot(
      q,
      (snapshot) => {
        const fetchedOrders = snapshot.docs.map((doc) => {
          const data = doc.data();
          return {
            orderId: doc.id,
            ...data,
            orderDate:
              data.orderDate instanceof Timestamp
                ? data.orderDate.toDate()
                : new Date(data.orderDate),
          } as Order;
        });

        setOrders(fetchedOrders);
        setIsLoading(false);
      },
      (err: FirestoreError) => {
        console.error("Error fetching orders:", err);
        setError(err);
        toast.error("Failed to load orders");
        setIsLoading(false);
      }
    );

    return () => unsubscribe();
  }, [clientId]);

  return { orders, isLoading, error };
}

/**
 * Hook to create a new order with optimistic updates
 */
export function useCreateOrder() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (orderData: Omit<Order, "orderId">) => {
      const { restaurantId, userId } = orderData;

      // Generate a unique order ID
      const orderId = `${Date.now()}-${Math.random()
        .toString(36)
        .substr(2, 9)}`;

      // Create the order with the generated ID
      const order = {
        ...orderData,
        orderId,
        orderDate: new Date(),
      };

      // Use a batch write for atomicity
      const batch = writeBatch(firestore);

      // Add to restaurant's orders subcollection
      const restaurantOrderRef = doc(
        collection(firestore, "restaurants", restaurantId, "orders"),
        orderId
      );
      batch.set(restaurantOrderRef, order);

      // Add to client's orders subcollection
      const clientOrderRef = doc(
        collection(firestore, "clients", userId, "orders"),
        orderId
      );
      batch.set(clientOrderRef, order);

      await batch.commit();

      return order;
    },
    onMutate: async (newOrder) => {
      const { restaurantId, userId } = newOrder;

      // Cancel any outgoing refetches
      await queryClient.cancelQueries({
        queryKey: queryKeys.restaurants.orders(restaurantId),
      });
      await queryClient.cancelQueries({
        queryKey: queryKeys.clients.orders(userId),
      });

      // Snapshot the previous values
      const previousRestaurantOrders = queryClient.getQueryData<Order[]>(
        queryKeys.restaurants.orders(restaurantId)
      );

      const previousClientOrders = queryClient.getQueryData<Order[]>(
        queryKeys.clients.orders(userId)
      );

      // Create a temporary ID for optimistic update
      const tempId = `temp_${Date.now()}`;

      // Create optimistic order
      const optimisticOrder: Order = {
        ...newOrder,
        orderId: tempId,
        orderDate: new Date(),
      };

      // Optimistically update orders
      if (previousRestaurantOrders) {
        queryClient.setQueryData(queryKeys.restaurants.orders(restaurantId), [
          optimisticOrder,
          ...previousRestaurantOrders,
        ]);
      }

      if (previousClientOrders) {
        queryClient.setQueryData(queryKeys.clients.orders(userId), [
          optimisticOrder,
          ...previousClientOrders,
        ]);
      }

      return {
        previousRestaurantOrders,
        previousClientOrders,
        tempId,
      };
    },
    onError: (_, newOrder, context) => {
      const { restaurantId, userId } = newOrder;

      // If the mutation fails, use the context returned from onMutate to roll back
      if (context?.previousRestaurantOrders) {
        queryClient.setQueryData(
          queryKeys.restaurants.orders(restaurantId),
          context.previousRestaurantOrders
        );
      }

      if (context?.previousClientOrders) {
        queryClient.setQueryData(
          queryKeys.clients.orders(userId),
          context.previousClientOrders
        );
      }

      toast.error("Failed to create order");
    },
    onSuccess: () => {
      toast.success("Order created successfully!");
    },
    onSettled: (_, __, newOrder) => {
      const { restaurantId, userId } = newOrder;

      // Always refetch after error or success to make sure our local data is correct
      queryClient.invalidateQueries({
        queryKey: queryKeys.restaurants.orders(restaurantId),
      });
      queryClient.invalidateQueries({
        queryKey: queryKeys.clients.orders(userId),
      });
    },
  });
}

/**
 * Hook to update an order status with optimistic updates
 */
export function useUpdateOrderStatus() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      orderId,
      restaurantId,
      userId,
      status,
    }: {
      orderId: string;
      restaurantId: string;
      userId: string;
      status: string;
    }) => {
      // Use a batch write for atomicity
      const batch = writeBatch(firestore);

      // Update in restaurant's orders subcollection
      const restaurantOrderRef = doc(
        collection(firestore, "restaurants", restaurantId, "orders"),
        orderId
      );
      batch.update(restaurantOrderRef, {
        status,
        updatedAt: serverTimestamp(),
      });

      // Update in client's orders subcollection
      const clientOrderRef = doc(
        collection(firestore, "clients", userId, "orders"),
        orderId
      );
      batch.update(clientOrderRef, {
        status,
        updatedAt: serverTimestamp(),
      });

      await batch.commit();

      return { orderId, status };
    },
    onMutate: async ({ orderId, restaurantId, userId, status }) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({
        queryKey: queryKeys.restaurants.orders(restaurantId),
      });
      await queryClient.cancelQueries({
        queryKey: queryKeys.clients.orders(userId),
      });

      // Snapshot the previous values
      const previousRestaurantOrders = queryClient.getQueryData<Order[]>(
        queryKeys.restaurants.orders(restaurantId)
      );

      const previousClientOrders = queryClient.getQueryData<Order[]>(
        queryKeys.clients.orders(userId)
      );

      // Optimistically update orders
      if (previousRestaurantOrders) {
        queryClient.setQueryData(
          queryKeys.restaurants.orders(restaurantId),
          previousRestaurantOrders.map((order) =>
            order.orderId === orderId
              ? { ...order, status, updatedAt: new Date() }
              : order
          )
        );
      }

      if (previousClientOrders) {
        queryClient.setQueryData(
          queryKeys.clients.orders(userId),
          previousClientOrders.map((order) =>
            order.orderId === orderId
              ? { ...order, status, updatedAt: new Date() }
              : order
          )
        );
      }

      return { previousRestaurantOrders, previousClientOrders };
    },
    onError: (_, variables, context) => {
      const { restaurantId, userId } = variables;

      // If the mutation fails, use the context returned from onMutate to roll back
      if (context?.previousRestaurantOrders) {
        queryClient.setQueryData(
          queryKeys.restaurants.orders(restaurantId),
          context.previousRestaurantOrders
        );
      }

      if (context?.previousClientOrders) {
        queryClient.setQueryData(
          queryKeys.clients.orders(userId),
          context.previousClientOrders
        );
      }

      toast.error("Failed to update order status");
    },
    onSuccess: (_, { status }) => {
      toast.success(
        `Order ${
          status === "completed" ? "completed" : "updated"
        } successfully!`
      );
    },
    onSettled: (_, __, { restaurantId, userId }) => {
      // Always refetch after error or success to make sure our local data is correct
      queryClient.invalidateQueries({
        queryKey: queryKeys.restaurants.orders(restaurantId),
      });
      queryClient.invalidateQueries({
        queryKey: queryKeys.clients.orders(userId),
      });
    },
  });
}
