import React, { createContext, useContext, useState, useEffect } from 'react';
import { offlineQueueService } from '@/services/OfflineQueueService';
import { toast } from 'sonner';

interface OfflineContextType {
  isOnline: boolean;
  hasPendingActions: boolean;
  offlineMode: 'enabled' | 'disabled';
  enableOfflineMode: () => void;
  disableOfflineMode: () => void;
  syncOfflineActions: () => Promise<void>;
}

const OfflineContext = createContext<OfflineContextType | undefined>(undefined);

// eslint-disable-next-line react-refresh/only-export-components
export const useOffline = () => {
  const context = useContext(OfflineContext);
  if (context === undefined) {
    throw new Error('useOffline must be used within an OfflineProvider');
  }
  return context;
};

interface OfflineProviderProps {
  children: React.ReactNode;
}

export const OfflineProvider: React.FC<OfflineProviderProps> = ({ children }) => {
  const [isOnline, setIsOnline] = useState<boolean>(navigator.onLine);
  const [hasPendingActions, setHasPendingActions] = useState<boolean>(false);
  const [offlineMode, setOfflineMode] = useState<'enabled' | 'disabled'>(() => {
    // Check local storage for user preference
    const savedPreference = localStorage.getItem('offlineMode');
    return savedPreference === 'enabled' ? 'enabled' : 'disabled';
  });

  // Check for pending offline actions
  const checkPendingActions = async () => {
    try {
      const pendingActions = await offlineQueueService.getPendingOfflineActions();
      // Make sure pendingActions is an array before checking its length
      if (Array.isArray(pendingActions)) {
        setHasPendingActions(pendingActions.length > 0);
      } else {
        // If pendingActions is not an array, assume there are no pending actions
        setHasPendingActions(false);
        console.warn('Expected pendingActions to be an array, got:', pendingActions);
      }
    } catch (error) {
      console.error('Error checking pending actions:', error);
      // In case of error, assume there are no pending actions
      setHasPendingActions(false);
    }
  };

  // Handle online status changes
  useEffect(() => {
    const handleOnline = () => {
      setIsOnline(true);
      toast.success('You are back online!');

      // Process offline queue when back online
      if (hasPendingActions) {
        toast.info('Syncing offline actions...');
        offlineQueueService.processOfflineQueue()
          .then(() => {
            checkPendingActions();
          })
          .catch(console.error);
      }
    };

    const handleOffline = () => {
      setIsOnline(false);
      toast.warning('You are offline. Some features may be limited.');
    };

    // Add event listeners
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Initialize offline queue service
    offlineQueueService.initOfflineQueue();

    // Check for pending actions on mount
    checkPendingActions();

    // Clean up
    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [hasPendingActions]);

  // Enable offline mode
  const enableOfflineMode = () => {
    setOfflineMode('enabled');
    localStorage.setItem('offlineMode', 'enabled');
    toast.success('Offline mode enabled. Content will be available offline.');
  };

  // Disable offline mode
  const disableOfflineMode = () => {
    setOfflineMode('disabled');
    localStorage.setItem('offlineMode', 'disabled');
    toast.info('Offline mode disabled.');
  };

  // Manually sync offline actions
  const syncOfflineActions = async () => {
    if (!isOnline) {
      toast.error('Cannot sync while offline. Please connect to the internet.');
      return;
    }

    toast.info('Syncing offline actions...');

    try {
      await offlineQueueService.processOfflineQueue();
      await checkPendingActions();

      if (!hasPendingActions) {
        toast.success('All offline actions synced successfully!');
      } else {
        toast.warning('Some actions could not be synced. Please try again later.');
      }
    } catch (error) {
      console.error('Error syncing offline actions:', error);
      toast.error('Failed to sync offline actions. Please try again.');
    }
  };

  const value = {
    isOnline,
    hasPendingActions,
    offlineMode,
    enableOfflineMode,
    disableOfflineMode,
    syncOfflineActions,
  };

  return (
    <OfflineContext.Provider value={value}>
      {children}
    </OfflineContext.Provider>
  );
};
