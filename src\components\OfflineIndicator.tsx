import React from 'react';
import { useOffline } from '@/providers/OfflineProvider';
import { Button } from '@/components/ui/button';
import { Wifi, WifiOff, RefreshCw, Database } from 'lucide-react';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';

export const OfflineIndicator: React.FC = () => {
  const { 
    isOnline, 
    hasPendingActions, 
    offlineMode, 
    enableOfflineMode, 
    disableOfflineMode, 
    syncOfflineActions 
  } = useOffline();

  return (
    <div className="flex items-center gap-2">
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <div className="flex items-center">
              {isOnline ? (
                <Wifi className="h-4 w-4 text-green-500" />
              ) : (
                <WifiOff className="h-4 w-4 text-amber-500" />
              )}
            </div>
          </TooltipTrigger>
          <TooltipContent>
            <p>{isOnline ? 'Online' : 'Offline'}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>

      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button 
            variant="ghost" 
            size="sm" 
            className={`relative ${hasPendingActions ? 'text-amber-500' : ''}`}
          >
            <Database className="h-4 w-4" />
            {hasPendingActions && (
              <span className="absolute top-0 right-0 h-2 w-2 rounded-full bg-amber-500" />
            )}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuLabel>Offline Settings</DropdownMenuLabel>
          <DropdownMenuSeparator />
          
          <div className="p-2">
            <div className="flex items-center space-x-2">
              <Switch 
                id="offline-mode" 
                checked={offlineMode === 'enabled'}
                onCheckedChange={(checked) => {
                  if (checked) {
                    enableOfflineMode();
                  } else {
                    disableOfflineMode();
                  }
                }}
              />
              <Label htmlFor="offline-mode">Enable Offline Mode</Label>
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              Save content for offline use
            </p>
          </div>
          
          <DropdownMenuSeparator />
          
          {hasPendingActions && (
            <DropdownMenuItem 
              disabled={!isOnline}
              onClick={syncOfflineActions}
              className="flex items-center"
            >
              <RefreshCw className="mr-2 h-4 w-4" />
              Sync Pending Actions
              <span className="ml-auto bg-amber-100 text-amber-800 text-xs px-1 rounded">
                !
              </span>
            </DropdownMenuItem>
          )}
          
          <DropdownMenuItem 
            onClick={() => {
              // Force reload from network, bypassing cache
              window.location.reload();
            }}
          >
            <RefreshCw className="mr-2 h-4 w-4" />
            Refresh Page
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};
