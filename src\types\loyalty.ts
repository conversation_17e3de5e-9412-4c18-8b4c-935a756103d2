import { Timestamp } from "firebase/firestore";

/**
 * Represents a loyalty point transaction
 */
export interface PointTransaction {
  id: string;
  userId: string;
  points: number; // Positive for earned, negative for spent
  type:
    | "order"
    | "review"
    | "referral"
    | "redemption"
    | "challenge"
    | "bonus"
    | "reward"
    | "game";
  description: string;
  createdAt: Timestamp;
  referenceId?: string; // ID of related entity (order, review, etc.)
  expiresAt?: Timestamp; // When these points expire (if applicable)
  rewardId?: string; // ID of the reward if type is 'reward'
}

/**
 * Represents a loyalty reward that can be redeemed
 */
export interface LoyaltyReward {
  id: string;
  name: string;
  description: string;
  pointsCost: number;
  type: "discount" | "freeItem" | "freeDelivery" | "vipAccess" | "other";
  discountValue?: number; // For discount rewards (percentage)
  discountType?: "percentage" | "fixed"; // Type of discount
  menuItemId?: string; // For free item rewards
  restaurantId?: string; // For restaurant-specific rewards
  imageUrl?: string;
  isActive: boolean;
  expiresAt?: Timestamp; // When this reward expires (if applicable)
  termsAndConditions?: string;
  minimumOrderValue?: number; // Minimum order value to use reward
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

/**
 * Represents a redeemed reward
 */
export interface RedeemedReward {
  id: string;
  userId: string;
  rewardId: string;
  pointsSpent: number;
  redeemedAt: Timestamp;
  expiresAt: Timestamp;
  isUsed: boolean;
  usedAt?: Timestamp;
  orderId?: string; // Order where the reward was used
  code: string; // Unique code for the redeemed reward
}

/**
 * Represents a loyalty badge that can be earned
 */
export interface LoyaltyBadge {
  id: string;
  name: string;
  description: string;
  imageUrl: string;
  criteria: {
    type: "orders" | "reviews" | "points" | "referrals" | "custom";
    threshold: number; // Number required to earn the badge
    timeFrame?: "all" | "month" | "year"; // Time frame for the criteria
  };
  bonusPoints: number; // Points awarded when badge is earned
  createdAt: Timestamp;
}

/**
 * Represents a badge earned by a user
 */
export interface EarnedBadge {
  id: string;
  userId: string;
  badgeId: string;
  earnedAt: Timestamp;
}

/**
 * Represents a loyalty challenge that can be completed
 */
export interface LoyaltyChallenge {
  id: string;
  name: string;
  description: string;
  imageUrl?: string;
  criteria: {
    type: "orders" | "reviews" | "points" | "referrals" | "custom";
    threshold: number; // Number required to complete the challenge
    timeFrame: "day" | "week" | "month"; // Time frame for the challenge
    specificRestaurants?: string[]; // For restaurant-specific challenges
    specificCategories?: string[]; // For category-specific challenges
  };
  rewardPoints: number; // Points awarded when challenge is completed
  startDate: Timestamp;
  endDate: Timestamp;
  isActive: boolean;
}

/**
 * Represents a challenge completed by a user
 */
export interface CompletedChallenge {
  id: string;
  userId: string;
  challengeId: string;
  completedAt: Timestamp;
  pointsAwarded: number;
}

/**
 * Represents a referral made by a user
 */
export interface Referral {
  id: string;
  referrerId: string; // User who made the referral
  referralCode: string; // Unique code for the referral
  referredEmail?: string; // Email of the referred user (if known)
  referredUserId?: string; // User who was referred (once they sign up)
  status: "pending" | "completed" | "expired";
  createdAt: Timestamp;
  completedAt?: Timestamp; // When the referred user signed up
  pointsAwarded?: number; // Points awarded to the referrer
}

/**
 * Represents a user's loyalty program status
 */
export interface LoyaltyStatus {
  userId: string;
  totalPoints: number; // Current available points
  lifetimePoints: number; // Total points earned over lifetime
  tier: "bronze" | "silver" | "gold" | "platinum";
  tierExpiresAt?: Timestamp; // When the current tier expires
  nextTierPoints: number; // Points needed for next tier
  referralCode: string; // User's unique referral code
  referralCount: number; // Number of successful referrals
  joinedAt: Timestamp; // When the user joined the loyalty program
  lastUpdated: Timestamp;
  gameStats?: {
    lastPlayed?: { [gameId: string]: Timestamp }; // Last time each game was played
    dailyPointsEarned?: number; // Points earned from games today
    lastPointsReset?: Timestamp; // When daily points were last reset
    checkInStreak?: number; // Current check-in streak
    lastCheckIn?: Timestamp; // Last check-in date
    playCount?: { [gameId: string]: number }; // Number of times each game was played
  };
}

/**
 * Tier configuration for the loyalty program
 */
export interface LoyaltyTier {
  id: "bronze" | "silver" | "gold" | "platinum";
  name: string;
  threshold: number; // Points required to reach this tier
  multiplier: number; // Points multiplier for this tier
  perks: string[]; // List of perks for this tier
  imageUrl?: string;
}

/**
 * Point earning rules for different actions
 */
export interface PointsConfig {
  orderPointsPerAmount: number; // Points per currency unit spent
  reviewPoints: number; // Points for writing a review
  referralPoints: number; // Points for successful referral
  referredUserPoints: number; // Points for signing up via referral
  challengeCompletionBonus: number; // Bonus points for completing challenges
  pointsExpiration: number; // Days until points expire
  gamePoints: {
    foodMatch: number; // Points for completing food match game
    trivia: number; // Points for answering trivia questions correctly
    dailyCheckIn: number; // Base points for daily check-in
    streakBonus: number; // Additional points per day of streak
    maxStreakDays: number; // Maximum days for streak bonus
  };
  dailyGameLimit: number; // Maximum points that can be earned from games per day
}
