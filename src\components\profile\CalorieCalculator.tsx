import React, { useState, useEffect } from "react";
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { CalorieCalculator as CalorieCalculatorType } from "@/types";
import { Timestamp } from "firebase/firestore";
import { toast } from "sonner";

interface CalorieCalculatorProps {
  initialData?: CalorieCalculatorType;
  onSave: (data: CalorieCalculatorType) => void;
}

export const CalorieCalculator: React.FC<CalorieCalculatorProps> = ({
  initialData,
  onSave,
}) => {
  const [formData, setFormData] = useState<Omit<CalorieCalculatorType, "calculatedAt">>({
    age: initialData?.age || 30,
    gender: initialData?.gender || "male",
    weight: initialData?.weight || 70,
    height: initialData?.height || 170,
    activityLevel: initialData?.activityLevel || "moderate",
    goal: initialData?.goal || "maintain",
    dailyCalorieGoal: initialData?.dailyCalorieGoal || 0,
  });

  const [result, setResult] = useState<number | null>(
    initialData?.dailyCalorieGoal || null
  );

  // Calculate BMR using Mifflin-St Jeor Equation
  const calculateBMR = () => {
    const { weight, height, age, gender } = formData;
    
    if (gender === "male") {
      return 10 * weight + 6.25 * height - 5 * age + 5;
    } else {
      return 10 * weight + 6.25 * height - 5 * age - 161;
    }
  };

  // Calculate TDEE (Total Daily Energy Expenditure)
  const calculateTDEE = () => {
    const bmr = calculateBMR();
    const { activityLevel } = formData;
    
    const activityMultipliers = {
      sedentary: 1.2, // Little or no exercise
      light: 1.375, // Light exercise 1-3 days/week
      moderate: 1.55, // Moderate exercise 3-5 days/week
      active: 1.725, // Hard exercise 6-7 days/week
      "very active": 1.9, // Very hard exercise & physical job or 2x training
    };
    
    return bmr * activityMultipliers[activityLevel];
  };

  // Calculate daily calorie goal based on goal
  const calculateCalorieGoal = () => {
    const tdee = calculateTDEE();
    const { goal } = formData;
    
    switch (goal) {
      case "lose":
        return Math.round(tdee - 500); // 500 calorie deficit for weight loss
      case "gain":
        return Math.round(tdee + 500); // 500 calorie surplus for weight gain
      case "maintain":
      default:
        return Math.round(tdee);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: name === "age" || name === "weight" || name === "height" 
        ? parseFloat(value) || 0 
        : value,
    }));
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleCalculate = () => {
    try {
      const calorieGoal = calculateCalorieGoal();
      setResult(calorieGoal);
      setFormData((prev) => ({
        ...prev,
        dailyCalorieGoal: calorieGoal,
      }));
      toast.success("Calorie goal calculated successfully!");
    } catch (error) {
      console.error("Calculation error:", error);
      toast.error("Error calculating calorie goal. Please check your inputs.");
    }
  };

  const handleSave = () => {
    if (!result) {
      toast.error("Please calculate your calorie goal first");
      return;
    }

    const calculatorData: CalorieCalculatorType = {
      ...formData,
      calculatedAt: Timestamp.now(),
    };

    onSave(calculatorData);
  };

  // Calculate on initial load if we have all the data
  useEffect(() => {
    if (initialData && !result) {
      handleCalculate();
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [initialData]);

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="text-xl font-semibold">
          Daily Calorie Calculator
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="age">Age</Label>
            <Input
              id="age"
              name="age"
              type="number"
              value={formData.age}
              onChange={handleInputChange}
              min="15"
              max="100"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="gender">Gender</Label>
            <Select
              value={formData.gender}
              onValueChange={(value) => handleSelectChange("gender", value)}
            >
              <SelectTrigger id="gender">
                <SelectValue placeholder="Select gender" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="male">Male</SelectItem>
                <SelectItem value="female">Female</SelectItem>
                <SelectItem value="other">Other</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="weight">Weight (kg)</Label>
            <Input
              id="weight"
              name="weight"
              type="number"
              value={formData.weight}
              onChange={handleInputChange}
              min="30"
              max="300"
              step="0.1"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="height">Height (cm)</Label>
            <Input
              id="height"
              name="height"
              type="number"
              value={formData.height}
              onChange={handleInputChange}
              min="100"
              max="250"
              step="0.1"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="activityLevel">Activity Level</Label>
            <Select
              value={formData.activityLevel}
              onValueChange={(value) => handleSelectChange("activityLevel", value)}
            >
              <SelectTrigger id="activityLevel">
                <SelectValue placeholder="Select activity level" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="sedentary">
                  Sedentary (little or no exercise)
                </SelectItem>
                <SelectItem value="light">
                  Light (exercise 1-3 days/week)
                </SelectItem>
                <SelectItem value="moderate">
                  Moderate (exercise 3-5 days/week)
                </SelectItem>
                <SelectItem value="active">
                  Active (exercise 6-7 days/week)
                </SelectItem>
                <SelectItem value="very active">
                  Very Active (intense exercise daily)
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="goal">Goal</Label>
            <Select
              value={formData.goal}
              onValueChange={(value) => handleSelectChange("goal", value)}
            >
              <SelectTrigger id="goal">
                <SelectValue placeholder="Select your goal" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="lose">Lose Weight</SelectItem>
                <SelectItem value="maintain">Maintain Weight</SelectItem>
                <SelectItem value="gain">Gain Weight</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="flex flex-col sm:flex-row gap-4 pt-4">
          <Button onClick={handleCalculate} className="flex-1">
            Calculate
          </Button>
          <Button onClick={handleSave} variant="outline" className="flex-1">
            Save Results
          </Button>
        </div>

        {result && (
          <div className="mt-6 p-4 bg-muted rounded-lg">
            <h3 className="text-lg font-semibold mb-2">Your Results</h3>
            <p className="text-sm text-muted-foreground mb-2">
              Based on your inputs, your estimated daily calorie needs are:
            </p>
            <p className="text-2xl font-bold text-primary">{result} calories/day</p>
            <p className="text-sm text-muted-foreground mt-2">
              {formData.goal === "lose"
                ? "This represents a calorie deficit to help you lose weight."
                : formData.goal === "gain"
                ? "This represents a calorie surplus to help you gain weight."
                : "This represents the calories needed to maintain your current weight."}
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
