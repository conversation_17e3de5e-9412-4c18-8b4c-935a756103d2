import React, { useState, useEffect } from "react";
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON>er, Card<PERSON><PERSON>le, CardDescription } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { NotificationPreferences as NotificationPreferencesType } from "@/types";
import { toast } from "sonner";
import { Switch } from "@/components/ui/switch";
import { Bell, Mail, Smartphone, Info, Volume2, VolumeX } from "lucide-react";
import { Separator } from "@/components/ui/separator";
import { notificationService } from "@/services/NotificationService";

interface NotificationPreferencesProps {
  initialData?: NotificationPreferencesType;
  onSave: (data: NotificationPreferencesType) => void;
}

export const NotificationPreferences: React.FC<NotificationPreferencesProps> = ({
  initialData,
  onSave,
}) => {
  const [preferences, setPreferences] = useState<NotificationPreferencesType>(
    initialData || {
      email: true,
      push: true,
      inApp: true,
      sound: true,
      orderUpdates: true,
      promotions: false,
      newRestaurants: true,
      weeklyDigest: false,
    }
  );
  const [soundEnabled, setSoundEnabled] = useState<boolean>(
    initialData?.sound !== undefined ? initialData.sound :
    (typeof localStorage !== 'undefined' && localStorage.getItem('notificationSound') === 'false') ? false : true
  );
  const [permissionStatus, setPermissionStatus] = useState<string>('default');

  // Check notification permission on component mount
  useEffect(() => {
    if (typeof window !== 'undefined' && 'Notification' in window) {
      setPermissionStatus(Notification.permission);
    }
  }, []);

  const handleToggle = (field: keyof NotificationPreferencesType) => {
    setPreferences((prev) => ({
      ...prev,
      [field]: !prev[field],
    }));
  };

  const handleToggleSound = () => {
    const newValue = !soundEnabled;
    setSoundEnabled(newValue);

    // Update both the notification service and the preferences state
    notificationService.toggleSound(newValue);

    // Update the preferences state to include the sound setting
    setPreferences(prev => ({
      ...prev,
      sound: newValue
    }));
  };

  const handleRequestPermission = async () => {
    try {
      const permission = await notificationService.requestNotificationPermission();
      setPermissionStatus(permission);

      if (permission === 'granted') {
        toast.success("Notification permission granted");

        // If push is not enabled but permission is granted, enable it
        if (!preferences.push) {
          setPreferences(prev => ({
            ...prev,
            push: true
          }));
        }

        // Send a test notification
        notificationService.notify({
          title: "Notifications Enabled",
          message: "You will now receive notifications from SaleX",
          playSound: true
        });
      } else {
        toast.error("Notification permission denied. Please enable notifications in your browser settings.");
      }
    } catch (error) {
      console.error("Error requesting notification permission:", error);
      toast.error("Failed to request notification permission");
    }
  };

  const handleSave = () => {
    onSave(preferences);
    toast.success("Notification preferences saved");
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="text-xl font-semibold">Notification Preferences</CardTitle>
        <CardDescription>
          Customize how and when you receive notifications
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Notification Channels</h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <Mail className="h-5 w-5 text-primary" />
                <Label htmlFor="email-notifications" className="font-medium">
                  Email Notifications
                </Label>
              </div>
              <Switch
                id="email-notifications"
                checked={preferences.email}
                onCheckedChange={() => handleToggle("email")}
              />
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <Smartphone className="h-5 w-5 text-primary" />
                  <div>
                    <Label htmlFor="push-notifications" className="font-medium">
                      Push Notifications
                    </Label>
                    {permissionStatus !== 'granted' && (
                      <p className="text-xs text-muted-foreground">
                        Browser permission required
                      </p>
                    )}
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  {permissionStatus !== 'granted' && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleRequestPermission}
                    >
                      Allow
                    </Button>
                  )}
                  <Switch
                    id="push-notifications"
                    checked={preferences.push}
                    onCheckedChange={() => handleToggle("push")}
                    disabled={permissionStatus !== 'granted'}
                  />
                </div>
              </div>
              {permissionStatus === 'denied' && (
                <p className="text-xs text-destructive ml-8">
                  Notifications are blocked. Please enable them in your browser settings.
                </p>
              )}
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <Bell className="h-5 w-5 text-primary" />
                <Label htmlFor="inapp-notifications" className="font-medium">
                  In-App Notifications
                </Label>
              </div>
              <Switch
                id="inapp-notifications"
                checked={preferences.inApp}
                onCheckedChange={() => handleToggle("inApp")}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                {soundEnabled ? (
                  <Volume2 className="h-5 w-5 text-primary" />
                ) : (
                  <VolumeX className="h-5 w-5 text-primary" />
                )}
                <Label htmlFor="notification-sound" className="font-medium">
                  Notification Sound
                </Label>
              </div>
              <Switch
                id="notification-sound"
                checked={soundEnabled}
                onCheckedChange={handleToggleSound}
              />
            </div>
          </div>
        </div>

        <Separator />

        <div className="space-y-4">
          <h3 className="text-lg font-medium">Notification Types</h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="order-updates" className="font-medium">
                  Order Updates
                </Label>
                <p className="text-sm text-muted-foreground">
                  Receive notifications about your order status
                </p>
              </div>
              <Switch
                id="order-updates"
                checked={preferences.orderUpdates}
                onCheckedChange={() => handleToggle("orderUpdates")}
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="promotions" className="font-medium">
                  Promotions and Offers
                </Label>
                <p className="text-sm text-muted-foreground">
                  Receive special offers and promotions from restaurants
                </p>
              </div>
              <Switch
                id="promotions"
                checked={preferences.promotions}
                onCheckedChange={() => handleToggle("promotions")}
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="new-restaurants" className="font-medium">
                  New Restaurant Alerts
                </Label>
                <p className="text-sm text-muted-foreground">
                  Get notified when new restaurants join the platform
                </p>
              </div>
              <Switch
                id="new-restaurants"
                checked={preferences.newRestaurants}
                onCheckedChange={() => handleToggle("newRestaurants")}
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="weekly-digest" className="font-medium">
                  Weekly Digest
                </Label>
                <p className="text-sm text-muted-foreground">
                  Receive a weekly summary of new restaurants and popular dishes
                </p>
              </div>
              <Switch
                id="weekly-digest"
                checked={preferences.weeklyDigest}
                onCheckedChange={() => handleToggle("weeklyDigest")}
              />
            </div>
          </div>
        </div>

        <div className="bg-muted p-4 rounded-md flex items-start space-x-3 mt-4">
          <Info className="h-5 w-5 text-muted-foreground flex-shrink-0 mt-0.5" />
          <p className="text-sm text-muted-foreground">
            You can change your notification preferences at any time. Some notifications,
            such as account security alerts, cannot be disabled.
          </p>
        </div>

        <Button onClick={handleSave} className="w-full mt-6">
          Save Preferences
        </Button>
      </CardContent>
    </Card>
  );
};
