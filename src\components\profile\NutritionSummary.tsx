import React, { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { DietaryGoal } from "@/types";
import { mealTrackingService } from "@/services/mealTrackingService";
import { subDays, addDays, differenceInDays, startOfWeek, endOfWeek } from "date-fns";
import { ChevronLeft, ChevronRight, PieChart, BarChart3 } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";
import { Progress } from "@/components/ui/progress";
import { DateRangePicker } from "@/components/ui/simple-date-picker";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

interface NutritionSummaryProps {
  userId: string;
  dateRange: {
    start: Date;
    end: Date;
  };
  onDateRangeChange: (start: Date, end: Date) => void;
  dietaryGoals?: DietaryGoal[];
}

interface NutritionData {
  totalCalories: number;
  totalProtein: number;
  totalCarbs: number;
  totalFat: number;
  dailyAverages: {
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
  };
  mealTypeBreakdown: {
    breakfast: number;
    lunch: number;
    dinner: number;
    snack: number;
  };
}

export const NutritionSummary: React.FC<NutritionSummaryProps> = ({
  userId,
  dateRange,
  onDateRangeChange,
  dietaryGoals = []
}) => {
  const [nutritionData, setNutritionData] = useState<NutritionData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("overview");

  // Fetch nutrition data
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      try {
        const data = await mealTrackingService.getNutritionSummary(
          userId,
          dateRange.start,
          dateRange.end
        );
        setNutritionData(data);
      } catch (error) {
        console.error("Error fetching nutrition data:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [userId, dateRange]);

  // Handle date navigation
  const handlePreviousPeriod = () => {
    const days = differenceInDays(dateRange.end, dateRange.start) + 1;
    const newStart = subDays(dateRange.start, days);
    const newEnd = subDays(dateRange.end, days);
    onDateRangeChange(newStart, newEnd);
  };

  const handleNextPeriod = () => {
    const days = differenceInDays(dateRange.end, dateRange.start) + 1;
    const newStart = addDays(dateRange.start, days);
    const newEnd = addDays(dateRange.end, days);
    onDateRangeChange(newStart, newEnd);
  };

  const handleSelectWeek = () => {
    const start = startOfWeek(new Date(), { weekStartsOn: 1 }); // Monday
    const end = endOfWeek(new Date(), { weekStartsOn: 1 }); // Sunday
    onDateRangeChange(start, end);
  };

  const handleSelectMonth = () => {
    const today = new Date();
    const start = new Date(today.getFullYear(), today.getMonth(), 1);
    const end = new Date(today.getFullYear(), today.getMonth() + 1, 0);
    onDateRangeChange(start, end);
  };

  // Calculate progress for goals
  const calculateGoalProgress = (goal: DietaryGoal) => {
    if (!nutritionData || goal.target <= 0) return 0;

    let progress = 0;
    switch (goal.type) {
      case "calorie":
        progress = nutritionData.totalCalories;
        break;
      case "protein":
        progress = nutritionData.totalProtein;
        break;
      case "carbs":
        progress = nutritionData.totalCarbs;
        break;
      case "fat":
        progress = nutritionData.totalFat;
        break;
      default:
        return 0;
    }

    return Math.min(Math.max((progress / goal.target) * 100, 0), 100);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <Button variant="outline" size="icon" onClick={handlePreviousPeriod}>
          <ChevronLeft className="h-4 w-4" />
        </Button>

        <div className="min-w-[240px] flex flex-col">
          <div className="flex gap-2 mb-2">
            <Button variant="outline" className="flex-1 text-xs" onClick={handleSelectWeek}>
              This Week
            </Button>
            <Button variant="outline" className="flex-1 text-xs" onClick={handleSelectMonth}>
              This Month
            </Button>
          </div>
          <DateRangePicker
            startDate={dateRange.start}
            endDate={dateRange.end}
            onStartDateChange={(date) => onDateRangeChange(date, dateRange.end)}
            onEndDateChange={(date) => onDateRangeChange(dateRange.start, date)}
            className="w-full"
          />
        </div>

        <Button variant="outline" size="icon" onClick={handleNextPeriod}>
          <ChevronRight className="h-4 w-4" />
        </Button>
      </div>

      <Tabs defaultValue={activeTab} value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid grid-cols-2 mb-4">
          <TabsTrigger value="overview" className="flex items-center">
            <PieChart className="mr-2 h-4 w-4" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="goals" className="flex items-center">
            <BarChart3 className="mr-2 h-4 w-4" />
            Goals Progress
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview">
          {isLoading ? (
            <div className="space-y-4">
              <Skeleton className="h-24 w-full" />
              <Skeleton className="h-24 w-full" />
            </div>
          ) : nutritionData ? (
            <div className="space-y-6">
              <Card>
                <CardContent className="p-6">
                  <h3 className="text-lg font-medium mb-4">Nutrition Totals</h3>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="space-y-1">
                      <p className="text-sm text-muted-foreground">Calories</p>
                      <p className="text-2xl font-bold">{nutritionData.totalCalories}</p>
                      <p className="text-xs text-muted-foreground">
                        Avg: {Math.round(nutritionData.dailyAverages.calories)}/day
                      </p>
                    </div>
                    <div className="space-y-1">
                      <p className="text-sm text-muted-foreground">Protein</p>
                      <p className="text-2xl font-bold">{nutritionData.totalProtein}g</p>
                      <p className="text-xs text-muted-foreground">
                        Avg: {Math.round(nutritionData.dailyAverages.protein)}g/day
                      </p>
                    </div>
                    <div className="space-y-1">
                      <p className="text-sm text-muted-foreground">Carbs</p>
                      <p className="text-2xl font-bold">{nutritionData.totalCarbs}g</p>
                      <p className="text-xs text-muted-foreground">
                        Avg: {Math.round(nutritionData.dailyAverages.carbs)}g/day
                      </p>
                    </div>
                    <div className="space-y-1">
                      <p className="text-sm text-muted-foreground">Fat</p>
                      <p className="text-2xl font-bold">{nutritionData.totalFat}g</p>
                      <p className="text-xs text-muted-foreground">
                        Avg: {Math.round(nutritionData.dailyAverages.fat)}g/day
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <h3 className="text-lg font-medium mb-4">Meal Distribution</h3>
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Breakfast</span>
                        <span>{nutritionData.mealTypeBreakdown.breakfast} meals</span>
                      </div>
                      <Progress
                        value={
                          nutritionData.mealTypeBreakdown.breakfast /
                          Math.max(1, Object.values(nutritionData.mealTypeBreakdown).reduce((a, b) => a + b, 0)) * 100
                        }
                        className="h-2"
                      />
                    </div>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Lunch</span>
                        <span>{nutritionData.mealTypeBreakdown.lunch} meals</span>
                      </div>
                      <Progress
                        value={
                          nutritionData.mealTypeBreakdown.lunch /
                          Math.max(1, Object.values(nutritionData.mealTypeBreakdown).reduce((a, b) => a + b, 0)) * 100
                        }
                        className="h-2"
                      />
                    </div>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Dinner</span>
                        <span>{nutritionData.mealTypeBreakdown.dinner} meals</span>
                      </div>
                      <Progress
                        value={
                          nutritionData.mealTypeBreakdown.dinner /
                          Math.max(1, Object.values(nutritionData.mealTypeBreakdown).reduce((a, b) => a + b, 0)) * 100
                        }
                        className="h-2"
                      />
                    </div>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Snacks</span>
                        <span>{nutritionData.mealTypeBreakdown.snack} meals</span>
                      </div>
                      <Progress
                        value={
                          nutritionData.mealTypeBreakdown.snack /
                          Math.max(1, Object.values(nutritionData.mealTypeBreakdown).reduce((a, b) => a + b, 0)) * 100
                        }
                        className="h-2"
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          ) : (
            <div className="text-center p-8 border border-dashed rounded-md">
              <p className="text-muted-foreground">No nutrition data available for this period</p>
            </div>
          )}
        </TabsContent>

        <TabsContent value="goals">
          {isLoading ? (
            <div className="space-y-4">
              <Skeleton className="h-24 w-full" />
              <Skeleton className="h-24 w-full" />
            </div>
          ) : dietaryGoals.length > 0 ? (
            <div className="space-y-4">
              {dietaryGoals
                .filter(goal => goal.trackingEnabled)
                .map((goal) => {
                  const progressPercent = calculateGoalProgress(goal);
                  return (
                    <Card key={goal.id}>
                      <CardContent className="p-6">
                        <div className="flex justify-between items-start mb-2">
                          <div>
                            <h4 className="font-medium">
                              {goal.type.charAt(0).toUpperCase() + goal.type.slice(1)} Goal
                            </h4>
                            <p className="text-sm text-muted-foreground">
                              Target: {goal.target} {goal.unit}
                            </p>
                          </div>
                          <div className="text-right">
                            <p className="font-medium">
                              {nutritionData ? (
                                <>
                                  {goal.type === "calorie" && nutritionData.totalCalories}
                                  {goal.type === "protein" && nutritionData.totalProtein}
                                  {goal.type === "carbs" && nutritionData.totalCarbs}
                                  {goal.type === "fat" && nutritionData.totalFat}
                                </>
                              ) : 0} {goal.unit}
                            </p>
                            <p className="text-sm text-muted-foreground">
                              {Math.round(progressPercent)}% of goal
                            </p>
                          </div>
                        </div>
                        <Progress value={progressPercent} className="h-2" />
                      </CardContent>
                    </Card>
                  );
                })}
            </div>
          ) : (
            <div className="text-center p-8 border border-dashed rounded-md">
              <p className="text-muted-foreground">No dietary goals set</p>
              <p className="text-sm text-muted-foreground mt-1">
                Add goals in the Dietary Goals section to track your progress
              </p>
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
};
