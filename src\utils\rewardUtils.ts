/**
 * Generates a random redemption code for rewards
 * Format: XXXX-XXXX-XXXX where X is an alphanumeric character
 * @returns A unique redemption code
 */
export const generateRedemptionCode = (): string => {
  const characters = "ABCDEFGHJKLMNPQRSTUVWXYZ23456789"; // Removed similar looking characters (0, O, 1, I)
  const length = 4; // Length of each segment
  const segments = 3; // Number of segments

  let code = "";

  for (let s = 0; s < segments; s++) {
    for (let i = 0; i < length; i++) {
      const randomIndex = Math.floor(Math.random() * characters.length);
      code += characters.charAt(randomIndex);
    }

    if (s < segments - 1) {
      code += "-";
    }
  }

  return code;
};

/**
 * Formats a reward's price or discount value for display
 * @param value The numeric value
 * @param type The type of discount ('percentage' or 'fixed')
 * @returns Formatted string with appropriate symbol
 */
export const formatRewardValue = (
  value: number | undefined,
  type?: "percentage" | "fixed"
): string => {
  if (value === undefined) return "";

  if (type === "percentage") {
    return `${value}%`;
  } else if (type === "fixed") {
    return `$${value.toFixed(2)}`;
  }

  return value.toString();
};

/**
 * Checks if a reward is currently active
 * @param reward The reward to check
 * @returns Boolean indicating if the reward is active
 */
export const isRewardActive = (
  reward: {
    isActive?: boolean;
    expiresAt?: { toDate: () => Date };
    availableQuantity?: number;
  } | null
): boolean => {
  if (!reward) return false;

  // Check if explicitly marked as inactive
  if (!reward.isActive) return false;

  // Check if expired
  if (reward.expiresAt && reward.expiresAt.toDate() < new Date()) return false;

  // Check if out of stock
  if (reward.availableQuantity !== undefined && reward.availableQuantity <= 0)
    return false;

  return true;
};
