import React, { useState } from "react";
import { MenuItem } from "@/types/menu";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Info } from "lucide-react";

interface NutritionInfoProps {
  item: MenuItem;
  trigger?: React.ReactNode;
}

export const NutritionInfo: React.FC<NutritionInfoProps> = ({
  item,
  trigger,
}) => {
  const [open, setOpen] = useState(false);

  // Helper function to check if nutrition data exists
  const hasNutritionData = () => {
    return (
      item.calories !== undefined ||
      item.protein !== undefined ||
      item.carbs !== undefined ||
      item.fat !== undefined
    );
  };

  // Helper function to format nutrition values
  const formatValue = (value: number | undefined, unit: string = "") => {
    if (value === undefined) return "N/A";
    return `${value}${unit}`;
  };

  // Helper function to get health label color
  const getHealthLabelColor = (label: string) => {
    switch (label) {
      case "Low Fat":
      case "Low Sodium":
      case "Low Calorie":
      case "Low Carb":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100";
      case "High Protein":
      case "High Fiber":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-100";
      case "Sugar Free":
      case "No Added Sugar":
        return "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-100";
      case "Heart Healthy":
      case "Immunity Boosting":
        return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-100";
      case "Antioxidant Rich":
      case "Superfood":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-100";
      case "Locally Sourced":
      case "Sustainable":
        return "bg-teal-100 text-teal-800 dark:bg-teal-900 dark:text-teal-100";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-100";
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button
            variant="ghost"
            size="sm"
            className="p-0 h-auto"
            title="Nutrition Information"
          >
            <Info className="h-4 w-4" />
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="sm:max-w-md" hideCloseButton>
        <DialogHeader>
          <DialogTitle>Nutrition Information</DialogTitle>
          <DialogDescription>
            Nutritional details for {item.name}
          </DialogDescription>
        </DialogHeader>

        {hasNutritionData() ? (
          <div className="space-y-4">
            {/* Serving information */}
            <div className="text-sm text-muted-foreground">
              {item.servingSize && (
                <p>
                  Serving Size: {item.servingSize}
                  {item.servingsPerItem && item.servingsPerItem > 1
                    ? ` (${item.servingsPerItem} servings per item)`
                    : ""}
                </p>
              )}
            </div>

            {/* Main nutrition info */}
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-1">
                <p className="text-sm font-medium">Calories</p>
                <p className="text-2xl font-bold">
                  {formatValue(item.calories)}
                </p>
              </div>
            </div>

            {/* Macronutrients */}
            <div>
              <h4 className="text-sm font-medium mb-2">Macronutrients</h4>
              <div className="grid grid-cols-3 gap-4">
                <div className="space-y-1">
                  <p className="text-xs text-muted-foreground">Protein</p>
                  <p className="font-medium">
                    {formatValue(item.protein, "g")}
                  </p>
                </div>
                <div className="space-y-1">
                  <p className="text-xs text-muted-foreground">Carbs</p>
                  <p className="font-medium">{formatValue(item.carbs, "g")}</p>
                </div>
                <div className="space-y-1">
                  <p className="text-xs text-muted-foreground">Fat</p>
                  <p className="font-medium">{formatValue(item.fat, "g")}</p>
                </div>
              </div>
            </div>

            {/* Detailed nutrition */}
            {(item.fiber !== undefined ||
              item.sugar !== undefined ||
              item.sodium !== undefined ||
              item.cholesterol !== undefined) && (
              <>
                <Separator />
                <div>
                  <h4 className="text-sm font-medium mb-2">
                    Detailed Nutrition
                  </h4>
                  <div className="grid grid-cols-2 gap-x-4 gap-y-2">
                    {item.fiber !== undefined && (
                      <div className="flex justify-between">
                        <span className="text-xs text-muted-foreground">
                          Fiber
                        </span>
                        <span className="text-xs">
                          {formatValue(item.fiber, "g")}
                        </span>
                      </div>
                    )}
                    {item.sugar !== undefined && (
                      <div className="flex justify-between">
                        <span className="text-xs text-muted-foreground">
                          Sugar
                        </span>
                        <span className="text-xs">
                          {formatValue(item.sugar, "g")}
                        </span>
                      </div>
                    )}
                    {item.sodium !== undefined && (
                      <div className="flex justify-between">
                        <span className="text-xs text-muted-foreground">
                          Sodium
                        </span>
                        <span className="text-xs">
                          {formatValue(item.sodium, "mg")}
                        </span>
                      </div>
                    )}
                    {item.cholesterol !== undefined && (
                      <div className="flex justify-between">
                        <span className="text-xs text-muted-foreground">
                          Cholesterol
                        </span>
                        <span className="text-xs">
                          {formatValue(item.cholesterol, "mg")}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              </>
            )}

            {/* Health labels */}
            {item.healthLabels && item.healthLabels.length > 0 && (
              <>
                <Separator />
                <div>
                  <h4 className="text-sm font-medium mb-2">Health Labels</h4>
                  <div className="flex flex-wrap gap-2">
                    {item.healthLabels.map((label) => (
                      <Badge
                        key={label}
                        variant="outline"
                        className={`${getHealthLabelColor(label)} border-0`}
                      >
                        {label}
                      </Badge>
                    ))}
                  </div>
                </div>
              </>
            )}

            {/* Allergens */}
            {item.allergens && item.allergens.length > 0 && (
              <>
                <Separator />
                <div>
                  <h4 className="text-sm font-medium mb-2">Allergens</h4>
                  <div className="flex flex-wrap gap-2">
                    {item.allergens.map((allergen) => (
                      <Badge key={allergen} variant="destructive">
                        {allergen}
                      </Badge>
                    ))}
                  </div>
                </div>
              </>
            )}
          </div>
        ) : (
          <div className="py-6 text-center text-muted-foreground">
            <p>No detailed nutrition information available for this item.</p>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};
