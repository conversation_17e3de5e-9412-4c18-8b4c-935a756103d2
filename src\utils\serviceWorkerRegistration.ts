// Service Worker Registration Utility

/**
 * Register the service worker for offline support
 * @returns A promise that resolves when the service worker is registered
 */
export const registerServiceWorker = async (): Promise<ServiceWorkerRegistration | null> => {
  if ('serviceWorker' in navigator) {
    try {
      const registration = await navigator.serviceWorker.register('/sw.js', {
        scope: '/',
      });

      // Check if there's an update available
      registration.addEventListener('updatefound', () => {
        const newWorker = registration.installing;

        if (newWorker) {
          newWorker.addEventListener('statechange', () => {
            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
              // You could show a notification to the user here
            }
          });
        }
      });

      return registration;
    } catch (error) {
      console.error('Service Worker registration failed:', error);
      return null;
    }
  } else {
    console.warn('Service Workers are not supported in this browser');
    return null;
  }
};

/**
 * Unregister all service workers
 * @returns A promise that resolves when all service workers are unregistered
 */
export const unregisterServiceWorkers = async (): Promise<void> => {
  if ('serviceWorker' in navigator) {
    try {
      const registrations = await navigator.serviceWorker.getRegistrations();

      for (const registration of registrations) {
        await registration.unregister();
      }
    } catch (error) {
      console.error('Service Worker unregistration failed:', error);
    }
  }
};

/**
 * Update all service workers
 * @returns A promise that resolves when all service workers are updated
 */
export const updateServiceWorkers = async (): Promise<void> => {
  if ('serviceWorker' in navigator) {
    try {
      const registrations = await navigator.serviceWorker.getRegistrations();

      for (const registration of registrations) {
        await registration.update();
      }
    } catch (error) {
      console.error('Service Worker update failed:', error);
    }
  }
};

/**
 * Check if the app is installed (PWA)
 * @returns A boolean indicating if the app is installed
 */
// Define extended Navigator interface for iOS standalone mode
interface NavigatorWithStandalone extends Navigator {
  standalone?: boolean;
}

export const isAppInstalled = (): boolean => {
  return window.matchMedia('(display-mode: standalone)').matches ||
         (window.navigator as NavigatorWithStandalone).standalone === true;
};

/**
 * Check if the app can be installed (PWA)
 * @param callback A function to call when the app can be installed
 * @returns A function to remove the event listener
 */
// Define BeforeInstallPromptEvent interface
interface BeforeInstallPromptEvent extends Event {
  prompt: () => Promise<void>;
  userChoice: Promise<{ outcome: 'accepted' | 'dismissed' }>;
}

export const checkAppInstallable = (callback: () => void): (() => void) => {
  // We don't need to store the prompt here since we're just notifying via callback
  const handler = (e: Event) => {
    // Prevent the mini-infobar from appearing on mobile
    e.preventDefault();
    // Store the event for later use (commented out as we're not using it directly)
    // const deferredPrompt = e as BeforeInstallPromptEvent;
    // Call the callback to notify that the app is installable
    callback();
  };

  window.addEventListener('beforeinstallprompt', handler);

  // Return a function to remove the event listener
  return () => {
    window.removeEventListener('beforeinstallprompt', handler);
  };
};

/**
 * Show the install prompt for the app (PWA)
 * @returns A promise that resolves with a boolean indicating if the app was installed
 */
export const showInstallPrompt = async (): Promise<boolean> => {
  let deferredPrompt: BeforeInstallPromptEvent | null = null;

  return new Promise((resolve) => {
    const handler = (e: Event) => {
      // Prevent the mini-infobar from appearing on mobile
      e.preventDefault();
      // Stash the event so it can be triggered later
      deferredPrompt = e as BeforeInstallPromptEvent;

      // Remove the event listener
      window.removeEventListener('beforeinstallprompt', handler);

      // Show the install prompt
      if (deferredPrompt) {
        deferredPrompt.prompt();

        deferredPrompt.userChoice.then((choiceResult) => {
          if (choiceResult.outcome === 'accepted') {
            resolve(true);
          } else {
            resolve(false);
          }

          deferredPrompt = null;
        });
      } else {
        resolve(false);
      }
    };

    window.addEventListener('beforeinstallprompt', handler);

    // If the event doesn't fire within 1 second, resolve with false
    setTimeout(() => {
      window.removeEventListener('beforeinstallprompt', handler);
      if (!deferredPrompt) {
        resolve(false);
      }
    }, 1000);
  });
};
