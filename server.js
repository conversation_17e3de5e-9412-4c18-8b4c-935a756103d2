/**
 * Newsletter API - Server Entry Point
 *
 * This file serves as the modern entry point for the Newsletter API.
 * It uses a more modular approach compared to index.js.
 */

// Import required modules
const express = require("express");
const cors = require("cors");
const helmet = require("helmet");
const morgan = require("morgan");
const dotenv = require("dotenv");
const logger = require("./utils/logger");
const scheduledTasks = require("./services/scheduledTasks");

// Load environment variables
dotenv.config();

// Import routes
const routes = require("./routes");

// Import middleware
const { errorHandler, notFound } = require("./middlewares/errorHandler");

// Create Express app
const app = express();
const PORT = process.env.PORT || 3000;

// Basic middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(helmet());
app.use(morgan("dev"));

// Enhanced CORS configuration with security features
const corsOptions = {
  origin: function (origin, callback) {
    // Allowed origins
    const allowedOrigins = [
      // Production origins
      "https://qonai.me",
      "https://salex-2025.firebaseapp.com",
      "https://api.qonai.me",
    ];

    // Add development origins if not in production
    if (process.env.NODE_ENV !== "production") {
      allowedOrigins.push(
        "http://localhost:5173",
        "http://localhost:3000",
        "http://localhost:5000"
      );
    }

    // Allow requests with no origin (like mobile apps, curl, postman)
    if (!origin) {
      return callback(null, true);
    }

    // Check if origin is allowed
    if (allowedOrigins.indexOf(origin) !== -1) {
      callback(null, true);
    } else {
      // Log unauthorized origin attempts
      logger.warn(`CORS blocked request from unauthorized origin: ${origin}`);
      callback(new Error(`Origin ${origin} not allowed by CORS`));
    }
  },
  methods: ["GET", "POST", "OPTIONS"],
  allowedHeaders: [
    "Content-Type",
    "Authorization",
    "X-API-Key",
    "X-Requested-With",
    "Accept",
  ],
  exposedHeaders: ["Content-Length", "X-Rate-Limit"],
  credentials: true,
  maxAge: 86400, // 24 hours
  preflightContinue: false,
  optionsSuccessStatus: 204,
};

app.use(cors(corsOptions));

// Mount routes at both root and /api prefix for backward compatibility
app.use("/", routes);
app.use("/api", routes);

// Error handling middleware
app.use(notFound);
app.use(errorHandler);

// Start server
if (process.env.NODE_ENV !== "test") {
  const server = app.listen(PORT, () => {
    logger.info(`Newsletter API running on port ${PORT}`);
    logger.info(`Environment: ${process.env.NODE_ENV || "development"}`);
    logger.info(`Health check: http://localhost:${PORT}/api/health`);

    // Start scheduled cleanup tasks
    scheduledTasks.startScheduledCleanup();
  });

  // Graceful shutdown
  process.on("SIGTERM", () => {
    logger.info("SIGTERM signal received: closing HTTP server");

    // Stop scheduled tasks
    scheduledTasks.stopScheduledCleanup();

    server.close(() => {
      logger.info("HTTP server closed");
    });
  });
}

// Export app for testing
module.exports = app;
