/**
 * Notification controller for handling email notifications
 */
const emailService = require('../services/emailService');

/**
 * Send notification email
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const sendNotificationEmail = async (req, res) => {
  try {
    const { to, subject, body, recipientName, type, data } = req.body;

    if (!to || !subject) {
      return res.status(400).json({ error: 'Recipient email and subject are required' });
    }

    // If no body or type is provided, return error
    if (!body && !type) {
      return res.status(400).json({ error: 'Either body or type is required' });
    }

    // Send notification email
    const success = await emailService.sendNotificationEmail({
      to,
      subject,
      body: body || '',
      recipientName: recipientName || '',
      type,
      data,
    });

    if (!success) {
      return res.status(500).json({ error: 'Failed to send notification email' });
    }

    res.status(200).json({ message: 'Notification email sent successfully' });
  } catch (error) {
    console.error('Error in sendNotificationEmail:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

module.exports = {
  sendNotificationEmail,
};
