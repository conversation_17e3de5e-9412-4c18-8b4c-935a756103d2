import { Restaurant, Table, Reservation, LayoutElement } from "./types";
import {
  collection,
  getDocs,
  query,
  where,
  doc,
  getDoc,
} from "firebase/firestore";
import { firestore } from "@/config/firebase";

export const isRestaurantOpen = (
  restaurant: Restaurant | null,
  date: string,
  time: string
): boolean => {
  try {
    // If the restaurant has manually set isOpen to true and is not using auto-update,
    // respect that setting and consider it open regardless of working hours
    if (restaurant?.isOpen === true && restaurant?.autoUpdateStatus === false) {
      return true;
    }

    if (!restaurant?.workingHours) return false;

    const dayOfWeek = new Date(date)
      .toLocaleDateString("en-US", { weekday: "long" })
      .toLowerCase();

    const workingHours = restaurant.workingHours.find(
      (h) => h?.day === dayOfWeek
    );
    if (
      !workingHours?.isOpen ||
      !workingHours?.openTime ||
      !workingHours?.closeTime
    )
      return false;

    const [hours, minutes] = time.split(":");
    if (!hours || !minutes) return false;

    const requestTime = new Date(date);
    requestTime.setHours(parseInt(hours), parseInt(minutes));

    const [openHours, openMinutes] = workingHours.openTime.split(":");
    const [closeHours, closeMinutes] = workingHours.closeTime.split(":");
    if (!openHours || !openMinutes || !closeHours || !closeMinutes)
      return false;

    const openTime = new Date(date);
    openTime.setHours(parseInt(openHours), parseInt(openMinutes));

    const closeTime = new Date(date);
    closeTime.setHours(parseInt(closeHours), parseInt(closeMinutes));

    // Special case for 24-hour restaurants (00:00 - 00:00)
    if (
      openTime.getTime() === closeTime.getTime() &&
      openTime.getHours() === 0 &&
      openTime.getMinutes() === 0
    ) {
      console.log(`Restaurant ${restaurant?.restaurantName} is open 24 hours`);
      return true;
    }

    // Handle restaurants that close after midnight
    if (workingHours.closeTime === "00:00") {
      // If closeTime is exactly midnight, consider the restaurant open until midnight
      const isOpen = time >= workingHours.openTime || time === "00:00";
      console.log(
        `Restaurant ${restaurant?.restaurantName} on ${dayOfWeek} at ${time}: Midnight closing case, isOpen=${isOpen}`
      );
      return isOpen;
    }

    // Handle cases where closing time is after midnight (e.g., 02:00)
    if (workingHours.closeTime < workingHours.openTime) {
      // If time is after opening time OR before closing time (next day)
      const isOpen =
        time >= workingHours.openTime || time <= workingHours.closeTime;
      console.log(
        `Restaurant ${restaurant?.restaurantName} on ${dayOfWeek} at ${time}: After-midnight case, isOpen=${isOpen}`
      );
      return isOpen;
    }

    // Normal case: opening and closing times are on the same day
    const isOpen =
      time >= workingHours.openTime && time <= workingHours.closeTime;
    console.log(
      `Restaurant ${restaurant?.restaurantName} on ${dayOfWeek} at ${time}: Normal case, isOpen=${isOpen}`
    );
    return isOpen;
  } catch (error) {
    console.error("Error checking restaurant hours:", error);
    return false;
  }
};

export const isValidDateTime = (
  date: string,
  time: string,
  departureTime?: string
): boolean => {
  const [hours, minutes] = time.split(":").map(Number);
  const selectedDateTime = new Date(date);
  selectedDateTime.setHours(hours, minutes, 0, 0);

  const now = new Date();
  now.setSeconds(0, 0);

  const selectedDate = new Date(date);
  const today = new Date();
  const isToday = selectedDate.toDateString() === today.toDateString();

  if (isToday) {
    const minimumAdvanceMinutes = 15;
    const timeDifferenceMinutes =
      (selectedDateTime.getTime() - now.getTime()) / (1000 * 60);
    if (timeDifferenceMinutes < minimumAdvanceMinutes) return false;
  } else if (selectedDateTime < now) {
    return false;
  }

  if (departureTime) {
    const [depHours, depMinutes] = departureTime.split(":").map(Number);
    const departureDateTime = new Date(date);
    departureDateTime.setHours(depHours, depMinutes, 0, 0);
    return departureDateTime > selectedDateTime;
  }

  return true;
};

export const checkTableAvailability = (
  restaurant: Restaurant | null,
  tables: Table[],
  reservations: Reservation[],
  newReservation: {
    date: string;
    arrivalTime: string;
    departureTime: string;
    partySize: number;
  },
  isRestaurantOpen: (
    restaurant: Restaurant | null,
    date: string,
    time: string
  ) => boolean
) => {
  if (
    !restaurant?.workingHours?.length ||
    !tables?.length ||
    !newReservation?.date ||
    !newReservation?.arrivalTime
  ) {
    return [];
  }

  const isOpen = isRestaurantOpen(
    restaurant,
    newReservation.date,
    newReservation.arrivalTime
  );

  // Return empty array if restaurant is not open during the requested time
  if (!isOpen) {
    return [];
  }

  try {
    const reservedTables = new Set<string>();
    reservations.forEach((reservation) => {
      if (
        reservation?.tableId &&
        (reservation.status === "confirmed" ||
          reservation.status === "active" ||
          reservation.status === "pending") &&
        reservation.date === newReservation.date
      ) {
        const reservationStart = new Date(
          `${reservation.date}T${reservation.arrivalTime}`
        ).getTime();
        const reservationEnd = reservation.departureTime
          ? new Date(
              `${reservation.date}T${reservation.departureTime}`
            ).getTime()
          : reservationStart + 2 * 60 * 60 * 1000;

        const newStart = new Date(
          `${newReservation.date}T${newReservation.arrivalTime}`
        ).getTime();
        const newEnd = newReservation.departureTime
          ? new Date(
              `${newReservation.date}T${newReservation.departureTime}`
            ).getTime()
          : newStart + 2 * 60 * 60 * 1000;

        if (!(newEnd <= reservationStart || newStart >= reservationEnd)) {
          reservedTables.add(reservation.tableId);
        }
      }
    });

    // For testing purposes, relax the capacity requirement
    const availableTables = tables.filter(
      (table) => table?.id && !reservedTables.has(table.id)
      // Comment out capacity checks for testing
      // && table.capacity >= (newReservation.partySize || 0)
      // && (newReservation.partySize || 0) >= Math.ceil(table.capacity * 0.5)
    );
    return availableTables;
  } catch (error) {
    console.error("Error checking table availability:", error);
    return [];
  }
};

export const fetchRestaurantData = async (
  username: string
): Promise<Restaurant | null> => {
  try {
    const restaurantsRef = collection(firestore, "restaurants");
    const q = query(restaurantsRef, where("username", "==", username));
    const querySnapshot = await getDocs(q);

    if (querySnapshot.empty) return null;

    const restaurantDoc = querySnapshot.docs[0];
    const restaurantData = restaurantDoc.data();

    return {
      id: restaurantDoc.id,
      ...restaurantData,
      workingHours: restaurantData.workingHours || [],
    } as Restaurant;
  } catch (error) {
    console.error("Error fetching restaurant data:", error);
    return null;
  }
};

export const fetchTablesAndLayoutData = async (
  restaurantId: string
): Promise<{ tables: Table[]; layout: LayoutElement[] } | null> => {
  try {
    const settingsDoc = await getDoc(
      doc(firestore, "restaurants", restaurantId, "settings", "config")
    );
    const settings = settingsDoc.exists()
      ? settingsDoc.data()
      : { tables: [], layout: [] };
    return {
      tables: settings.tables || [],
      layout: settings.layout || [],
    };
  } catch (error) {
    console.error("Error fetching tables and layout data:", error);
    return null;
  }
};

export const fetchReservationsData = async (
  restaurantId: string,
  date: string
): Promise<Reservation[] | null> => {
  try {
    const restaurantRef = doc(firestore, "restaurants", restaurantId);
    const reservationsRef = collection(restaurantRef, "reservations");
    const reservationsQuery = query(
      reservationsRef,
      where("date", "==", date),
      where("status", "in", ["confirmed", "pending", "active"])
    );
    const reservationsSnapshot = await getDocs(reservationsQuery);
    return reservationsSnapshot.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    })) as Reservation[];
  } catch (error) {
    console.error("Error fetching reservations data:", error);
    return null;
  }
};
