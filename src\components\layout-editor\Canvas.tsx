// src/components/layout-editor/Canvas.tsx
import React, { useCallback } from "react";
import { Rnd, DraggableData, ResizableD<PERSON><PERSON>, Position } from "react-rnd"; // Kullanılan tipler import edildi
import type { DraggableEvent } from "react-draggable"; // react-draggable'dan <PERSON> import edildi
import type { Element } from "./types";

// ResizeDirection tipi tanımlandı
type ResizeDirection =
  | "top"
  | "right"
  | "bottom"
  | "left"
  | "topLeft"
  | "topRight"
  | "bottomLeft"
  | "bottomRight";

const MIN_ELEMENT_SIZE = 20;
// Maximum size will be dynamically calculated based on canvas dimensions

interface CanvasProps {
  elements: Element[];
  selectedElements: string[];
  zoom: number;
  gridSize: number;
  snapToGrid: boolean;
  layoutRef: React.RefObject<HTMLDivElement>;
  onElementClick: (e: React.MouseEvent, elementId: string) => void;
  onCanvasClick: (e: React.MouseEvent<HTMLDivElement>) => void;
  onUpdateElement: (
    id: string,
    updates: Partial<Element>,
    updateHistory?: boolean
  ) => void;
  style?: React.CSSProperties; // Add style prop for custom dimensions
}

export const Canvas: React.FC<CanvasProps> = ({
  elements,
  selectedElements,
  zoom,
  gridSize,
  snapToGrid,
  layoutRef,
  onElementClick,
  onCanvasClick,
  onUpdateElement,
  style, // Add style prop
}) => {
  // --- Rnd için Olay Yöneticileri ---

  const handleRndDragStop = useCallback(
    (elementId: string) => (_e: DraggableEvent, d: DraggableData) => {
      // Get canvas dimensions to limit element position
      const canvasWidth = layoutRef.current?.clientWidth || 1000;
      const canvasHeight = layoutRef.current?.clientHeight || 600;

      // Find the element to get its dimensions
      const element = elements.find((el) => el.id === elementId);
      if (!element) return;

      // If snap to grid is enabled, ensure the position is snapped
      if (snapToGrid) {
        // Use Math.floor instead of Math.round for more precise snapping
        // This ensures elements align exactly with grid lines
        let snappedX = Math.floor(d.x / gridSize) * gridSize;
        let snappedY = Math.floor(d.y / gridSize) * gridSize;

        // Ensure element stays within canvas bounds
        snappedX = Math.max(0, Math.min(snappedX, canvasWidth - element.width));
        snappedY = Math.max(
          0,
          Math.min(snappedY, canvasHeight - element.height)
        );

        onUpdateElement(elementId, { x: snappedX, y: snappedY }, true);
      } else {
        // Even without snapping, ensure element stays within canvas bounds
        const boundedX = Math.max(
          0,
          Math.min(d.x, canvasWidth - element.width)
        );
        const boundedY = Math.max(
          0,
          Math.min(d.y, canvasHeight - element.height)
        );

        onUpdateElement(elementId, { x: boundedX, y: boundedY }, true);
      }
    },
    [onUpdateElement, snapToGrid, gridSize, elements, layoutRef]
  );

  const handleRndResizeStop = useCallback(
    (elementId: string) =>
      (
        _e: MouseEvent | TouchEvent,
        _direction: ResizeDirection,
        ref: HTMLElement,
        _delta: ResizableDelta,
        position: Position
      ) => {
        // If snap to grid is enabled, ensure the size and position are snapped
        if (snapToGrid) {
          // Get canvas dimensions to limit element size
          const canvasWidth = layoutRef.current?.clientWidth || 1000;
          const canvasHeight = layoutRef.current?.clientHeight || 600;

          // Use Math.floor for position to ensure exact alignment with grid lines
          // Use Math.ceil for width/height to ensure elements cover full grid cells
          let snappedWidth = Math.ceil(ref.offsetWidth / gridSize) * gridSize;
          let snappedHeight = Math.ceil(ref.offsetHeight / gridSize) * gridSize;

          // Limit element size to canvas dimensions
          snappedWidth = Math.min(snappedWidth, canvasWidth);
          snappedHeight = Math.min(snappedHeight, canvasHeight);

          const snappedX = Math.floor(position.x / gridSize) * gridSize;
          const snappedY = Math.floor(position.y / gridSize) * gridSize;

          // Ensure element stays within canvas bounds
          const boundedX = Math.min(snappedX, canvasWidth - snappedWidth);
          const boundedY = Math.min(snappedY, canvasHeight - snappedHeight);

          onUpdateElement(
            elementId,
            {
              width: snappedWidth,
              height: snappedHeight,
              x: boundedX, // Use bounded values to keep element within canvas
              y: boundedY,
            },
            true
          );
        } else {
          onUpdateElement(
            elementId,
            {
              width: ref.offsetWidth,
              height: ref.offsetHeight,
              x: position.x,
              y: position.y,
            },
            true
          );
        }
      },
    [onUpdateElement, snapToGrid, gridSize, layoutRef]
  );

  // --- Element İçeriği Render Fonksiyonu (Styling için) ---
  const renderElementContent = useCallback((element: Element) => {
    const style: React.CSSProperties = {
      width: "100%",
      height: "100%",
      transform: `rotate(${element.rotation}deg)`,
      zIndex: element.zIndex,
      opacity: element.opacity,
      borderRadius:
        element.borderRadius === 9999 ? "50%" : `${element.borderRadius}px`,
      backgroundColor: element.color,
      border: `1px solid ${element.borderColor}`,
      cursor: element.isLocked ? "not-allowed" : "grab",
      display: "flex",
      alignItems: "center",
      justifyContent: "center",
      fontSize: `12px`,
      overflow: "hidden",
      userSelect: "none",
      boxSizing: "border-box",
    };

    // Create more descriptive content for table elements and private rooms
    let content;
    if (element.type.startsWith("table-")) {
      // For tables, show both name and capacity
      content = element.name
        ? `${element.name}${element.capacity ? ` (${element.capacity})` : ""}`
        : element.capacity
        ? `Cap: ${element.capacity}`
        : "Table";
    } else if (element.type === "private-room") {
      // For private rooms, show name and capacity with a special indicator
      content = element.name
        ? `${element.name}${element.capacity ? ` (${element.capacity})` : ""}`
        : element.capacity
        ? `Private Room (${element.capacity})`
        : "Private Room";
    } else {
      // For other elements, use the original logic
      content = element.name ?? null;
    }

    return (
      <div style={style} className="element-render relative h-full w-full">
        {content && (
          <div className="flex flex-col items-center justify-center w-full h-full">
            <span
              style={{
                color: "#333",
                backgroundColor: "rgba(255, 255, 255, 0.8)",
                padding: `3px 6px`,
                borderRadius: `4px`,
                textAlign: "center",
                maxWidth: "90%",
                overflow: "hidden",
                textOverflow: "ellipsis",
                whiteSpace: "nowrap",
                fontSize:
                  element.type.startsWith("table-") ||
                  element.type === "private-room"
                    ? "14px"
                    : "inherit",
                fontWeight:
                  element.type.startsWith("table-") ||
                  element.type === "private-room"
                    ? "bold"
                    : "normal",
                boxShadow: "0 1px 2px rgba(0,0,0,0.2)",
              }}
            >
              {content}
            </span>

            {/* Add table/room ID for debugging */}
            {(element.type.startsWith("table-") ||
              element.type === "private-room") &&
              element.tableId && (
                <span
                  style={{
                    color: "#666",
                    backgroundColor: "rgba(255, 255, 255, 0.6)",
                    padding: `1px 3px`,
                    borderRadius: `2px`,
                    marginTop: "2px",
                    fontSize: "10px",
                    maxWidth: "90%",
                    overflow: "hidden",
                    textOverflow: "ellipsis",
                    whiteSpace: "nowrap",
                  }}
                >
                  ID: {element.tableId.substring(0, 8)}...
                </span>
              )}
          </div>
        )}
      </div>
    );
  }, []);

  // --- Ana Canvas Render ---
  const backgroundSize = `${gridSize * zoom}px ${gridSize * zoom}px`;

  return (
    <div
      ref={layoutRef}
      className="relative w-full h-full bg-gray-100 border overflow-hidden cursor-crosshair"
      style={{
        position: "relative",
        // Use a more precise grid with darker lines for better visibility
        backgroundImage: `linear-gradient(90deg, rgba(0,0,0,0.12) 1px, transparent 1px), linear-gradient(rgba(0,0,0,0.12) 1px, transparent 1px)`,
        backgroundPosition: "0px 0px", // Start grid from top-left corner for precise alignment
        backgroundSize: backgroundSize,
        transform: `scale(${zoom})`,
        transformOrigin: "top left",
        ...(style || {}), // Merge custom styles if provided
      }}
      onClick={onCanvasClick}
    >
      {elements.map((element) => {
        const isSelected = selectedElements.includes(element.id);
        const isLocked = element.isLocked;

        return (
          <Rnd
            key={element.id}
            size={{ width: element.width, height: element.height }}
            position={{ x: element.x, y: element.y }}
            minWidth={MIN_ELEMENT_SIZE}
            minHeight={MIN_ELEMENT_SIZE}
            dragGrid={snapToGrid ? [gridSize, gridSize] : undefined}
            resizeGrid={snapToGrid ? [gridSize, gridSize] : undefined}
            // Force grid snapping during drag and resize
            onDrag={(_e, d) => {
              if (snapToGrid) {
                d.x = Math.floor(d.x / gridSize) * gridSize;
                d.y = Math.floor(d.y / gridSize) * gridSize;
              }
            }}
            dragHandleClassName="element-render" // Make entire element draggable
            scale={zoom} // Ensure Rnd respects the zoom level
            bounds="parent"
            enableResizing={!isLocked && isSelected}
            disableDragging={isLocked}
            onDragStop={handleRndDragStop(element.id)}
            onResizeStop={handleRndResizeStop(element.id)}
            onClick={(e: React.MouseEvent) => {
              e.stopPropagation();
              // Check if element is marked as non-clickable (for reservation view)
              if (element.isNonClickable) {
                return;
              }
              // Call the onElementClick prop with the element id
              onElementClick(e, element.id);
            }}
            style={{
              zIndex: element.zIndex,
              boxShadow: isSelected
                ? "0 0 8px rgba(59, 130, 246, 0.5)"
                : "none",
            }}
            className={
              isLocked
                ? "cursor-not-allowed"
                : isSelected
                ? "cursor-grab"
                : "cursor-pointer"
            }
          >
            {renderElementContent(element)}
          </Rnd>
        );
      })}
    </div>
  );
};
