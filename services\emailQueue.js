/**
 * Email Queue Service
 *
 * This service manages a queue for sending emails with rate limiting
 * to improve deliverability and avoid Gmail sending limits.
 */
const { db } = require("../config/firebase");
const { transporter, defaultSender } = require("../config/nodemailer");
const logger = require("../utils/logger");

// Queue configuration
const PROCESSING_INTERVAL = 3000; // 3 seconds between emails
const MAX_RETRIES = 5; // Increased from 3 to 5 for better reliability
const RETRY_DELAY = 60000; // 1 minute
const EXPONENTIAL_BACKOFF = true; // Use exponential backoff for retries

// Queue state
let isProcessing = false;
let currentBatchSize = 0;
const MAX_BATCH_SIZE = 50; // Process 50 emails before taking a longer break
const BATCH_BREAK_TIME = 300000; // 5 minutes break between batches

// Priority levels
const PRIORITY = {
  HIGHEST: 1,
  HIGH: 2,
  NORMAL: 3,
  LOW: 4,
  LOWEST: 5,
};

/**
 * Add an email to the queue
 * @param {Object} emailData - Email data to queue
 * @returns {Promise<string>} - ID of the queued email
 */
const queueEmail = async (emailData) => {
  try {
    // Validate required fields
    if (!emailData.to || !emailData.subject || !emailData.html) {
      logger.warn("Missing required fields for email", {
        to: emailData.to || "missing",
        subject: emailData.subject || "missing",
        hasHtml: !!emailData.html,
      });
      throw new Error("Missing required email fields");
    }

    // Set default priority if not provided
    const priority = emailData.priority || PRIORITY.NORMAL;

    // Calculate process time based on priority
    const processAfter = new Date();
    if (priority > PRIORITY.HIGHEST) {
      // Add delay for lower priority emails (1 minute per priority level above HIGHEST)
      processAfter.setTime(
        processAfter.getTime() + (priority - PRIORITY.HIGHEST) * 60000
      );
    }

    // Add timestamp and status
    const queuedEmail = {
      ...emailData,
      queuedAt: new Date(),
      status: "queued",
      attempts: 0,
      processAfter,
      priority,
    };

    // Save to Firestore
    const docRef = await db.collection("emailQueue").add(queuedEmail);
    logger.info(`Email queued with ID: ${docRef.id}`, {
      to: emailData.to,
      subject: emailData.subject,
      priority,
      processAfter: processAfter.toISOString(),
    });

    // Start processing if not already running
    if (!isProcessing) {
      processQueue();
    }

    return docRef.id;
  } catch (error) {
    logger.error("Error queuing email:", {
      error: error.message,
      to: emailData.to,
    });
    throw error;
  }
};

/**
 * Process the email queue
 */
const processQueue = async () => {
  if (isProcessing) return;

  try {
    isProcessing = true;

    // Check if we need to take a break between batches
    if (currentBatchSize >= MAX_BATCH_SIZE) {
      logger.info(
        `Processed ${currentBatchSize} emails, taking a break for ${
          BATCH_BREAK_TIME / 60000
        } minutes`
      );
      currentBatchSize = 0;

      // Schedule next batch after break
      setTimeout(() => {
        isProcessing = false;
        processQueue();
      }, BATCH_BREAK_TIME);

      return;
    }

    // Get next email to process - prioritize by priority level and then by processAfter time
    const now = new Date();
    const snapshot = await db
      .collection("emailQueue")
      .where("status", "in", ["queued", "failed"])
      .where("processAfter", "<=", now)
      .where("attempts", "<", MAX_RETRIES)
      .orderBy("priority") // Process higher priority first (lower number)
      .orderBy("processAfter")
      .limit(1)
      .get();

    if (snapshot.empty) {
      logger.debug("No emails in queue to process");
      isProcessing = false;
      return;
    }

    // Process the email
    const doc = snapshot.docs[0];
    const emailData = doc.data();
    const emailId = doc.id;

    logger.info(`Processing email ${emailId}`, {
      attempt: emailData.attempts + 1,
      maxRetries: MAX_RETRIES,
      to: emailData.to,
      priority: emailData.priority,
    });

    try {
      // Update status to processing
      await doc.ref.update({
        status: "processing",
        attempts: emailData.attempts + 1,
        processingStartedAt: new Date(),
      });

      // Send the email
      const info = await transporter.sendMail({
        from: emailData.from || defaultSender,
        to: emailData.to,
        subject: emailData.subject,
        text: emailData.text || "",
        html: emailData.html,
        priority:
          emailData.priority === 1
            ? "high"
            : emailData.priority === 5
            ? "low"
            : "normal",
      });

      // Update status to sent
      await doc.ref.update({
        status: "sent",
        sentAt: new Date(),
        messageId: info.messageId,
        response: info.response,
      });

      logger.info(`Email ${emailId} sent successfully`, {
        messageId: info.messageId,
        to: emailData.to,
      });
      currentBatchSize++;
    } catch (error) {
      logger.error(`Error sending email ${emailId}:`, {
        error: error.message,
        code: error.code,
        to: emailData.to,
      });

      // Calculate next retry time with exponential backoff if enabled
      const nextRetry = new Date();
      if (EXPONENTIAL_BACKOFF) {
        // Exponential backoff: retry_delay * (2 ^ attempt_number)
        const backoffTime = RETRY_DELAY * Math.pow(2, emailData.attempts);
        nextRetry.setTime(nextRetry.getTime() + backoffTime);
      } else {
        nextRetry.setTime(nextRetry.getTime() + RETRY_DELAY);
      }

      // Update status to failed
      await doc.ref.update({
        status: "failed",
        lastError: error.message,
        lastErrorCode: error.code || null,
        processAfter: nextRetry,
      });
    }

    // Schedule next email processing
    setTimeout(() => {
      isProcessing = false;
      processQueue();
    }, PROCESSING_INTERVAL);
  } catch (error) {
    logger.error("Error in queue processing:", {
      error: error.message,
      stack: error.stack,
    });

    // Reset processing state after error
    setTimeout(() => {
      isProcessing = false;
      processQueue();
    }, PROCESSING_INTERVAL);
  }
};

/**
 * Get queue statistics
 * @returns {Promise<Object>} - Queue statistics
 */
const getQueueStats = async () => {
  try {
    const stats = {
      queued: 0,
      processing: 0,
      sent: 0,
      failed: 0,
      total: 0,
      byPriority: {
        1: 0, // Highest
        2: 0, // High
        3: 0, // Normal
        4: 0, // Low
        5: 0, // Lowest
      },
    };

    const snapshot = await db.collection("emailQueue").get();

    snapshot.forEach((doc) => {
      const email = doc.data();
      stats[email.status]++;
      stats.total++;

      // Count by priority
      if (email.priority && stats.byPriority[email.priority] !== undefined) {
        stats.byPriority[email.priority]++;
      }
    });

    return stats;
  } catch (error) {
    logger.error("Error getting queue stats:", { error: error.message });
    throw error;
  }
};

// Export functions
module.exports = {
  queueEmail,
  processQueue,
  getQueueStats,
  PRIORITY,
};
