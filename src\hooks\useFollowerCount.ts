import { useState, useEffect } from "react";
import { firestore } from "@/config/firebase";
import { collection, query, onSnapshot } from "firebase/firestore";

/**
 * Hook to get real-time follower count updates
 * @param restaurantId The ID of the restaurant
 * @returns The number of followers
 */
export const useFollowerCount = (restaurantId: string): { count: number; loading: boolean } => {
  const [count, setCount] = useState(0);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (!restaurantId) {
      setLoading(false);
      return;
    }

    setLoading(true);
    
    const followersRef = collection(firestore, "restaurants", restaurantId, "followers");
    const q = query(followersRef);
    
    // Set up real-time listener
    const unsubscribe = onSnapshot(q, (snapshot) => {
      setCount(snapshot.size);
      setLoading(false);
    }, (error) => {
      console.error("Error getting follower count:", error);
      setLoading(false);
    });

    // Clean up listener on unmount
    return () => unsubscribe();
  }, [restaurantId]);

  return { count, loading };
};
