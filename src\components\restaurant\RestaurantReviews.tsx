import { useState, useEffect, useMemo } from "react";
import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Restaurant, Review, EditingReview } from "@/types/restaurant";
import { User } from "firebase/auth";
import { toast } from "sonner";
import {
  doc,
  updateDoc,
  serverTimestamp,
  Timestamp,
  writeBatch,
  getDoc,
} from "firebase/firestore";
import { firestore } from "@/config/firebase";
import { formatDistanceToNowStrict } from "date-fns";
import {
  Star,
  MessageSquare,
  Edit3,
  Trash2,
  Loader2,
  Send,
  Pencil,
  Info,
} from "lucide-react";
import { calculateRatingStats } from "@/utils/reviewUtils";
import { loyaltyService } from "@/services/LoyaltyService";
import { useAddReview } from "@/lib/react-query/hooks/useReviews";

// Helper component: Star rating
const StarRatingDisplay = ({ rating }: { rating: number }) => (
  <div className="flex items-center">
    {[1, 2, 3, 4, 5].map((star) => (
      <Star
        key={star}
        className={`w-4 h-4 ${
          rating >= star
            ? "text-yellow-400 fill-yellow-400"
            : "text-muted-foreground/30"
        }`}
      />
    ))}
  </div>
);

const StarRatingInput = ({
  rating,
  onRate,
  disabled = false,
}: {
  rating: number;
  onRate: (star: number) => void;
  disabled?: boolean;
}) => (
  <div className="flex items-center gap-1">
    {[1, 2, 3, 4, 5].map((star) => (
      <button
        key={star}
        type="button"
        onClick={() => !disabled && onRate(star)}
        disabled={disabled}
        className={`transition-transform duration-150 ease-in-out ${
          disabled
            ? "cursor-not-allowed"
            : "cursor-pointer hover:scale-110 focus:scale-110 focus:outline-none"
        }`}
        aria-label={`Rate ${star} stars`}
      >
        <Star
          className={`w-6 h-6 transition-colors ${
            rating >= star
              ? "text-yellow-400 fill-yellow-400"
              : "text-muted-foreground/30 hover:text-yellow-300"
          }`}
        />
      </button>
    ))}
    {rating > 0 && (
      <span className="ml-2 text-sm font-medium text-muted-foreground">
        ({rating}/5)
      </span>
    )}
  </div>
);

interface RestaurantReviewsProps {
  restaurant: Restaurant;
  reviews: Review[];
  setReviews: (reviews: Review[]) => void;
  setRestaurant: React.Dispatch<React.SetStateAction<Restaurant | null>>;
  user: User | null;
}

// Helper to safely format date
const formatReviewDate = (createdAt: Review["createdAt"]): string => {
  try {
    let date: Date;
    if (createdAt instanceof Date) {
      date = createdAt;
    } else if (
      createdAt &&
      typeof createdAt === "object" &&
      "toDate" in createdAt
    ) {
      // Firestore Timestamp object
      date = (createdAt as Timestamp).toDate();
    } else if (typeof createdAt === "string" || typeof createdAt === "number") {
      date = new Date(createdAt);
    } else {
      return "Recently"; // Fallback for unknown types
    }

    if (isNaN(date.getTime())) {
      return "Recently"; // Fallback for invalid dates
    }

    // Return relative time (e.g., "3 days ago", "about 1 hour ago")
    return formatDistanceToNowStrict(date, { addSuffix: true });
  } catch (error) {
    console.error("Date formatting error:", error);
    return "Recently";
  }
};

export const RestaurantReviews = ({
  restaurant,
  reviews,
  setReviews,
  setRestaurant,
  user,
}: RestaurantReviewsProps) => {
  const [isSubmittingReview, setIsSubmittingReview] = useState(false);
  const [isEditingReview, setIsEditingReview] = useState(false);
  const [editingReview, setEditingReview] = useState<EditingReview | null>(
    null
  );
  const [isDeleting, setIsDeleting] = useState<string | null>(null); // Track deleting review ID
  const [newReview, setNewReview] = useState({
    rating: 0, // Start with 0 rating
    comment: "",
  });

  // Calculate local rating and review count for immediate display
  const [localRating, setLocalRating] = useState<number | undefined>(undefined);
  const [localReviewCount, setLocalReviewCount] = useState<number | undefined>(
    undefined
  );

  const userHasReviewed = useMemo(() => {
    return user ? reviews.some((review) => review.userId === user.uid) : false;
  }, [reviews, user]);

  // Recalculate average rating whenever reviews change
  useEffect(() => {
    if (!restaurant) return;

    // Calculate local rating for immediate display
    const { averageRating, reviewCount } = calculateRatingStats(reviews);
    setLocalRating(averageRating);
    setLocalReviewCount(reviewCount);

    // Update Firestore in the background
    const updateFirestoreRating = async () => {
      if (
        restaurant.reviewCount !== reviewCount ||
        restaurant.rating !== averageRating
      ) {
        await updateRestaurantStats(restaurant.id, reviewCount, averageRating);
        setRestaurant((prev) =>
          prev
            ? { ...prev, reviewCount: reviewCount, rating: averageRating }
            : null
        );
      }
    };

    // Update Firestore in the background
    updateFirestoreRating();

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [reviews, restaurant?.id]); // Depend on reviews and restaurant ID

  // Function to update the reviewCount and rating fields atomically
  const updateRestaurantStats = async (
    restaurantId: string,
    count: number,
    rating: number
  ) => {
    try {
      const restaurantRef = doc(firestore, "restaurants", restaurantId);
      await updateDoc(restaurantRef, {
        reviewCount: count,
        rating: rating,
      });
    } catch (error) {
      console.error("Error updating restaurant review stats:", error);
      // Optional: Add toast notification for failure
    }
  };

  // Use the React Query mutation hook
  const addReviewMutation = useAddReview();

  const submitReview = async () => {
    if (!user || !restaurant) {
      toast.error("You must be logged in to submit a review.");
      return;
    }
    if (newReview.rating === 0) {
      toast.error("Please select a rating (1-5 stars).");
      return;
    }
    if (!newReview.comment.trim()) {
      toast.error("Please provide a comment to submit your review.");
      return;
    }
    if (newReview.comment.trim().length < 10) {
      toast.error("Review comment must be at least 10 characters long.");
      return;
    }
    if (restaurant.uid === user.uid) {
      toast.error("You cannot review your own restaurant.");
      return;
    }
    if (userHasReviewed) {
      toast.error(
        "You have already reviewed this restaurant. Edit your existing review instead."
      );
      return;
    }

    setIsSubmittingReview(true);
    try {
      // Use the mutation hook to add the review
      await addReviewMutation.mutateAsync({
        restaurantId: restaurant.id,
        userId: user.uid,
        userName: user.displayName || user.email?.split("@")[0] || "Anonymous",
        userAvatarUrl: user.photoURL || undefined,
        rating: newReview.rating,
        comment: newReview.comment.trim(),
        restaurantName: restaurant.restaurantName,
      });

      // Award loyalty points for the review
      if (user) {
        try {
          // We don't have the review ID here, but we can use a timestamp as a reference
          await loyaltyService.awardReviewPoints(
            user.uid,
            `review_${Date.now()}`
          );
        } catch (loyaltyError) {
          console.error("Error awarding loyalty points:", loyaltyError);
          // Don't show error to user, as the review was still submitted successfully
        }
      }

      // Reset form
      setNewReview({ rating: 0, comment: "" });
    } catch (error) {
      console.error("Error submitting review:", error);
      // Error toast is handled by the mutation
    } finally {
      setIsSubmittingReview(false);
    }
  };

  const handleEditReview = async () => {
    if (!editingReview || !restaurant || !user) return;
    if (editingReview.comment.trim().length < 10) {
      toast.error("Review comment must be at least 10 characters long.");
      return;
    }
    if (editingReview.rating === 0) {
      toast.error("Please select a rating (1-5 stars).");
      return;
    }

    setIsEditingReview(true);
    try {
      const reviewRef = doc(
        firestore,
        "restaurants",
        restaurant.id,
        "reviews",
        editingReview.id
      );
      const globalReviewRef = doc(firestore, "reviews", editingReview.id);
      const timestamp = serverTimestamp();

      const updateData = {
        rating: editingReview.rating,
        comment: editingReview.comment.trim(),
        updatedAt: timestamp,
      };

      const batch = writeBatch(firestore);
      batch.update(reviewRef, updateData);

      // Check if the global review exists before attempting to update/delete
      const globalDoc = await getDoc(globalReviewRef).catch(() => null); // Gracefully handle potential errors

      if (editingReview.rating >= 4) {
        // Update or set (if it didn't exist before but now qualifies)
        batch.set(globalReviewRef, updateData, { merge: true });
      } else if (globalDoc?.exists()) {
        // Delete from global if rating drops below 4 and it existed
        batch.delete(globalReviewRef);
      }

      // Create updated review object
      const updatedReview = {
        id: editingReview.id,
        rating: editingReview.rating,
        comment: editingReview.comment.trim(),
        updatedAt: new Date(), // Use local date for immediate display
      };

      // Create a temporary array with the updated review for calculation
      const tempReviews = reviews.map((review) =>
        review.id === editingReview.id
          ? { ...review, ...updatedReview }
          : review
      );

      const { averageRating, reviewCount } = calculateRatingStats(tempReviews);

      // Update the restaurant document with the new rating and review count in the same batch
      const restaurantRef = doc(firestore, "restaurants", restaurant.id);
      batch.update(restaurantRef, {
        rating: averageRating,
        reviewCount: reviewCount,
      });

      await batch.commit();

      // Optimistically update local state
      const updatedReviews = reviews.map((review) =>
        review.id === editingReview.id
          ? { ...review, ...updatedReview }
          : review
      );
      setReviews(updatedReviews);

      // We already calculated the rating and review count, so just use those values
      // No need to recalculate
      setLocalRating(averageRating);
      setLocalReviewCount(reviewCount);

      toast.success("Review updated successfully!");
      setEditingReview(null); // Close editing form
    } catch (error) {
      console.error("Error updating review:", error);
      toast.error("Failed to update review. Please try again.");
    } finally {
      setIsEditingReview(false);
    }
  };

  const handleDeleteReview = async (reviewId: string) => {
    if (!restaurant) return;
    setIsDeleting(reviewId); // Indicate which review is being deleted

    try {
      const reviewRef = doc(
        firestore,
        "restaurants",
        restaurant.id,
        "reviews",
        reviewId
      );
      const globalReviewRef = doc(firestore, "reviews", reviewId);

      const batch = writeBatch(firestore);
      batch.delete(reviewRef);

      // Check if the global review exists before attempting to delete
      const globalDoc = await getDoc(globalReviewRef).catch(() => null);
      if (globalDoc?.exists()) {
        batch.delete(globalReviewRef);
      }

      // Create a temporary array without the deleted review for calculation
      const tempReviews = reviews.filter((review) => review.id !== reviewId);

      const { averageRating, reviewCount } = calculateRatingStats(tempReviews);

      // Update the restaurant document with the new rating and review count in the same batch
      const restaurantRef = doc(firestore, "restaurants", restaurant.id);
      batch.update(restaurantRef, {
        rating: averageRating,
        reviewCount: reviewCount,
      });

      await batch.commit();

      // Optimistically update local state
      const updatedReviews = reviews.filter((review) => review.id !== reviewId);
      setReviews(updatedReviews);

      // We already calculated the rating and review count, so just use those values
      // No need to recalculate
      setLocalRating(averageRating);
      setLocalReviewCount(reviewCount);

      toast.success("Review deleted successfully!");
    } catch (error) {
      console.error("Error deleting review:", error);
      toast.error("Failed to delete review. Please try again.");
    } finally {
      setIsDeleting(null); // Clear deleting indicator
    }
  };

  return (
    <Card className="mb-8 shadow-sm border w-full">
      <CardHeader className="border-b p-3 sm:p-4 md:p-6">
        <CardTitle className="flex items-center gap-1.5 sm:gap-2 text-base sm:text-lg font-semibold truncate">
          <MessageSquare size={18} className="text-primary flex-shrink-0" />
          <span className="truncate">Reviews & Ratings</span>
        </CardTitle>
        <CardDescription className="text-xs sm:text-sm">
          {reviews.length === 0
            ? "No reviews yet. Be the first one!"
            : `${localReviewCount ?? restaurant.reviewCount ?? 0} ${
                (localReviewCount ?? restaurant.reviewCount ?? 0) === 1
                  ? "review"
                  : "reviews"
              } • Average: ${(localRating ?? restaurant.rating ?? 0).toFixed(
                1
              )}/5`}
          {/* Display average rating - prefer local calculation over database value */}
          {reviews.length > 0 && (
            <span className="ml-1 sm:ml-2">
              <StarRatingDisplay
                rating={localRating ?? restaurant.rating ?? 0}
              />
            </span>
          )}
        </CardDescription>
      </CardHeader>
      <CardContent className="p-3 sm:p-4 md:p-6 space-y-4 sm:space-y-6">
        {user && !userHasReviewed && restaurant.uid !== user.uid && (
          <div className="border rounded-lg p-4 sm:p-6 bg-muted/20 shadow-sm">
            <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
              <Pencil size={18} className="text-primary" />
              Write Your Review
            </h3>
            <div className="space-y-4">
              <div>
                <Label className="text-sm font-medium mb-2 block">
                  Your Rating*
                </Label>
                <StarRatingInput
                  rating={newReview.rating}
                  onRate={(star) =>
                    setNewReview((prev) => ({ ...prev, rating: star }))
                  }
                  disabled={isSubmittingReview}
                />
              </div>
              <div>
                <Label htmlFor="reviewComment" className="text-sm font-medium">
                  Your Experience*
                </Label>
                <Textarea
                  id="reviewComment"
                  value={newReview.comment}
                  onChange={(e) =>
                    setNewReview((prev) => ({
                      ...prev,
                      comment: e.target.value,
                    }))
                  }
                  placeholder="Share details of your experience (min 10 characters)..."
                  className="mt-2 resize-none text-sm"
                  rows={4}
                  disabled={isSubmittingReview}
                  aria-required="true"
                />
              </div>
              <Button
                onClick={submitReview}
                disabled={
                  isSubmittingReview ||
                  !newReview.comment.trim() ||
                  newReview.rating === 0
                }
                className="w-full sm:w-auto"
              >
                {isSubmittingReview ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <Send className="mr-2 h-4 w-4" />
                )}
                {isSubmittingReview ? "Submitting..." : "Submit Review"}
              </Button>
            </div>
          </div>
        )}
        {user && userHasReviewed && !editingReview && (
          <div className="border rounded-lg p-4 bg-blue-50 border-blue-200 shadow-sm">
            <div className="flex items-center gap-2">
              <Info size={16} className="text-blue-600" />
              <p className="text-sm text-blue-700">
                You've already reviewed. You can edit or delete your review
                below.
              </p>
            </div>
          </div>
        )}
        {user && restaurant.uid === user.uid && (
          <div className="border rounded-lg p-4 bg-yellow-50 border-yellow-200 shadow-sm">
            <div className="flex items-center gap-2">
              <Info size={16} className="text-yellow-700" />
              <p className="text-sm text-yellow-800">
                Note: Restaurant owners cannot review their own establishment.
              </p>
            </div>
          </div>
        )}

        <div className="space-y-5">
          <h3 className="text-lg font-semibold">
            {reviews.length === 0 ? "No Reviews Yet" : "All Reviews"}
          </h3>
          {reviews.length > 0 ? (
            <ScrollArea className="max-h-[500px] pr-3 -mr-3">
              {" "}
              {/* Add padding-right and negative margin for scrollbar spacing */}
              <div className="space-y-5">
                {reviews
                  .sort((a, b) => {
                    // Sort reviews by date, newest first
                    const dateA =
                      a.createdAt instanceof Date
                        ? a.createdAt.getTime()
                        : (a.createdAt as Timestamp)?.toDate().getTime() ?? 0;
                    const dateB =
                      b.createdAt instanceof Date
                        ? b.createdAt.getTime()
                        : (b.createdAt as Timestamp)?.toDate().getTime() ?? 0;
                    return dateB - dateA;
                  })
                  .map((review) => (
                    <div
                      key={review.id}
                      className={`border rounded-lg p-4 transition-colors duration-200 ${
                        editingReview?.id === review.id
                          ? "bg-muted/40"
                          : user?.uid === review.userId
                          ? "bg-primary/5 border-primary/20"
                          : "bg-background hover:bg-muted/20"
                      }`}
                    >
                      <div className="flex justify-between items-start mb-3 gap-4">
                        <div className="flex items-center gap-3 flex-1 min-w-0">
                          <Avatar className="w-9 h-9 sm:w-10 sm:h-10 border">
                            <AvatarImage
                              src={review.userAvatarUrl || ""}
                              alt={`${review.userName}'s avatar`}
                            />
                            <AvatarFallback className="text-sm bg-muted">
                              {review.userName?.charAt(0).toUpperCase() ?? "A"}
                            </AvatarFallback>
                          </Avatar>
                          <div className="min-w-0">
                            <p className="font-semibold text-sm sm:text-base truncate">
                              {review.userName}
                            </p>
                            <p className="text-xs text-muted-foreground">
                              {formatReviewDate(review.createdAt)}
                            </p>
                          </div>
                        </div>
                        <div className="flex-shrink-0">
                          <StarRatingDisplay rating={review.rating} />
                        </div>
                      </div>

                      {editingReview?.id === review.id ? (
                        <div className="space-y-3 pt-2">
                          <Label className="text-sm font-medium mb-1 block">
                            Edit Rating*
                          </Label>
                          <StarRatingInput
                            rating={editingReview.rating}
                            onRate={(star) =>
                              setEditingReview((prev) =>
                                prev ? { ...prev, rating: star } : null
                              )
                            }
                            disabled={isEditingReview}
                          />
                          <Label
                            htmlFor={`editComment-${review.id}`}
                            className="text-sm font-medium"
                          >
                            Edit Comment*
                          </Label>
                          <Textarea
                            id={`editComment-${review.id}`}
                            value={editingReview.comment}
                            onChange={(e) =>
                              setEditingReview((prev) =>
                                prev
                                  ? { ...prev, comment: e.target.value }
                                  : null
                              )
                            }
                            className="w-full text-sm"
                            rows={3}
                            disabled={isEditingReview}
                            aria-required="true"
                          />
                          <div className="flex gap-2 justify-end pt-1">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => setEditingReview(null)}
                              disabled={isEditingReview}
                            >
                              Cancel
                            </Button>
                            <Button
                              variant="default"
                              size="sm"
                              onClick={handleEditReview}
                              disabled={
                                isEditingReview ||
                                !editingReview.comment.trim() ||
                                editingReview.rating === 0
                              }
                              className="min-w-[100px]"
                            >
                              {isEditingReview ? (
                                <Loader2 className="h-4 w-4 animate-spin" />
                              ) : (
                                "Save"
                              )}
                            </Button>
                          </div>
                        </div>
                      ) : (
                        <>
                          <p className="text-sm text-foreground/90 leading-relaxed whitespace-pre-wrap break-words">
                            {review.comment}
                          </p>
                          {user && user.uid === review.userId && (
                            <div className="flex gap-2 mt-3 justify-end">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() =>
                                  setEditingReview({
                                    id: review.id,
                                    rating: review.rating,
                                    comment: review.comment,
                                  })
                                }
                                className="text-xs"
                                aria-label="Edit your review"
                              >
                                <Edit3 size={14} className="mr-1.5" />
                                Edit
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleDeleteReview(review.id)}
                                disabled={isDeleting === review.id}
                                className="text-xs text-destructive hover:bg-destructive/10 hover:text-destructive"
                                aria-label="Delete your review"
                              >
                                {isDeleting === review.id ? (
                                  <Loader2
                                    size={14}
                                    className="mr-1.5 animate-spin"
                                  />
                                ) : (
                                  <Trash2 size={14} className="mr-1.5" />
                                )}
                                {isDeleting === review.id
                                  ? "Deleting"
                                  : "Delete"}
                              </Button>
                            </div>
                          )}
                        </>
                      )}
                    </div>
                  ))}
              </div>
            </ScrollArea>
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              <p className="text-sm">
                Be the first to share your thoughts about{" "}
                {restaurant.restaurantName}!
              </p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
