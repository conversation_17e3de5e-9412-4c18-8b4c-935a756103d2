import { QueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

// Constants for cache configuration
export const CACHE_TIME = 24 * 60 * 60 * 1000; // 24 hours in milliseconds
export const STALE_TIME = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

// Create a client
export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      gcTime: CACHE_TIME,
      staleTime: STALE_TIME,
      refetchOnWindowFocus: false,
      refetchOnMount: true,
      retry: (failureCount, error) => {
        // Don't retry on 404s or authentication errors
        if (
          error instanceof Error &&
          (error.message.includes("not found") ||
            error.message.includes("permission") ||
            error.message.includes("unauthorized"))
        ) {
          return false;
        }
        // Retry 3 times on other errors
        return failureCount < 3;
      },
      // Global error handler for queries
    },
    mutations: {
      onError: (error) => {
        // Global error handler for mutations
        console.error("Mutation error:", error);
        if (error instanceof Error) {
          toast.error(`Error: ${error.message}`);
        } else {
          toast.error("An unknown error occurred");
        }
      },
    },
  },
});

// Query key factory to ensure consistent keys
export const queryKeys = {
  restaurants: {
    all: ["restaurants"] as const,
    filtered: (filters: Record<string, unknown>) =>
      ["restaurants", "filtered", filters] as const,
    detail: (id: string) => ["restaurants", id] as const,
    menu: (id: string) => ["restaurants", id, "menu"] as const,
    reviews: (id: string) => ["restaurants", id, "reviews"] as const,
    followers: (id: string) => ["restaurants", id, "followers"] as const,
    orders: (id: string) => ["restaurants", id, "orders"] as const,
  },
  clients: {
    detail: (id: string) => ["clients", id] as const,
    orders: (id: string) => ["clients", id, "orders"] as const,
    following: (id: string) => ["clients", id, "following"] as const,
    loyalty: {
      status: (id: string) => ["clients", id, "loyalty", "status"] as const,
      transactions: (id: string) =>
        ["clients", id, "loyalty", "transactions"] as const,
      referrals: (id: string) =>
        ["clients", id, "loyalty", "referrals"] as const,
      games: {
        stats: (id: string) => ["clients", id, "loyalty", "games", "stats"] as const,
        history: (id: string) => ["clients", id, "loyalty", "games", "history"] as const,
      },
    },
  },
  reviews: {
    all: ["reviews"] as const,
    featured: ["reviews", "featured"] as const,
  },
  users: {
    detail: (id: string) => ["users", id] as const,
  },
  rewards: {
    all: ["rewards"] as const,
    global: ["rewards", "global"] as const,
    featured: (limit: number) => ["rewards", "featured", limit] as const,
    restaurant: (restaurantId: string) =>
      ["rewards", "restaurant", restaurantId] as const,
    available: (userId: string) => ["rewards", "available", userId] as const,
    redeemed: (userId: string) => ["rewards", "redeemed", userId] as const,
    detail: (rewardId: string) => ["rewards", rewardId] as const,
  },
  games: {
    all: ["games"] as const,
    detail: (gameId: string) => ["games", gameId] as const,
    foodMatch: {
      cards: (count: number) => ["games", "foodMatch", "cards", count] as const,
    },
    trivia: {
      questions: (count: number, difficulty?: string, category?: string) =>
        ["games", "trivia", "questions", count, difficulty, category] as const,
    },
  },
};

// Helper function to invalidate related queries
export const invalidateRelatedQueries = async (
  queryKey: string[],
  relatedKeys: string[][] = []
) => {
  // Always invalidate the exact query key
  await queryClient.invalidateQueries({ queryKey });

  // Invalidate related query keys
  for (const key of relatedKeys) {
    await queryClient.invalidateQueries({ queryKey: key });
  }
};
