import { useQuery } from '@tanstack/react-query';
import { FilterOptions } from '@/services/RestaurantFilterService';
import { RestaurantFilterService } from '@/services/RestaurantFilterService';
import { toast } from 'sonner';

// Stale time: 5 minutes (data is considered fresh for 5 minutes)
const STALE_TIME = 5 * 60 * 1000;

// Cache time: 10 minutes (data is kept in cache for 10 minutes)
const CACHE_TIME = 10 * 60 * 1000;

export function useRestaurants(filterOptions: FilterOptions) {
  return useQuery({
    queryKey: ['restaurants', filterOptions],
    queryFn: async () => {
      try {
        const result = await RestaurantFilterService.getFilteredRestaurants(filterOptions);

        return {
          restaurants: result.restaurants,
          facets: result.facets
        };
      } catch (error) {
        // Log error and show toast
        toast.error("Failed to load restaurant data.");
        throw error;
      }
    },
    staleTime: STALE_TIME,
    gcTime: CACHE_TIME,
    refetchOnWindowFocus: false,
  });
}

// Hook to fetch all restaurants once and cache them
export function useAllRestaurants() {
  return useQuery({
    queryKey: ['allRestaurants'],
    queryFn: async () => {
      try {
        const result = await RestaurantFilterService.getAllRestaurants();
        return result;
      } catch (error) {
        // Log error and show toast
        toast.error("Failed to load restaurant data.");
        throw error;
      }
    },
    staleTime: STALE_TIME,
    gcTime: CACHE_TIME,
    refetchOnWindowFocus: false,
  });
}
