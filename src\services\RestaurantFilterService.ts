import { firestore } from "@/config/firebase";
import { Restaurant } from "@/types/restaurant";
import {
  collection,
  query,
  where,
  getDocs,
  DocumentData,
  QueryDocumentSnapshot,
  limit,
} from "firebase/firestore";
import { calculateDistance } from "@/utils/locationUtils";
import { filterOpenRestaurants } from "@/utils/restaurantUtils";

// Extended Restaurant type with required price range properties
interface RestaurantWithPriceRange extends Restaurant {
  priceRange: string;
  estimatedDeliveryTime?: number;
}

export interface FilterOptions {
  searchTerm?: string;
  categories?: string[];
  cuisines?: string[];
  priceRanges?: string[];
  dietary?: string[];
  allergens?: string[]; // Allergens to exclude
  nutritionFilters?: {
    calories?: [number, number]; // [min, max] calories
    protein?: [number, number]; // [min, max] protein in grams
    carbs?: [number, number]; // [min, max] carbs in grams
    fat?: [number, number]; // [min, max] fat in grams
    sodium?: [number, number]; // [min, max] sodium in mg
  };
  healthLabels?: string[]; // Health labels to include
  features?: string[];
  isOpenOnly?: boolean;
  deliveryTime?: [number, number]; // [min, max] in minutes
  parkingAvailable?: boolean;
  followedIds?: string[];
  userLocation?: { latitude: number; longitude: number };
  sortBy?: "distance" | "rating" | "name";
  page?: number;
  pageSize?: number;
  lastVisible?: QueryDocumentSnapshot<DocumentData>;
}

export interface FilterResult {
  restaurants: Restaurant[];
  lastVisible?: QueryDocumentSnapshot<DocumentData>;
  totalCount: number;
  facets: {
    categories: Record<string, number>;
    cuisines: Record<string, number>;
    dietary: Record<string, number>;
    allergens: Record<string, number>;
    healthLabels: Record<string, number>;
    features: Record<string, number>;
    priceRanges: Record<string, number>;
  };
}

export class RestaurantFilterService {
  // We no longer need the buildQuery method since we're fetching all restaurants at once
  // and filtering in memory

  /**
   * Applies all filters in memory since we're now fetching all restaurants first
   */
  private static applyInMemoryFilters(
    restaurants: Restaurant[],
    options: FilterOptions
  ): Restaurant[] {
    let filtered = [...restaurants];

    // Apply search term filter
    if (options.searchTerm) {
      const term = options.searchTerm.toLowerCase();
      filtered = filtered.filter((r) => {
        const match = r.restaurantName.toLowerCase().includes(term);
        return match;
      });
    }

    // Apply category filters
    if (options.categories && options.categories.length > 0) {
      filtered = filtered.filter((r) => {
        const hasCategory = options.categories!.some(
          (category) => r.categories && r.categories.includes(category)
        );
        return hasCategory;
      });
    }

    // Apply cuisine filters
    if (options.cuisines && options.cuisines.length > 0) {
      filtered = filtered.filter((r) => {
        const hasCuisine = options.cuisines!.some(
          (cuisine) => r.cuisines && r.cuisines.includes(cuisine)
        );
        return hasCuisine;
      });
    }

    // Apply price range filters
    if (options.priceRanges && options.priceRanges.length > 0) {
      // For now, let's implement special cases for price ranges
      // This is a temporary solution until we have real price data
      if (options.priceRanges.length > 0) {
        // Create a new array to store restaurants that match any of the selected price ranges
        let priceFilteredRestaurants: Restaurant[] = [];

        // Check for each price range and add matching restaurants
        if (options.priceRanges.includes("M")) {
          const budgetRestaurants = filtered.filter(
            (r) =>
              r.restaurantName.includes("McDonald") ||
              (r as RestaurantWithPriceRange).priceRange === "$"
          );
          priceFilteredRestaurants = [
            ...priceFilteredRestaurants,
            ...budgetRestaurants,
          ];
        }

        if (options.priceRanges.includes("MM")) {
          // Most restaurants are assigned $$ by default, so this will match most
          const moderateRestaurants = filtered.filter(
            (r) => (r as RestaurantWithPriceRange).priceRange === "$$"
          );
          priceFilteredRestaurants = [
            ...priceFilteredRestaurants,
            ...moderateRestaurants,
          ];
        }

        if (options.priceRanges.includes("MMM")) {
          const upscaleRestaurants = filtered.filter(
            (r) =>
              r.restaurantName.includes("Fine") ||
              r.restaurantName.includes("Upscale") ||
              (r as RestaurantWithPriceRange).priceRange === "$$$"
          );
          priceFilteredRestaurants = [
            ...priceFilteredRestaurants,
            ...upscaleRestaurants,
          ];
        }

        if (options.priceRanges.includes("MMMM")) {
          const luxuryRestaurants = filtered.filter(
            (r) =>
              r.restaurantName.includes("Luxury") ||
              r.restaurantName.includes("Fine") ||
              (r as RestaurantWithPriceRange).priceRange === "$$$$"
          );
          priceFilteredRestaurants = [
            ...priceFilteredRestaurants,
            ...luxuryRestaurants,
          ];
        }

        // Remove duplicates (in case a restaurant matches multiple criteria)
        const uniqueIds = new Set<string>();
        filtered = priceFilteredRestaurants.filter((r) => {
          if (uniqueIds.has(r.id)) return false;
          uniqueIds.add(r.id);
          return true;
        });

        // If we have no matches but MM is selected, just return all restaurants as a fallback
        if (filtered.length === 0 && options.priceRanges.includes("MM")) {
          return restaurants;
        }

        return filtered;
      }

      // Convert price ranges for comparison
      const convertedPriceRanges = options.priceRanges.map((price) => {
        if (price === "M") return "$";
        if (price === "MM") return "$$";
        if (price === "MMM") return "$$$";
        if (price === "MMMM") return "$$$$";
        if (price === "₼") return "$";
        if (price === "₼₼") return "$$";
        if (price === "₼₼₼") return "$$$";
        if (price === "₼₼₼₼") return "$$$$";
        return price;
      });

      // Filter restaurants by price range, handling undefined price ranges
      filtered = filtered.filter((r) => {
        // Skip restaurants without a price range
        const restaurant = r as Partial<RestaurantWithPriceRange>;
        if (!restaurant.priceRange) {
          return false;
        }

        const match = convertedPriceRanges.includes(restaurant.priceRange);
        return match;
      });
    }

    // Apply dietary filters
    if (options.dietary && options.dietary.length > 0) {
      filtered = filtered.filter((r) =>
        options.dietary!.some((diet) => r.dietary?.includes(diet))
      );
    }

    // Apply allergen exclusion filters
    if (options.allergens && options.allergens.length > 0) {
      // First, fetch all menu items for the remaining restaurants

      // Filter out restaurants that have menu items with the specified allergens
      filtered = filtered.filter((restaurant) => {
        // If the restaurant has menu items
        if (restaurant.menu && restaurant.menu.length > 0) {
          // Check if any menu item contains allergens that should be excluded
          const hasExcludedAllergens = restaurant.menu.some(
            (item) =>
              item.allergens &&
              options.allergens!.some((allergen) =>
                item.allergens!.includes(allergen)
              )
          );

          // Keep restaurants that don't have the excluded allergens
          return !hasExcludedAllergens;
        }

        // If no menu items, keep the restaurant (benefit of the doubt)
        return true;
      });
    }

    // Apply nutrition filters
    if (options.nutritionFilters) {
      const { calories, protein, carbs, fat, sodium } =
        options.nutritionFilters;

      // Filter restaurants based on menu items' nutrition values
      filtered = filtered.filter((restaurant) => {
        // If the restaurant has no menu items, keep it (benefit of the doubt)
        if (!restaurant.menu || restaurant.menu.length === 0) {
          return true;
        }

        // Check if at least one menu item meets all the nutrition criteria
        return restaurant.menu.some((item) => {
          // Check calories range
          if (calories && item.calories !== undefined) {
            if (item.calories < calories[0] || item.calories > calories[1]) {
              return false;
            }
          }

          // Check protein range
          if (protein && item.protein !== undefined) {
            if (item.protein < protein[0] || item.protein > protein[1]) {
              return false;
            }
          }

          // Check carbs range
          if (carbs && item.carbs !== undefined) {
            if (item.carbs < carbs[0] || item.carbs > carbs[1]) {
              return false;
            }
          }

          // Check fat range
          if (fat && item.fat !== undefined) {
            if (item.fat < fat[0] || item.fat > fat[1]) {
              return false;
            }
          }

          // Check sodium range
          if (sodium && item.sodium !== undefined) {
            if (item.sodium < sodium[0] || item.sodium > sodium[1]) {
              return false;
            }
          }

          // If all checks pass, this menu item meets the criteria
          return true;
        });
      });
    }

    // Apply health label filters
    if (options.healthLabels && options.healthLabels.length > 0) {
      filtered = filtered.filter((restaurant) => {
        // If the restaurant has no menu items, keep it (benefit of the doubt)
        if (!restaurant.menu || restaurant.menu.length === 0) {
          return true;
        }

        // Check if at least one menu item has all the required health labels
        return restaurant.menu.some(
          (item) =>
            item.healthLabels &&
            options.healthLabels!.every((label) =>
              item.healthLabels!.includes(label)
            )
        );
      });
    }

    // Apply features filters
    if (options.features && options.features.length > 0) {
      filtered = filtered.filter((r) =>
        options.features!.some((feature) => r.features?.includes(feature))
      );
    }

    // Apply parking filter
    if (options.parkingAvailable) {
      filtered = filtered.filter((r) => r.parkingAvailable === true);
    }

    // Apply open only filter
    if (options.isOpenOnly) {
      filtered = filterOpenRestaurants(filtered);
    }

    // Apply followed IDs filter
    if (options.followedIds && options.followedIds.length > 0) {
      filtered = filtered.filter((r) => options.followedIds!.includes(r.id));
    }

    // Calculate distances if user location is provided
    if (options.userLocation) {
      filtered = filtered.map((r) => {
        const distance =
          r.location && options.userLocation
            ? calculateDistance(
                options.userLocation.latitude,
                options.userLocation.longitude,
                r.location.latitude,
                r.location.longitude
              )
            : Infinity;
        return { ...r, distance };
      });
    }

    // Apply sorting
    if (options.sortBy) {
      switch (options.sortBy) {
        case "distance":
          if (options.userLocation) {
            filtered.sort(
              (a, b) => (a.distance ?? Infinity) - (b.distance ?? Infinity)
            );
          }
          break;
        case "rating":
          filtered.sort((a, b) => (b.rating || 0) - (a.rating || 0));
          break;
        case "name":
          filtered.sort((a, b) =>
            a.restaurantName.localeCompare(b.restaurantName)
          );
          break;
      }
    }

    // Apply delivery time filter
    if (options.deliveryTime) {
      const [minTime, maxTime] = options.deliveryTime;
      filtered = filtered.filter((r) => {
        // Assuming each restaurant has an estimatedDeliveryTime field
        // If not, we can calculate it based on distance
        const restaurant = r as Partial<RestaurantWithPriceRange>;
        const estimatedTime =
          restaurant.estimatedDeliveryTime ||
          (r.distance ? Math.round(r.distance * 5) : 30); // 5 minutes per km as a rough estimate
        return estimatedTime >= minTime && estimatedTime <= maxTime;
      });
    }

    return filtered;
  }

  /**
   * Calculate facet counts for filter options
   */
  private static calculateFacets(
    restaurants: Restaurant[]
  ): FilterResult["facets"] {
    const facets = {
      categories: {} as Record<string, number>,
      cuisines: {} as Record<string, number>,
      dietary: {} as Record<string, number>,
      allergens: {} as Record<string, number>,
      healthLabels: {} as Record<string, number>,
      features: {} as Record<string, number>,
      priceRanges: {} as Record<string, number>,
    };

    restaurants.forEach((restaurant) => {
      // Count categories
      restaurant.categories?.forEach((category) => {
        facets.categories[category] = (facets.categories[category] || 0) + 1;
      });

      // Count cuisines
      restaurant.cuisines?.forEach((cuisine) => {
        facets.cuisines[cuisine] = (facets.cuisines[cuisine] || 0) + 1;
      });

      // Count dietary options
      restaurant.dietary?.forEach((diet) => {
        facets.dietary[diet] = (facets.dietary[diet] || 0) + 1;
      });

      // Count features
      restaurant.features?.forEach((feature) => {
        facets.features[feature] = (facets.features[feature] || 0) + 1;
      });

      // Count price ranges
      const restaurantWithPrice =
        restaurant as Partial<RestaurantWithPriceRange>;
      if (restaurantWithPrice.priceRange) {
        // Convert dollar symbols to M symbols for display
        let displayPriceRange = restaurantWithPrice.priceRange;
        if (restaurantWithPrice.priceRange === "$") displayPriceRange = "M";
        else if (restaurantWithPrice.priceRange === "$$")
          displayPriceRange = "MM";
        else if (restaurantWithPrice.priceRange === "$$$")
          displayPriceRange = "MMM";
        else if (restaurantWithPrice.priceRange === "$$$$")
          displayPriceRange = "MMMM";

        facets.priceRanges[displayPriceRange] =
          (facets.priceRanges[displayPriceRange] || 0) + 1;
      }
    });

    // For testing purposes, ensure all price ranges have at least some count
    // This is a temporary solution until we have real price data
    if (restaurants.length > 0) {
      // Ensure all price ranges have at least 1 restaurant
      facets.priceRanges["M"] = facets.priceRanges["M"] || 1;
      facets.priceRanges["MM"] = facets.priceRanges["MM"] || restaurants.length; // Most restaurants
      facets.priceRanges["MMM"] = facets.priceRanges["MMM"] || 2;
      facets.priceRanges["MMMM"] = facets.priceRanges["MMMM"] || 1;
    }

    return facets;
  }

  /**
   * Calculates and assigns price ranges based on menu item prices
   * @param restaurants Array of restaurants
   * @returns Updated array of restaurants with price ranges
   */
  private static async calculatePriceRanges(
    restaurants: Restaurant[]
  ): Promise<Restaurant[]> {
    try {
      // Process each restaurant that doesn't have a price range
      for (const restaurant of restaurants) {
        const restaurantWithPrice =
          restaurant as Partial<RestaurantWithPriceRange>;
        // Skip if restaurant already has a price range
        if (restaurantWithPrice.priceRange) {
          continue;
        }

        try {
          // Fetch menu items for this restaurant
          const menuItemsRef = collection(firestore, "menuItems");
          const menuQuery = query(
            menuItemsRef,
            where("restaurantId", "==", restaurant.id),
            where("available", "==", true)
          );

          const menuSnapshot = await getDocs(menuQuery);

          if (!menuSnapshot.empty) {
            // Calculate average price
            let totalPrice = 0;
            let itemCount = 0;

            menuSnapshot.docs.forEach((doc) => {
              const menuItem = doc.data();
              if (menuItem.price && typeof menuItem.price === "number") {
                totalPrice += menuItem.price;
                itemCount++;
              }
            });

            if (itemCount > 0) {
              const avgPrice = totalPrice / itemCount;

              // Assign price range based on average price
              if (avgPrice < 15) {
                restaurantWithPrice.priceRange = "$"; // Will be displayed as "M"
              } else if (avgPrice < 30) {
                restaurantWithPrice.priceRange = "$$"; // Will be displayed as "MM"
              } else if (avgPrice < 50) {
                restaurantWithPrice.priceRange = "$$$"; // Will be displayed as "MMM"
              } else {
                restaurantWithPrice.priceRange = "$$$$"; // Will be displayed as "MMMM"
              }
            } else {
              // Assign a default price range if no menu items with prices
              restaurantWithPrice.priceRange = "$$";
            }
          } else {
            // Assign a default price range if no menu items
            restaurantWithPrice.priceRange = "$$";
          }
        } catch {
          // Assign a default price range on error
          restaurantWithPrice.priceRange = "$$";
        }
      }

      return restaurants;
    } catch {
      return restaurants;
    }
  }

  /**
   * Fetches all restaurants from the database - private method for internal use
   * @returns Promise with array of all restaurants
   */
  private static async fetchAllRestaurants(): Promise<Restaurant[]> {
    try {
      // Fetch all restaurants from database
      const restaurantsRef = collection(firestore, "restaurants");
      const querySnapshot = await getDocs(
        query(restaurantsRef, where("isActive", "==", true), limit(500))
      );

      const restaurants = querySnapshot.docs.map(
        (doc) => ({ id: doc.id, ...doc.data() } as Restaurant)
      );

      // Return the fetched restaurants
      return restaurants;
    } catch {
      // Handle error and return empty array
      return [];
    }
  }

  /**
   * Public method to get all restaurants - for caching purposes
   * @returns Promise with array of all restaurants
   */
  public static async getAllRestaurants(): Promise<Restaurant[]> {
    try {
      const restaurants = await this.fetchAllRestaurants();
      // Calculate price ranges for restaurants that don't have them
      const restaurantsWithPriceRanges = await this.calculatePriceRanges(
        restaurants
      );
      return restaurantsWithPriceRanges;
    } catch {
      // Handle error and return empty array
      return [];
    }
  }

  /**
   * Fetches restaurants based on the provided filter options
   * This method is now optimized to work with React Query
   */
  public static async getFilteredRestaurants(
    options: FilterOptions
  ): Promise<FilterResult> {
    try {
      // Fetch all restaurants
      const restaurants = await this.getAllRestaurants();

      // Calculate facets before applying filters
      const allRestaurantsFacets = this.calculateFacets(restaurants);

      // Apply all filters in memory
      const filteredRestaurants = this.applyInMemoryFilters(
        restaurants,
        options
      );

      // Return the filtered restaurants and facets
      return {
        restaurants: filteredRestaurants,
        lastVisible: undefined, // Not needed when we fetch all restaurants
        totalCount: filteredRestaurants.length,
        // Use the facets from all restaurants to show all available options
        facets: allRestaurantsFacets,
      };
    } catch {
      // Handle error
      throw new Error("Failed to fetch restaurants");
    }
  }
}
