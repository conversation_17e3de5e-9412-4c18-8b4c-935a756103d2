// LoginRegister.tsx
import React, { useState, useEffect } from "react";
import { useNavigate, useLocation, useSearchParams } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { useLoading } from "@/hooks/useLoading";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { toast } from "sonner";
import { useAuth } from "@/providers/AuthProvider";
import { Timestamp } from "firebase/firestore";
import { TermsAndConditions } from "@/components/TermsAndConditions";
import { <PERSON><PERSON>, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  <PERSON>,
  <PERSON>Off,
  Mail,
  Lock,
  User,
  Phone,
  Store,
  Award,
} from "lucide-react";
import { motion } from "framer-motion";
import {
  processReferral,
  verifyReferralCode,
} from "@/services/DirectReferralService";

interface ClientDetails {
  username: string;
  firstName: string;
  lastName: string;
  phone: string;
}

interface RestaurantDetails {
  username: string;
  restaurantName: string;
  email: string;
  uid: string;
  createdAt: Date | Timestamp;
  isProfileComplete: boolean;
}

export function LoginRegister() {
  const [isLogin, setIsLogin] = useState(true);
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [role, setRole] = useState<"restaurant" | "client">("client");
  const [searchParams] = useSearchParams();
  const [referralCode, setReferralCode] = useState("");
  const [referrerInfo, setReferrerInfo] = useState<{
    referrerId: string;
    referrerName: string;
    referrerEmail: string;
  } | null>(null);
  const [verifyingReferral, setVerifyingReferral] = useState(false);

  const {
    user,
    signInWithEmail,
    signUpWithEmail,
    loading: authLoading,
    error,
    clearError,
  } = useAuth();
  const {
    isLoading: isSubmitting,
    startLoading: startSubmit,
    stopLoading: stopSubmit,
  } = useLoading();
  const navigate = useNavigate();
  const location = useLocation();

  // Password visibility toggle
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  // Check for referral code in URL
  useEffect(() => {
    const refCode = searchParams.get("ref");
    if (refCode) {
      setReferralCode(refCode);
      setIsLogin(false); // Switch to registration view
      setRole("client"); // Set role to client
    }
  }, [searchParams]);

  // Verify referral code when it changes
  useEffect(() => {
    const verifyCode = async () => {
      if (referralCode.trim()) {
        setVerifyingReferral(true);
        setReferrerInfo(null);

        try {
          const info = await verifyReferralCode(referralCode.trim());
          setReferrerInfo(info);
        } catch (error) {
          console.error("Error verifying referral code:", error);
        } finally {
          setVerifyingReferral(false);
        }
      } else {
        setReferrerInfo(null);
      }
    };

    // Use a debounce to avoid too many requests
    const timeoutId = setTimeout(verifyCode, 500);
    return () => clearTimeout(timeoutId);
  }, [referralCode]);

  // Process pending referral code after user creation and handle navigation
  useEffect(() => {
    const processPendingReferralAndNavigate = async () => {
      if (user) {
        console.log(
          "[DIRECT_REFERRAL_UI] User is available, checking for pending referral code"
        );
        const pendingCode = localStorage.getItem("pendingReferralCode");
        const shouldRedirectToLoyalty =
          localStorage.getItem("redirectToLoyalty") === "true";

        let referralProcessed = false;

        if (pendingCode) {
          console.log(
            "[DIRECT_REFERRAL_UI] Found pending referral code:",
            pendingCode
          );
          try {
            // Use the new direct approach
            console.log(
              "[DIRECT_REFERRAL_UI] Processing referral directly for user:",
              user.uid
            );

            // Add a small delay to ensure Firebase auth is fully initialized
            await new Promise((resolve) => setTimeout(resolve, 1000));

            // Process the referral with the new direct method
            const result = await processReferral(user.uid, pendingCode);

            if (result) {
              console.log(
                "[DIRECT_REFERRAL_UI] Referral processed successfully"
              );
              referralProcessed = true;
              // Clear the pending code
              localStorage.removeItem("pendingReferralCode");
              toast.success(
                "Referral bonus applied! You've received 75 points."
              );
            } else {
              console.error("[DIRECT_REFERRAL_UI] Referral processing failed");
              toast.error(
                "Failed to apply referral bonus. Please contact support."
              );
              localStorage.removeItem("pendingReferralCode");
            }
          } catch (referralError) {
            console.error(
              "[DIRECT_REFERRAL_UI] Error processing referral:",
              referralError
            );
            if (referralError instanceof Error) {
              console.error(
                "[DIRECT_REFERRAL_UI] Error message:",
                referralError.message
              );
              console.error(
                "[DIRECT_REFERRAL_UI] Error stack:",
                referralError.stack
              );
              toast.error(`Error applying referral: ${referralError.message}`);
            } else {
              toast.error(
                "An unknown error occurred while processing your referral"
              );
            }
            localStorage.removeItem("pendingReferralCode");
          }
        } else {
          console.log("[DIRECT_REFERRAL_UI] No pending referral code found");
        }

        // Handle navigation after processing referral
        if (shouldRedirectToLoyalty) {
          localStorage.removeItem("redirectToLoyalty");
          console.log("[DIRECT_REFERRAL_UI] Should redirect to loyalty page");

          // Navigate to loyalty page if referral was processed
          if (referralProcessed) {
            console.log("[DIRECT_REFERRAL_UI] Navigating to loyalty page");
            navigate("/loyalty");
          } else {
            console.log(
              "[DIRECT_REFERRAL_UI] Navigating to profile page (referral not processed)"
            );
            navigate("/profile");
          }
        } else if (
          location.pathname === "/login" ||
          location.pathname === "/register"
        ) {
          // Only navigate away from login/register pages
          console.log(
            "[DIRECT_REFERRAL_UI] Navigating to profile page (from login/register)"
          );
          navigate("/profile");
        }
      }
    };

    processPendingReferralAndNavigate();
  }, [user, navigate, location.pathname]);

  useEffect(() => {
    if (error) {
      toast.error(error.message);
      clearError();
    }
  }, [error, clearError]);

  // Redirect if user is already logged in when component mounts
  // This prevents automatic redirection after registration
  const [redirecting, setRedirecting] = useState(false);

  useEffect(() => {
    // We check if the user exists but we're not in the registration process
    const isInitialLogin =
      sessionStorage.getItem("registrationInProgress") !== "true";

    if (user && isInitialLogin && !redirecting) {
      setRedirecting(true);
      // Check if we have a previous location to redirect to
      const from = location.state?.from?.pathname || "/";
      // Use a small timeout to ensure the redirect happens after render
      setTimeout(() => {
        navigate(from, { replace: true });
      }, 0);
    }
  }, [user, navigate, location, redirecting]);

  // Additional registration fields
  const [clientDetails, setClientDetails] = useState<ClientDetails>({
    username: "",
    firstName: "",
    lastName: "",
    phone: "",
  });

  const [restaurantDetails, setRestaurantDetails] = useState<RestaurantDetails>(
    {
      username: "",
      restaurantName: "",
      email: "",
      uid: "",
      createdAt: new Date(),
      isProfileComplete: false,
    }
  );

  const [agreedToTerms, setAgreedToTerms] = useState(false);
  const [subscribeNewsletter, setSubscribeNewsletter] = useState(false);
  const [newsletterOtp, setNewsletterOtp] = useState("");
  const [showOtpModal, setShowOtpModal] = useState(false);
  const [isNewsletterSubmitting, setIsNewsletterSubmitting] = useState(false);
  const [formValidated, setFormValidated] = useState(false);

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1, transition: { duration: 0.5 } },
  };

  const validateForm = () => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      toast.error("Please enter a valid email address");
      return false;
    }

    if (password.length < 8) {
      toast.error("Password must be at least 8 characters long");
      return false;
    }

    if (password !== confirmPassword) {
      toast.error("Passwords do not match");
      return false;
    }

    if (role === "client") {
      if (!clientDetails.username.trim()) {
        toast.error("Username is required");
        return false;
      }
      if (!clientDetails.firstName.trim()) {
        toast.error("First name is required");
        return false;
      }
      if (!clientDetails.lastName.trim()) {
        toast.error("Last name is required");
        return false;
      }
      if (!clientDetails.phone.trim()) {
        toast.error("Phone number is required");
        return false;
      }
    }

    if (role === "restaurant") {
      if (!restaurantDetails.username.trim()) {
        toast.error("Username is required");
        return false;
      }
      if (!restaurantDetails.restaurantName.trim()) {
        toast.error("Restaurant name is required");
        return false;
      }
    }

    if (!agreedToTerms) {
      toast.error("You must agree to the terms and conditions");
      return false;
    }

    return true;
  };

  const handleSendOtp = async () => {
    setIsNewsletterSubmitting(true);

    try {
      const response = await fetch(
        "http://localhost:3000/api/newsletter/subscribe",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ email }),
        }
      );

      if (!response.ok) {
        const text = await response.text();
        try {
          const errorData = JSON.parse(text);
          throw new Error(errorData.error || "Failed to send OTP");
        } catch {
          throw new Error(
            `Failed to send OTP: ${response.status} ${
              text || response.statusText
            }`
          );
        }
      }

      await response.json(); // We don't need the response data
      toast.success("Kayıt oldunuz! OTP ile aboneliğinizi doğrulayın.");
      setShowOtpModal(true);
    } catch (error) {
      console.error("Error sending OTP:", error);
      toast.error(
        error instanceof Error
          ? error.message
          : "Failed to send OTP. Please try again."
      );
    } finally {
      setIsNewsletterSubmitting(false);
    }
  };

  const handleVerifyOtp = async () => {
    if (!newsletterOtp) {
      toast.error("Please enter the OTP");
      return;
    }

    setIsNewsletterSubmitting(true);

    try {
      const response = await fetch(
        "http://localhost:3000/api/newsletter/verify",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ email, otp: newsletterOtp }),
        }
      );

      if (!response.ok) {
        const text = await response.text();
        try {
          const errorData = JSON.parse(text);
          throw new Error(errorData.error || "Failed to verify OTP");
        } catch {
          throw new Error(
            `Failed to verify OTP: ${response.status} ${
              text || response.statusText
            }`
          );
        }
      }

      await response.json();
      toast.success("Subscription confirmed! Completing registration...");

      const registrationDetails: ClientDetails = { ...clientDetails };
      await signUpWithEmail(email, password, role, registrationDetails);
      setShowOtpModal(false);
      navigate("/profile");
    } catch (error) {
      console.error("Error verifying OTP:", error);
      toast.error(
        error instanceof Error
          ? error.message
          : "Failed to verify OTP. Please try again."
      );
    } finally {
      setIsNewsletterSubmitting(false);
      setNewsletterOtp("");
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (isLogin) {
      if (!email || !password) {
        toast.error("Please enter both email and password");
        return;
      }

      try {
        startSubmit();
        await signInWithEmail(email, password);
        // After successful login, redirect will happen automatically via useEffect
        // No need to navigate here as it can cause double navigation
        setRedirecting(true);
      } catch (err) {
        if (err instanceof Error) {
          toast.error(err.message);
        } else {
          toast.error("An error occurred during authentication");
        }
      } finally {
        stopSubmit();
      }
      return;
    }

    if (!formValidated) {
      const isValid = validateForm();
      if (!isValid) return;
      setFormValidated(true);
    }

    try {
      startSubmit();

      // Set flag to indicate registration is in progress
      sessionStorage.setItem("registrationInProgress", "true");

      if (role === "client" && subscribeNewsletter) {
        await handleSendOtp();
      } else {
        if (role === "restaurant") {
          const registrationDetails: RestaurantDetails = {
            ...restaurantDetails,
            email,
          };
          await signUpWithEmail(email, password, role, registrationDetails);
        } else {
          const registrationDetails: ClientDetails = { ...clientDetails };
          await signUpWithEmail(email, password, role, registrationDetails);

          // Handle referral code if provided
          if (referralCode.trim()) {
            try {
              // Store the referral code in localStorage to process after user creation
              localStorage.setItem("pendingReferralCode", referralCode.trim());

              // Also store a flag to indicate we should redirect to loyalty page
              localStorage.setItem("redirectToLoyalty", "true");

              // We'll process this in a useEffect after user is created
              // This is because user might not be available immediately after signUpWithEmail
            } catch (referralError) {
              console.error("Error storing referral code:", referralError);
              // Don't block registration if referral processing fails
            }
          }
        }
        // Don't navigate here - we'll handle navigation in the useEffect after user creation
      }
    } catch (err) {
      if (err instanceof Error) {
        toast.error(err.message);
      } else {
        toast.error("An error occurred during authentication");
      }
    } finally {
      stopSubmit();
    }
  };

  if (authLoading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <svg
          className="animate-spin h-8 w-8 text-blue-500"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
        >
          <circle
            className="opacity-25"
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            strokeWidth="4"
          ></circle>
          <path
            className="opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-8v8h-8z"
          ></path>
        </svg>
      </div>
    );
  }

  return (
    <section>
      <div className="flex justify-center items-center min-h-screen bg-gray-50 p-10">
        <motion.div
          className="w-full grid md:grid-cols-2 rounded-xl overflow-hidden shadow-md"
          initial="hidden"
          animate="visible"
          variants={containerVariants}
        >
          {/* Left side - Image/Illustration */}
          <div className="hidden md:block relative bg-gray-900 overflow-hidden">
            <div className="absolute inset-0 flex flex-col justify-center items-center p-8 text-center z-10">
              <h2 className="text-4xl font-bold mb-4 text-white">
                Welcome to Qonai
              </h2>
              <p className="text-lg text-white/90 mb-10">
                Discover the best restaurants near you
              </p>
              <div className="grid grid-cols-2 gap-6 w-full max-w-xs">
                <div className="flex flex-col items-center">
                  <Store className="h-10 w-10 mb-2 text-white" />
                  <p className="text-sm text-white">Find top restaurants</p>
                </div>
                <div className="flex flex-col items-center">
                  <User className="h-10 w-10 mb-2 text-white" />
                  <p className="text-sm text-white">Personalized experience</p>
                </div>
              </div>
            </div>
          </div>

          {/* Right side - Form */}
          <Card className="w-full border-0 shadow-none bg-white md:rounded-l-none">
            <CardHeader className="pb-2">
              <CardTitle className="text-2xl font-bold text-center">
                {isLogin ? "Sign In" : "Create Account"}
              </CardTitle>
              <CardDescription className="text-center text-gray-500">
                {isLogin
                  ? "Enter your credentials to access your account"
                  : "Fill in the details to create your account"}
              </CardDescription>
            </CardHeader>
            <CardContent className="px-8">
              <Tabs
                defaultValue={isLogin ? "login" : "register"}
                onValueChange={(value) => setIsLogin(value === "login")}
                className="w-full"
              >
                <TabsList className="grid grid-cols-2 mb-6 bg-gray-100 p-1 rounded-md">
                  <TabsTrigger
                    value="login"
                    className="rounded-md data-[state=active]:bg-white data-[state=active]:shadow-sm"
                  >
                    Login
                  </TabsTrigger>
                  <TabsTrigger
                    value="register"
                    className="rounded-md data-[state=active]:bg-white data-[state=active]:shadow-sm"
                  >
                    Register
                  </TabsTrigger>
                </TabsList>

                <form onSubmit={handleSubmit} className="space-y-4">
                  {!isLogin && (
                    <>
                      <div className="space-y-2">
                        <Label className="text-sm font-medium">
                          Account Type
                        </Label>
                        <Select
                          value={role}
                          onValueChange={(value: "restaurant" | "client") => {
                            setRole(value);
                            setFormValidated(false);
                            if (value === "restaurant") {
                              setSubscribeNewsletter(false);
                              setShowOtpModal(false);
                              setNewsletterOtp("");
                              setReferralCode(""); // Clear referral code for restaurant accounts
                            }
                          }}
                        >
                          <SelectTrigger className="bg-transparent border border-input focus:ring-1 focus:ring-ring rounded-md">
                            <SelectValue placeholder="Select account type" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="client">Client</SelectItem>
                            <SelectItem value="restaurant">
                              Restaurant
                            </SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      {role === "client" && (
                        <div className="space-y-2">
                          <Label
                            htmlFor="referralCode"
                            className="text-sm font-medium"
                          >
                            Referral Code (Optional)
                          </Label>
                          <div className="relative flex items-center">
                            <div className="absolute left-3 z-10 pointer-events-none">
                              <Award className="h-4 w-4 text-muted-foreground" />
                            </div>
                            <Input
                              id="referralCode"
                              type="text"
                              value={referralCode}
                              onChange={(e) => setReferralCode(e.target.value)}
                              className="pl-10 transition-all duration-200 border-input focus:border-primary focus:ring-1 focus:ring-primary rounded-md"
                              placeholder="Enter referral code if you have one"
                            />
                          </div>
                          {verifyingReferral && (
                            <div className="mt-2 p-2 bg-blue-50 border border-blue-200 rounded-md">
                              <p className="text-sm text-blue-700 font-medium flex items-center">
                                <svg
                                  className="animate-spin h-4 w-4 mr-1.5 text-blue-600"
                                  xmlns="http://www.w3.org/2000/svg"
                                  fill="none"
                                  viewBox="0 0 24 24"
                                >
                                  <circle
                                    className="opacity-25"
                                    cx="12"
                                    cy="12"
                                    r="10"
                                    stroke="currentColor"
                                    strokeWidth="4"
                                  ></circle>
                                  <path
                                    className="opacity-75"
                                    fill="currentColor"
                                    d="M4 12a8 8 0 018-8v8h-8z"
                                  ></path>
                                </svg>
                                Verifying referral code...
                              </p>
                            </div>
                          )}

                          {!verifyingReferral &&
                            referralCode &&
                            referrerInfo && (
                              <div className="mt-2 p-2 bg-green-50 border border-green-200 rounded-md">
                                <p className="text-sm text-green-700 font-medium flex items-center">
                                  <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    className="h-4 w-4 mr-1.5 text-green-600"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                  >
                                    <path
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      strokeWidth={2}
                                      d="M5 13l4 4L19 7"
                                    />
                                  </svg>
                                  Valid referral code from{" "}
                                  <span className="font-bold mx-1">
                                    {referrerInfo.referrerName}
                                  </span>
                                  ! You'll receive{" "}
                                  <span className="font-bold mx-1">75</span>{" "}
                                  bonus points when you register.
                                </p>
                              </div>
                            )}

                          {!verifyingReferral &&
                            referralCode &&
                            !referrerInfo && (
                              <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded-md">
                                <p className="text-sm text-red-700 font-medium flex items-center">
                                  <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    className="h-4 w-4 mr-1.5 text-red-600"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                  >
                                    <path
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      strokeWidth={2}
                                      d="M6 18L18 6M6 6l12 12"
                                    />
                                  </svg>
                                  Invalid referral code. Please check and try
                                  again.
                                </p>
                              </div>
                            )}
                        </div>
                      )}
                    </>
                  )}

                  <div className="space-y-2">
                    <Label htmlFor="email" className="text-sm font-medium">
                      Email
                    </Label>
                    <div className="relative flex items-center">
                      <div className="absolute left-3 z-10 pointer-events-none">
                        <Mail className="h-4 w-4 text-muted-foreground" />
                      </div>
                      <Input
                        id="email"
                        type="email"
                        value={email}
                        onChange={(e) => {
                          setEmail(e.target.value);
                          setFormValidated(false);
                        }}
                        className="pl-10 pr-10 transition-all duration-200 border-input focus:border-primary rounded-md"
                        placeholder="Enter your email"
                        required
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="password" className="text-sm font-medium">
                      Password
                    </Label>
                    <div className="relative flex items-center">
                      <div className="absolute left-3 z-10 pointer-events-none">
                        <Lock className="h-4 w-4 text-muted-foreground" />
                      </div>
                      <Input
                        id="password"
                        type={showPassword ? "text" : "password"}
                        value={password}
                        onChange={(e) => {
                          setPassword(e.target.value);
                          setFormValidated(false);
                        }}
                        className="pl-10 pr-10 transition-all duration-200 border-input focus:border-primary rounded-md"
                        placeholder="Enter your password"
                        required
                      />
                      <button
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                        className="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground"
                      >
                        {showPassword ? (
                          <EyeOff className="h-4 w-4" />
                        ) : (
                          <Eye className="h-4 w-4" />
                        )}
                      </button>
                    </div>
                  </div>

                  {!isLogin && (
                    <div className="space-y-2">
                      <Label
                        htmlFor="confirm-password"
                        className="text-sm font-medium"
                      >
                        Confirm Password
                      </Label>
                      <div className="relative flex items-center">
                        <div className="absolute left-3 z-10 pointer-events-none">
                          <Lock className="h-4 w-4 text-muted-foreground" />
                        </div>
                        <Input
                          id="confirm-password"
                          type={showConfirmPassword ? "text" : "password"}
                          value={confirmPassword}
                          onChange={(e) => {
                            setConfirmPassword(e.target.value);
                            setFormValidated(false);
                          }}
                          className="pl-10 pr-10 transition-all duration-200 border-input focus:border-primary rounded-md"
                          placeholder="Confirm your password"
                          required
                        />
                        <button
                          type="button"
                          onClick={() =>
                            setShowConfirmPassword(!showConfirmPassword)
                          }
                          className="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground"
                        >
                          {showConfirmPassword ? (
                            <EyeOff className="h-4 w-4" />
                          ) : (
                            <Eye className="h-4 w-4" />
                          )}
                        </button>
                      </div>
                    </div>
                  )}

                  {!isLogin && role === "client" && (
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <Label
                          htmlFor="client-username"
                          className="text-sm font-medium"
                        >
                          Username
                        </Label>
                        <div className="relative flex items-center">
                          <div className="absolute left-3 z-10 pointer-events-none">
                            <User className="h-4 w-4 text-muted-foreground" />
                          </div>
                          <Input
                            id="client-username"
                            value={clientDetails.username}
                            onChange={(e) => {
                              setClientDetails((prev) => ({
                                ...prev,
                                username: e.target.value,
                              }));
                              setFormValidated(false);
                            }}
                            className="pl-10 transition-all duration-200 rounded-md"
                            placeholder="Choose a unique username"
                            required
                          />
                        </div>
                      </div>

                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label
                            htmlFor="first-name"
                            className="text-sm font-medium"
                          >
                            First Name
                          </Label>
                          <div className="relative flex items-center">
                            <div className="absolute left-3 z-10 pointer-events-none">
                              <User className="h-4 w-4 text-muted-foreground" />
                            </div>
                            <Input
                              id="first-name"
                              value={clientDetails.firstName}
                              onChange={(e) => {
                                setClientDetails((prev) => ({
                                  ...prev,
                                  firstName: e.target.value,
                                }));
                                setFormValidated(false);
                              }}
                              className="pl-10 transition-all duration-200 rounded-md"
                              placeholder="Enter your first name"
                              required
                            />
                          </div>
                        </div>

                        <div className="space-y-2">
                          <Label
                            htmlFor="last-name"
                            className="text-sm font-medium"
                          >
                            Last Name
                          </Label>
                          <div className="relative flex items-center">
                            <div className="absolute left-3 z-10 pointer-events-none">
                              <User className="h-4 w-4 text-muted-foreground" />
                            </div>
                            <Input
                              id="last-name"
                              value={clientDetails.lastName}
                              onChange={(e) => {
                                setClientDetails((prev) => ({
                                  ...prev,
                                  lastName: e.target.value,
                                }));
                                setFormValidated(false);
                              }}
                              className="pl-10 transition-all duration-200 rounded-md"
                              placeholder="Enter your last name"
                              required
                            />
                          </div>
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label
                          htmlFor="client-phone"
                          className="text-sm font-medium"
                        >
                          Phone Number
                        </Label>
                        <div className="relative flex items-center">
                          <div className="absolute left-3 z-10 pointer-events-none">
                            <Phone className="h-4 w-4 text-muted-foreground" />
                          </div>
                          <Input
                            id="client-phone"
                            value={clientDetails.phone}
                            onChange={(e) => {
                              setClientDetails((prev) => ({
                                ...prev,
                                phone: e.target.value,
                              }));
                              setFormValidated(false);
                            }}
                            className="pl-10 transition-all duration-200 rounded-md"
                            placeholder="Enter your phone number"
                            required
                          />
                        </div>
                      </div>
                    </div>
                  )}

                  {!isLogin && role === "restaurant" && (
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <Label
                          htmlFor="restaurant-username"
                          className="text-sm font-medium"
                        >
                          Username
                        </Label>
                        <div className="relative flex items-center">
                          <div className="absolute left-3 z-10 pointer-events-none">
                            <User className="h-4 w-4 text-muted-foreground" />
                          </div>
                          <Input
                            id="restaurant-username"
                            value={restaurantDetails.username}
                            onChange={(e) => {
                              setRestaurantDetails((prev) => ({
                                ...prev,
                                username: e.target.value,
                              }));
                              setFormValidated(false);
                            }}
                            className="pl-10 transition-all duration-200 rounded-md"
                            placeholder="Choose a unique username"
                            required
                          />
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label
                          htmlFor="restaurant-name"
                          className="text-sm font-medium"
                        >
                          Restaurant Name
                        </Label>
                        <div className="relative flex items-center">
                          <div className="absolute left-3 z-10 pointer-events-none">
                            <Store className="h-4 w-4 text-muted-foreground" />
                          </div>
                          <Input
                            id="restaurant-name"
                            value={restaurantDetails.restaurantName}
                            onChange={(e) => {
                              setRestaurantDetails((prev) => ({
                                ...prev,
                                restaurantName: e.target.value,
                              }));
                              setFormValidated(false);
                            }}
                            className="pl-10 transition-all duration-200 rounded-md"
                            placeholder="Enter your restaurant name"
                            required
                          />
                        </div>
                      </div>
                    </div>
                  )}

                  {!isLogin && (
                    <div className="flex items-center space-x-2 p-3 rounded-md">
                      <Checkbox
                        id="terms"
                        checked={agreedToTerms}
                        onCheckedChange={(checked) => {
                          setAgreedToTerms(!!checked);
                          setFormValidated(false);
                        }}
                        className="border-primary/50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground"
                      />
                      <div className="grid gap-1.5 leading-none">
                        <Label
                          htmlFor="terms"
                          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        >
                          I agree to the{" "}
                          <TermsAndConditions>
                            <button
                              className="text-primary underline hover:text-primary/90 font-semibold"
                              type="button"
                            >
                              Terms and Conditions
                            </button>
                          </TermsAndConditions>
                        </Label>
                      </div>
                    </div>
                  )}

                  {!isLogin && role === "client" && (
                    <div className="flex items-center space-x-2 p-3 rounded-md">
                      <Checkbox
                        id="newsletter"
                        checked={subscribeNewsletter}
                        onCheckedChange={(checked) => {
                          setSubscribeNewsletter(!!checked);
                          if (!checked) {
                            setShowOtpModal(false);
                            setNewsletterOtp("");
                          }
                          setFormValidated(false);
                        }}
                        className="border-primary/50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground"
                      />
                      <div className="grid gap-1.5 leading-none">
                        <Label
                          htmlFor="newsletter"
                          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        >
                          Subscribe to our newsletter for the latest restaurant
                          recommendations
                        </Label>
                      </div>
                    </div>
                  )}

                  <Button
                    type="submit"
                    className="w-full h-12 mt-4 text-base font-medium transition-all duration-200 bg-primary hover:bg-primary/90 rounded-md"
                    disabled={isSubmitting || isNewsletterSubmitting}
                  >
                    {isSubmitting || isNewsletterSubmitting ? (
                      <span className="flex items-center justify-center">
                        <svg
                          className="animate-spin h-5 w-5 mr-2 text-white"
                          xmlns="http://www.w3.org/2000/svg"
                          fill="none"
                          viewBox="0 0 24 24"
                        >
                          <circle
                            className="opacity-25"
                            cx="12"
                            cy="12"
                            r="10"
                            stroke="currentColor"
                            strokeWidth="4"
                          ></circle>
                          <path
                            className="opacity-75"
                            fill="currentColor"
                            d="M4 12a8 8 0 018-8v8h-8z"
                          ></path>
                        </svg>
                        {isLogin ? "Signing in..." : "Creating account..."}
                      </span>
                    ) : (
                      <>{isLogin ? "Sign In" : "Create Account"}</>
                    )}
                  </Button>
                </form>
              </Tabs>
            </CardContent>
          </Card>
        </motion.div>
      </div>

      {/* OTP Modal */}
      {showOtpModal && (
        <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50">
          <div className="bg-white p-6 rounded-xl shadow-xl max-w-sm w-full border border-gray-200">
            <h3 className="text-xl font-semibold mb-4 text-center">
              Verify Your Subscription
            </h3>
            <Label htmlFor="newsletter-otp" className="text-sm font-medium">
              Enter OTP sent to your email
            </Label>
            <div className="flex gap-2 mt-2">
              <div className="relative flex-1">
                <Input
                  id="newsletter-otp"
                  type="text"
                  value={newsletterOtp}
                  onChange={(e) => setNewsletterOtp(e.target.value)}
                  placeholder="Enter the OTP"
                  disabled={isNewsletterSubmitting}
                  className="pl-3 transition-all duration-200 rounded-md"
                />
              </div>
              <Button
                onClick={handleVerifyOtp}
                disabled={isNewsletterSubmitting}
                variant="outline"
                className="flex-shrink-0 rounded-md"
              >
                {isNewsletterSubmitting ? (
                  <span className="flex items-center justify-center">
                    <svg
                      className="animate-spin h-4 w-4 mr-2"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                      ></circle>
                      <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8v8h-8z"
                      ></path>
                    </svg>
                    Verifying...
                  </span>
                ) : (
                  "Verify"
                )}
              </Button>
            </div>
            <Button
              className="mt-4 w-full rounded-md"
              onClick={() => setShowOtpModal(false)}
            >
              Cancel
            </Button>
          </div>
        </div>
      )}
    </section>
  );
}
