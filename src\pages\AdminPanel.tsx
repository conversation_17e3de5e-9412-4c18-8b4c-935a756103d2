// src/components/admin/AdminPanel.tsx
import { useState, useEffect, useCallback, useRef } from "react";
import { useAuth } from "@/providers/AuthProvider";
import { Link } from "react-router-dom";
import { Loading } from "@/components/ui/loading";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Gift } from "lucide-react";
import { MenuPanel } from "@/components/admin/MenuPanel";
import { TablesPanel } from "@/components/admin/TablesPanel"; // Assuming TablesPanel path
import type { MenuItem } from "@/types/menu"; // Assuming menu types path
import type { Element, ElementType } from "@/components/layout-editor/types"; // Import Element type from TablesPanel's types
import {
  getDefaultColor,
  getDefaultSize,
} from "@/components/layout-editor/utils"; // Import helpers

import {
  collection,
  query,
  where,
  orderBy,
  onSnapshot,
  addDoc,
  doc,
  updateDoc,
  deleteDoc,
  FirestoreError,
  getDocs,
  setDoc,
  getDoc, // Added for handleLoadLayout
  serverTimestamp, // Added for saving
  FieldValue,
  DocumentData,
} from "firebase/firestore";
import { firestore } from "@/config/firebase"; // Assuming firebase config path
import { toast } from "sonner";

// Default canvas dimensions (can be overridden by dynamic sizing)
const DEFAULT_CANVAS_WIDTH = 1200;
const DEFAULT_CANVAS_HEIGHT = 600;

// TableConfig remains the same for syncing tables list
interface TableConfig {
  id: string;
  name: string;
  capacity: number;
}

// Interface for raw layout element data from Firestore
interface RawLayoutElement {
  id?: string;
  type?: string;
  x?: number;
  y?: number;
  width?: number;
  height?: number;
  rotation?: number;
  zIndex?: number;
  opacity?: number;
  borderRadius?: number;
  isLocked?: boolean;
  name?: string;
  capacity?: number;
  color?: string;
  borderColor?: string;
  isGrouped?: boolean;
  groupId?: string;
}

// Define types for the sanitizable object
type FirestoreValue =
  | string
  | number
  | boolean
  | null
  | undefined
  | FirestoreObject
  | FirestoreArray
  | TableConfig
  | Element
  | TableConfig[]
  | Element[]
  | FirestoreDocData;

type FirestoreArray = FirestoreValue[];

interface FirestoreObject {
  [key: string]: FirestoreValue;
}

// Define a more specific type for the data structure we're saving
interface FirestoreDocData {
  tables: TableConfig[];
  layout: Element[];
  lastUpdated: FieldValue; // More specific type for serverTimestamp()
  [key: string]: TableConfig[] | Element[] | FieldValue | DocumentData; // More specific index signature
}

export const AdminPanel = () => {
  const {
    user,
    loading: authLoading,
    error: authError,
    clearError,
    userRole,
  } = useAuth();
  const [menuItems, setMenuItems] = useState<MenuItem[]>([]);
  const [tables, setTables] = useState<TableConfig[]>([]);
  const [layout, setLayout] = useState<Element[]>([]); // State now holds Element[]
  const [isLoading, setIsLoading] = useState(true);

  // State for dynamic canvas dimensions
  const [canvasDims, setCanvasDims] = useState({
    width: DEFAULT_CANVAS_WIDTH,
    height: DEFAULT_CANVAS_HEIGHT,
  });
  // Ref for the container whose size we want to measure
  const canvasContainerRef = useRef<HTMLDivElement>(null);

  // Error handling for authentication
  useEffect(() => {
    if (authError) {
      toast.error(`Authentication Error: ${authError.message}`);
      clearError();
    }
  }, [authError, clearError]);

  // Fetch menu items (unchanged from your original code)
  const fetchMenuItems = useCallback(async () => {
    if (!user) return;
    // setIsLoading(true); // Loading managed by tables/layout fetch now
    try {
      const restaurantsRef = collection(firestore, "restaurants");
      const restaurantQuery = query(
        restaurantsRef,
        where("uid", "==", user.uid)
      );
      const restaurantSnapshot = await getDocs(restaurantQuery);

      if (restaurantSnapshot.empty) {
        // toast.error("Restaurant not found for menu"); // Avoid redundant toasts
        console.warn("Restaurant document not found for user:", user.uid);
        return;
      }

      const restaurantDoc = restaurantSnapshot.docs[0];
      const menuRef = collection(restaurantDoc.ref, "menu");
      const q = query(menuRef, orderBy("category"), orderBy("name"));

      const unsubscribe = onSnapshot(
        q,
        (querySnapshot) => {
          const fetchedItems: MenuItem[] = [];
          querySnapshot.forEach((doc) => {
            fetchedItems.push({ ...doc.data(), itemId: doc.id } as MenuItem);
          });
          setMenuItems(fetchedItems);
          // setIsLoading(false); // Loading managed elsewhere
        },
        (error: FirestoreError) => {
          toast.error(`Failed to fetch menu items: ${error.message}`);
          console.error("Error fetching menu items:", error);
          // setIsLoading(false);
        }
      );
      return () => unsubscribe(); // Cleanup listener
    } catch (error: unknown) {
      toast.error(
        `Error in fetchMenuItems: ${(error as FirestoreError).message}`
      );
      console.error("Error in fetchMenuItems:", error);
      // setIsLoading(false);
    }
  }, [user]);

  // Fetch tables and layout in real-time
  useEffect(() => {
    if (!user) return;

    setIsLoading(true);
    let unsubscribeSettings: (() => void) | null = null;

    const restaurantsRef = collection(firestore, "restaurants");
    const restaurantQuery = query(restaurantsRef, where("uid", "==", user.uid));

    getDocs(restaurantQuery)
      .then((restaurantSnapshot) => {
        if (restaurantSnapshot.empty) {
          toast.error("Restaurant configuration not found.");
          console.warn("Restaurant document not found for user:", user.uid);
          setIsLoading(false);
          setLayout([]); // Ensure layout is empty if not found
          setTables([]);
          return;
        }

        const restaurantDoc = restaurantSnapshot.docs[0];
        const settingsRef = doc(restaurantDoc.ref, "settings", "config");

        // Listen for real-time updates on settings
        unsubscribeSettings = onSnapshot(
          settingsRef,
          (docSnapshot) => {
            if (docSnapshot.exists()) {
              const settings = docSnapshot.data();
              // Load and validate Tables
              setTables(Array.isArray(settings.tables) ? settings.tables : []);

              // Load and validate Layout (ensure it conforms to Element[])
              const loadedLayout = Array.isArray(settings.layout)
                ? settings.layout
                : [];
              const validatedLayout: Element[] = loadedLayout.map(
                (el: RawLayoutElement) => {
                  const type = el.type || "table-rect"; // Default type if missing
                  const defaults = getDefaultSize(type as ElementType);
                  const defaultColor = getDefaultColor(type as ElementType);
                  return {
                    id: el.id || crypto.randomUUID(),
                    type: type as ElementType,
                    x: el.x ?? 0,
                    y: el.y ?? 0,
                    width: el.width ?? defaults.width,
                    height: el.height ?? defaults.height,
                    rotation: el.rotation ?? 0,
                    zIndex: el.zIndex ?? 0,
                    opacity: el.opacity ?? 1,
                    borderRadius:
                      el.borderRadius ?? (type === "table-round" ? 9999 : 0),
                    isLocked: el.isLocked ?? false,
                    name: el.name || undefined,
                    capacity:
                      el.capacity ?? (type.includes("table") ? 4 : undefined),
                    color: el.color ?? defaultColor,
                    borderColor: el.borderColor ?? "#000000",
                    isGrouped: el.isGrouped ?? false,
                    groupId: el.groupId || undefined,
                  };
                }
              );
              setLayout(validatedLayout);
            } else {
              // Document doesn't exist
              setTables([]);
              setLayout([]);
            }
            setIsLoading(false); // Loading finished after processing snapshot
          },
          (error: FirestoreError) => {
            toast.error(`Failed to fetch settings: ${error.message}`);
            console.error("Error fetching settings:", error);
            setIsLoading(false);
          }
        );
      })
      .catch((error) => {
        toast.error(
          `Error querying restaurant: ${(error as FirestoreError).message}`
        );
        console.error("Error querying restaurant:", error);
        setIsLoading(false);
      });

    // Fetch menu items after restaurant check
    fetchMenuItems();

    // Cleanup function for the settings listener
    return () => {
      if (unsubscribeSettings) {
        unsubscribeSettings();
      }
    };
  }, [user, fetchMenuItems]); // Include fetchMenuItems here

  // --- Dynamic Canvas Sizing Effect (Placeholder) ---
  useEffect(() => {
    const container = canvasContainerRef.current;
    if (!container) return;

    // Use ResizeObserver to watch the container size
    const observer = new ResizeObserver((entries) => {
      const entry = entries[0];
      if (entry) {
        const { width, height } = entry.contentRect;
        // Update state, potentially with debouncing if needed
        setCanvasDims({ width, height });
      }
    });

    observer.observe(container);

    // Initial size check (optional)
    const { width, height } = container.getBoundingClientRect();
    setCanvasDims({ width, height }); // Set initial size

    return () => {
      observer.disconnect();
    };
  }, []); // Run once on mount

  // --- Menu item operations (unchanged) ---
  const handleAddMenuItem = async (
    item: Omit<MenuItem, "itemId" | "restaurantId">
  ) => {
    if (!user) return;
    try {
      // (Code remains the same as your original)
      const restaurantsRef = collection(firestore, "restaurants");
      const restaurantQuery = query(
        restaurantsRef,
        where("uid", "==", user.uid)
      );
      const restaurantSnapshot = await getDocs(restaurantQuery);
      if (restaurantSnapshot.empty) throw new Error("Restaurant not found");
      const restaurantDoc = restaurantSnapshot.docs[0];
      const menuRef = collection(restaurantDoc.ref, "menu");
      await addDoc(menuRef, { ...item, createdAt: new Date() });
      toast.success("Menu item added!");
    } catch (error: unknown) {
      console.error("Error adding menu item:", error);
      toast.error(
        `Failed to add menu item: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    }
  };

  const handleUpdateMenuItem = async (item: MenuItem) => {
    if (!user) return;
    try {
      const restaurantsRef = collection(firestore, "restaurants");
      const restaurantQuery = query(
        restaurantsRef,
        where("uid", "==", user.uid)
      );
      const restaurantSnapshot = await getDocs(restaurantQuery);
      if (restaurantSnapshot.empty) throw new Error("Restaurant not found");
      const restaurantDoc = restaurantSnapshot.docs[0];
      const itemRef = doc(restaurantDoc.ref, "menu", item.itemId);

      // Exclude itemId from update data but keep restaurantId
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { itemId, ...updateData } = item;

      // Ensure restaurantId is set to the restaurant's ID if it's missing or undefined
      if (!updateData.restaurantId) {
        updateData.restaurantId = restaurantDoc.id;
      }

      await updateDoc(itemRef, updateData);
      toast.success("Menu item updated!");
    } catch (error: unknown) {
      console.error("Error updating menu item:", error);
      toast.error(
        `Failed to update menu item: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    }
  };

  const handleDeleteMenuItem = async (itemId: string) => {
    if (!user) return;
    try {
      // (Code remains the same as your original)
      const restaurantsRef = collection(firestore, "restaurants");
      const restaurantQuery = query(
        restaurantsRef,
        where("uid", "==", user.uid)
      );
      const restaurantSnapshot = await getDocs(restaurantQuery);
      if (restaurantSnapshot.empty) throw new Error("Restaurant not found");
      const restaurantDoc = restaurantSnapshot.docs[0];
      const itemRef = doc(restaurantDoc.ref, "menu", itemId);
      await deleteDoc(itemRef);
      toast.success("Menu item deleted!");
    } catch (error: unknown) {
      console.error("Error deleting menu item:", error);
      toast.error(
        `Failed to delete menu item: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    }
  };

  // --- Helper: Sanitize object for Firestore (undefined -> null) ---
  const sanitizeObject = (obj: FirestoreValue): FirestoreValue => {
    if (obj === null || typeof obj !== "object") {
      // Primitives or null are returned directly
      return obj;
    }

    if (Array.isArray(obj)) {
      // Recursively sanitize array elements
      return obj.map((item) => sanitizeObject(item));
    }

    // Handle objects
    const sanitized: FirestoreObject = {};
    for (const key in obj as FirestoreObject) {
      // Ensure it's an own property, not from prototype
      if (Object.prototype.hasOwnProperty.call(obj, key)) {
        const value = (obj as FirestoreObject)[key];
        // Replace undefined with null, otherwise recurse
        sanitized[key] = value === undefined ? null : sanitizeObject(value);
      }
    }
    return sanitized;
  };

  // --- Save Layout Handler (Updated) ---
  const handleSaveLayout = async (elementsFromPanel: Element[]) => {
    if (!user) {
      toast.error("User not authenticated.");
      return; // Return added for type safety
    }
    try {
      const restaurantsRef = collection(firestore, "restaurants");
      const restaurantQuery = query(
        restaurantsRef,
        where("uid", "==", user.uid)
      );
      const restaurantSnapshot = await getDocs(restaurantQuery);

      if (restaurantSnapshot.empty) {
        throw new Error("Restaurant configuration not found.");
      }

      const restaurantDoc = restaurantSnapshot.docs[0];
      const settingsRef = doc(restaurantDoc.ref, "settings", "config");

      // 1. Sync tables state with the table elements from the panel
      const tableElements = elementsFromPanel.filter((el) =>
        el.type.includes("table")
      );
      // Keep existing table IDs if names match, generate new IDs otherwise
      const updatedTables: TableConfig[] = tableElements.map((el, index) => {
        const existingTable = tables.find((t) => t.name === el.name && el.name); // Match only if name exists
        return {
          id: existingTable?.id || crypto.randomUUID(), // Keep existing ID or generate new
          name: el.name || `Table ${index + 1}`, // Provide default name if missing
          capacity: el.capacity || 4, // Default capacity
        };
      });

      // 2. Prepare the complete data object for Firestore
      //    We save the elementsFromPanel directly as the new layout
      const dataToSave: FirestoreDocData = {
        tables: updatedTables,
        layout: elementsFromPanel, // Save the Element[] directly
        lastUpdated: serverTimestamp(), // Use server timestamp
      };

      // 3. Sanitize the data (convert undefined to null recursively)
      const sanitizedData = sanitizeObject(dataToSave) as DocumentData;

      // 4. Save to Firestore using setDoc with merge
      await setDoc(settingsRef, sanitizedData, { merge: true });

      // 5. Update local state optimistically (optional but good UX)
      setTables(updatedTables);
      setLayout(elementsFromPanel); // Update local layout state

      toast.success("Layout and tables saved successfully!");
    } catch (error: unknown) {
      console.error("Error saving layout:", error);
      toast.error(
        `Failed to save layout: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
      // Re-throw if TablesPanel needs to handle its own save status feedback
      throw error;
    }
  };

  // --- Load Layout Handler (Updated) ---
  const handleLoadLayout = async (): Promise<Element[]> => {
    if (!user) {
      toast.error("User not authenticated.");
      return []; // Return empty array on error
    }
    try {
      const restaurantsRef = collection(firestore, "restaurants");
      const restaurantQuery = query(
        restaurantsRef,
        where("uid", "==", user.uid)
      );
      const restaurantSnapshot = await getDocs(restaurantQuery);

      if (restaurantSnapshot.empty) {
        throw new Error("Restaurant configuration not found.");
      }

      const restaurantDoc = restaurantSnapshot.docs[0];
      const settingsRef = doc(restaurantDoc.ref, "settings", "config");
      const docSnap = await getDoc(settingsRef);

      if (docSnap.exists()) {
        const settings = docSnap.data();
        const loadedLayout = Array.isArray(settings.layout)
          ? settings.layout
          : [];
        // Validate and apply defaults to the loaded layout
        const validatedLayout: Element[] = loadedLayout.map(
          (el: RawLayoutElement) => {
            const type = el.type || "table-rect";
            const defaults = getDefaultSize(type as ElementType);
            const defaultColor = getDefaultColor(type as ElementType);
            return {
              id: el.id || crypto.randomUUID(),
              type: type as ElementType,
              x: el.x ?? 0,
              y: el.y ?? 0,
              width: el.width ?? defaults.width,
              height: el.height ?? defaults.height,
              rotation: el.rotation ?? 0,
              zIndex: el.zIndex ?? 0,
              opacity: el.opacity ?? 1,
              borderRadius:
                el.borderRadius ?? (type === "table-round" ? 9999 : 0),
              isLocked: el.isLocked ?? false,
              name: el.name || undefined,
              capacity: el.capacity ?? (type.includes("table") ? 4 : undefined),
              color: el.color ?? defaultColor,
              borderColor: el.borderColor ?? "#000000",
              isGrouped: el.isGrouped ?? false,
              groupId: el.groupId || undefined,
            };
          }
        );
        // Update the main layout state as well when loading manually
        setLayout(validatedLayout);
        toast.info("Layout reloaded from saved state.");
        return validatedLayout; // Return the validated layout
      } else {
        toast.warning("No saved layout found to load.");
        return []; // Return empty if no document
      }
    } catch (error: unknown) {
      console.error("Error loading layout:", error);
      toast.error(
        `Failed to load layout: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
      return []; // Return empty on error
    }
  };

  // --- Render Logic ---
  if (authLoading) {
    // Show loading indicator while checking auth status
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loading type="default" />
      </div>
    );
  }

  if (!user || userRole !== "restaurant") {
    // Redirect or show message if not authorized
    return (
      <div className="p-4 text-center text-red-600">
        You do not have permission to access this page. Please log in as a
        restaurant owner.
      </div>
    );
  }

  // Main content when authenticated and authorized
  return (
    <section className="p-4 min-h-screen">
      <div className="flex justify-between items-center mb-4">
        {user?.email === "<EMAIL>" && (
          <Button asChild variant="outline" className="ml-auto">
            <Link to="/admin-rewards">
              <Gift className="h-4 w-4 mr-2" />
              Manage Global Rewards
            </Link>
          </Button>
        )}
      </div>

      <Tabs defaultValue="menu" className="flex-1">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="menu">Menu</TabsTrigger>
          <TabsTrigger value="tables">Layout</TabsTrigger>
        </TabsList>

        <TabsContent value="menu" className="flex-grow">
          <MenuPanel
            menuItems={menuItems}
            isTableLoading={isLoading} // Use combined loading state
            onAddItem={handleAddMenuItem}
            onUpdateItem={handleUpdateMenuItem}
            onDeleteItem={handleDeleteMenuItem}
          />
        </TabsContent>

        <TabsContent
          value="tables"
          className="flex-grow h-full" // Ensure content takes height
          ref={canvasContainerRef} // Add ref for size measurement
        >
          {/* Show loading specific to tables/layout fetch */}
          {isLoading ? (
            <div className="flex items-center justify-center h-64">
              <Loading type="default" />
            </div>
          ) : (
            <TablesPanel
              // Pass the layout state directly (it's already Element[])
              initialElements={layout}
              // Pass the updated save handler
              onSave={handleSaveLayout}
              // Pass the updated load handler
              onLoad={handleLoadLayout}
              // Pass canvas dimensions (potentially dynamic in future)
              canvasWidth={canvasDims.width}
              canvasHeight={canvasDims.height}
            />
          )}
        </TabsContent>
      </Tabs>
    </section>
  );
};
