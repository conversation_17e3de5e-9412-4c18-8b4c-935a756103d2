import { Timestamp } from "firebase/firestore";

/**
 * Represents a loyalty reward that can be redeemed
 */
export interface Reward {
  id: string;
  name: string;
  description: string;
  pointsCost: number;
  type: "discount" | "freeItem" | "freeDelivery" | "vipAccess" | "other";
  discountValue?: number; // For discount rewards (percentage)
  discountType?: "percentage" | "fixed"; // Type of discount
  menuItemId?: string; // For free item rewards
  restaurantId?: string; // For restaurant-specific rewards
  imageUrl?: string;
  isActive: boolean;
  isGlobal?: boolean; // Whether this is a platform-wide reward
  isFeatured?: boolean; // Whether this reward is featured
  availableQuantity?: number; // Limited quantity if applicable
  expiresAt?: Timestamp; // When this reward expires (if applicable)
  termsAndConditions?: string;
  minimumOrderValue?: number; // Minimum order value to use reward
  createdAt: Timestamp;
  updatedAt: Timestamp;
  createdBy: string; // User ID who created the reward
}

/**
 * Represents a redeemed reward
 */
export interface RewardRedemption {
  id: string;
  userId: string;
  rewardId: string;
  reward?: Reward; // Denormalized reward data
  pointsSpent: number;
  redeemedAt: Timestamp;
  expiresAt: Timestamp;
  isUsed: boolean;
  usedAt?: Timestamp;
  code: string; // Unique redemption code
  restaurantId?: string; // Restaurant ID if applicable
  orderId?: string; // Order ID where the reward was used
}

/**
 * Represents analytics data for a reward
 */
export interface RewardAnalytics {
  rewardId: string;
  totalRedemptions: number;
  uniqueUsers: number;
  pointsSpent: number;
  lastRedemption?: Timestamp;
  redemptionsByDay: Record<string, number>; // Format: "YYYY-MM-DD": count
  redemptionsByMonth: Record<string, number>; // Format: "YYYY-MM": count
}

/**
 * Represents a special offer or limited-time reward
 */
export interface SpecialOffer {
  id: string;
  name: string;
  description: string;
  rewardIds: string[]; // IDs of rewards included in this offer
  startDate: Timestamp;
  endDate: Timestamp;
  isActive: boolean;
  imageUrl?: string;
  createdAt: Timestamp;
  updatedAt: Timestamp;
  createdBy: string; // User ID who created the offer
}
