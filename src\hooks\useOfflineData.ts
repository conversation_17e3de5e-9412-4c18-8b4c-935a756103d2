import { useState, useEffect, useCallback } from 'react';
import { useOffline } from '@/providers/OfflineProvider';
import { getData, addData, updateData, STORES } from '@/utils/indexedDB';

/**
 * A hook for managing data with offline support
 * @param key The unique identifier for this data
 * @param fetchFn The function to fetch data from the network
 * @param storeName The IndexedDB store to use (from STORES enum)
 * @returns An object with data, loading state, error state, and refresh function
 */
export function useOfflineData<T>(
  key: string,
  fetchFn: () => Promise<T>,
  storeName: string = STORES.RESTAURANTS
) {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);
  const { isOnline, offlineMode } = useOffline();

  const fetchData = useCallback(async (forceRefresh = false) => {
    setLoading(true);
    setError(null);

    try {
      // Try to get data from IndexedDB first
      if (!forceRefresh) {
        const cachedData = await getData<T>(storeName, key);
        if (cachedData) {
          setData(cachedData);
          setLoading(false);

          // If we're offline or not forcing refresh, return early with cached data
          if (!isOnline) {
            return;
          }
        }
      }

      // If online, fetch fresh data
      if (isOnline) {
        const freshData = await fetchFn();
        setData(freshData);

        // Cache the data if offline mode is enabled
        if (offlineMode === 'enabled') {
          if (await getData(storeName, key)) {
            await updateData(storeName, { id: key, ...freshData });
          } else {
            await addData(storeName, { id: key, ...freshData });
          }
        }
      } else if (!data) {
        // If we're offline and have no data, throw an error
        throw new Error('You are offline and no cached data is available');
      }
    } catch (err) {
      console.error(`[Offline] Error fetching data for ${key}:`, err);
      setError(err instanceof Error ? err : new Error('An unknown error occurred'));
    } finally {
      setLoading(false);
    }
  }, [key, isOnline, offlineMode, storeName, fetchFn, data]);

  // Fetch data on mount and when dependencies change
  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return {
    data,
    loading,
    error,
    refresh: () => fetchData(true),
  };
}

/**
 * A hook for managing a collection of data with offline support
 * @param collectionName The name of the collection
 * @param fetchFn The function to fetch data from the network
 * @param storeName The IndexedDB store to use (from STORES enum)
 * @returns An object with data array, loading state, error state, and refresh function
 */
export function useOfflineCollection<T>(
  collectionName: string,
  fetchFn: () => Promise<T[]>,
  storeName: string = STORES.RESTAURANTS
) {
  const [data, setData] = useState<T[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);
  const { isOnline, offlineMode } = useOffline();

  const fetchCollection = useCallback(async (forceRefresh = false) => {
    setLoading(true);
    setError(null);

    try {
      // Try to get data from IndexedDB first
      if (!forceRefresh) {
        const cachedData = await getData<T[]>(storeName, collectionName);
        if (cachedData) {
          setData(cachedData);
          setLoading(false);

          // If we're offline or not forcing refresh, return early with cached data
          if (!isOnline) {
            return;
          }
        }
      }

      // If online, fetch fresh data
      if (isOnline) {
        const freshData = await fetchFn();
        setData(freshData);

        // Cache the data if offline mode is enabled
        if (offlineMode === 'enabled') {
          if (await getData(storeName, collectionName)) {
            await updateData(storeName, { id: collectionName, items: freshData });
          } else {
            await addData(storeName, { id: collectionName, items: freshData });
          }
        }
      } else if (!data.length) {
        // If we're offline and have no data, throw an error
        throw new Error('You are offline and no cached data is available');
      }
    } catch (err) {
      console.error(`[Offline] Error fetching collection for ${collectionName}:`, err);
      setError(err instanceof Error ? err : new Error('An unknown error occurred'));
    } finally {
      setLoading(false);
    }
  }, [collectionName, isOnline, offlineMode, storeName, fetchFn, data]);

  // Fetch data on mount and when dependencies change
  useEffect(() => {
    fetchCollection();
  }, [fetchCollection]);

  return {
    data,
    loading,
    error,
    refresh: () => fetchCollection(true),
  };
}
