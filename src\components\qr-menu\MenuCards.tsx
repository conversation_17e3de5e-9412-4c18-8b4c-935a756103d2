import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { MenuItem } from "@/types/restaurant";
import {
  AlertCircle,
  Clock,
  Flame,
  Heart,
  Award,
  Timer,
  Sparkles,
  Plus,
  Utensils,
} from "lucide-react";

// Menu Item Card Component
interface MenuItemCardProps {
  item: MenuItem;
  onAddToCart: () => void;
  onClick: () => void;
}

export const MenuItemCard = ({
  item,
  onAddToCart,
  onClick,
}: MenuItemCardProps) => {
  return (
    <Card
      className="overflow-hidden group transition-all duration-300 hover:shadow-lg border hover:border-primary/50 relative rounded-xl p-3 sm:p-4"
      onClick={onClick}
    >
      <div className="flex h-20 sm:h-24">
        {/* Image */}
        <div className="w-20 sm:w-24 h-20 sm:h-24 relative cursor-pointer flex-shrink-0 overflow-hidden">
          {item.imageUrl ? (
            <img
              src={item.imageUrl}
              alt={item.name}
              className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
              loading="lazy"
            />
          ) : (
            <div className="w-full h-full bg-muted flex items-center justify-center">
              <Utensils className="h-5 sm:h-6 w-5 sm:w-6 text-muted-foreground" />
            </div>
          )}

          {/* Spicy indicator */}
          {item.spicyLevel && (
            <div className="absolute bottom-0 left-0 bg-red-500/90 text-white text-[9px] py-0.5 px-1.5 flex items-center font-medium">
              <Flame className="h-2.5 w-2.5 mr-0.5" />
              {item.spicyLevel.toUpperCase()}
            </div>
          )}

          {/* Signature dish indicator */}
          {item.isSignatureDish && (
            <div className="absolute top-0 right-0 bg-amber-500/90 text-white text-[9px] py-0.5 px-1.5 flex items-center font-medium">
              <Award className="h-2.5 w-2.5 mr-0.5" />
              SIGNATURE
            </div>
          )}
        </div>

        {/* Content */}
        <div className="flex-1 pl-3 sm:pl-4 flex flex-col justify-between min-w-0 relative">
          <div className="min-h-[40px] sm:min-h-[48px] flex flex-col">
            <div className="flex justify-between items-start gap-1 sm:gap-2">
              <h3 className="font-semibold text-sm sm:text-base line-clamp-1 group-hover:text-primary transition-colors">
                {item.name}
              </h3>
            </div>

            <p className="text-xs text-muted-foreground line-clamp-1 sm:line-clamp-2 mt-0.5 flex-grow">
              {item.description || "No description available."}
            </p>
          </div>

          <div className="flex justify-between items-center mt-1 sm:mt-1.5">
            {/* Price and Dietary Indicators */}
            <div className="flex items-center gap-1 sm:gap-2 flex-wrap">
              <Badge className="bg-primary/90 hover:bg-primary text-white font-medium text-xs sm:text-sm px-2 sm:px-2.5 py-0.5 shadow-sm rounded-md">
                {item.price.toFixed(2)} AZN
              </Badge>

              {/* Dietary Indicators - Only show on larger screens */}
              <div className="hidden sm:flex flex-wrap gap-1">
                {item.dietary?.slice(0, 1).map((diet) => (
                  <Badge
                    key={diet}
                    variant="outline"
                    className="text-[10px] px-1.5 py-0 h-5 border-green-300 bg-green-50 text-green-700 font-medium rounded-md"
                  >
                    {diet}
                  </Badge>
                ))}
                {item.dietary && item.dietary.length > 1 && (
                  <span className="text-[10px] text-muted-foreground font-medium">
                    +{item.dietary.length - 1}
                  </span>
                )}
              </div>
            </div>

            {/* Add to Cart Button */}
            <Button
              size="sm"
              variant={item.available ? "default" : "ghost"}
              className={`h-7 w-7 sm:h-8 sm:w-8 p-0 rounded-full ${
                item.available
                  ? "bg-primary hover:bg-primary/90"
                  : "hover:bg-primary/10"
              } shadow-sm`}
              onClick={(e) => {
                e.stopPropagation();
                onAddToCart();
              }}
              disabled={!item.available}
              aria-label={item.available ? "Add to cart" : "Not available"}
            >
              {!item.available ? (
                <AlertCircle className="h-3.5 sm:h-4 w-3.5 sm:w-4 text-muted-foreground" />
              ) : (
                <Plus className="h-3.5 sm:h-4 w-3.5 sm:w-4 text-white" />
              )}
            </Button>
          </div>
        </div>
      </div>
    </Card>
  );
};

// Special Offer Card Component
export const SpecialOfferCard = ({
  item,
  onAddToCart,
  onClick,
}: MenuItemCardProps) => {
  return (
    <Card
      className="overflow-hidden group transition-all duration-300 hover:shadow-lg border border-orange-300 bg-gradient-to-r from-orange-50 to-orange-100/50 relative rounded-xl p-3 sm:p-4"
      onClick={onClick}
    >
      <div className="flex h-20 sm:h-24">
        {/* Image */}
        <div className="w-20 sm:w-24 h-20 sm:h-24 relative cursor-pointer flex-shrink-0 overflow-hidden">
          {item.imageUrl ? (
            <img
              src={item.imageUrl}
              alt={item.name}
              className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
              loading="lazy"
            />
          ) : (
            <div className="w-full h-full bg-muted flex items-center justify-center">
              <Utensils className="h-5 sm:h-6 w-5 sm:w-6 text-muted-foreground" />
            </div>
          )}

          {/* Special Offer Badge */}
          <div className="absolute top-0 left-0">
            <div className="bg-gradient-to-r from-orange-500 to-amber-500 text-white text-[8px] sm:text-[10px] py-1 px-2 sm:px-3 flex items-center justify-center font-medium rounded-md rounded-tl-none shadow-sm">
              <Sparkles className="h-2.5 sm:h-3 w-2.5 sm:w-3 mr-0.5 sm:mr-1" />
              SPECIAL OFFER
            </div>
          </div>

          {/* Spicy indicator */}
          {item.spicyLevel && (
            <div className="absolute bottom-0 left-0 bg-red-500/90 text-white text-[9px] py-0.5 px-2 flex items-center font-medium rounded-md rounded-bl-none shadow-sm">
              <Flame className="h-2.5 w-2.5 mr-0.5" />
              {item.spicyLevel.toUpperCase()}
            </div>
          )}
        </div>

        {/* Content */}
        <div className="flex-1 pl-3 sm:pl-4 flex flex-col justify-between min-w-0">
          <div className="min-h-[40px] sm:min-h-[48px] flex flex-col">
            <div className="flex justify-between items-start gap-1 sm:gap-2">
              <h3 className="font-semibold text-sm sm:text-base line-clamp-1 group-hover:text-orange-600 transition-colors">
                {item.name}
              </h3>
            </div>

            <p className="text-xs text-muted-foreground line-clamp-1 sm:line-clamp-2 mt-0.5 flex-grow">
              {item.promotionDescription ||
                item.description ||
                "No description available."}
            </p>
          </div>

          <div className="flex justify-between items-center mt-1 sm:mt-1.5">
            <div className="flex items-center gap-1 sm:gap-2 flex-wrap">
              {/* Price */}
              <Badge className="bg-orange-500 hover:bg-orange-600 text-white font-medium text-xs sm:text-sm px-2 sm:px-2.5 py-0.5 shadow-sm rounded-md">
                {item.price.toFixed(2)} AZN
              </Badge>

              {/* Discount percentage if available */}
              {item.discountPercentage && item.discountPercentage > 0 && (
                <Badge
                  variant="outline"
                  className="text-[9px] sm:text-[10px] px-1 sm:px-1.5 py-0 h-4 sm:h-5 border-orange-300 bg-orange-50 text-orange-700 font-medium rounded-md"
                >
                  {item.discountPercentage}% OFF
                </Badge>
              )}
            </div>

            {/* Add to Cart Button */}
            <Button
              size="sm"
              variant="default"
              className="h-7 w-7 sm:h-8 sm:w-8 p-0 rounded-full bg-orange-500 hover:bg-orange-600 shadow-sm"
              onClick={(e) => {
                e.stopPropagation();
                onAddToCart();
              }}
              aria-label="Add to cart"
            >
              <Plus className="h-3.5 sm:h-4 w-3.5 sm:w-4 text-white" />
            </Button>
          </div>
        </div>
      </div>
    </Card>
  );
};

// Popular Item Card Component
export const PopularItemCard = ({
  item,
  onAddToCart,
  onClick,
}: MenuItemCardProps) => {
  return (
    <Card
      className="overflow-hidden group transition-all duration-300 hover:shadow-lg border border-red-200 bg-gradient-to-r from-red-50 to-red-100/50 relative rounded-xl p-3 sm:p-4"
      onClick={onClick}
    >
      <div className="flex h-20 sm:h-24">
        {/* Image */}
        <div className="w-20 sm:w-24 h-20 sm:h-24 relative cursor-pointer flex-shrink-0 overflow-hidden">
          {item.imageUrl ? (
            <img
              src={item.imageUrl}
              alt={item.name}
              className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
              loading="lazy"
            />
          ) : (
            <div className="w-full h-full bg-muted flex items-center justify-center">
              <Utensils className="h-5 sm:h-6 w-5 sm:w-6 text-muted-foreground" />
            </div>
          )}

          {/* Popular Badge */}
          <div className="absolute top-0 left-0">
            <div className="bg-gradient-to-r from-red-500 to-rose-500 text-white text-[8px] sm:text-[10px] py-1 px-2 sm:px-3 flex items-center justify-center font-medium rounded-md rounded-tl-none shadow-sm">
              <Heart className="h-2.5 sm:h-3 w-2.5 sm:w-3 mr-0.5 sm:mr-1" />
              POPULAR CHOICE
            </div>
          </div>

          {/* Spicy indicator */}
          {item.spicyLevel && (
            <div className="absolute bottom-0 left-0 bg-red-500/90 text-white text-[9px] py-0.5 px-2 flex items-center font-medium rounded-md rounded-bl-none shadow-sm">
              <Flame className="h-2.5 w-2.5 mr-0.5" />
              {item.spicyLevel.toUpperCase()}
            </div>
          )}
        </div>

        {/* Content */}
        <div className="flex-1 pl-3 sm:pl-4 flex flex-col justify-between min-w-0">
          <div className="min-h-[40px] sm:min-h-[48px] flex flex-col">
            <div className="flex justify-between items-start gap-1 sm:gap-2">
              <h3 className="font-semibold text-sm sm:text-base line-clamp-1 group-hover:text-red-600 transition-colors">
                {item.name}
              </h3>
            </div>

            <p className="text-xs text-muted-foreground line-clamp-1 sm:line-clamp-2 mt-0.5 flex-grow">
              {item.description || "No description available."}
            </p>
          </div>

          <div className="flex justify-between items-center mt-1 sm:mt-1.5">
            <div className="flex items-center gap-1 sm:gap-2 flex-wrap">
              {/* Price */}
              <Badge className="bg-red-500 hover:bg-red-600 text-white font-medium text-xs sm:text-sm px-2 sm:px-2.5 py-0.5 shadow-sm rounded-md">
                {item.price.toFixed(2)} AZN
              </Badge>

              {/* Dietary Indicators - Only show on larger screens */}
              <div className="hidden sm:flex flex-wrap gap-1">
                {item.dietary?.slice(0, 1).map((diet) => (
                  <Badge
                    key={diet}
                    variant="outline"
                    className="text-[10px] px-1.5 py-0 h-5 border-green-300 bg-green-50 text-green-700 font-medium rounded-md"
                  >
                    {diet}
                  </Badge>
                ))}
                {item.dietary && item.dietary.length > 1 && (
                  <span className="text-[10px] text-muted-foreground font-medium">
                    +{item.dietary.length - 1}
                  </span>
                )}
              </div>
            </div>

            {/* Add to Cart Button */}
            <Button
              size="sm"
              variant="default"
              className="h-7 w-7 sm:h-8 sm:w-8 p-0 rounded-full bg-red-500 hover:bg-red-600 shadow-sm"
              onClick={(e) => {
                e.stopPropagation();
                onAddToCart();
              }}
              aria-label="Add to cart"
            >
              <Plus className="h-3.5 sm:h-4 w-3.5 sm:w-4 text-white" />
            </Button>
          </div>
        </div>
      </div>
    </Card>
  );
};

// Chef's Recommendation Card Component
export const RecommendationCard = ({
  item,
  onAddToCart,
  onClick,
}: MenuItemCardProps) => {
  return (
    <Card
      className="overflow-hidden group transition-all duration-300 hover:shadow-lg border border-blue-200 bg-gradient-to-r from-blue-50 to-blue-100/50 relative rounded-xl p-3 sm:p-4"
      onClick={onClick}
    >
      <div className="flex h-20 sm:h-24">
        {/* Image */}
        <div className="w-20 sm:w-24 h-20 sm:h-24 relative cursor-pointer flex-shrink-0 overflow-hidden">
          {item.imageUrl ? (
            <img
              src={item.imageUrl}
              alt={item.name}
              className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
              loading="lazy"
            />
          ) : (
            <div className="w-full h-full bg-muted flex items-center justify-center">
              <Utensils className="h-5 sm:h-6 w-5 sm:w-6 text-muted-foreground" />
            </div>
          )}

          {/* Chef's Recommendation Badge */}
          <div className="absolute top-0 left-0">
            <div className="bg-gradient-to-r from-blue-500 to-indigo-500 text-white text-[8px] sm:text-[10px] py-1 px-2 sm:px-3 flex items-center justify-center font-medium rounded-md rounded-tl-none shadow-sm">
              <Award className="h-2.5 sm:h-3 w-2.5 sm:w-3 mr-0.5 sm:mr-1" />
              CHEF'S RECOMMENDATION
            </div>
          </div>

          {/* Spicy indicator */}
          {item.spicyLevel && (
            <div className="absolute bottom-0 left-0 bg-red-500/90 text-white text-[9px] py-0.5 px-2 flex items-center font-medium rounded-md rounded-bl-none shadow-sm">
              <Flame className="h-2.5 w-2.5 mr-0.5" />
              {item.spicyLevel.toUpperCase()}
            </div>
          )}
        </div>

        {/* Content */}
        <div className="flex-1 pl-3 sm:pl-4 flex flex-col justify-between min-w-0">
          <div className="min-h-[40px] sm:min-h-[48px] flex flex-col">
            <div className="flex justify-between items-start gap-1 sm:gap-2">
              <h3 className="font-semibold text-sm sm:text-base line-clamp-1 group-hover:text-blue-600 transition-colors">
                {item.name}
              </h3>
            </div>

            <p className="text-xs text-muted-foreground line-clamp-1 sm:line-clamp-2 mt-0.5 flex-grow">
              {item.description || "No description available."}
            </p>
          </div>

          <div className="flex justify-between items-center mt-1 sm:mt-1.5">
            <div className="flex items-center gap-1 sm:gap-2 flex-wrap">
              {/* Price */}
              <Badge className="bg-blue-500 hover:bg-blue-600 text-white font-medium text-xs sm:text-sm px-2 sm:px-2.5 py-0.5 shadow-sm rounded-md">
                {item.price.toFixed(2)} AZN
              </Badge>

              {/* Dietary Indicators - Only show on larger screens */}
              <div className="hidden sm:flex flex-wrap gap-1">
                {item.dietary?.slice(0, 1).map((diet) => (
                  <Badge
                    key={diet}
                    variant="outline"
                    className="text-[10px] px-1.5 py-0 h-5 border-green-700 font-medium rounded-md"
                  >
                    {diet}
                  </Badge>
                ))}
                {item.dietary && item.dietary.length > 1 && (
                  <span className="text-[10px] text-muted-foreground font-medium">
                    +{item.dietary.length - 1}
                  </span>
                )}
              </div>
            </div>

            {/* Add to Cart Button */}
            <Button
              size="sm"
              variant="default"
              className="h-7 w-7 sm:h-8 sm:w-8 p-0 rounded-full bg-blue-500 hover:bg-blue-600 shadow-sm"
              onClick={(e) => {
                e.stopPropagation();
                onAddToCart();
              }}
              aria-label="Add to cart"
            >
              <Plus className="h-3.5 sm:h-4 w-3.5 sm:w-4 text-white" />
            </Button>
          </div>
        </div>
      </div>
    </Card>
  );
};

// Limited Time Offer Card Component
export const LimitedTimeOfferCard = ({
  item,
  onAddToCart,
  onClick,
}: MenuItemCardProps) => {
  return (
    <Card
      className="overflow-hidden group transition-all duration-300 hover:shadow-lg border border-purple-300 bg-gradient-to-r from-purple-50 to-purple-100/50 relative rounded-xl p-3 sm:p-4"
      onClick={onClick}
    >
      <div className="flex h-20 sm:h-24">
        {/* Image */}
        <div className="w-20 sm:w-24 h-20 sm:h-24 relative cursor-pointer flex-shrink-0 overflow-hidden">
          {item.imageUrl ? (
            <img
              src={item.imageUrl}
              alt={item.name}
              className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
              loading="lazy"
            />
          ) : (
            <div className="w-full h-full bg-muted flex items-center justify-center">
              <Utensils className="h-5 sm:h-6 w-5 sm:w-6 text-muted-foreground" />
            </div>
          )}

          {/* Limited Time Offer Badge */}
          <div className="absolute top-0 left-0">
            <div className="bg-gradient-to-r from-purple-500 to-fuchsia-500 text-white text-[8px] sm:text-[10px] py-1 px-2 sm:px-3 flex items-center justify-center font-medium rounded-md rounded-tl-none shadow-sm">
              <Timer className="h-2.5 sm:h-3 w-2.5 sm:w-3 mr-0.5 sm:mr-1" />
              LIMITED TIME OFFER
            </div>
          </div>

          {/* Spicy indicator */}
          {item.spicyLevel && (
            <div className="absolute bottom-0 left-0 bg-red-500/90 text-white text-[9px] py-0.5 px-2 flex items-center font-medium rounded-md rounded-bl-none shadow-sm">
              <Flame className="h-2.5 w-2.5 mr-0.5" />
              {item.spicyLevel.toUpperCase()}
            </div>
          )}
        </div>

        {/* Content */}
        <div className="flex-1 pl-3 sm:pl-4 flex flex-col justify-between min-w-0">
          <div className="min-h-[40px] sm:min-h-[48px] flex flex-col">
            <div className="flex justify-between items-start gap-1 sm:gap-2">
              <h3 className="font-semibold text-sm sm:text-base line-clamp-1 group-hover:text-purple-600 transition-colors">
                {item.name}
              </h3>
            </div>

            <p className="text-xs text-muted-foreground line-clamp-1 sm:line-clamp-2 mt-0.5 flex-grow">
              {item.promotionDescription ||
                item.description ||
                "No description available."}
            </p>
          </div>

          <div className="flex justify-between items-center mt-1 sm:mt-1.5">
            <div className="flex items-center gap-1 sm:gap-2 flex-wrap">
              {/* Price */}
              <Badge className="bg-purple-500 hover:bg-purple-600 text-white font-medium text-xs sm:text-sm px-2 sm:px-2.5 py-0.5 shadow-sm rounded-md">
                {item.price.toFixed(2)} AZN
              </Badge>

              {/* Countdown Timer - Only show on larger screens */}
              {item.promotionEndDate && (
                <Badge
                  variant="outline"
                  className="hidden sm:flex text-[9px] sm:text-[10px] px-1 sm:px-1.5 py-0 h-4 sm:h-5 border-purple-300 bg-purple-50 text-purple-700 font-medium items-center rounded-md"
                >
                  <Clock className="h-2 sm:h-2.5 w-2 sm:w-2.5 mr-0.5" />
                  {(() => {
                    try {
                      // Handle both Date objects and Firestore timestamps
                      const date =
                        typeof item.promotionEndDate === "object" &&
                        item.promotionEndDate !== null &&
                        "toDate" in item.promotionEndDate &&
                        typeof item.promotionEndDate.toDate === "function"
                          ? item.promotionEndDate.toDate()
                          : new Date(item.promotionEndDate);

                      return date.toLocaleDateString();
                    } catch {
                      return "Coming soon";
                    }
                  })()}
                </Badge>
              )}
            </div>

            {/* Add to Cart Button */}
            <Button
              size="sm"
              variant="default"
              className="h-7 w-7 sm:h-8 sm:w-8 p-0 rounded-full bg-purple-500 hover:bg-purple-600 shadow-sm"
              onClick={(e) => {
                e.stopPropagation();
                onAddToCart();
              }}
              aria-label="Add to cart"
            >
              <Plus className="h-3.5 sm:h-4 w-3.5 sm:w-4 text-white" />
            </Button>
          </div>
        </div>
      </div>
    </Card>
  );
};
