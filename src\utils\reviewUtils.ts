import { Review } from "@/types/restaurant";

/**
 * Calculates the average rating and review count from an array of reviews
 * Handles deduplication of reviews by user (keeping only the latest review per user)
 * 
 * @param reviews Array of reviews
 * @returns Object containing the calculated average rating and review count
 */
export const calculateRatingStats = (reviews: Review[]) => {
  if (!reviews || reviews.length === 0) {
    return { 
      averageRating: 0, 
      reviewCount: 0 
    };
  }
  
  // Deduplicate reviews by user (keep only the latest)
  const userReviewMap = new Map<string, Review>();
  reviews.forEach((review) => {
    const existing = userReviewMap.get(review.userId);
    if (
      !existing ||
      (review.createdAt &&
        existing.createdAt &&
        review.createdAt > existing.createdAt)
    ) {
      userReviewMap.set(review.userId, review);
    }
  });
  
  const uniqueReviews = Array.from(userReviewMap.values());
  const sum = uniqueReviews.reduce((acc, review) => acc + review.rating, 0);
  const average =
    uniqueReviews.length > 0
      ? Number((sum / uniqueReviews.length).toFixed(1))
      : 0;
  const count = uniqueReviews.length;
  
  return { 
    averageRating: average, 
    reviewCount: count 
  };
};

/**
 * Updates the local rating state based on a new review being added, edited, or deleted
 * 
 * @param reviews Current array of reviews
 * @param action Action being performed ('add', 'edit', or 'delete')
 * @param reviewData The review data being added, edited, or deleted
 * @returns Object containing the updated average rating and review count
 */
export const updateLocalRating = (
  reviews: Review[],
  action: 'add' | 'edit' | 'delete',
  reviewData: Review | { id: string }
) => {
  let updatedReviews: Review[];
  
  switch (action) {
    case 'add':
      updatedReviews = [...reviews, reviewData as Review];
      break;
    case 'edit':
      updatedReviews = reviews.map(review => 
        review.id === reviewData.id 
          ? { ...review, ...(reviewData as Review) } 
          : review
      );
      break;
    case 'delete':
      updatedReviews = reviews.filter(review => review.id !== reviewData.id);
      break;
    default:
      updatedReviews = reviews;
  }
  
  return calculateRatingStats(updatedReviews);
};
