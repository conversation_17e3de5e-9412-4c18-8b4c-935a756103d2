import { Reward } from "@/types/rewards";
import { RewardFilters } from "@/components/rewards/RewardFilters";

export const applyRewardFilters = (
  rewards: Array<Reward & { restaurantInfo?: { name: string; imageUrl?: string } }>,
  filters: RewardFilters
): Array<Reward & { restaurantInfo?: { name: string; imageUrl?: string } }> => {
  return rewards.filter((reward) => {
    // Type filter
    if (filters.type && filters.type.length > 0) {
      if (!filters.type.includes(reward.type)) {
        return false;
      }
    }

    // Points range filter
    if (filters.pointsRange) {
      if (
        reward.pointsCost < filters.pointsRange.min ||
        reward.pointsCost > filters.pointsRange.max
      ) {
        return false;
      }
    }

    // Global rewards filter
    if (filters.isGlobal !== undefined) {
      if (filters.isGlobal && !reward.isGlobal) {
        return false;
      }
    }

    // Restaurant rewards filter
    if (filters.hasRestaurant !== undefined) {
      if (filters.hasRestaurant && !reward.restaurantId) {
        return false;
      }
    }

    // Expiring soon filter (within 7 days)
    if (filters.isExpiring !== undefined) {
      if (filters.isExpiring) {
        if (!reward.expiresAt) {
          return false;
        }
        
        const now = new Date();
        const expirationDate = reward.expiresAt.toDate();
        const sevenDaysFromNow = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);
        
        if (expirationDate > sevenDaysFromNow) {
          return false;
        }
      }
    }

    return true;
  });
};

export const getFilteredRewardsCount = (
  rewards: Array<Reward & { restaurantInfo?: { name: string; imageUrl?: string } }>,
  filters: RewardFilters
): number => {
  return applyRewardFilters(rewards, filters).length;
};

export const getRewardTypeStats = (
  rewards: Array<Reward & { restaurantInfo?: { name: string; imageUrl?: string } }>
): Record<string, number> => {
  const stats: Record<string, number> = {};
  
  rewards.forEach((reward) => {
    if (reward.type) {
      stats[reward.type] = (stats[reward.type] || 0) + 1;
    }
  });
  
  return stats;
};

export const getPointsRangeStats = (
  rewards: Array<Reward & { restaurantInfo?: { name: string; imageUrl?: string } }>
): {
  min: number;
  max: number;
  average: number;
  ranges: {
    low: number;
    medium: number;
    high: number;
  };
} => {
  if (rewards.length === 0) {
    return {
      min: 0,
      max: 0,
      average: 0,
      ranges: { low: 0, medium: 0, high: 0 },
    };
  }

  const points = rewards.map((reward) => reward.pointsCost);
  const min = Math.min(...points);
  const max = Math.max(...points);
  const average = Math.round(points.reduce((sum, p) => sum + p, 0) / points.length);

  const lowThreshold = Math.floor(max * 0.33);
  const mediumThreshold = Math.floor(max * 0.66);

  const ranges = {
    low: points.filter((p) => p <= lowThreshold).length,
    medium: points.filter((p) => p > lowThreshold && p <= mediumThreshold).length,
    high: points.filter((p) => p > mediumThreshold).length,
  };

  return { min, max, average, ranges };
};
