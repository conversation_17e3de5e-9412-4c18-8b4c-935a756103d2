import { <PERSON>, <PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON>, CardContent } from "@/components/ui/card";
import { CollapsibleCard } from "@/components/ui/collapsible-card";
import { Restaurant, WorkingHours } from "@/types/restaurant";
import { Info, Clock, Armchair, CheckCircle2, XCircle } from "lucide-react";
import { Badge } from "@/components/ui/badge"; // Assuming you have a Badge component

interface RestaurantInfoProps {
  restaurant: Restaurant;
}

const getDayDisplayName = (day: WorkingHours["day"]): string => {
  const formattedDay = day.charAt(0).toUpperCase() + day.slice(1);
  // Optional: Handle specific cases if needed (e.g., "Thu" instead of "Thursday")
  return formattedDay;
};

// Helper component for displaying info items consistently
const InfoRow = ({
  label,
  children,
}: {
  label: string;
  children: React.ReactNode;
}) => (
  <div className="flex flex-col w-full bg-muted/50 rounded-lg p-2 sm:p-3">
    <span className="font-semibold text-xs sm:text-sm text-muted-foreground mb-1">
      {label}:
    </span>
    <span className="text-xs sm:text-sm break-words w-full overflow-hidden">
      {children || (
        <span className="italic text-muted-foreground/80">Not specified</span>
      )}
    </span>
  </div>
);

export const RestaurantInfo = ({ restaurant }: RestaurantInfoProps) => {
  const hasWorkingHours =
    restaurant.workingHours && restaurant.workingHours.length > 0;

  return (
    <div className="space-y-6">
      <Card className="w-full">
        <CardHeader className="bg-muted/30 p-3 sm:p-4 md:p-6">
          <CardTitle className="flex items-center gap-1.5 sm:gap-2 text-base sm:text-lg font-semibold truncate">
            <Info size={18} className="text-primary flex-shrink-0" />
            <span className="truncate">Basic Information</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="p-3 sm:p-4 md:p-6">
          <div className="flex flex-col gap-2 sm:gap-3 w-full">
            <InfoRow label="Name">{restaurant.restaurantName}</InfoRow>
            <InfoRow label="Description">{restaurant.description}</InfoRow>
            <InfoRow label="Address">{restaurant.address}</InfoRow>
            <InfoRow label="Phone">{restaurant.phone}</InfoRow>
          </div>
        </CardContent>
      </Card>

      {restaurant.seating && (
        <CollapsibleCard
          title="Seating Information"
          icon={<Armchair size={20} className="text-primary" />}
        >
          <div className="grid gap-4">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div className="p-4 bg-muted/50 rounded-lg flex flex-col items-center justify-center text-center">
                <div className="font-semibold mb-2 text-sm">Indoor Seating</div>
                <div className="flex items-center gap-2">
                  {restaurant.seating.indoor ? (
                    <CheckCircle2 size={18} className="text-green-600" />
                  ) : (
                    <XCircle size={18} className="text-red-600" />
                  )}
                  <span
                    className={`text-sm font-medium ${
                      restaurant.seating.indoor
                        ? "text-green-700"
                        : "text-red-700"
                    }`}
                  >
                    {restaurant.seating.indoor ? "Available" : "Not Available"}
                  </span>
                </div>
              </div>
              <div className="p-4 bg-muted/50 rounded-lg flex flex-col items-center justify-center text-center">
                <div className="font-semibold mb-2 text-sm">
                  Outdoor Seating
                </div>
                <div className="flex items-center gap-2">
                  {restaurant.seating.outdoor ? (
                    <CheckCircle2 size={18} className="text-green-600" />
                  ) : (
                    <XCircle size={18} className="text-red-600" />
                  )}
                  <span
                    className={`text-sm font-medium ${
                      restaurant.seating.outdoor
                        ? "text-green-700"
                        : "text-red-700"
                    }`}
                  >
                    {restaurant.seating.outdoor ? "Available" : "Not Available"}
                  </span>
                </div>
              </div>
            </div>
            <div className="p-4 bg-muted/50 rounded-lg text-center">
              <div className="font-semibold mb-1 text-sm">Total Capacity</div>
              <div className="text-xl font-bold">
                {restaurant.seating.totalCapacity || "N/A"}
                {restaurant.seating.totalCapacity ? " seats" : ""}
              </div>
            </div>
          </div>
        </CollapsibleCard>
      )}

      <CollapsibleCard
        title="Working Hours"
        icon={<Clock size={20} className="text-primary" />}
        defaultOpen={true}
      >
        {hasWorkingHours ? (
          <div className="overflow-hidden rounded-md border">
            <div className="overflow-x-auto">
              <table className="w-full text-xs sm:text-sm">
                <thead className="bg-muted/50">
                  <tr>
                    <th className="py-1.5 sm:py-2 px-2 sm:px-4 text-left font-semibold text-muted-foreground w-1/3">
                      Day
                    </th>
                    <th className="py-1.5 sm:py-2 px-2 sm:px-4 text-left font-semibold text-muted-foreground">
                      Hours
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {restaurant.workingHours.map((hours) => (
                    <tr key={hours.day} className="border-b last:border-b-0">
                      <td className="py-2 sm:py-3 px-2 sm:px-4 font-medium">
                        {getDayDisplayName(hours.day)}
                      </td>
                      <td className="py-2 sm:py-3 px-2 sm:px-4">
                        {!hours.isOpen ? (
                          <Badge variant="destructive" className="font-normal text-xs">
                            Closed
                          </Badge>
                        ) : hours.openTime === "00:00" &&
                          hours.closeTime === "00:00" ? (
                          <Badge
                            variant="secondary"
                            className="font-normal bg-blue-100 text-blue-800 text-xs"
                          >
                            Open 24h
                          </Badge>
                        ) : (
                          <span className="text-green-700 font-medium text-xs sm:text-sm">{`${hours.openTime} - ${hours.closeTime}`}</span>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
            <div className="p-2 sm:p-3 bg-muted/50 flex items-center justify-between border-t text-xs">
              <span className="font-semibold text-muted-foreground">
                Status:
              </span>
              <Badge
                variant={restaurant.autoUpdateStatus ? "default" : "secondary"}
                className={`font-medium text-xs ${
                  restaurant.autoUpdateStatus
                    ? "bg-green-100 text-green-800"
                    : "bg-yellow-100 text-yellow-800"
                }`}
              >
                {restaurant.autoUpdateStatus ? "Auto" : "Manual"}
              </Badge>
            </div>
          </div>
        ) : (
          <p className="text-sm text-muted-foreground text-center p-4">
            No working hours specified.
          </p>
        )}
      </CollapsibleCard>
    </div>
  );
};
