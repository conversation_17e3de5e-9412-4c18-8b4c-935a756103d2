import { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { SimpleDatePicker } from "@/components/ui/simple-date-picker";
import { TimePicker } from "@/components/ui/time-picker";
import {
  addDays,
  addHours,
  format,
  isAfter,
  isBefore,
  startOfDay,
} from "date-fns";

import {
  ShoppingCart,
  Plus,
  Minus,
  X,
  AlertCircle,
  InfoIcon,
  ArrowLeft,
  Loader2,
  Calendar,
  Clock,
} from "lucide-react";
import { toast } from "sonner";
import { useAuth } from "@/providers/AuthProvider";
import {
  doc,
  setDoc,
  serverTimestamp,
  getDoc,
  collection,
  query,
  where,
  getDocs,
  orderBy,
  limit,
  onSnapshot,
  Timestamp,
  FieldValue,
} from "firebase/firestore";
import { firestore } from "@/config/firebase";
import { v4 as uuidv4 } from "uuid";
import { notificationService } from "@/services/NotificationService";
import { orderSchedulingService } from "@/services/OrderSchedulingService";
import { RewardCodeInput } from "@/components/rewards/RewardCodeInput";
import { RewardRedemption } from "@/types/rewards";
import { rewardsService } from "@/services/RewardsService";

import { MenuItem } from "@/types/restaurant";
import { Table, Order } from "@/types/dashboard";

interface CartItem {
  item: MenuItem;
  quantity: number;
  notes?: string;
}

export const CartPage = () => {
  const { username } = useParams<{ username: string }>();
  const [restaurantId, setRestaurantId] = useState<string>("");
  const navigate = useNavigate();
  const { user, userRole } = useAuth();

  const [cart, setCart] = useState<CartItem[]>([]);
  const [orderNotes, setOrderNotes] = useState("");
  const [isConfirmingOrder, setIsConfirmingOrder] = useState(false);
  const [selectedTableId, setSelectedTableId] = useState<string>("");
  const [tables, setTables] = useState<Table[]>([]);
  const [isLoadingTables, setIsLoadingTables] = useState(true);
  const [hasActiveOrder, setHasActiveOrder] = useState(false);
  const [activeTableId, setActiveTableId] = useState<string | null>(null);
  const [restaurantName, setRestaurantName] = useState("");

  // Scheduling state
  const [isScheduled, setIsScheduled] = useState(false);
  const [scheduledDate, setScheduledDate] = useState<Date>(() => {
    // Default to tomorrow at noon
    return addHours(addDays(startOfDay(new Date()), 1), 12);
  });
  // State for tracking schedule availability check
  const [, setIsCheckingScheduleAvailability] = useState(false);
  const [scheduleAvailabilityMessage, setScheduleAvailabilityMessage] =
    useState<string | null>(null);

  // Reward state
  const [appliedReward, setAppliedReward] = useState<{
    discountAmount: number;
    redemption: RewardRedemption;
    affectedItemId?: string;
  } | null>(null);

  // Calculate cart total
  const cartTotal = cart.reduce((total, cartItem) => {
    return total + cartItem.item.price * cartItem.quantity;
  }, 0);

  // Calculate final total with discount
  const finalTotal = cartTotal - (appliedReward?.discountAmount || 0);

  // Calculate cart total

  // Fetch restaurant ID from username
  useEffect(() => {
    if (!username) {
      navigate("/");
      return;
    }

    const fetchRestaurantData = async () => {
      try {
        // Query restaurants collection to find the restaurant with the given username
        const restaurantsRef = collection(firestore, "restaurants");
        const q = query(restaurantsRef, where("username", "==", username));
        const querySnapshot = await getDocs(q);

        if (querySnapshot.empty) {
          console.error("No restaurant found with username:", username);
          navigate("/");
          return;
        }

        const restaurantData = querySnapshot.docs[0].data();
        const id = querySnapshot.docs[0].id;
        setRestaurantId(id);
        setRestaurantName(restaurantData.name || "Restaurant");

        // Load cart from localStorage
        const storedCart = localStorage.getItem(`cart_${id}`);
        if (storedCart) {
          setCart(JSON.parse(storedCart));
        }
      } catch (error) {
        console.error("Error fetching restaurant data:", error);
        navigate("/");
      }
    };

    fetchRestaurantData();
  }, [username, navigate]);

  // Save cart to localStorage whenever it changes
  useEffect(() => {
    if (restaurantId && cart.length > 0) {
      localStorage.setItem(`cart_${restaurantId}`, JSON.stringify(cart));
    } else if (restaurantId) {
      localStorage.removeItem(`cart_${restaurantId}`);
    }
  }, [cart, restaurantId]);

  // Fetch tables
  useEffect(() => {
    if (!restaurantId) {
      setIsLoadingTables(false);
      return;
    }
    setIsLoadingTables(true);
    const restaurantRef = doc(firestore, "restaurants", restaurantId);
    const settingsRef = collection(restaurantRef, "settings");
    const configDocRef = doc(settingsRef, "config");
    const ordersRef = collection(restaurantRef, "orders");

    const fetchAndSetTables = async () => {
      try {
        const configDoc = await getDoc(configDocRef);
        let allTables: Table[] = [];
        if (configDoc.exists()) {
          allTables = configDoc.data()?.tables || [];
        } else {
          console.warn(
            "No config document found for restaurant:",
            restaurantId
          );
        }

        const activeOrdersQuery = query(
          ordersRef,
          where("status", "in", ["pending", "preparing", "ready"])
        );
        const activeOrdersSnapshot = await getDocs(activeOrdersQuery);
        const occupiedTableIds = new Set(
          activeOrdersSnapshot.docs
            .map((doc) => doc.data().tableId)
            .filter(Boolean)
        );

        const currentUserActiveOrder = activeOrdersSnapshot.docs.find(
          (doc) => doc.data().userId === user?.uid
        );
        const currentUserOccupiedTableId =
          currentUserActiveOrder?.data().tableId;

        const availableTables = allTables.filter(
          (table) =>
            !occupiedTableIds.has(table.id) ||
            table.id === currentUserOccupiedTableId
        );

        setTables(availableTables);

        if (
          selectedTableId &&
          !availableTables.some((table) => table.id === selectedTableId)
        ) {
          if (
            currentUserOccupiedTableId &&
            allTables.some((t) => t.id === currentUserOccupiedTableId)
          ) {
            setSelectedTableId(currentUserOccupiedTableId);
          } else {
            setSelectedTableId("");
          }
        }
      } catch (error) {
        console.error("Error fetching tables:", error);
        toast.error("Failed to fetch available tables");
        setTables([]);
        setSelectedTableId("");
      } finally {
        setIsLoadingTables(false);
      }
    };

    fetchAndSetTables();

    const unsubscribeConfig = onSnapshot(
      configDocRef,
      (docSnapshot) => {
        if (docSnapshot.exists()) {
          fetchAndSetTables();
        }
      },
      (error) => {
        console.error("Error listening to config changes:", error);
      }
    );

    const unsubscribeOrders = onSnapshot(
      query(
        ordersRef,
        where("status", "in", ["pending", "preparing", "ready"])
      ),
      () => {
        fetchAndSetTables();
      },
      (error) => {
        console.error("Error listening to order changes:", error);
      }
    );

    return () => {
      unsubscribeConfig();
      unsubscribeOrders();
    };
  }, [restaurantId, user?.uid, selectedTableId]);

  // Check for active orders
  useEffect(() => {
    if (!user || !restaurantId) return;

    const clientRef = doc(firestore, "clients", user.uid);
    const ordersRef = collection(clientRef, "orders");
    const q = query(
      ordersRef,
      where("status", "in", ["pending", "preparing", "ready"]),
      where("restaurantId", "==", restaurantId),
      orderBy("orderDate", "desc"),
      limit(1)
    );

    const unsubscribe = onSnapshot(
      q,
      (snapshot) => {
        if (!snapshot.empty) {
          const activeOrder = snapshot.docs[0].data();
          setHasActiveOrder(true);
          setActiveTableId(activeOrder.tableId || null);
          if (
            activeOrder.tableId &&
            tables.some((t) => t.id === activeOrder.tableId)
          ) {
            setSelectedTableId(activeOrder.tableId);
          } else if (activeOrder.tableId) {
            setSelectedTableId(activeOrder.tableId);
          }
        } else {
          setHasActiveOrder(false);
          setActiveTableId(null);
          if (
            selectedTableId &&
            !tables.some((t) => t.id === selectedTableId)
          ) {
            setSelectedTableId("");
          }
        }
      },
      (error) => {
        console.error("Error listening to client orders:", error);
        setHasActiveOrder(false);
        setActiveTableId(null);
      }
    );

    return () => unsubscribe();
  }, [user, restaurantId, tables, selectedTableId]);

  const updateCartItemQuantity = (itemId: string, newQuantity: number) => {
    if (newQuantity < 1) {
      removeFromCart(itemId);
      return;
    }

    setCart((prevCart) =>
      prevCart.map((cartItem) => {
        // Check both item.id and item.itemId to ensure we find the correct item
        const cartItemId = cartItem.item.itemId || cartItem.item.id;
        return cartItemId === itemId
          ? { ...cartItem, quantity: newQuantity }
          : cartItem;
      })
    );
  };

  const removeFromCart = (itemId: string) => {
    setCart((prevCart) =>
      prevCart.filter((cartItem) => {
        // Check both item.id and item.itemId to ensure we find the correct item
        const cartItemId = cartItem.item.itemId || cartItem.item.id;
        return cartItemId !== itemId;
      })
    );
  };

  const clearCart = () => {
    setCart([]);
    setOrderNotes("");
    setAppliedReward(null);
    if (!hasActiveOrder) {
      setSelectedTableId("");
    }
    if (restaurantId) {
      localStorage.removeItem(`cart_${restaurantId}`);
    }
  };

  // Reward handlers
  const handleRewardApplied = (
    discountAmount: number,
    redemption: RewardRedemption,
    affectedItemId?: string
  ) => {
    setAppliedReward({ discountAmount, redemption, affectedItemId });
  };

  const handleRewardRemoved = () => {
    setAppliedReward(null);
  };

  const handlePlaceOrder = async () => {
    if (!user) {
      toast.error("Please log in to place an order");
      return;
    }

    if (userRole === "restaurant") {
      toast.error(
        "Restaurant accounts cannot place orders. Please use a client account."
      );
      return;
    }

    if (!restaurantId) {
      toast.error("Restaurant information is missing");
      return;
    }

    if (cart.length === 0) {
      toast.error("Your cart is empty");
      return;
    }

    if (!selectedTableId) {
      toast.error("Please select a table");
      return;
    }

    // Check if the customer already has an active order at another restaurant
    const orderStatusCheck = await checkCustomerOrderStatus();
    if (!orderStatusCheck.canOrder) {
      toast.error(
        orderStatusCheck.message || "Cannot place order at this time"
      );
      return;
    }

    // Check if the selected table is still available
    const isTableAvailable = await checkTableAvailability(selectedTableId);
    if (!isTableAvailable) {
      return; // Error toast is shown in the checkTableAvailability function
    }

    // Check scheduling availability if this is a scheduled order
    if (isScheduled) {
      const isSchedulingAvailable = await checkSchedulingAvailability();
      if (!isSchedulingAvailable) {
        toast.error(
          scheduleAvailabilityMessage ||
            "This time slot is not available for scheduling"
        );
        return;
      }
    }

    setIsConfirmingOrder(true);

    try {
      const orderId = hasActiveOrder
        ? `${user.uid}_${restaurantId}` // Use consistent ID for active orders
        : uuidv4(); // Generate new ID for new orders

      const orderItems = cart.map((cartItem) => {
        // Ensure we have a valid ID for each item (never undefined)
        const itemId =
          cartItem.item.itemId ||
          cartItem.item.id ||
          `item-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;

        return {
          id: itemId,
          name: cartItem.item.name,
          price: cartItem.item.price,
          quantity: cartItem.quantity,
          totalPrice: cartItem.item.price * cartItem.quantity,
        };
      });

      // Get the table name from the selected table ID
      const selectedTable = tables.find(
        (table) => table.id === selectedTableId
      );
      const tableName = selectedTable
        ? selectedTable.name
        : `Table ${selectedTableId.substring(0, 4)}`;

      // Create order data with scheduling information if needed
      const orderData: {
        id: string;
        userId: string;
        restaurantId: string;
        tableId: string;
        tableName: string;
        items: {
          id: string;
          name: string;
          price: number;
          quantity: number;
          totalPrice: number;
        }[];
        totalAmount: number;
        totalPrice: number;
        originalAmount: number;
        discountAmount: number;
        appliedRewardCode?: string;
        affectedItemId?: string;
        couponUsed: boolean;
        status: string;
        notes: string;
        orderDate: FieldValue;
        isScheduled?: boolean;
        scheduledFor?: Timestamp;
        reminderSent?: boolean;
      } = {
        id: orderId,
        userId: user.uid,
        restaurantId: restaurantId,
        tableId: selectedTableId,
        tableName: tableName,
        items: orderItems,
        totalAmount: finalTotal,
        totalPrice: finalTotal,
        originalAmount: cartTotal,
        discountAmount: appliedReward?.discountAmount || 0,
        ...(appliedReward?.redemption.code && {
          appliedRewardCode: appliedReward.redemption.code,
        }),
        ...(appliedReward?.affectedItemId && {
          affectedItemId: appliedReward.affectedItemId,
        }),
        couponUsed: !!appliedReward?.redemption.code,
        status: hasActiveOrder ? "updated" : "pending",
        notes: orderNotes,
        orderDate: serverTimestamp(),
      };

      // Add scheduling information if this is a scheduled order
      if (isScheduled) {
        orderData.isScheduled = true;
        orderData.scheduledFor = Timestamp.fromDate(scheduledDate);
        orderData.reminderSent = false;
      }

      // Validate order data to ensure no undefined values
      const validateOrderData = (data: Record<string, unknown>): boolean => {
        // Check for undefined values in the top level
        for (const [key, value] of Object.entries(data)) {
          if (value === undefined) {
            console.error(
              `Order data contains undefined value for key: ${key}`
            );
            return false;
          }

          // Check for undefined values in nested arrays
          if (Array.isArray(value)) {
            for (const item of value) {
              if (typeof item === "object" && item !== null) {
                for (const [itemKey, itemValue] of Object.entries(item)) {
                  if (itemValue === undefined) {
                    console.error(
                      `Order item contains undefined value for key: ${itemKey}`
                    );
                    return false;
                  }
                }
              }
            }
          }
        }
        return true;
      };

      // Validate order data before saving
      if (!validateOrderData(orderData)) {
        throw new Error(
          "Order data contains undefined values which Firestore doesn't accept"
        );
      }

      // Save to restaurant's orders collection
      await setDoc(
        doc(firestore, "restaurants", restaurantId, "orders", orderId),
        orderData,
        { merge: true } // Use merge to update existing order if it exists
      );

      // Save to client's orders collection
      await setDoc(
        doc(firestore, "clients", user.uid, "orders", orderId),
        orderData,
        { merge: true }
      );

      // Mark reward as used if one was applied
      if (appliedReward?.redemption.code) {
        try {
          await rewardsService.markRewardAsUsed(
            appliedReward.redemption.code,
            orderId
          );
        } catch (error) {
          console.error("Error marking reward as used:", error);
          // Don't fail the order if reward marking fails
        }
      }

      // Points will be awarded when the restaurant accepts the order

      // Show appropriate success message
      if (isScheduled) {
        const formattedDate = format(scheduledDate, "PPP 'at' p");
        toast.success(`Order scheduled for ${formattedDate} successfully!`);
      } else {
        toast.success(
          hasActiveOrder
            ? "Items added to your existing order!"
            : "Order placed successfully!"
        );
      }

      // Send notification to the user
      if (hasActiveOrder) {
        notificationService.notify({
          title: "Order Updated",
          message: `Items added to your order at ${restaurantName}.`,
          playSound: true,
        });
      } else if (isScheduled) {
        notificationService.notify({
          title: "Order Scheduled",
          message: `Your order at ${restaurantName} has been scheduled for ${format(
            scheduledDate,
            "PPP 'at' p"
          )}.`,
          playSound: true,
        });
      }

      clearCart();
      setIsConfirmingOrder(false);
      navigate(`/restaurants/${username}`);
    } catch (error) {
      console.error("Error placing order:", error);

      // Provide more specific error messages based on the error type
      if (error instanceof Error) {
        if (error.message.includes("undefined values")) {
          toast.error(
            "Order contains invalid data. Please try refreshing the page and placing the order again."
          );
        } else if (error.message.includes("permission-denied")) {
          toast.error(
            "You don't have permission to place this order. Please log in again."
          );
        } else if (error.message.includes("network")) {
          toast.error(
            "Network error. Please check your internet connection and try again."
          );
        } else {
          toast.error(`Failed to place order: ${error.message}`);
        }
      } else {
        toast.error("Failed to place order. Please try again.");
      }

      setIsConfirmingOrder(false);
    }
  };

  const checkCustomerOrderStatus = async () => {
    if (!user)
      return { canOrder: false, message: "Please log in to place an order" };

    try {
      const clientRef = doc(firestore, "clients", user.uid);
      const ordersRef = collection(clientRef, "orders");
      const activeOrdersQuery = query(
        ordersRef,
        where("status", "in", ["pending", "preparing", "ready"]),
        limit(1)
      );

      const activeOrders = await getDocs(activeOrdersQuery);
      if (!activeOrders.empty) {
        const activeOrder = activeOrders.docs[0].data() as Order;
        if (activeOrder.restaurantId !== restaurantId) {
          return {
            canOrder: false,
            message:
              "You have an active order at another restaurant. Please complete it first.",
          };
        }
      }

      return { canOrder: true };
    } catch (error) {
      console.error("Error checking customer order status:", error);
      return {
        canOrder: false,
        message: "Failed to verify order status. Please try again.",
      };
    }
  };

  const checkTableAvailability = async (tableId: string) => {
    if (!restaurantId || !user) return false;

    try {
      const restaurantRef = doc(firestore, "restaurants", restaurantId);
      const ordersRef = collection(restaurantRef, "orders");
      const activeOrdersQuery = query(
        ordersRef,
        where("tableId", "==", tableId),
        where("status", "in", ["pending", "preparing", "ready"])
      );

      const activeOrders = await getDocs(activeOrdersQuery);

      if (!activeOrders.empty) {
        const order = activeOrders.docs[0].data() as Order;
        if (order.userId === user.uid) return true; // Allow if it's the user's own active order

        toast.error("This table is currently occupied.");
        return false;
      }

      return true;
    } catch (error) {
      console.error("Error checking table availability:", error);
      toast.error("Failed to check table availability");
      return false;
    }
  };

  // Check if scheduling is available for the selected time
  const checkSchedulingAvailability = async () => {
    if (!restaurantId || !isScheduled) return true;

    setIsCheckingScheduleAvailability(true);
    setScheduleAvailabilityMessage(null);

    try {
      // Validate the scheduled time
      const now = new Date();
      const minScheduleTime = addHours(now, 1); // Minimum 1 hour in advance
      const maxScheduleTime = addDays(now, 7); // Maximum 7 days in advance

      if (isBefore(scheduledDate, minScheduleTime)) {
        setScheduleAvailabilityMessage(
          "Orders must be scheduled at least 1 hour in advance."
        );
        return false;
      }

      if (isAfter(scheduledDate, maxScheduleTime)) {
        setScheduleAvailabilityMessage(
          "Orders cannot be scheduled more than 7 days in advance."
        );
        return false;
      }

      // Check with the scheduling service
      const result = await orderSchedulingService.isSchedulingAvailable(
        restaurantId,
        scheduledDate
      );

      if (!result.available) {
        setScheduleAvailabilityMessage(
          result.message || "This time slot is not available for scheduling."
        );
        return false;
      }

      setScheduleAvailabilityMessage(null);
      return true;
    } catch (error) {
      console.error("Error checking scheduling availability:", error);
      setScheduleAvailabilityMessage(
        "Failed to check scheduling availability. Please try again."
      );
      return false;
    } finally {
      setIsCheckingScheduleAvailability(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto py-6 px-4 sm:px-6">
      <div className="flex items-center mb-6">
        <Button
          variant="ghost"
          size="icon"
          onClick={() => navigate(`/restaurants/${username}`)}
          className="mr-2"
          aria-label="Back to menu"
        >
          <ArrowLeft className="h-5 w-5" />
        </Button>
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold">Your Order</h1>
          <p className="text-sm text-muted-foreground">
            {restaurantName
              ? `at ${restaurantName}`
              : "Review your items and select a table"}
          </p>
        </div>
      </div>

      {cart.length > 0 ? (
        <>
          <div className="bg-card rounded-lg border shadow-sm mb-6">
            <div className="p-4 sm:p-6">
              <h2 className="text-xl font-semibold mb-4">Order Items</h2>
              <div className="space-y-4">
                {cart.map((cartItem) => {
                  // Generate a unique key using both id and itemId to ensure uniqueness
                  const itemKey =
                    cartItem.item.itemId ||
                    cartItem.item.id ||
                    `item-${Math.random()}`;

                  return (
                    <div
                      key={itemKey}
                      className="flex items-start justify-between gap-4 border-b pb-4 last:border-b-0"
                    >
                      <div className="flex items-start gap-3 flex-1 min-w-0">
                        {cartItem.item.imageUrl && (
                          <img
                            src={cartItem.item.imageUrl}
                            alt={cartItem.item.name}
                            className="w-14 h-14 sm:w-16 sm:h-16 object-cover rounded-md flex-shrink-0"
                            onError={(e) => {
                              e.currentTarget.src = "/placeholder-food.jpg";
                            }}
                            loading="lazy"
                          />
                        )}
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2">
                            <h4 className="font-medium text-sm sm:text-base truncate">
                              {cartItem.item.name}
                            </h4>
                            {appliedReward?.affectedItemId ===
                              (cartItem.item.itemId || cartItem.item.id) && (
                              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                {appliedReward?.redemption.reward?.type ===
                                "freeItem"
                                  ? "1 Free"
                                  : "Discount Applied"}
                              </span>
                            )}
                          </div>
                          <p className="text-xs sm:text-sm text-muted-foreground">
                            {cartItem.item.price.toFixed(2)} AZN
                            {appliedReward?.affectedItemId ===
                              (cartItem.item.itemId || cartItem.item.id) &&
                              appliedReward?.redemption.reward?.type ===
                                "discount" && (
                                <span className="ml-2 text-green-600 font-medium">
                                  (1 item: -
                                  {appliedReward?.discountAmount.toFixed(2)}{" "}
                                  AZN)
                                </span>
                              )}
                          </p>
                          <div className="flex items-center gap-2 mt-2">
                            <Button
                              variant="outline"
                              size="icon"
                              className="h-7 w-7"
                              onClick={() => {
                                const itemId =
                                  cartItem.item.itemId || cartItem.item.id;
                                if (itemId) {
                                  updateCartItemQuantity(
                                    itemId,
                                    cartItem.quantity - 1
                                  );
                                } else {
                                  console.error("Item ID is missing", cartItem);
                                  toast.error("Could not update item quantity");
                                }
                              }}
                            >
                              <Minus className="h-3.5 w-3.5" />
                            </Button>
                            <span className="w-6 text-center font-medium text-sm">
                              {cartItem.quantity}
                            </span>
                            <Button
                              variant="outline"
                              size="icon"
                              className="h-7 w-7"
                              onClick={() => {
                                const itemId =
                                  cartItem.item.itemId || cartItem.item.id;
                                if (itemId) {
                                  updateCartItemQuantity(
                                    itemId,
                                    cartItem.quantity + 1
                                  );
                                } else {
                                  console.error("Item ID is missing", cartItem);
                                  toast.error("Could not update item quantity");
                                }
                              }}
                            >
                              <Plus className="h-3.5 w-3.5" />
                            </Button>
                          </div>
                        </div>
                      </div>
                      <div className="flex flex-col items-end gap-2 flex-shrink-0">
                        <p className="font-semibold text-sm sm:text-base">
                          {(cartItem.item.price * cartItem.quantity).toFixed(2)}{" "}
                          AZN
                        </p>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-7 w-7 text-red-500 hover:text-red-600 hover:bg-red-50/50"
                          onClick={() => {
                            const itemId =
                              cartItem.item.itemId || cartItem.item.id;
                            if (itemId) {
                              removeFromCart(itemId);
                            } else {
                              console.error("Item ID is missing", cartItem);
                              toast.error("Could not remove item");
                            }
                          }}
                          aria-label={`Remove ${cartItem.item.name}`}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>

          <div className="bg-card rounded-lg border shadow-sm mb-6">
            <div className="p-4 sm:p-6">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="table-select" className="text-sm font-medium">
                    Select Table
                  </Label>
                  {isLoadingTables ? (
                    <div className="flex items-center justify-center p-4 text-muted-foreground text-sm">
                      <Loader2 className="animate-spin h-4 w-4 mr-2" />
                      Loading tables...
                    </div>
                  ) : hasActiveOrder ? (
                    <div className="space-y-2">
                      <div className="rounded-md bg-blue-50 p-3 border border-blue-200">
                        <div className="flex items-center">
                          <InfoIcon className="h-4 w-4 text-blue-500 mr-2 flex-shrink-0" />
                          <span className="text-blue-800 text-sm">
                            Active order at Table{" "}
                            <span className="font-semibold">
                              {tables.find((t) => t.id === activeTableId)
                                ?.name ||
                                activeTableId ||
                                "..."}
                            </span>
                          </span>
                        </div>
                      </div>
                      <select
                        value={selectedTableId}
                        disabled={true}
                        className="w-full h-10 px-3 py-2 text-sm rounded-md border border-input bg-background ring-offset-background focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 opacity-70 cursor-not-allowed"
                      >
                        <option value="" disabled>
                          Select a table
                        </option>
                        {tables
                          .concat(
                            activeTableId &&
                              !tables.some((t) => t.id === activeTableId)
                              ? [
                                  {
                                    id: activeTableId,
                                    name: `Table ${activeTableId.substring(
                                      0,
                                      4
                                    )} (Yours)`,
                                    capacity: 0,
                                  },
                                ]
                              : []
                          )
                          .map((table) => (
                            <option key={table.id} value={table.id}>
                              {table.name}{" "}
                              {table.capacity > 0
                                ? `(Capacity: ${table.capacity})`
                                : ""}
                            </option>
                          ))}
                      </select>
                    </div>
                  ) : tables.length > 0 ? (
                    <select
                      value={selectedTableId}
                      onChange={(e) => setSelectedTableId(e.target.value)}
                      className="w-full h-10 px-3 py-2 text-sm rounded-md border border-input bg-background ring-offset-background focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
                    >
                      <option value="" disabled>
                        Select a table
                      </option>
                      {tables.map((table) => (
                        <option key={table.id} value={table.id}>
                          {table.name} (Capacity: {table.capacity})
                        </option>
                      ))}
                    </select>
                  ) : (
                    <div className="rounded-md bg-yellow-50 p-3 border border-yellow-200 text-yellow-800 text-sm">
                      <div className="flex items-center">
                        <AlertCircle className="h-4 w-4 mr-2 flex-shrink-0" />
                        <span>No tables available currently.</span>
                      </div>
                    </div>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="order-notes" className="text-sm font-medium">
                    Order Notes{" "}
                    <span className="text-muted-foreground">(Optional)</span>
                  </Label>
                  <Textarea
                    id="order-notes"
                    value={orderNotes}
                    onChange={(e) => setOrderNotes(e.target.value)}
                    placeholder="Special requests? (e.g., no onions)"
                    className="text-sm min-h-[60px]"
                  />
                </div>

                {/* Order Scheduling Section */}
                <div className="space-y-4 pt-2">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label
                        htmlFor="schedule-toggle"
                        className="text-sm font-medium"
                      >
                        Schedule for Later
                      </Label>
                      <p className="text-xs text-muted-foreground">
                        Choose a future date and time for your order
                      </p>
                    </div>
                    <Switch
                      id="schedule-toggle"
                      checked={isScheduled}
                      onCheckedChange={setIsScheduled}
                      disabled={hasActiveOrder}
                    />
                  </div>

                  {isScheduled && (
                    <div className="space-y-4 border rounded-md p-4 bg-muted/30">
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label className="flex items-center gap-2">
                            <Calendar className="h-4 w-4 text-muted-foreground" />
                            <span>Date</span>
                          </Label>
                          <SimpleDatePicker
                            value={scheduledDate}
                            onChange={(date) => setScheduledDate(date)}
                            isDateDisabled={(date) => {
                              const now = new Date();
                              const minDate = startOfDay(now);
                              const maxDate = addDays(now, 7);
                              return (
                                isBefore(date, minDate) ||
                                isAfter(date, maxDate)
                              );
                            }}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label className="flex items-center gap-2">
                            <Clock className="h-4 w-4 text-muted-foreground" />
                            <span>Time</span>
                          </Label>
                          <TimePicker
                            value={scheduledDate}
                            onChange={(date) => setScheduledDate(date)}
                            step={15}
                            minTime="08:00"
                            maxTime="22:00"
                          />
                        </div>
                      </div>

                      {scheduleAvailabilityMessage && (
                        <div className="rounded-md bg-yellow-50 p-3 border border-yellow-200 text-yellow-800 text-sm">
                          <div className="flex items-center">
                            <AlertCircle className="h-4 w-4 mr-2 flex-shrink-0" />
                            <span>{scheduleAvailabilityMessage}</span>
                          </div>
                        </div>
                      )}

                      <div className="text-xs text-muted-foreground">
                        <p>• Orders can be scheduled up to 7 days in advance</p>
                        <p>• Minimum scheduling time is 1 hour from now</p>
                        <p>
                          • You'll receive a reminder 30 minutes before your
                          order is processed
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Reward Code Input */}
          {user && (
            <div className="mb-6">
              <RewardCodeInput
                userId={user.uid}
                orderTotal={cartTotal}
                cartItems={cart}
                onRewardApplied={handleRewardApplied}
                onRewardRemoved={handleRewardRemoved}
                appliedReward={appliedReward}
              />
            </div>
          )}

          <div className="bg-card rounded-lg border shadow-sm">
            <div className="p-4 sm:p-6">
              <div className="space-y-4">
                {/* Order Summary */}
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Subtotal:</span>
                    <span>{cartTotal.toFixed(2)} AZN</span>
                  </div>
                  {appliedReward && (
                    <div className="flex justify-between text-sm text-green-600">
                      <span>
                        Discount ({appliedReward.redemption.reward?.name}):
                      </span>
                      <span>
                        -{appliedReward.discountAmount.toFixed(2)} AZN
                      </span>
                    </div>
                  )}
                  <div className="border-t pt-2">
                    <div className="flex justify-between font-semibold text-lg sm:text-xl">
                      <span>Total:</span>
                      <span>{finalTotal.toFixed(2)} AZN</span>
                    </div>
                  </div>
                </div>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                  <Button
                    variant="outline"
                    onClick={() => {
                      clearCart();
                      navigate(`/restaurants/${username}`);
                    }}
                    className="h-12"
                    size="lg"
                  >
                    Cancel Order
                  </Button>
                  <Button
                    onClick={handlePlaceOrder}
                    disabled={
                      isConfirmingOrder ||
                      !selectedTableId ||
                      (tables.length === 0 && !hasActiveOrder)
                    }
                    className="h-12"
                    size="lg"
                  >
                    {isConfirmingOrder ? (
                      <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                    ) : (
                      <ShoppingCart className="mr-2 h-5 w-5" />
                    )}
                    {isConfirmingOrder
                      ? "Processing..."
                      : hasActiveOrder
                      ? "Add to Order"
                      : isScheduled
                      ? "Schedule Order"
                      : "Confirm Order"}
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </>
      ) : (
        <div className="bg-card rounded-lg border shadow-sm p-8 text-center">
          <div className="flex flex-col items-center justify-center py-12">
            <ShoppingCart className="h-16 w-16 text-muted-foreground/50 mb-4" />
            <p className="font-medium text-lg text-muted-foreground">
              Your cart is empty
            </p>
            <p className="text-sm text-muted-foreground/80 mt-1 max-w-md">
              Add items from the menu to get started.
            </p>
            <Button
              variant="default"
              size="lg"
              className="mt-6 px-8"
              onClick={() => navigate(`/restaurants/${username}`)}
            >
              Browse Menu
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};
