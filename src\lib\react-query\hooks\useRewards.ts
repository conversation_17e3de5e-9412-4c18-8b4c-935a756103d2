import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Reward } from "@/types/rewards";
import { rewardsService } from "@/services/RewardsService";
import { CACHE_TIME, STALE_TIME } from "../index";
import { toast } from "sonner";

/**
 * Hook to fetch rewards for a restaurant
 */
export function useRestaurantRewards(restaurantId: string | undefined) {
  return useQuery({
    queryKey: ["rewards", "restaurant", restaurantId],
    queryFn: async () => {
      if (!restaurantId) {
        throw new Error("Restaurant ID is required");
      }
      return rewardsService.getRestaurantRewards(restaurantId);
    },
    enabled: !!restaurantId,
    staleTime: STALE_TIME,
    gcTime: CACHE_TIME,
  });
}

/**
 * Hook to fetch global rewards
 */
export function useGlobalRewards() {
  return useQuery({
    queryKey: ["rewards", "global"],
    queryFn: async () => rewardsService.getGlobalRewards(),
    staleTime: STALE_TIME,
    gcTime: CACHE_TIME,
  });
}

/**
 * Hook to fetch featured rewards
 */
export function useFeaturedRewards(limit = 5) {
  return useQuery({
    queryKey: ["rewards", "featured", limit],
    queryFn: async () => rewardsService.getFeaturedRewards(limit),
    staleTime: STALE_TIME,
    gcTime: CACHE_TIME,
  });
}

/**
 * Hook to fetch rewards available to a user
 */
export function useAvailableRewards(userId: string | undefined) {
  return useQuery({
    queryKey: ["rewards", "available", userId],
    queryFn: async () => {
      if (!userId) {
        throw new Error("User ID is required");
      }
      return rewardsService.getAvailableRewardsForUser(userId);
    },
    enabled: !!userId,
    staleTime: STALE_TIME,
    gcTime: CACHE_TIME,
  });
}

/**
 * Hook to fetch available rewards with restaurant information for a user
 */
export function useAvailableRewardsWithRestaurantInfo(
  userId: string | undefined
) {
  return useQuery({
    queryKey: ["rewards", "available-with-info", userId],
    queryFn: async () => {
      if (!userId) {
        throw new Error("User ID is required");
      }
      return rewardsService.getAvailableRewardsWithRestaurantInfo(userId);
    },
    enabled: !!userId,
    staleTime: STALE_TIME,
    gcTime: CACHE_TIME,
  });
}

/**
 * Hook to fetch a user's redeemed rewards
 */
export function useRedeemedRewards(userId: string | undefined) {
  return useQuery({
    queryKey: ["rewards", "redeemed", userId],
    queryFn: async () => {
      if (!userId) {
        throw new Error("User ID is required");
      }
      return rewardsService.getUserRedeemedRewards(userId);
    },
    enabled: !!userId,
    staleTime: STALE_TIME,
    gcTime: CACHE_TIME,
  });
}

/**
 * Hook to redeem a reward
 */
export function useRedeemReward() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      userId,
      rewardId,
    }: {
      userId: string;
      rewardId: string;
    }) => {
      const redemptionCode = await rewardsService.redeemReward(
        userId,
        rewardId
      );
      if (!redemptionCode) {
        throw new Error("Failed to redeem reward");
      }
      return redemptionCode;
    },
    onSuccess: (_, { userId }) => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({
        queryKey: ["rewards", "redeemed", userId],
      });
      queryClient.invalidateQueries({
        queryKey: ["rewards", "available", userId],
      });

      // Invalidate loyalty status and transactions
      queryClient.invalidateQueries({
        queryKey: ["loyalty", "status", userId],
      });
      queryClient.invalidateQueries({
        queryKey: ["loyalty", "transactions", userId],
      });

      toast.success("Reward redeemed successfully!");
    },
    onError: (error) => {
      console.error("Error redeeming reward:", error);
      toast.error(
        error instanceof Error ? error.message : "Failed to redeem reward"
      );
    },
  });
}

/**
 * Hook to create a reward
 */
export function useCreateReward() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      reward,
      userId,
    }: {
      reward: Omit<Reward, "id" | "createdAt" | "updatedAt" | "createdBy">;
      userId: string;
    }) => {
      return rewardsService.createReward(reward, userId);
    },
    onSuccess: (_, { reward }) => {
      // Invalidate relevant queries
      if (reward.restaurantId) {
        queryClient.invalidateQueries({
          queryKey: ["rewards", "restaurant", reward.restaurantId],
        });
      }

      if (reward.isGlobal) {
        queryClient.invalidateQueries({ queryKey: ["rewards", "global"] });
      }

      if (reward.isFeatured) {
        queryClient.invalidateQueries({ queryKey: ["rewards", "featured"] });
      }

      toast.success("Reward created successfully!");
    },
    onError: (error) => {
      console.error("Error creating reward:", error);
      toast.error(
        error instanceof Error ? error.message : "Failed to create reward"
      );
    },
  });
}

/**
 * Hook to update a reward
 */
export function useUpdateReward() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      rewardId,
      data,
      restaurantId,
    }: {
      rewardId: string;
      data: Partial<Reward>;
      restaurantId?: string;
    }) => {
      const success = await rewardsService.updateReward(
        rewardId,
        data,
        restaurantId
      );
      if (!success) {
        throw new Error("Failed to update reward");
      }
      return success;
    },
    onSuccess: (_, { data, restaurantId }) => {
      // Invalidate relevant queries
      if (restaurantId || data.restaurantId) {
        queryClient.invalidateQueries({
          queryKey: [
            "rewards",
            "restaurant",
            restaurantId || data.restaurantId,
          ],
        });
      }

      queryClient.invalidateQueries({ queryKey: ["rewards", "global"] });
      queryClient.invalidateQueries({ queryKey: ["rewards", "featured"] });

      toast.success("Reward updated successfully!");
    },
    onError: (error) => {
      console.error("Error updating reward:", error);
      toast.error(
        error instanceof Error ? error.message : "Failed to update reward"
      );
    },
  });
}

/**
 * Hook to delete a reward
 */
export function useDeleteReward() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      rewardId,
      restaurantId,
    }: {
      rewardId: string;
      restaurantId?: string;
    }) => {
      const success = await rewardsService.deleteReward(rewardId, restaurantId);
      if (!success) {
        throw new Error("Failed to delete reward");
      }
      return success;
    },
    onSuccess: (_, { restaurantId }) => {
      // Invalidate relevant queries
      if (restaurantId) {
        queryClient.invalidateQueries({
          queryKey: ["rewards", "restaurant", restaurantId],
        });
      }

      queryClient.invalidateQueries({ queryKey: ["rewards", "global"] });
      queryClient.invalidateQueries({ queryKey: ["rewards", "featured"] });

      toast.success("Reward deleted successfully!");
    },
    onError: (error) => {
      console.error("Error deleting reward:", error);
      toast.error(
        error instanceof Error ? error.message : "Failed to delete reward"
      );
    },
  });
}

/**
 * Hook to validate a redemption code
 */
export function useValidateRedemptionCode() {
  return useMutation({
    mutationFn: async ({
      userId,
      redemptionCode,
      orderTotal,
    }: {
      userId: string;
      redemptionCode: string;
      orderTotal: number;
    }) => {
      return rewardsService.validateRedemptionCode(
        userId,
        redemptionCode,
        orderTotal
      );
    },
    onError: (error) => {
      console.error("Error validating redemption code:", error);
      toast.error("Failed to validate redemption code");
    },
  });
}

/**
 * Hook to mark a reward as used
 */
export function useMarkRewardAsUsed() {
  return useMutation({
    mutationFn: async ({
      redemptionCode,
      orderId,
    }: {
      redemptionCode: string;
      orderId: string;
    }) => {
      const success = await rewardsService.markRewardAsUsed(
        redemptionCode,
        orderId
      );
      if (!success) {
        throw new Error("Failed to mark reward as used");
      }
      return success;
    },
    onError: (error) => {
      console.error("Error marking reward as used:", error);
    },
  });
}
