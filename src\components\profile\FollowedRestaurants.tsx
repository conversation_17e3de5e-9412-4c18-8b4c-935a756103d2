import { useState, useEffect } from "react";
import { firestore } from "@/config/firebase";
import {
  collection,
  query,
  getDocs,
  orderBy,
  limit,
  DocumentData,
  QueryDocumentSnapshot,
} from "firebase/firestore";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { FollowedRestaurant } from "@/types/followers";
import { Link } from "react-router-dom";
import { ExternalLink } from "lucide-react";
import { Loading } from "@/components/ui/loading";

interface FollowedRestaurantsProps {
  userId: string;
}

export const FollowedRestaurants = ({ userId }: FollowedRestaurantsProps) => {
  const [followedRestaurants, setFollowedRestaurants] = useState<FollowedRestaurant[]>([]);
  const [loading, setLoading] = useState(true);
  const [showAll, setShowAll] = useState(false);
  const [totalCount, setTotalCount] = useState(0);

  useEffect(() => {
    const fetchFollowedRestaurants = async () => {
      try {
        setLoading(true);
        const followingRef = collection(firestore, "clients", userId, "following");
        const followingQuery = query(
          followingRef,
          orderBy("createdAt", "desc"),
          limit(showAll ? 100 : 5)
        );

        const querySnapshot = await getDocs(followingQuery);

        // Get total count
        const countQuery = query(followingRef);
        const countSnapshot = await getDocs(countQuery);
        setTotalCount(countSnapshot.size);

        const restaurants: FollowedRestaurant[] = [];

        querySnapshot.forEach((doc: QueryDocumentSnapshot<DocumentData>) => {
          const data = doc.data() as Omit<FollowedRestaurant, "restaurantId">;
          restaurants.push({
            restaurantId: doc.id,
            ...data,
          } as FollowedRestaurant);
        });

        setFollowedRestaurants(restaurants);
      } catch (error) {
        console.error("Error fetching followed restaurants:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchFollowedRestaurants();
  }, [userId, showAll]);

  const toggleShowAll = () => {
    setShowAll(!showAll);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-xl font-semibold">Followed Restaurants</CardTitle>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="flex justify-center py-8">
            <Loading type="default" />
          </div>
        ) : followedRestaurants.length === 0 ? (
          <div className="text-center py-6 text-muted-foreground">
            <p>You haven't followed any restaurants yet.</p>
            <Link to="/restaurants" className="text-primary hover:underline mt-2 inline-block">
              Discover restaurants
            </Link>
          </div>
        ) : (
          <>
            <div className="grid gap-4">
              {followedRestaurants.map((restaurant) => (
                <div
                  key={restaurant.restaurantId}
                  className="flex items-center justify-between p-3 rounded-lg border border-border/50 hover:border-primary/30 transition-colors"
                >
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 rounded-full overflow-hidden bg-muted flex-shrink-0">
                      {restaurant.imageUrl ? (
                        <img
                          src={restaurant.imageUrl}
                          alt={restaurant.restaurantName}
                          className="w-full h-full object-cover"
                          onError={(e) => {
                            e.currentTarget.src = "/placeholder-restaurant.jpg";
                          }}
                        />
                      ) : (
                        <div className="w-full h-full bg-muted flex items-center justify-center text-muted-foreground text-xs">
                          No Img
                        </div>
                      )}
                    </div>
                    <div>
                      <h4 className="font-medium">{restaurant.restaurantName}</h4>
                      <p className="text-sm text-muted-foreground">
                        {(() => {
                          try {
                            if (restaurant.createdAt instanceof Date) {
                              return restaurant.createdAt.toLocaleDateString();
                            } else if (restaurant.createdAt && typeof restaurant.createdAt.toDate === 'function') {
                              return restaurant.createdAt.toDate().toLocaleDateString();
                            } else if (restaurant.createdAt) {
                              return new Date((restaurant.createdAt as unknown) as string | number).toLocaleDateString();
                            }
                            return 'Unknown date';
                          } catch (error) {
                            console.error('Error formatting date:', error);
                            return 'Unknown date';
                          }
                        })()}
                      </p>
                    </div>
                  </div>
                  <Link to={`/restaurants/${restaurant.restaurantUsername || restaurant.restaurantId}`}>
                    <Button variant="ghost" size="sm">
                      <ExternalLink className="h-4 w-4 mr-1" />
                      Visit
                    </Button>
                  </Link>
                </div>
              ))}
            </div>

            {totalCount > 5 && (
              <div className="mt-4 text-center">
                <Button
                  variant="outline"
                  onClick={toggleShowAll}
                  className="w-full"
                >
                  {showAll ? "Show Less" : `Show All (${totalCount})`}
                </Button>
              </div>
            )}
          </>
        )}
      </CardContent>
    </Card>
  );
};
