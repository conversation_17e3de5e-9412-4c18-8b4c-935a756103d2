export function HowItWorks() {
  const steps = [
    {
      id: 1,
      title: "Create Profile",
      description:
        "Tell us about your food preferences and dietary requirements",
      icon: "👤",
    },
    {
      id: 2,
      title: "Get Recommendations",
      description:
        "Our AI analyzes your preferences to suggest perfect restaurants",
      icon: "🎯",
    },
    {
      id: 3,
      title: "Make Reservation",
      description: "Book your table with just a few clicks",
      icon: "📅",
    },
  ];

  return (
    <section className="py-20 bg-white">
      <div className="mx-auto px-4">
        <h2 className="text-4xl font-bold text-center mb-12">How It Works</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {steps.map((step) => (
            <div key={step.id} className="text-center border p-4 rounded-xl">
              <div className="relative">
                <span className="text-5xl mb-6 block">{step.icon}</span>
                <span className="absolute top-0 w-8 h-8 rounded-full bg-orange-500 text-white flex items-center justify-center">
                  {step.id}
                </span>
              </div>
              <h3 className="text-xl font-semibold mb-4">{step.title}</h3>
              <p className="text-gray-600">{step.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
