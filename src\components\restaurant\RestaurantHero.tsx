import { Link } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Restaurant, Review } from "@/types/restaurant";
import { FollowButton } from "./FollowButton";
import { FollowerCount } from "./FollowerCount";
import { useAuth } from "@/providers/AuthProvider";
import { MapPin, Clock, Utensils, Share2, Star, QrCode } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { useMemo } from "react";
import { calculateRatingStats } from "@/utils/reviewUtils";

import { getRestaurantShareUrl, generateHashtags } from "@/utils/shareUtils";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { ShareableCard } from "@/components/ui/shareable-card";

interface RestaurantHeroProps {
  restaurant: Restaurant;
  scrollToMenu: () => void;
  reviews?: Review[];
}

export const RestaurantHero = ({
  restaurant,
  scrollToMenu,
  reviews = [],
}: RestaurantHeroProps) => {
  const { user } = useAuth();

  // Get today's working hours
  const today = new Date()
    .toLocaleDateString("en-US", { weekday: "long" })
    .toLowerCase();
  const todayHours = restaurant.workingHours?.find((h) => h.day === today);

  // Calculate total number of cuisines
  const totalCuisines = restaurant.cuisines?.length || 0;

  // Calculate local rating from reviews if available
  const { localRating, localReviewCount } = useMemo(() => {
    if (!reviews || reviews.length === 0) {
      return {
        localRating: restaurant.rating,
        localReviewCount: restaurant.reviewCount || 0,
      };
    }

    // Use the utility function to calculate rating stats
    const { averageRating, reviewCount } = calculateRatingStats(reviews);
    return { localRating: averageRating, localReviewCount: reviewCount };
  }, [reviews, restaurant.rating, restaurant.reviewCount]);

  // Use local values if available, otherwise fall back to restaurant data
  const displayRating =
    localRating !== undefined ? localRating : restaurant.rating;
  const reviewCount =
    localReviewCount !== undefined
      ? localReviewCount
      : restaurant.reviewCount || 0;

  return (
    <div className="w-full rounded-lg sm:rounded-xl overflow-hidden mb-4 sm:mb-6 shadow-md border bg-card">
      {/* Cover Photo */}
      <div className="relative w-full h-[25vh] sm:h-[30vh] overflow-hidden">
        <img
          src={restaurant.imageUrl || "/placeholder-restaurant.jpg"}
          alt={`${restaurant.restaurantName} cover image`}
          className="w-full h-full object-cover object-center"
          onError={(e) => {
            e.currentTarget.onerror = null;
            e.currentTarget.src = "/placeholder-restaurant.jpg";
          }}
          loading="lazy"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent"></div>
      </div>

      {/* Profile Section - Instagram Style */}
      <div className="relative px-4 sm:px-6 md:px-8 pb-4 sm:pb-6">
        {/* Profile Image */}
        <div className="relative -mt-16 sm:-mt-20 mb-4 flex justify-between items-end">
          <div className="relative z-10">
            <div className="w-24 h-24 sm:w-32 sm:h-32 rounded-full overflow-hidden border-4 border-background shadow-md">
              <img
                src={restaurant.imageUrl || "/placeholder-restaurant.jpg"}
                alt={restaurant.restaurantName}
                className="w-full h-full object-cover"
                onError={(e) => {
                  e.currentTarget.src = "/placeholder-restaurant.jpg";
                }}
              />
            </div>
          </div>

          {/* Follow Button */}
          {user && (
            <div className="flex items-center gap-2">
              <FollowButton
                restaurantId={restaurant.id}
                restaurantName={restaurant.restaurantName}
                restaurantUsername={restaurant.username}
                variant="default"
                size="sm"
                className="h-9 px-4 text-sm font-medium"
              />
            </div>
          )}
        </div>

        {/* Restaurant Info */}
        <div className="space-y-4">
          {/* Name and Status */}
          <div>
            <div className="flex items-center gap-2 flex-wrap">
              <h1 className="text-2xl sm:text-3xl font-bold">
                {restaurant.restaurantName}
              </h1>
              <Badge
                variant={restaurant.isOpen ? "default" : "destructive"}
                className="text-xs"
              >
                {restaurant.isOpen ? "Open Now" : "Closed"}
              </Badge>
            </div>

            <div className="flex items-center gap-1.5 text-muted-foreground text-sm mt-1">
              <MapPin size={14} className="flex-shrink-0" />
              <span className="truncate">{restaurant.address}</span>
            </div>
          </div>

          {/* Stats Row */}
          <div className="grid grid-cols-4 gap-2 py-3 border-y">
            <div className="flex flex-col items-center justify-center">
              <div className="flex items-center gap-1">
                <span className="text-lg font-bold">
                  {displayRating.toFixed(1)}
                </span>
                <Star className="h-4 w-4 text-yellow-400 fill-yellow-400" />
              </div>
              <span className="text-xs text-muted-foreground">Rating</span>
            </div>
            <div className="flex flex-col items-center justify-center">
              <span className="text-lg font-bold">{reviewCount}</span>
              <span className="text-xs text-muted-foreground">Reviews</span>
            </div>
            <div className="flex flex-col items-center justify-center">
              <FollowerCount
                restaurantId={restaurant.id}
                className="text-lg font-bold"
                showLabel={false}
                showIcon={false}
                isStatic={true}
              />
              <span className="text-xs text-muted-foreground">Followers</span>
            </div>
            <div className="flex flex-col items-center justify-center">
              <span className="text-lg font-bold">{totalCuisines}</span>
              <span className="text-xs text-muted-foreground">Cuisines</span>
            </div>
          </div>

          {/* Description */}
          <div>
            <p className="text-sm text-muted-foreground line-clamp-2">
              {restaurant.description || "No description available."}
            </p>
          </div>

          {/* Tags and Hours */}
          <div className="space-y-2">
            {/* Cuisines */}
            <div className="flex flex-wrap gap-1.5">
              {restaurant.cuisines?.slice(0, 3).map((cuisine, index) => (
                <Badge
                  key={index}
                  variant="secondary"
                  className="text-xs capitalize"
                >
                  {cuisine}
                </Badge>
              ))}
              {totalCuisines > 3 && (
                <Badge variant="outline" className="text-xs">
                  +{totalCuisines - 3} more
                </Badge>
              )}
            </div>

            {/* Today's Hours */}
            {todayHours && todayHours.isOpen && (
              <div className="flex items-center gap-1.5 text-xs text-muted-foreground">
                <Clock size={14} />
                <span>
                  Today: {todayHours.openTime} - {todayHours.closeTime}
                </span>
              </div>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex flex-wrap items-center gap-2 sm:gap-3 pt-2">
            <Button
              variant="default"
              onClick={scrollToMenu}
              className="h-9 px-4 text-sm font-medium"
              size="sm"
            >
              <Utensils className="mr-1.5 h-4 w-4" />
              View Menu
            </Button>
            <Button
              variant="outline"
              asChild
              className="h-9 px-4 text-sm font-medium"
              size="sm"
            >
              <Link to={`/restaurants/${restaurant.username}/reservations`}>
                <Clock className="mr-1.5 h-4 w-4" />
                Make Reservation
              </Link>
            </Button>

            <Button
              variant="outline"
              asChild
              className="h-9 px-4 text-sm font-medium"
              size="sm"
            >
              <Link to={`/restaurants/${restaurant.username}/menu`}>
                <QrCode className="mr-1.5 h-4 w-4" />
                QR Menu
              </Link>
            </Button>

            <Dialog>
              <DialogTrigger asChild>
                <Button
                  variant="outline"
                  className="h-9 px-4 text-sm font-medium"
                  size="sm"
                >
                  <Share2 className="mr-1.5 h-4 w-4" />
                  Share
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-md">
                <DialogHeader className="mb-4">
                  <DialogTitle className="text-xl">
                    Share Restaurant
                  </DialogTitle>
                  <DialogDescription>
                    Share {restaurant.restaurantName} with your friends and
                    family
                  </DialogDescription>
                </DialogHeader>

                <ShareableCard
                  title={restaurant.restaurantName}
                  description={
                    restaurant.description ||
                    `${restaurant.restaurantName} - ${restaurant.cuisines?.join(
                      ", "
                    )}`
                  }
                  imageUrl={restaurant.imageUrl}
                  url={getRestaurantShareUrl(restaurant.username)}
                  hashtags={generateHashtags(
                    ["Qonai", "Food", "Restaurant"],
                    restaurant.cuisines
                  )}
                >
                  <div className="flex items-center gap-1.5 text-sm text-muted-foreground mt-2">
                    <MapPin size={14} className="flex-shrink-0" />
                    <span className="truncate">{restaurant.address}</span>
                  </div>

                  <div className="flex flex-wrap gap-1.5 mt-2">
                    {restaurant.cuisines?.slice(0, 3).map((cuisine, index) => (
                      <Badge
                        key={index}
                        variant="secondary"
                        className="text-xs capitalize"
                      >
                        {cuisine}
                      </Badge>
                    ))}
                  </div>
                </ShareableCard>
              </DialogContent>
            </Dialog>
          </div>
        </div>
      </div>
    </div>
  );
};
