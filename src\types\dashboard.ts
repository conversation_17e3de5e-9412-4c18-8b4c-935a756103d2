import { Timestamp } from "firebase/firestore";

export interface OrderItem {
  id?: string;
  itemId?: string;
  name: string;
  quantity: number;
  price: number;
  notes?: string;
}

export interface ClientDetails {
  username: string;
  firstName: string;
  lastName: string;
  phone: string;
  email?: string;
}

export interface MenuItem {
  itemId: string;
  name: string;
  description: string;
  price: number;
  category: string;
  imageUrl?: string;
  available?: boolean;
}

export interface Table {
  id: string;
  capacity: number;
  name: string;
}

export interface TableAnalytics {
  totalReservations: number;
  pendingCount: number;
  confirmedCount: number;
  cancelledCount: number;
  noShowCount: number;
  completedCount: number;
  utilization: number;
}

export interface RestaurantSettings {
  tables: Table[];
}

export interface Reservation {
  id: string;
  userId: string;
  restaurantId: string;
  restaurantName: string;
  tableId: string;
  date: string;
  arrivalTime: string;
  departureTime?: string;
  partySize: number;
  status:
    | "pending"
    | "confirmed"
    | "cancelled"
    | "no-show"
    | "active"
    | "completed";
  createdAt: Date;
  arrivedAt?: Date;
  duration?: number;
  customerName?: string;
  customerPhone?: string;
  notes?: string;
}

export interface Order {
  orderId: string;
  userId: string;
  userEmail?: string;
  userFirstName?: string;
  userLastName?: string;
  restaurantId: string;
  restaurantName: string;
  items: OrderItem[];
  totalPrice: number;
  status: "pending" | "preparing" | "ready" | "completed" | "cancelled";
  orderDate: Timestamp;
  deliveryAddress?: string;
  notes?: string;
  receiptUrl?: string;
  autoAcceptedAt?: Timestamp;
  tableId?: string;
  tableName?: string;
  updatedAt?: Timestamp;
  createdAt?: Timestamp;
  // Scheduling fields
  isScheduled?: boolean;
  scheduledFor?: Timestamp;
  reminderSent?: boolean;
  // Coupon/Discount fields
  originalAmount?: number;
  discountAmount?: number;
  appliedCouponCode?: string;
  appliedRewardCode?: string;
  affectedItemId?: string; // ID of the specific item affected by discount/reward
  couponUsed?: boolean;
}

export interface ReservationTimer {
  reservationId: string;
  startTime: Date;
  elapsedTime: number;
}
