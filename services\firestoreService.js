/**
 * Firestore service for database operations
 */
const { db, collections } = require("../config/firebase");
const crypto = require("crypto");

/**
 * Create a new subscriber
 * @param {Object} subscriberData - Subscriber data
 * @returns {Promise<string>} - Subscriber ID
 */
const createSubscriber = async (subscriberData) => {
  try {
    const { email, name, preferences = {} } = subscriberData;

    // Check if subscriber already exists
    const existingSubscriber = await getSubscriberByEmail(email);

    if (existingSubscriber) {
      // If subscriber exists but is inactive, reactivate
      if (!existingSubscriber.active) {
        await collections.subscribers.doc(existingSubscriber.id).update({
          active: true,
          updatedAt: new Date(),
        });
        return existingSubscriber.id;
      }
      return existingSubscriber.id;
    }

    // Generate unsubscribe token
    const unsubscribeToken = crypto.randomBytes(32).toString("hex");

    // Create new subscriber
    const subscriberRef = await collections.subscribers.add({
      email,
      name: name || "",
      preferences,
      unsubscribeToken,
      active: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    return subscriberRef.id;
  } catch (error) {
    console.error("Error creating subscriber:", error);
    throw error;
  }
};

/**
 * Get subscriber by email
 * @param {string} email - Subscriber email
 * @returns {Promise<Object|null>} - Subscriber data or null
 */
const getSubscriberByEmail = async (email) => {
  try {
    const snapshot = await collections.subscribers
      .where("email", "==", email)
      .limit(1)
      .get();

    if (snapshot.empty) {
      return null;
    }

    const doc = snapshot.docs[0];
    return {
      id: doc.id,
      ...doc.data(),
    };
  } catch (error) {
    console.error("Error getting subscriber by email:", error);
    throw error;
  }
};

/**
 * Get subscriber by unsubscribe token
 * @param {string} token - Unsubscribe token
 * @returns {Promise<Object|null>} - Subscriber data or null
 */
const getSubscriberByToken = async (token) => {
  try {
    const snapshot = await collections.subscribers
      .where("unsubscribeToken", "==", token)
      .limit(1)
      .get();

    if (snapshot.empty) {
      return null;
    }

    const doc = snapshot.docs[0];
    return {
      id: doc.id,
      ...doc.data(),
    };
  } catch (error) {
    console.error("Error getting subscriber by token:", error);
    throw error;
  }
};

/**
 * Unsubscribe a subscriber
 * @param {string} token - Unsubscribe token
 * @returns {Promise<boolean>} - Success status
 */
const unsubscribeByToken = async (token) => {
  try {
    const subscriber = await getSubscriberByToken(token);

    if (!subscriber) {
      return false;
    }

    await collections.subscribers.doc(subscriber.id).update({
      active: false,
      updatedAt: new Date(),
    });

    return true;
  } catch (error) {
    console.error("Error unsubscribing:", error);
    throw error;
  }
};

/**
 * Create a pending subscription with OTP
 * @param {string} email - Subscriber email
 * @param {string} otp - One-time password
 * @param {string} name - Subscriber name
 * @returns {Promise<string>} - Pending subscription ID
 */
const createPendingSubscription = async (email, otp, name) => {
  try {
    // Validate required fields
    if (!email || !otp) {
      throw new Error("Email and OTP are required for pending subscription");
    }

    if (!name) {
      throw new Error("Name is required for pending subscription");
    }

    // Create expiration time (10 minutes from now)
    const expiresAt = new Date();
    expiresAt.setMinutes(expiresAt.getMinutes() + 10);

    // Check for existing pending subscriptions for this email and delete them
    await cleanupPendingSubscriptions(email);

    // Create pending subscription
    const pendingRef = await collections.pendingSubscriptions.add({
      email,
      name,
      otp,
      verified: false,
      createdAt: new Date(),
      expiresAt,
    });

    return pendingRef.id;
  } catch (error) {
    console.error("Error creating pending subscription:", error);
    throw error;
  }
};

/**
 * Clean up pending subscriptions for a specific email
 * @param {string} email - Subscriber email
 * @returns {Promise<void>}
 */
const cleanupPendingSubscriptions = async (email) => {
  try {
    // Find all pending subscriptions for this email
    const snapshot = await collections.pendingSubscriptions
      .where("email", "==", email)
      .get();

    // Delete all found documents
    const batch = db.batch();
    snapshot.docs.forEach((doc) => {
      batch.delete(doc.ref);
    });

    if (snapshot.docs.length > 0) {
      await batch.commit();
      console.log(
        `Cleaned up ${snapshot.docs.length} pending subscriptions for ${email}`
      );
    }
  } catch (error) {
    console.error("Error cleaning up pending subscriptions:", error);
    // Don't throw the error to avoid disrupting the main flow
  }
};

/**
 * Clean up expired pending subscriptions
 * @returns {Promise<number>} - Number of deleted subscriptions
 */
const cleanupExpiredSubscriptions = async () => {
  try {
    const now = new Date();

    // Find all expired pending subscriptions
    const snapshot = await collections.pendingSubscriptions
      .where("expiresAt", "<", now)
      .where("verified", "==", false)
      .get();

    if (snapshot.empty) {
      return 0;
    }

    // Delete all expired documents
    const batch = db.batch();
    snapshot.docs.forEach((doc) => {
      batch.delete(doc.ref);
    });

    await batch.commit();
    console.log(
      `Cleaned up ${snapshot.docs.length} expired pending subscriptions`
    );

    return snapshot.docs.length;
  } catch (error) {
    console.error("Error cleaning up expired subscriptions:", error);
    return 0;
  }
};

/**
 * Verify OTP for pending subscription
 * @param {string} email - Subscriber email
 * @param {string} otp - One-time password
 * @returns {Promise<Object|null>} - Pending subscription data or null
 */
const verifyOtp = async (email, otp) => {
  try {
    const now = new Date();

    // Get pending subscription
    const snapshot = await collections.pendingSubscriptions
      .where("email", "==", email)
      .where("otp", "==", otp)
      .where("expiresAt", ">", now)
      .where("verified", "==", false)
      .limit(1)
      .get();

    if (snapshot.empty) {
      return null;
    }

    const doc = snapshot.docs[0];
    const pendingData = doc.data();

    // Mark as verified
    await collections.pendingSubscriptions.doc(doc.id).update({
      verified: true,
    });

    return {
      id: doc.id,
      ...pendingData,
    };
  } catch (error) {
    console.error("Error verifying OTP:", error);
    throw error;
  }
};

/**
 * List all subscribers with optional filtering
 * @param {Object} options - Filter options
 * @param {boolean} options.activeOnly - Filter by active status
 * @returns {Promise<Array>} - List of subscribers
 */
const listSubscribers = async (options = {}) => {
  try {
    const { activeOnly } = options;

    // Build query
    let query = collections.subscribers;

    // Apply filters
    if (activeOnly !== undefined) {
      query = query.where("active", "==", activeOnly);
    }

    // Execute query
    const snapshot = await query.get();

    // Map results
    return snapshot.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    }));
  } catch (error) {
    console.error("Error listing subscribers:", error);
    throw error;
  }
};

module.exports = {
  createSubscriber,
  getSubscriberByEmail,
  getSubscriberByToken,
  unsubscribeByToken,
  createPendingSubscription,
  verifyOtp,
  listSubscribers,
  cleanupPendingSubscriptions,
  cleanupExpiredSubscriptions,
};
