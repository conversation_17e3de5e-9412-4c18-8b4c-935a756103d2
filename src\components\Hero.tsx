import { motion } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";
import { Link } from "react-router-dom";

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2,
      delayChildren: 0.3,
    },
  },
};

const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      duration: 0.6,
      ease: "easeOut",
    },
  },
};

const floatingItemVariants = {
  animate: (i: number) => ({
    y: [-5, 5, -5],
    rotate: i % 2 === 0 ? [-2, 2, -2] : [1, -1, 1],
    transition: {
      duration: 4 + i * 0.5,
      repeat: Infinity,
      ease: "easeInOut",
      delay: i * 0.3,
    },
  }),
};

export function Hero() {
  return (
    <section
      className="relative hero-grid-bg min-h-screen flex items-center overflow-hidden bg-background
                 py-20 md:py-24      
                 px-4 sm:px-6 lg:px-8"
    >
      <div className="absolute inset-0 z-[-1] opacity-15 dark:opacity-5 mix-blend-soft-light">
        {" "}
        <div className="absolute top-[10%] left-[5%] w-60 h-60 sm:w-72 sm:h-72 bg-primary/10 rounded-full filter blur-3xl animate-blob animation-delay-2000" />
        <div className="absolute bottom-[15%] right-[10%] w-72 h-72 sm:w-96 sm:h-96 bg-accent/15 rounded-full filter blur-3xl animate-blob animation-delay-4000" />
        <div className="absolute top-[30%] right-[25%] w-40 h-40 sm:w-56 sm:h-56 bg-secondary/10 rounded-full filter blur-3xl animate-blob" />
      </div>

      <div
        className="mx-auto relative z-10 grid
                   gap-12 lg:gap-16   
                   lg:grid-cols-2    
                   items-center"
      >
        <motion.div
          className="text-center lg:text-left"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          <motion.h1
            variants={itemVariants}
            className="text-4xl sm:text-5xl lg:text-6xl 
                       font-extrabold tracking-tight text-foreground mb-6 leading-tight"
          >
            Discover the Best
            <span className="block text-primary mt-1 sm:mt-2">
              Restaurants Near You
            </span>
          </motion.h1>

          <motion.p
            variants={itemVariants}
            className="text-md sm:text-lg lg:text-xl 
                       text-muted-foreground mb-10 max-w-xl mx-auto lg:mx-0"
          >
            Experience seamless food ordering with real-time tracking and
            exclusive deals from your favorite local restaurants.
          </motion.p>

          <motion.div
            variants={itemVariants}
            className="flex flex-col sm:flex-row gap-4 
                       justify-center lg:justify-start"
          >
            <Button
              asChild
              size="lg"
              className="text-base px-6 md:px-8 py-3 md:py-4 
                         shadow-lg hover:shadow-primary/30 transition-all duration-300 hover:scale-[1.03] group"
            >
              <Link to="/restaurants">
                Find Restaurants{" "}
                <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
              </Link>
            </Button>
            <Button
              asChild
              variant="outline"
              size="lg"
              className="text-base px-6 md:px-8 py-3 md:py-4 
                         hover:bg-accent hover:text-accent-foreground transition-all duration-300 hover:scale-[1.03]"
            >
              <Link to="/about">Learn More</Link>
            </Button>
          </motion.div>
        </motion.div>

        <motion.div
          className="relative hidden lg:flex items-center justify-center h-80 md:h-96"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          {[0, 1, 2].map((i) => (
            <motion.div
              key={i}
              className={`absolute rounded-xl shadow-xl border border-border/10 ${
                i === 0
                  ? "w-44 h-56 md:w-48 md:h-60 bg-card/80 backdrop-blur-sm rotate-[-8deg] top-10 left-10"
                  : i === 1
                  ? "w-52 h-64 md:w-56 md:h-72 bg-primary/10 backdrop-blur-sm rotate-[5deg] z-10"
                  : "w-40 h-52 md:w-44 md:h-56 bg-secondary/10 backdrop-blur-sm rotate-[12deg] bottom-10 right-10"
              }`}
              custom={i}
              variants={floatingItemVariants}
              animate="animate"
            >
              <div className="flex items-center justify-center h-full opacity-80">
                {i === 0 && (
                  <img
                    src="/pasta.png"
                    alt="Pasta illustration"
                    className="w-28 md:w-32"
                  />
                )}
                {i === 1 && (
                  <img
                    src="/salad.png"
                    alt="Salad illustration"
                    className="w-28 md:w-32"
                  />
                )}
                {i === 2 && (
                  <img
                    src="/meal.png"
                    alt="Meal illustration"
                    className="w-28 md:w-32"
                  />
                )}
              </div>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  );
}
