import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>Header,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { PriceLabel } from "./PriceLabel";
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { MenuItem } from "@/types/restaurant";
import {
  ShoppingCart,
  AlertCircle,
  Clock,
  Flame,
  Leaf,
  Info,
  Award,
  Sparkles,
  Utensils,
  X,
  Wheat,
  Egg,
  Milk,
  Fish,
  Nut,
  Salad,
  ZoomIn,
  ZoomOut,
  Coffee,
} from "lucide-react";

// Allergen icons mapping
const ALLERGEN_ICONS: Record<string, React.ElementType> = {
  gluten: Wheat,
  eggs: Egg,
  milk: Milk,
  fish: Fish,
  nuts: Nut,
  soy: Leaf,
  default: AlertCircle,
};

interface MenuItemDialogProps {
  item: MenuItem;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onAddToCart: () => void;
}

export const MenuItemDialog = ({
  item,
  open,
  onOpenChange,
  onAddToCart,
}: MenuItemDialogProps) => {
  const [imageZoomed, setImageZoomed] = useState(false);

  // Toggle zoom state
  const toggleZoom = (e: React.MouseEvent) => {
    e.stopPropagation();
    setImageZoomed((prev) => !prev);
  };

  // Format nutrition information
  const formatNutrition = (value: number | undefined, unit: string) => {
    if (value === undefined) return "N/A";
    return `${value}${unit}`;
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        className="sm:max-w-[550px] md:max-w-[600px] w-[95vw] p-0 overflow-y-auto max-h-[90vh]"
        hideCloseButton
      >
        {/* Image with zoom capability */}
        <div
          className={`relative cursor-pointer transition-all duration-300 ease-in-out ${
            imageZoomed ? "h-[40vh]" : "h-[120px]"
          }`}
          onClick={toggleZoom}
        >
          <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent opacity-60 z-10"></div>

          {item.imageUrl ? (
            <div className="w-full h-full overflow-hidden">
              <img
                src={item.imageUrl}
                alt={item.name}
                className={`w-full h-full transition-all duration-300 ease-in-out ${
                  imageZoomed ? "object-contain" : "object-cover"
                }`}
                style={{ objectPosition: "center" }}
              />
            </div>
          ) : (
            <div className="w-full h-full bg-muted flex items-center justify-center">
              <Utensils className="h-16 w-16 text-muted-foreground" />
            </div>
          )}

          {/* Close button for dialog */}
          <button
            onClick={(e) => {
              e.stopPropagation();
              onOpenChange(false);
            }}
            className="absolute top-3 left-3 z-20 bg-black/80 text-white rounded-full p-1.5 hover:bg-black/90 transition-colors shadow-md"
            aria-label="Close dialog"
          >
            <X className="h-4 w-4" />
          </button>

          {/* Zoom control button */}
          <button
            onClick={toggleZoom}
            className="absolute bottom-3 right-3 z-20 bg-black/80 text-white rounded-full p-2.5 hover:bg-black/90 transition-colors shadow-md border border-white/20"
            aria-label={imageZoomed ? "Zoom out" : "Zoom in"}
          >
            {imageZoomed ? (
              <ZoomOut className="h-5 w-5" />
            ) : (
              <ZoomIn className="h-5 w-5" />
            )}
          </button>

          {/* Item name overlay for zoomed state */}
          {imageZoomed && (
            <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4 z-20">
              <h2 className="text-white font-bold text-xl">{item.name}</h2>
            </div>
          )}
        </div>

        <div className="p-6">
          <DialogHeader className="pb-3 border-b">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <DialogTitle className="text-xl font-bold tracking-tight">
                  {item.name}
                </DialogTitle>
                <div className="flex flex-wrap gap-2 mt-1.5">
                  {item.isSignatureDish && (
                    <Badge
                      variant="outline"
                      className="border-amber-300 bg-amber-50 text-amber-700 rounded-md"
                    >
                      <Award className="h-3 w-3 mr-1" /> Signature Dish
                    </Badge>
                  )}
                  {item.preparationTime && (
                    <Badge
                      variant="outline"
                      className="flex items-center gap-1 text-xs rounded-md"
                    >
                      <Clock className="h-3 w-3" />
                      {item.preparationTime}
                    </Badge>
                  )}
                </div>
              </div>
              <div className="ml-3">
                <PriceLabel price={item.price} />
              </div>
            </div>
            {item.description && (
              <DialogDescription className="text-sm mt-3 leading-relaxed">
                {item.description}
              </DialogDescription>
            )}
          </DialogHeader>

          {/* Dietary and Allergen Information */}
          <div className="mt-4">
            <div className="flex flex-wrap gap-4">
              {item.dietary && item.dietary.length > 0 && (
                <div className="flex-1 min-w-[45%]">
                  <h4 className="text-sm font-semibold mb-2 flex items-center">
                    <Leaf className="h-4 w-4 mr-1.5 text-green-600" />
                    Dietary Options
                  </h4>
                  <div className="flex flex-wrap gap-1.5">
                    {item.dietary.map((diet, index) => (
                      <Badge
                        key={index}
                        variant="outline"
                        className="text-xs border-green-300 bg-green-50 text-green-700 py-1"
                      >
                        {diet}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              {item.allergens && item.allergens.length > 0 && (
                <div className="flex-1 min-w-[45%]">
                  <h4 className="text-sm font-semibold mb-2 flex items-center">
                    <AlertCircle className="h-4 w-4 mr-1.5 text-red-600" />
                    Allergens
                  </h4>
                  <div className="flex flex-wrap gap-1.5">
                    {item.allergens.map((allergen, index) => {
                      const AllergenIcon =
                        ALLERGEN_ICONS[allergen.toLowerCase()] ||
                        ALLERGEN_ICONS.default;
                      return (
                        <Badge
                          key={index}
                          variant="outline"
                          className="text-xs border-red-300 bg-red-50 text-red-700 flex items-center py-1"
                        >
                          <AllergenIcon className="h-3 w-3 mr-1" />
                          {allergen}
                        </Badge>
                      );
                    })}
                  </div>
                </div>
              )}
            </div>

            {/* Spicy Level Indicator */}
            {item.spicyLevel && (
              <div className="mt-4 p-2 bg-red-50/50 border border-red-100 rounded-md">
                <div className="flex items-center">
                  <Flame className="h-4 w-4 mr-2 text-red-500" />
                  <span className="text-sm font-medium text-red-700">
                    Spicy Level:{" "}
                  </span>
                  <div className="ml-auto flex">
                    {["mild", "medium", "hot", "extra hot"].map((_, index) => {
                      const active =
                        (item.spicyLevel === "mild" && index === 0) ||
                        (item.spicyLevel === "medium" && index <= 1) ||
                        (item.spicyLevel === "hot" && index <= 2) ||
                        (item.spicyLevel === "extra hot" && index <= 3);

                      return (
                        <Flame
                          key={index}
                          className={`h-4 w-4 ${
                            active ? "text-red-500" : "text-gray-300"
                          }`}
                        />
                      );
                    })}
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Detailed Information Tabs */}
          <Tabs defaultValue="ingredients" className="mt-5">
            <TabsList className="grid grid-cols-2 w-full">
              <TabsTrigger
                value="ingredients"
                className="flex items-center gap-1.5"
              >
                <Utensils className="h-3.5 w-3.5" />
                Ingredients
              </TabsTrigger>
              <TabsTrigger
                value="nutrition"
                className="flex items-center gap-1.5"
              >
                <Info className="h-3.5 w-3.5" />
                Nutrition
              </TabsTrigger>
            </TabsList>

            <TabsContent
              value="ingredients"
              className="mt-3 bg-muted/20 p-3 rounded-md border"
            >
              {item.ingredients && item.ingredients.length > 0 ? (
                <ul className="text-sm space-y-1.5 pl-5 list-disc marker:text-primary">
                  {item.ingredients.map((ingredient, index) => (
                    <li key={index} className="pl-1">
                      {ingredient}
                    </li>
                  ))}
                </ul>
              ) : (
                <p className="text-sm text-muted-foreground italic">
                  Ingredients information not available.
                </p>
              )}
            </TabsContent>

            <TabsContent
              value="nutrition"
              className="mt-3 bg-muted/20 p-3 rounded-md border"
            >
              {item.calories || item.protein || item.carbs || item.fat ? (
                <div className="space-y-3">
                  <div className="flex flex-wrap gap-3">
                    {item.calories !== undefined && (
                      <div className="bg-primary/10 px-3 py-1.5 rounded-md flex-1 min-w-[40%]">
                        <div className="text-xs text-muted-foreground">
                          Calories
                        </div>
                        <div className="font-semibold text-sm">
                          {item.calories} kcal
                        </div>
                      </div>
                    )}

                    {item.servingSize && (
                      <div className="bg-primary/10 px-3 py-1.5 rounded-md flex-1 min-w-[40%]">
                        <div className="text-xs text-muted-foreground">
                          Serving Size
                        </div>
                        <div className="font-semibold text-sm">
                          {item.servingSize}
                        </div>
                      </div>
                    )}
                  </div>

                  <div className="pt-2">
                    <h5 className="text-xs font-semibold mb-2 text-primary">
                      Macronutrients
                    </h5>
                    <div className="grid grid-cols-3 gap-2">
                      <div className="bg-blue-50 p-2 rounded-md text-center shadow-sm">
                        <div className="text-xs text-blue-700">Protein</div>
                        <div className="font-semibold text-blue-800">
                          {formatNutrition(item.protein, "g")}
                        </div>
                      </div>
                      <div className="bg-amber-50 p-2 rounded-md text-center shadow-sm">
                        <div className="text-xs text-amber-700">Carbs</div>
                        <div className="font-semibold text-amber-800">
                          {formatNutrition(item.carbs, "g")}
                        </div>
                      </div>
                      <div className="bg-red-50 p-2 rounded-md text-center shadow-sm">
                        <div className="text-xs text-red-700">Fat</div>
                        <div className="font-semibold text-red-800">
                          {formatNutrition(item.fat, "g")}
                        </div>
                      </div>
                    </div>
                  </div>

                  {(item.fiber !== undefined ||
                    item.sugar !== undefined ||
                    item.sodium !== undefined ||
                    item.cholesterol !== undefined) && (
                    <div className="border-t pt-3 mt-2">
                      <h5 className="text-xs font-semibold mb-2 text-primary">
                        Additional Nutrition
                      </h5>
                      <div className="grid grid-cols-2 gap-x-4 gap-y-1.5">
                        {item.fiber !== undefined && (
                          <div className="flex justify-between text-xs">
                            <span className="text-muted-foreground">
                              Fiber:
                            </span>
                            <span className="font-medium">{item.fiber}g</span>
                          </div>
                        )}
                        {item.sugar !== undefined && (
                          <div className="flex justify-between text-xs">
                            <span className="text-muted-foreground">
                              Sugar:
                            </span>
                            <span className="font-medium">{item.sugar}g</span>
                          </div>
                        )}
                        {item.sodium !== undefined && (
                          <div className="flex justify-between text-xs">
                            <span className="text-muted-foreground">
                              Sodium:
                            </span>
                            <span className="font-medium">{item.sodium}mg</span>
                          </div>
                        )}
                        {item.cholesterol !== undefined && (
                          <div className="flex justify-between text-xs">
                            <span className="text-muted-foreground">
                              Cholesterol:
                            </span>
                            <span className="font-medium">
                              {item.cholesterol}mg
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <p className="text-sm text-muted-foreground italic">
                  Nutrition information not available.
                </p>
              )}
            </TabsContent>
          </Tabs>

          {/* We've moved the spicy level indicator to the dietary section */}

          {/* Suggested Pairings */}
          <div className="mt-5 bg-primary/5 p-3 rounded-md border border-primary/10">
            <h4 className="text-sm font-semibold mb-2 flex items-center text-primary">
              <Utensils className="h-4 w-4 mr-1.5" />
              Pairs Well With
            </h4>
            <div className="flex flex-wrap gap-2">
              <Badge
                variant="outline"
                className="bg-white/80 hover:bg-white transition-colors flex items-center gap-1 rounded-md"
              >
                <Salad className="h-3 w-3" />
                Side Salad
              </Badge>
              <Badge
                variant="outline"
                className="bg-white/80 hover:bg-white transition-colors flex items-center gap-1 rounded-md"
              >
                <Utensils className="h-3 w-3" />
                French Fries
              </Badge>
              <Badge
                variant="outline"
                className="bg-white/80 hover:bg-white transition-colors flex items-center gap-1 rounded-md"
              >
                <Coffee className="h-3 w-3" />
                Soft Drink
              </Badge>
            </div>
          </div>
        </div>

        <DialogFooter className="p-4 pt-3 border-t bg-gradient-to-b from-muted/10 to-muted/30">
          <div className="flex justify-between items-center w-full">
            <div>
              {item.isSpecialOffer &&
                item.discountPercentage &&
                item.discountPercentage > 0 && (
                  <div className="flex items-center text-xs text-green-600 font-medium">
                    <Sparkles className="h-3 w-3 mr-1" />
                    {item.discountPercentage}% off
                  </div>
                )}
            </div>
            <Button
              onClick={() => {
                onAddToCart();
                onOpenChange(false);
              }}
              disabled={!item.available}
              size="lg"
              className={`h-10 px-6 ${
                item.available ? "bg-primary hover:bg-primary/90" : ""
              }`}
            >
              {!item.available ? (
                <AlertCircle className="mr-2 h-5 w-5" />
              ) : (
                <ShoppingCart className="mr-2 h-5 w-5" />
              )}
              {item.available ? "Add to Cart" : "Not Available"}
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
