import { useState } from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ontent,
  CardDescription,
} from "@/components/ui/card";
import { ChevronDown, ChevronUp } from "lucide-react";

interface CollapsibleCardProps {
  title: React.ReactNode;
  description?: React.ReactNode;
  children: React.ReactNode;
  icon?: React.ReactNode;
  defaultOpen?: boolean;
  className?: string;
}

export function CollapsibleCard({
  title,
  description,
  children,
  icon,
  defaultOpen = false,
  className = "",
}: CollapsibleCardProps) {
  const [isOpen, setIsOpen] = useState(defaultOpen);

  return (
    <Card className={`w-full ${className}`}>
      <CardHeader
        className="cursor-pointer flex flex-row items-center justify-between p-3 sm:p-4 md:p-6"
        onClick={() => setIsOpen(!isOpen)}
      >
        <div className="flex items-center gap-1.5 sm:gap-2 max-w-[calc(100%-2rem)] overflow-hidden">
          {icon}
          <div className="overflow-hidden">
            <CardTitle className="flex items-center gap-1.5 sm:gap-2 text-base sm:text-lg truncate">
              {title}
            </CardTitle>
            {description && <CardDescription className="truncate">{description}</CardDescription>}
          </div>
        </div>
        <div className="text-muted-foreground flex-shrink-0">
          {isOpen ? (
            <ChevronUp className="h-4 w-4 sm:h-5 sm:w-5" />
          ) : (
            <ChevronDown className="h-4 w-4 sm:h-5 sm:w-5" />
          )}
        </div>
      </CardHeader>
      <div
        className={`transition-all duration-300 ease-in-out overflow-hidden ${
          isOpen ? "max-h-[2000px] opacity-100" : "max-h-0 opacity-0"
        }`}
      >
        <CardContent className="p-3 sm:p-4 md:p-6 pt-0 sm:pt-0 md:pt-0">{children}</CardContent>
      </div>
    </Card>
  );
}
