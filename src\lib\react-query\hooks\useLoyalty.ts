import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import {
  collection,
  query,
  getDocs,
  getDoc,
  doc,
  setDoc,
  addDoc,
  onSnapshot,
  orderBy,
  limit,
  serverTimestamp,
  FirestoreError,
  Timestamp,
} from "firebase/firestore";
import { firestore } from "@/config/firebase";
import { queryKeys, CACHE_TIME, STALE_TIME } from "../index";
import { useEffect, useState } from "react";
import { toast } from "sonner";

interface LoyaltyStatus {
  userId: string;
  tier: string;
  totalPoints: number;
  lifetimePoints: number;
  nextTierPoints: number;
  referralCode: string;
  referralCount: number;
  joinedAt: Date;
  lastUpdated: Date;
  gameStats?: {
    lastPlayed?: { [gameId: string]: Date };
    dailyPointsEarned?: number;
    lastPointsReset?: Date;
    checkInStreak?: number;
    lastCheckIn?: Date;
    playCount?: { [gameId: string]: number };
  };
}

interface PointTransaction {
  id: string;
  userId: string;
  points: number;
  type: string;
  description: string;
  referenceId?: string;
  createdAt: Date;
  expiresAt?: Date;
}

interface Referral {
  id: string;
  referrerId: string;
  referralCode: string;
  referredEmail: string;
  referredUserId?: string;
  status: "pending" | "completed";
  createdAt: Date;
  completedAt?: Date;
  pointsAwarded?: number;
}

/**
 * Hook to fetch loyalty status for a client with caching
 */
export function useLoyaltyStatus(clientId: string | undefined) {
  return useQuery({
    queryKey: queryKeys.clients.loyalty.status(clientId || ""),
    queryFn: async () => {
      if (!clientId) {
        throw new Error("Client ID is required");
      }

      const statusRef = doc(
        collection(doc(firestore, "clients", clientId), "loyaltyStatus"),
        "current"
      );
      const docSnap = await getDoc(statusRef);

      if (!docSnap.exists()) {
        throw new Error("Loyalty status not found");
      }

      const data = docSnap.data();
      const processedData = {
        ...data,
        joinedAt:
          data.joinedAt instanceof Timestamp
            ? data.joinedAt.toDate()
            : new Date(data.joinedAt),
        lastUpdated:
          data.lastUpdated instanceof Timestamp
            ? data.lastUpdated.toDate()
            : new Date(data.lastUpdated),
        gameStats: {},
      };

      // Process game stats if they exist
      if (data.gameStats) {
        const gameStats = { ...data.gameStats };

        // Process lastPlayed timestamps
        if (gameStats.lastPlayed) {
          const processedLastPlayed: { [gameId: string]: Date } = {};
          Object.entries(gameStats.lastPlayed).forEach(
            ([gameId, timestamp]) => {
              if (timestamp instanceof Timestamp) {
                processedLastPlayed[gameId] = timestamp.toDate();
              } else if (timestamp) {
                processedLastPlayed[gameId] = new Date();
              }
            }
          );
          gameStats.lastPlayed = processedLastPlayed;
        }

        // Process other timestamps
        if (gameStats.lastPointsReset instanceof Timestamp) {
          gameStats.lastPointsReset = gameStats.lastPointsReset.toDate();
        } else if (gameStats.lastPointsReset) {
          gameStats.lastPointsReset = new Date(gameStats.lastPointsReset);
        }

        if (gameStats.lastCheckIn instanceof Timestamp) {
          gameStats.lastCheckIn = gameStats.lastCheckIn.toDate();
        } else if (gameStats.lastCheckIn) {
          gameStats.lastCheckIn = new Date(gameStats.lastCheckIn);
        }

        processedData.gameStats = gameStats;
      }

      return processedData as LoyaltyStatus;
    },
    enabled: !!clientId,
    staleTime: STALE_TIME,
    gcTime: CACHE_TIME,
  });
}

/**
 * Hook to fetch point transactions for a client with caching
 */
export function usePointTransactions(
  clientId: string | undefined,
  transactionLimit = 20
) {
  return useQuery({
    queryKey: queryKeys.clients.loyalty.transactions(clientId || ""),
    queryFn: async () => {
      if (!clientId) {
        throw new Error("Client ID is required");
      }

      const transactionsRef = collection(
        doc(firestore, "clients", clientId),
        "pointTransactions"
      );
      const q = query(
        transactionsRef,
        orderBy("createdAt", "desc"),
        limit(transactionLimit)
      );
      const querySnapshot = await getDocs(q);

      const transactions: PointTransaction[] = [];
      querySnapshot.forEach((doc) => {
        const data = doc.data();
        transactions.push({
          id: doc.id,
          ...data,
          createdAt:
            data.createdAt instanceof Timestamp
              ? data.createdAt.toDate()
              : new Date(data.createdAt),
          expiresAt:
            data.expiresAt instanceof Timestamp
              ? data.expiresAt.toDate()
              : data.expiresAt
              ? new Date(data.expiresAt)
              : undefined,
        } as PointTransaction);
      });

      return transactions;
    },
    enabled: !!clientId,
    staleTime: STALE_TIME,
    gcTime: CACHE_TIME,
  });
}

/**
 * Hook to fetch referrals for a client with caching
 */
export function useReferrals(clientId: string | undefined) {
  return useQuery({
    queryKey: queryKeys.clients.loyalty.referrals(clientId || ""),
    queryFn: async () => {
      if (!clientId) {
        throw new Error("Client ID is required");
      }

      const referralsRef = collection(
        doc(firestore, "clients", clientId),
        "referrals"
      );
      const q = query(referralsRef, orderBy("createdAt", "desc"));
      const querySnapshot = await getDocs(q);

      const referrals: Referral[] = [];
      querySnapshot.forEach((doc) => {
        const data = doc.data();
        referrals.push({
          id: doc.id,
          ...data,
          createdAt:
            data.createdAt instanceof Timestamp
              ? data.createdAt.toDate()
              : new Date(data.createdAt),
          completedAt:
            data.completedAt instanceof Timestamp
              ? data.completedAt.toDate()
              : data.completedAt
              ? new Date(data.completedAt)
              : undefined,
        } as Referral);
      });

      return referrals;
    },
    enabled: !!clientId,
    staleTime: STALE_TIME,
    gcTime: CACHE_TIME,
  });
}

/**
 * Hook to fetch loyalty status with real-time updates
 */
export function useRealtimeLoyaltyStatus(clientId: string | undefined) {
  const [status, setStatus] = useState<LoyaltyStatus | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<FirestoreError | null>(null);

  useEffect(() => {
    if (!clientId) {
      setIsLoading(false);
      setStatus(null);
      setError(null);
      return;
    }

    setIsLoading(true);
    setError(null);

    const statusRef = doc(
      collection(doc(firestore, "clients", clientId), "loyaltyStatus"),
      "current"
    );

    const unsubscribe = onSnapshot(
      statusRef,
      (docSnap) => {
        if (docSnap.exists()) {
          const data = docSnap.data();
          const processedData = {
            ...data,
            joinedAt:
              data.joinedAt instanceof Timestamp
                ? data.joinedAt.toDate()
                : new Date(data.joinedAt),
            lastUpdated:
              data.lastUpdated instanceof Timestamp
                ? data.lastUpdated.toDate()
                : new Date(data.lastUpdated),
            gameStats: {},
          };

          // Process game stats if they exist
          if (data.gameStats) {
            const gameStats = { ...data.gameStats };

            // Process lastPlayed timestamps
            if (gameStats.lastPlayed) {
              const processedLastPlayed: { [gameId: string]: Date } = {};
              Object.entries(gameStats.lastPlayed).forEach(
                ([gameId, timestamp]) => {
                  if (timestamp instanceof Timestamp) {
                    processedLastPlayed[gameId] = timestamp.toDate();
                  } else if (timestamp) {
                    processedLastPlayed[gameId] = new Date();
                  }
                }
              );
              gameStats.lastPlayed = processedLastPlayed;
            }

            // Process other timestamps
            if (gameStats.lastPointsReset instanceof Timestamp) {
              gameStats.lastPointsReset = gameStats.lastPointsReset.toDate();
            } else if (gameStats.lastPointsReset) {
              gameStats.lastPointsReset = new Date(gameStats.lastPointsReset);
            }

            if (gameStats.lastCheckIn instanceof Timestamp) {
              gameStats.lastCheckIn = gameStats.lastCheckIn.toDate();
            } else if (gameStats.lastCheckIn) {
              gameStats.lastCheckIn = new Date(gameStats.lastCheckIn);
            }

            processedData.gameStats = gameStats;
          }

          setStatus(processedData as LoyaltyStatus);
        } else {
          setStatus(null);
        }
        setIsLoading(false);
      },
      (err: FirestoreError) => {
        console.error("Error fetching loyalty status:", err);
        setError(err);
        toast.error("Failed to load loyalty status");
        setIsLoading(false);
      }
    );

    return () => unsubscribe();
  }, [clientId]);

  return { status, isLoading, error };
}

/**
 * Hook to fetch point transactions with real-time updates
 */
export function useRealtimePointTransactions(
  clientId: string | undefined,
  transactionLimit = 20
) {
  const [transactions, setTransactions] = useState<PointTransaction[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<FirestoreError | null>(null);

  useEffect(() => {
    if (!clientId) {
      setIsLoading(false);
      setTransactions([]);
      setError(null);
      return;
    }

    setIsLoading(true);
    setError(null);

    const transactionsRef = collection(
      doc(firestore, "clients", clientId),
      "pointTransactions"
    );
    const q = query(
      transactionsRef,
      orderBy("createdAt", "desc"),
      limit(transactionLimit)
    );

    const unsubscribe = onSnapshot(
      q,
      (snapshot) => {
        const fetchedTransactions = snapshot.docs.map((doc) => {
          const data = doc.data();
          return {
            id: doc.id,
            ...data,
            createdAt:
              data.createdAt instanceof Timestamp
                ? data.createdAt.toDate()
                : new Date(data.createdAt),
            expiresAt:
              data.expiresAt instanceof Timestamp
                ? data.expiresAt.toDate()
                : data.expiresAt
                ? new Date(data.expiresAt)
                : undefined,
          } as PointTransaction;
        });

        setTransactions(fetchedTransactions);
        setIsLoading(false);
      },
      (err: FirestoreError) => {
        console.error("Error fetching point transactions:", err);
        setError(err);
        toast.error("Failed to load point transactions");
        setIsLoading(false);
      }
    );

    return () => unsubscribe();
  }, [clientId, transactionLimit]);

  return { transactions, isLoading, error };
}

/**
 * Hook to create a referral with optimistic updates
 */
export function useCreateReferral() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      referrerId,
      referralCode,
      referredEmail,
    }: {
      referrerId: string;
      referralCode: string;
      referredEmail: string;
    }) => {
      // Create a new referral document
      const referralsRef = collection(
        doc(firestore, "clients", referrerId),
        "referrals"
      );

      const referralData = {
        referrerId,
        referralCode,
        referredEmail,
        status: "pending",
        createdAt: serverTimestamp(),
      };

      const docRef = await addDoc(referralsRef, referralData);

      // Also add to global referrals collection for lookup
      const globalReferralRef = doc(firestore, "referrals", docRef.id);
      await setDoc(globalReferralRef, {
        ...referralData,
        id: docRef.id,
      });

      return {
        id: docRef.id,
        ...referralData,
        createdAt: new Date(),
      } as Referral;
    },
    onMutate: async (variables) => {
      const { referrerId } = variables;

      // Cancel any outgoing refetches
      await queryClient.cancelQueries({
        queryKey: queryKeys.clients.loyalty.referrals(referrerId),
      });

      // Snapshot the previous value
      const previousReferrals = queryClient.getQueryData<Referral[]>(
        queryKeys.clients.loyalty.referrals(referrerId)
      );

      // Create a temporary ID for optimistic update
      const tempId = `temp_${Date.now()}`;

      // Create optimistic referral
      const optimisticReferral: Referral = {
        id: tempId,
        ...variables,
        status: "pending",
        createdAt: new Date(),
      };

      // Optimistically update referrals
      if (previousReferrals) {
        queryClient.setQueryData(
          queryKeys.clients.loyalty.referrals(referrerId),
          [optimisticReferral, ...previousReferrals]
        );
      }

      return { previousReferrals, tempId };
    },
    onError: (_, variables, context) => {
      const { referrerId } = variables;

      // If the mutation fails, use the context returned from onMutate to roll back
      if (context?.previousReferrals) {
        queryClient.setQueryData(
          queryKeys.clients.loyalty.referrals(referrerId),
          context.previousReferrals
        );
      }

      toast.error("Failed to create referral");
    },
    onSuccess: () => {
      toast.success("Referral created successfully!");
    },
    onSettled: (_, __, variables) => {
      const { referrerId } = variables;

      // Always refetch after error or success to make sure our local data is correct
      queryClient.invalidateQueries({
        queryKey: queryKeys.clients.loyalty.referrals(referrerId),
      });
    },
  });
}
