/**
 * Authentication Service
 * 
 * Handles JWT token generation, verification, and API key management
 */
const jwt = require('jsonwebtoken');
const crypto = require('crypto');
const { db } = require('../config/firebase');
const logger = require('../utils/logger');

// JWT configuration
const JWT_SECRET = process.env.JWT_SECRET || crypto.randomBytes(64).toString('hex');
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '24h';
const API_KEY_COLLECTION = 'apiKeys';

/**
 * Generate a JWT token
 * @param {Object} payload - Token payload
 * @param {string} expiresIn - Token expiration time
 * @returns {string} - JWT token
 */
const generateToken = (payload, expiresIn = JWT_EXPIRES_IN) => {
  return jwt.sign(payload, JWT_SECRET, { expiresIn });
};

/**
 * Verify a JWT token
 * @param {string} token - JWT token
 * @returns {Object} - Decoded token payload
 * @throws {Error} - If token is invalid
 */
const verifyToken = (token) => {
  try {
    return jwt.verify(token, JWT_SECRET);
  } catch (error) {
    logger.error('JWT verification error:', error);
    throw error;
  }
};

/**
 * Generate a new API key
 * @param {string} clientId - Client ID
 * @param {string} name - API key name
 * @param {string} scope - API key scope (e.g., 'read', 'write', 'admin')
 * @returns {Promise<Object>} - API key object
 */
const generateApiKey = async (clientId, name, scope = 'read') => {
  // Generate a secure random API key
  const apiKey = crypto.randomBytes(32).toString('hex');
  
  // Generate a secure random API secret
  const apiSecret = crypto.randomBytes(64).toString('hex');
  
  // Hash the API key for storage
  const hashedKey = crypto.createHash('sha256').update(apiKey).digest('hex');
  
  // Create API key document
  const apiKeyDoc = {
    clientId,
    name,
    scope,
    hashedKey,
    createdAt: new Date(),
    lastUsed: null,
    isActive: true,
  };
  
  // Store in Firestore
  const docRef = await db.collection(API_KEY_COLLECTION).add(apiKeyDoc);
  
  // Return the API key details (only shown once)
  return {
    id: docRef.id,
    apiKey,
    apiSecret,
    clientId,
    name,
    scope,
    createdAt: apiKeyDoc.createdAt,
  };
};

/**
 * Validate an API key
 * @param {string} apiKey - API key to validate
 * @returns {Promise<Object|null>} - API key document if valid, null otherwise
 */
const validateApiKey = async (apiKey) => {
  try {
    // Hash the provided API key
    const hashedKey = crypto.createHash('sha256').update(apiKey).digest('hex');
    
    // Query Firestore for the API key
    const snapshot = await db.collection(API_KEY_COLLECTION)
      .where('hashedKey', '==', hashedKey)
      .where('isActive', '==', true)
      .limit(1)
      .get();
    
    if (snapshot.empty) {
      logger.warn(`Invalid API key attempt: ${apiKey.substring(0, 8)}...`);
      return null;
    }
    
    // Get the API key document
    const apiKeyDoc = snapshot.docs[0];
    const apiKeyData = apiKeyDoc.data();
    
    // Update last used timestamp
    await apiKeyDoc.ref.update({ lastUsed: new Date() });
    
    // Return the API key data
    return {
      id: apiKeyDoc.id,
      ...apiKeyData,
    };
  } catch (error) {
    logger.error('API key validation error:', error);
    return null;
  }
};

/**
 * Revoke an API key
 * @param {string} apiKeyId - API key ID
 * @returns {Promise<boolean>} - True if successful, false otherwise
 */
const revokeApiKey = async (apiKeyId) => {
  try {
    await db.collection(API_KEY_COLLECTION).doc(apiKeyId).update({
      isActive: false,
      revokedAt: new Date(),
    });
    
    return true;
  } catch (error) {
    logger.error('API key revocation error:', error);
    return false;
  }
};

/**
 * List API keys for a client
 * @param {string} clientId - Client ID
 * @returns {Promise<Array>} - Array of API key documents
 */
const listApiKeys = async (clientId) => {
  try {
    const snapshot = await db.collection(API_KEY_COLLECTION)
      .where('clientId', '==', clientId)
      .get();
    
    return snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
    }));
  } catch (error) {
    logger.error('List API keys error:', error);
    return [];
  }
};

module.exports = {
  generateToken,
  verifyToken,
  generateApiKey,
  validateApiKey,
  revokeApiKey,
  listApiKeys,
};
