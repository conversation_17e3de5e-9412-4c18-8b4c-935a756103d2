import { motion } from "framer-motion";
import { useState, useEffect, FormEvent, ChangeEvent } from "react";
import { Mail, Phone, Users, BarChart2, Rocket, Target } from "lucide-react"; // Using Lucide consistently
import { FaUtensils, FaMobileAlt, FaRegCalendarAlt } from "react-icons/fa"; // Kept for feature icons if preferred

// Import Shadcn UI Components
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

// Firebase imports for statistics
import {
  collection,
  getDocs,
  query,
  where,
  getCountFromServer,
  Timestamp,
} from "firebase/firestore";
import { firestore } from "@/config/firebase";
import { Loading } from "@/components/ui/loading";

interface ContactFormData {
  name: string;
  email: string;
  message: string;
}

// Animation Variants
const sectionVariants = {
  hidden: { opacity: 0, y: 30 },
  visible: {
    opacity: 1,
    y: 0,
    transition: { duration: 0.6, ease: "easeOut" },
  },
};

const itemVariants = {
  hidden: { opacity: 0, scale: 0.95 },
  visible: { opacity: 1, scale: 1, transition: { duration: 0.5 } },
};

const About = () => {
  const [formData, setFormData] = useState<ContactFormData>({
    name: "",
    email: "",
    message: "",
  });

  // Statistics state
  const [stats, setStats] = useState({
    restaurants: 0,
    users: 0,
    averageRating: 0,
    successRate: 0,
  });
  const [loading, setLoading] = useState(true);

  // Fetch real statistics from Firebase
  useEffect(() => {
    const fetchStatistics = async () => {
      try {
        // Get active restaurants count and calculate weighted rating
        const restaurantsRef = collection(firestore, "restaurants");
        const activeRestaurantsQuery = query(
          restaurantsRef,
          where("isActive", "==", true)
        );
        const restaurantsSnapshot = await getDocs(activeRestaurantsQuery);
        const restaurantsCount = restaurantsSnapshot.size;

        // Calculate weighted rating based on number of reviews and rating
        let totalWeightedRating = 0;
        let totalReviews = 0;
        restaurantsSnapshot.forEach((doc) => {
          const data = doc.data();
          const reviews = data.reviews?.length || 0;
          // Ensure rating is valid and not more than 5
          const rating = data.rating
            ? Math.min(Math.max(data.rating, 0), 5)
            : 0;
          if (rating > 0 && reviews > 0) {
            // Weight the rating based on number of reviews
            const weight = Math.min(reviews / 10, 1); // Max weight at 10 reviews
            totalWeightedRating += rating * weight;
            totalReviews += weight;
          }
        });
        const avgRating =
          totalReviews > 0
            ? Math.min(
                Number((totalWeightedRating / totalReviews).toFixed(1)),
                5
              )
            : 0;

        // Get users count (clients)
        const clientsRef = collection(firestore, "clients");
        const clientsSnapshot = await getCountFromServer(clientsRef);
        const usersCount = clientsSnapshot.data().count;

        // Calculate success rate with more factors
        const reservationsRef = collection(firestore, "reservations");
        const lastMonth = Timestamp.fromDate(
          new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
        );

        // Get recent reservations
        const recentReservationsQuery = query(
          reservationsRef,
          where("createdAt", ">=", lastMonth)
        );
        const reservationsSnapshot = await getDocs(recentReservationsQuery);

        let successPoints = 0;
        let totalPoints = 0;

        reservationsSnapshot.forEach((doc) => {
          const reservation = doc.data();

          // Base points for each reservation
          const basePoints = 1;
          totalPoints += basePoints;

          switch (reservation.status) {
            case "completed":
              // Full points for completed reservations
              successPoints += basePoints;
              break;
            case "confirmed":
              // Partial points for confirmed but not yet completed
              successPoints += basePoints * 0.8;
              break;
            case "pending":
              // Small points for pending
              successPoints += basePoints * 0.3;
              break;
            case "cancelled":
              // Check if cancelled by restaurant (less penalty) or user
              if (reservation.cancelledBy === "restaurant") {
                successPoints += basePoints * 0.1;
              }
              break;
            // No points for no-shows or user cancellations
          }
        });

        const successRateValue =
          totalPoints > 0
            ? ((successPoints / totalPoints) * 100).toFixed(0)
            : 0;

        setStats({
          restaurants: restaurantsCount,
          users: usersCount,
          averageRating: Number(avgRating),
          successRate: Number(successRateValue),
        });
      } catch (error) {
        console.error("Error fetching statistics:", error);
        // Set fallback values if there's an error
        setStats({
          restaurants: 100,
          users: 5000,
          averageRating: 4.5,
          successRate: 95,
        });
      } finally {
        setLoading(false);
      }
    };

    fetchStatistics();
  }, []);

  const handleSubmit = (e: FormEvent) => {
    e.preventDefault();
    alert("Thank you for your message! We'll get back to you soon. (Demo)");
    setFormData({ name: "", email: "", message: "" });
  };

  const handleChange = (
    e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  return (
    <div className="min-h-screen bg-background text-foreground">
      {/* Hero Section */}
      <motion.section
        variants={sectionVariants}
        initial="hidden"
        animate="visible"
        className="mx-auto px-4 pt-28 pb-20 text-center max-w-4xl"
      >
        <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold mb-6 tracking-tight">
          About <span className="text-primary">Qonai</span>
        </h1>
        <p className="text-lg sm:text-xl text-muted-foreground max-w-3xl mx-auto">
          A modern restaurant platform combining social features & AI menus to
          transform dining discovery, enjoyment, and sharing.
        </p>
      </motion.section>

      {/* Mission & Stats Section */}
      <motion.section
        variants={sectionVariants}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, amount: 0.3 }}
        className="mx-auto px-4 py-16"
      >
        {loading ? (
          <div className="flex justify-center py-12">
            <Loading />
          </div>
        ) : (
          <div className="grid lg:grid-cols-2 gap-12 lg:gap-16 items-center">
            <div className="space-y-4">
              <h2 className="text-3xl md:text-4xl font-semibold tracking-tight">
                Our Mission
              </h2>
              <p className="text-muted-foreground text-lg">
                At Qonai, we connect diners with exceptional restaurants while
                empowering owners with effective management tools.
              </p>
              <p className="text-muted-foreground">
                We streamline reservations, enhance experiences, and help
                restaurants thrive digitally through innovation and
                personalization. Dining is about discovery, connection, and
                memorable moments – our AI platform and community make it
                happen.
              </p>
            </div>
            <div className="grid grid-cols-2 gap-4 sm:gap-6">
              <Card className="text-center hover:shadow-lg transition-shadow">
                <CardHeader>
                  <CardTitle className="text-4xl font-bold text-primary">
                    {stats.restaurants}+
                  </CardTitle>
                </CardHeader>
                <CardContent className="flex flex-col items-center gap-1">
                  <Users className="w-6 h-6 text-muted-foreground" />
                  <p className="text-sm text-muted-foreground font-medium">
                    Restaurants
                  </p>
                  <p className="text-xs text-muted-foreground">
                    Active on our platform
                  </p>
                </CardContent>
              </Card>
              <Card className="text-center hover:shadow-lg transition-shadow">
                <CardHeader>
                  <CardTitle className="text-4xl font-bold text-primary">
                    {stats.users}+
                  </CardTitle>
                </CardHeader>
                <CardContent className="flex flex-col items-center gap-1">
                  <Rocket className="w-6 h-6 text-muted-foreground" />
                  <p className="text-sm text-muted-foreground font-medium">
                    Happy Customers
                  </p>
                  <p className="text-xs text-muted-foreground">
                    Registered users
                  </p>
                </CardContent>
              </Card>
              <Card className="text-center hover:shadow-lg transition-shadow">
                <CardHeader>
                  <CardTitle className="text-4xl font-bold text-primary">
                    {stats.averageRating}/5
                  </CardTitle>
                </CardHeader>
                <CardContent className="flex flex-col items-center gap-1">
                  <Target className="w-6 h-6 text-muted-foreground" />
                  <p className="text-sm text-muted-foreground font-medium">
                    Average Rating
                  </p>
                  <p className="text-xs text-muted-foreground">
                    From verified reviews
                  </p>
                </CardContent>
              </Card>
              <Card className="text-center hover:shadow-lg transition-shadow">
                <CardHeader>
                  <CardTitle className="text-4xl font-bold text-primary">
                    {stats.successRate}%
                  </CardTitle>
                </CardHeader>
                <CardContent className="flex flex-col items-center gap-1">
                  <BarChart2 className="w-6 h-6 text-muted-foreground" />
                  <p className="text-sm text-muted-foreground font-medium">
                    Success Rate
                  </p>
                  <p className="text-xs text-muted-foreground">
                    Reservation completion
                  </p>
                </CardContent>
              </Card>
            </div>
          </div>
        )}
      </motion.section>

      {/* Features Section - CORRECTED */}
      <motion.section
        variants={sectionVariants}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, amount: 0.2 }}
        className="py-20 bg-muted/40" // Subtle background difference
      >
        <div className="mx-auto px-4">
          <h2 className="text-3xl md:text-4xl font-semibold tracking-tight mb-12 text-center">
            What Makes Us Different
          </h2>
          <div className="grid sm:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              {
                icon: FaMobileAlt,
                title: "AI Recommendations",
                desc: "Our system learns your preferences to suggest dishes and restaurants you'll love.",
              },
              {
                icon: FaUtensils,
                title: "Digital Menus",
                desc: "Interactive menus with photos, ingredients, and dietary info for informed choices.",
              },
              {
                icon: FaRegCalendarAlt,
                title: "Seamless Reservations",
                desc: "Book tables instantly with real-time availability and confirmations.",
              },
              {
                icon: Users,
                title: "Restaurant Tools",
                desc: "Dashboard for restaurants to manage menus, reservations, and customer data.",
              },
            ].map((feature, index) => {
              // Assign component to an uppercase variable
              const IconComponent = feature.icon;
              return (
                <motion.div key={index} variants={itemVariants}>
                  <Card className="h-full hover:border-primary/50 transition-colors group hover:bg-card/80 backdrop-blur-sm">
                    <CardHeader className="items-center text-center">
                      <div className="w-14 h-14 bg-primary/10 rounded-full flex items-center justify-center mb-4 group-hover:bg-primary/20 transition-colors">
                        {/* Render the uppercase variable */}
                        <IconComponent className="text-primary text-2xl" />
                      </div>
                      <CardTitle className="text-lg">{feature.title}</CardTitle>
                    </CardHeader>
                    <CardContent className="text-center text-sm text-muted-foreground">
                      {feature.desc}
                    </CardContent>
                  </Card>
                </motion.div>
              );
            })}
          </div>
        </div>
      </motion.section>

      {/* Story Section */}
      <motion.section
        variants={sectionVariants}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, amount: 0.3 }}
        className="mx-auto px-4 py-20"
      >
        <Card className="max-w-4xl mx-auto overflow-hidden bg-gradient-to-r from-secondary/10 to-transparent">
          <div className="grid md:grid-cols-2 items-center">
            <div className="p-8 lg:p-12 space-y-4">
              <h2 className="text-3xl md:text-4xl font-semibold tracking-tight">
                Our Story
              </h2>
              <p className="text-muted-foreground">
                Qonai is a concept platform transforming dining via technology.
                This demo showcases modern web capabilities.
              </p>
              <p className="text-muted-foreground">
                It illustrates how AI recommendations, digital menus, and social
                features can enhance diner and owner experiences.
              </p>
              <p className="text-muted-foreground">
                While a demo, Qonai represents our vision for improving the
                restaurant industry through better connections.
              </p>
            </div>
            <div className="hidden md:block h-full">
              <img
                src="/restaurant.png" // Replace with a relevant image
                alt="Restaurant atmosphere"
                className="w-full h-full object-cover"
              />
            </div>
          </div>
        </Card>
      </motion.section>

      {/* Contact Section */}
      <motion.section
        variants={sectionVariants}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, amount: 0.2 }}
        className="mx-auto px-4 py-16"
      >
        <h2 className="text-3xl md:text-4xl font-semibold tracking-tight mb-12 text-center">
          Get in Touch
        </h2>
        <div className="grid lg:grid-cols-2 gap-12 lg:gap-16">
          <div className="space-y-6">
            <p className="text-muted-foreground text-lg">
              Have questions or want to learn more? Reach out to us!
            </p>
            <div className="flex items-start space-x-4">
              <div className="mt-1 flex-shrink-0 w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                <Mail className="text-primary w-5 h-5" />
              </div>
              <div>
                <h3 className="font-semibold text-lg">Email</h3>
                <a
                  href="mailto:<EMAIL>"
                  className="text-muted-foreground hover:text-primary transition-colors"
                >
                  <EMAIL>
                </a>
              </div>
            </div>
            <div className="flex items-start space-x-4">
              <div className="mt-1 flex-shrink-0 w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                <Phone className="text-primary w-5 h-5" />
              </div>
              <div>
                <h3 className="font-semibold text-lg">Phone</h3>
                <p className="text-muted-foreground">+994 50 204 77 22</p>
              </div>
            </div>
          </div>
          <Card>
            <CardHeader>
              <CardTitle>Send Us a Message</CardTitle>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Name</Label>
                  <Input
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    placeholder="Your Name"
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <Input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleChange}
                    placeholder="<EMAIL>"
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="message">Message</Label>
                  <Textarea
                    id="message"
                    name="message"
                    value={formData.message}
                    onChange={handleChange}
                    rows={4}
                    placeholder="Your message..."
                    required
                  />
                </div>
                <Button type="submit" className="w-full">
                  Send Message
                </Button>
                <p className="text-xs text-muted-foreground text-center pt-2">
                  This form is for demonstration only.
                </p>
              </form>
            </CardContent>
          </Card>
        </div>
      </motion.section>
    </div>
  );
};

export default About;
