import { useState, useEffect, useRef, useCallback } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  LineChart,
  Line,
  BarChart,
  Bar,
  Pie<PERSON>hart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from "recharts";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Order, MenuItem, Reservation } from "@/types/dashboard";
import { format, subDays } from "date-fns";
import {
  exportAsCSV,
  exportAsPDF,
  exportAnalyticsReport,
} from "@/utils/exportUtils";
import { ReportGenerator } from "@/components/dashboard/ReportGenerator";
import { ForecastingTab } from "@/components/dashboard/ForecastingTab";
import { RewardAnalytics } from "@/components/dashboard/RewardAnalytics";
import "./report-buttons.css";
import { Download, FileText, FileSpreadsheet, TrendingUp } from "lucide-react";

// Define colors for charts
const COLORS = [
  "#0088FE",
  "#00C49F",
  "#FFBB28",
  "#FF8042",
  "#8884d8",
  "#82ca9d",
];

// Define export headers and labels
const ORDER_HEADERS = ["date", "count", "revenue"];
const ORDER_LABELS = ["Date", "Orders", "Revenue (AZN)"];

const MENU_ITEM_HEADERS = ["name", "count"];
const MENU_ITEM_LABELS = ["Item Name", "Orders"];

const RESERVATION_HEADERS = ["name", "value"];
const RESERVATION_LABELS = ["Status", "Count"];

interface AnalyticsTabProps {
  orders: Order[];
  menuItems: MenuItem[];
  reservations: Reservation[];
}

export const AnalyticsTab = ({
  orders,
  menuItems,
  reservations,
}: AnalyticsTabProps) => {
  const [timeRange, setTimeRange] = useState<"week" | "month" | "year">("week");
  const [restaurantName, setRestaurantName] = useState<string>("Restaurant");
  const [comparisonMode, setComparisonMode] = useState<boolean>(false);

  // Define types for chart data
  interface OrderChartData {
    date: string;
    count: number;
    revenue: number;
  }

  interface MenuItemChartData {
    name: string;
    count: number;
  }

  interface ReservationChartData {
    name: string;
    value: number;
  }

  // Data states for current period
  const [orderData, setOrderData] = useState<OrderChartData[]>([]);
  const [menuItemData, setMenuItemData] = useState<MenuItemChartData[]>([]);
  const [reservationData, setReservationData] = useState<
    ReservationChartData[]
  >([]);
  const [revenueData, setRevenueData] = useState<OrderChartData[]>([]);

  // Data states for previous period
  const [prevOrderData, setPrevOrderData] = useState<OrderChartData[]>([]);
  const [prevMenuItemData, setPrevMenuItemData] = useState<MenuItemChartData[]>(
    []
  );
  const [prevReservationData, setPrevReservationData] = useState<
    ReservationChartData[]
  >([]);
  const [prevRevenueData, setPrevRevenueData] = useState<OrderChartData[]>([]);

  // Advanced metrics states
  const [averageOrderValue, setAverageOrderValue] = useState<number>(0);
  const [prevAverageOrderValue, setPrevAverageOrderValue] = useState<number>(0);
  const [customerRetentionRate, setCustomerRetentionRate] = useState<number>(0);
  const [prevCustomerRetentionRate, setPrevCustomerRetentionRate] =
    useState<number>(0);
  const [orderFrequency, setOrderFrequency] = useState<number>(0);
  const [prevOrderFrequency, setPrevOrderFrequency] = useState<number>(0);
  const [revenueGrowth, setRevenueGrowth] = useState<number>(0);
  interface TopCustomer {
    userId: string;
    customerName: string;
    totalSpent: number;
    orderCount: number;
  }

  interface CustomerMetric {
    name: string;
    value: number;
  }

  const [topCustomers, setTopCustomers] = useState<TopCustomer[]>([]);
  const [customerMetrics, setCustomerMetrics] = useState<CustomerMetric[]>([]);

  // Refs for chart containers
  const orderChartRef = useRef<HTMLDivElement>(null);
  const revenueChartRef = useRef<HTMLDivElement>(null);
  const menuChartRef = useRef<HTMLDivElement>(null);
  const reservationChartRef = useRef<HTMLDivElement>(null);

  // Helper function to get date range based on time range
  const getDateRange = (
    timeRange: "week" | "month" | "year",
    isPrevious = false
  ) => {
    const today = new Date();
    const daysToSubtract =
      timeRange === "week" ? 7 : timeRange === "month" ? 30 : 365;

    if (isPrevious) {
      // For previous period, we go back 2x the time range
      const endDate = subDays(today, daysToSubtract);
      const startDate = subDays(endDate, daysToSubtract);
      return { startDate, endDate };
    } else {
      // For current period
      const startDate = subDays(today, daysToSubtract);
      return { startDate, endDate: today };
    }
  };

  // Define Firestore timestamp type
  interface FirestoreTimestamp {
    seconds: number;
    nanoseconds: number;
    toDate?: () => Date;
  }

  // Helper function to parse order date
  const parseOrderDate = useCallback(
    (
      orderDate: string | FirestoreTimestamp | Date | number | unknown
    ): Date | null => {
      try {
        if (typeof orderDate === "object" && orderDate !== null) {
          // Check if it's a Date object
          if (orderDate instanceof Date) {
            return orderDate;
          }

          // Check if it's a Firestore timestamp with toDate method
          const timestampWithMethod = orderDate as { toDate?: () => Date };
          if (typeof timestampWithMethod.toDate === "function") {
            return timestampWithMethod.toDate();
          }

          // Check if it's a Firestore timestamp object
          const timestamp = orderDate as FirestoreTimestamp;
          if (
            timestamp.seconds !== undefined &&
            timestamp.nanoseconds !== undefined
          ) {
            return new Date(timestamp.seconds * 1000);
          }

          // Try to convert other objects to date
          try {
            return new Date(orderDate as unknown as string | number);
          } catch {
            return null;
          }
        } else if (typeof orderDate === "string") {
          return new Date(orderDate);
        } else if (typeof orderDate === "number") {
          return new Date(orderDate);
        }
        return null; // Invalid date
      } catch (error) {
        console.error("Date parsing error:", error);
        return null; // Error parsing date
      }
    },
    []
  );

  // Helper function to process orders for a specific date range
  const processOrdersForDateRange = useCallback(
    (
      orders: Order[],
      { startDate, endDate }: { startDate: Date; endDate: Date }
    ) => {
      // Filter orders by date range
      const filteredOrders = orders.filter((order) => {
        const orderDate = parseOrderDate(order.orderDate);
        if (!orderDate || isNaN(orderDate.getTime())) return false;
        return orderDate >= startDate && orderDate <= endDate;
      });

      // Group orders by date
      const ordersByDate = filteredOrders.reduce((acc, order) => {
        const orderDate = parseOrderDate(order.orderDate);
        if (!orderDate || isNaN(orderDate.getTime())) return acc;

        const dateStr = format(orderDate, "yyyy-MM-dd");

        if (!acc[dateStr]) {
          acc[dateStr] = {
            date: dateStr,
            count: 0,
            revenue: 0,
          };
        }
        acc[dateStr].count += 1;
        acc[dateStr].revenue += order.totalPrice;
        return acc;
      }, {} as Record<string, { date: string; count: number; revenue: number }>);

      // Convert to array and sort by date
      const orderDataArray = Object.values(ordersByDate).sort((a, b) =>
        a.date.localeCompare(b.date)
      );

      // Format dates for display
      return orderDataArray.map((item) => ({
        ...item,
        date: format(new Date(item.date), "MMM dd"),
      }));
    },
    [parseOrderDate]
  );

  // Calculate advanced metrics
  const calculateAdvancedMetrics = useCallback(
    (
      orders: Order[],
      dateRange: { startDate: Date; endDate: Date },
      isPrevious = false
    ) => {
      // Filter orders by date range
      const filteredOrders = orders.filter((order) => {
        const orderDate = parseOrderDate(order.orderDate);
        if (!orderDate || isNaN(orderDate.getTime())) return false;
        return (
          orderDate >= dateRange.startDate && orderDate <= dateRange.endDate
        );
      });

      if (filteredOrders.length === 0) return;

      // Calculate total revenue and orders
      const totalRevenue = filteredOrders.reduce(
        (sum, order) => sum + order.totalPrice,
        0
      );
      const totalOrders = filteredOrders.length;

      // Calculate Average Order Value (AOV)
      const aov = totalOrders > 0 ? totalRevenue / totalOrders : 0;

      // Get unique customers
      const uniqueCustomers = new Set(
        filteredOrders.map((order) => order.userId)
      );
      const customerCount = uniqueCustomers.size;

      // Calculate order frequency (orders per customer)
      const frequency = customerCount > 0 ? totalOrders / customerCount : 0;

      // Calculate customer retention rate (simplified version)
      // For a more accurate calculation, we would need historical data
      const repeatCustomers = filteredOrders.reduce((acc, order) => {
        if (!acc[order.userId]) {
          acc[order.userId] = 0;
        }
        acc[order.userId]++;
        return acc;
      }, {} as Record<string, number>);

      const repeatCustomerCount = Object.values(repeatCustomers).filter(
        (count) => count > 1
      ).length;
      const retentionRate =
        customerCount > 0 ? (repeatCustomerCount / customerCount) * 100 : 0;

      // Get top customers
      const customerSpending = filteredOrders.reduce((acc, order) => {
        if (!acc[order.userId]) {
          // Get customer name from firstName and lastName fields if available
          let customerName = "Unknown";

          if (order.userFirstName && order.userLastName) {
            // Use full name if both first and last name are available
            customerName = `${order.userFirstName} ${order.userLastName}`;
          } else if (order.userFirstName) {
            // Use just first name if that's all we have
            customerName = order.userFirstName;
          } else if (order.userLastName) {
            // Use just last name if that's all we have
            customerName = order.userLastName;
          } else if (order.userEmail) {
            // Fall back to email if no name is available
            customerName = order.userEmail;
          }

          acc[order.userId] = {
            userId: order.userId,
            customerName: customerName,
            totalSpent: 0,
            orderCount: 0,
          };
        }
        acc[order.userId].totalSpent += order.totalPrice;
        acc[order.userId].orderCount += 1;
        return acc;
      }, {} as Record<string, { userId: string; customerName: string; totalSpent: number; orderCount: number }>);

      const topCustomersList = Object.values(customerSpending)
        .sort((a, b) => b.totalSpent - a.totalSpent)
        .slice(0, 5);

      // Customer metrics for visualization
      const customerMetricsList = [
        { name: "New Customers", value: customerCount - repeatCustomerCount },
        { name: "Returning Customers", value: repeatCustomerCount },
      ];

      // Update state based on whether this is current or previous period
      if (isPrevious) {
        setPrevAverageOrderValue(aov);
        setPrevCustomerRetentionRate(retentionRate);
        setPrevOrderFrequency(frequency);
      } else {
        setAverageOrderValue(aov);
        setCustomerRetentionRate(retentionRate);
        setOrderFrequency(frequency);
        setTopCustomers(topCustomersList);
        setCustomerMetrics(customerMetricsList);

        // Calculate revenue growth (only if we have previous data)
        if (prevRevenueData.length > 0) {
          const prevTotalRevenue = prevRevenueData.reduce(
            (sum, item) => sum + item.revenue,
            0
          );
          if (prevTotalRevenue > 0) {
            const growth =
              ((totalRevenue - prevTotalRevenue) / prevTotalRevenue) * 100;
            setRevenueGrowth(growth);
          }
        }
      }
    },
    [
      parseOrderDate,
      prevRevenueData,
      setAverageOrderValue,
      setCustomerRetentionRate,
      setOrderFrequency,
      setPrevAverageOrderValue,
      setPrevCustomerRetentionRate,
      setPrevOrderFrequency,
      setTopCustomers,
      setCustomerMetrics,
      setRevenueGrowth,
    ]
  );

  // Process order data for charts
  useEffect(() => {
    if (!orders.length) return;

    // Get current period data
    const currentDateRange = getDateRange(timeRange);
    const currentPeriodData = processOrdersForDateRange(
      orders,
      currentDateRange
    );
    setOrderData(currentPeriodData);
    setRevenueData(currentPeriodData);

    // Calculate advanced metrics for current period
    calculateAdvancedMetrics(orders, currentDateRange);

    // If comparison mode is enabled, get previous period data
    if (comparisonMode) {
      const previousDateRange = getDateRange(timeRange, true);
      const previousPeriodData = processOrdersForDateRange(
        orders,
        previousDateRange
      );
      setPrevOrderData(previousPeriodData);
      setPrevRevenueData(previousPeriodData);

      // Calculate advanced metrics for previous period
      calculateAdvancedMetrics(orders, previousDateRange, true);
    }
  }, [
    orders,
    timeRange,
    comparisonMode,
    calculateAdvancedMetrics,
    processOrdersForDateRange,
  ]);

  // Helper function to process menu items for a specific date range
  const processMenuItemsForDateRange = useCallback(
    (
      orders: Order[],
      { startDate, endDate }: { startDate: Date; endDate: Date }
    ) => {
      // Filter orders by date range
      const filteredOrders = orders.filter((order) => {
        const orderDate = parseOrderDate(order.orderDate);
        if (!orderDate || isNaN(orderDate.getTime())) return false;
        return orderDate >= startDate && orderDate <= endDate;
      });

      // Count item occurrences in filtered orders
      const itemCounts: Record<string, { name: string; count: number }> = {};

      filteredOrders.forEach((order) => {
        order.items.forEach((item) => {
          if (!itemCounts[item.name]) {
            itemCounts[item.name] = { name: item.name, count: 0 };
          }
          itemCounts[item.name].count += item.quantity;
        });
      });

      // Convert to array and sort by count
      return Object.values(itemCounts)
        .sort((a, b) => b.count - a.count)
        .slice(0, 10); // Top 10 items
    },
    [parseOrderDate]
  );

  // Process menu item data for charts
  useEffect(() => {
    if (!orders.length || !menuItems.length) return;

    // Get current period data
    const currentDateRange = getDateRange(timeRange);
    const currentPeriodData = processMenuItemsForDateRange(
      orders,
      currentDateRange
    );
    setMenuItemData(currentPeriodData);

    // If comparison mode is enabled, get previous period data
    if (comparisonMode) {
      const previousDateRange = getDateRange(timeRange, true);
      const previousPeriodData = processMenuItemsForDateRange(
        orders,
        previousDateRange
      );
      setPrevMenuItemData(previousPeriodData);
    }
  }, [
    orders,
    menuItems,
    timeRange,
    comparisonMode,
    processMenuItemsForDateRange,
  ]);

  // Helper function to parse reservation date
  const parseReservationDate = useCallback(
    (
      resDate: string | FirestoreTimestamp | Date | number | unknown
    ): Date | null => {
      try {
        if (typeof resDate === "object" && resDate !== null) {
          // Check if it's a Date object
          if (resDate instanceof Date) {
            return resDate;
          }

          // Check if it's a Firestore timestamp with toDate method
          const timestampWithMethod = resDate as { toDate?: () => Date };
          if (typeof timestampWithMethod.toDate === "function") {
            return timestampWithMethod.toDate();
          }

          // Check if it's a Firestore timestamp object
          const timestamp = resDate as FirestoreTimestamp;
          if (
            timestamp.seconds !== undefined &&
            timestamp.nanoseconds !== undefined
          ) {
            return new Date(timestamp.seconds * 1000);
          }

          // Try to convert other objects to date
          try {
            return new Date(resDate as unknown as string | number);
          } catch {
            return null;
          }
        } else if (typeof resDate === "string") {
          const date = new Date(resDate);
          // Check if the date is valid
          if (isNaN(date.getTime())) {
            // If date is in format 'YYYY-MM-DD'
            const [year, month, day] = resDate.split("-").map(Number);
            return new Date(year, month - 1, day);
          }
          return date;
        } else if (typeof resDate === "number") {
          return new Date(resDate);
        }
        return null; // Invalid date
      } catch (error) {
        console.error("Reservation date parsing error:", error);
        return null; // Error parsing date
      }
    },
    []
  );

  // Helper function to process reservations for a specific date range
  const processReservationsForDateRange = useCallback(
    (
      reservations: Reservation[],
      { startDate, endDate }: { startDate: Date; endDate: Date }
    ) => {
      // Initialize status counts
      const statusCounts = {
        pending: 0,
        confirmed: 0,
        cancelled: 0,
        "no-show": 0,
        active: 0,
        completed: 0,
      };

      // Filter reservations by date range
      const filteredReservations = reservations.filter((res) => {
        const resDate = parseReservationDate(res.date);
        if (!resDate || isNaN(resDate.getTime())) return false;
        return resDate >= startDate && resDate <= endDate;
      });

      // Count by status
      filteredReservations.forEach((res) => {
        statusCounts[res.status] += 1;
      });

      // Convert to array format for pie chart
      return Object.entries(statusCounts).map(([status, count]) => ({
        name: status.charAt(0).toUpperCase() + status.slice(1),
        value: count,
      }));
    },
    [parseReservationDate]
  );

  // Process reservation data for charts
  useEffect(() => {
    if (!reservations.length) return;

    // Get current period data
    const currentDateRange = getDateRange(timeRange);
    const currentPeriodData = processReservationsForDateRange(
      reservations,
      currentDateRange
    );
    setReservationData(currentPeriodData);

    // If comparison mode is enabled, get previous period data
    if (comparisonMode) {
      const previousDateRange = getDateRange(timeRange, true);
      const previousPeriodData = processReservationsForDateRange(
        reservations,
        previousDateRange
      );
      setPrevReservationData(previousPeriodData);
    }
  }, [
    reservations,
    timeRange,
    comparisonMode,
    processReservationsForDateRange,
  ]);

  // Function to handle exporting all data as a comprehensive report
  const handleExportFullReport = () => {
    exportAnalyticsReport(
      orderData,
      revenueData,
      menuItemData,
      reservationData,
      restaurantName,
      timeRange,
      comparisonMode
        ? {
            orders: prevOrderData,
            revenue: prevRevenueData,
            menuItems: prevMenuItemData,
            reservations: prevReservationData,
          }
        : undefined,
      {
        averageOrderValue,
        customerRetentionRate,
        orderFrequency,
        revenueGrowth,
        topCustomers,
        customerMetrics,
        prevAverageOrderValue: comparisonMode
          ? prevAverageOrderValue
          : undefined,
        prevCustomerRetentionRate: comparisonMode
          ? prevCustomerRetentionRate
          : undefined,
        prevOrderFrequency: comparisonMode ? prevOrderFrequency : undefined,
      }
    );
  };

  // Get restaurant name from localStorage if available
  useEffect(() => {
    const user = JSON.parse(localStorage.getItem("user") || "{}");
    if (user && user.restaurantName) {
      setRestaurantName(user.restaurantName);
    }
  }, []);

  return (
    <Card>
      <CardHeader>
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <CardTitle>Restaurant Analytics</CardTitle>
            <CardDescription>
              Visualize your restaurant's performance metrics
            </CardDescription>
          </div>
          <div className="flex flex-col sm:flex-row gap-2 items-end sm:items-center">
            <div className="flex flex-col gap-2 sm:flex-row">
              <div className="flex rounded-md overflow-hidden border">
                <Button
                  variant={timeRange === "week" ? "default" : "ghost"}
                  className="rounded-none px-4"
                  onClick={() => setTimeRange("week")}
                >
                  Week
                </Button>
                <Button
                  variant={timeRange === "month" ? "default" : "ghost"}
                  className="rounded-none px-4"
                  onClick={() => setTimeRange("month")}
                >
                  Month
                </Button>
                <Button
                  variant={timeRange === "year" ? "default" : "ghost"}
                  className="rounded-none px-4"
                  onClick={() => setTimeRange("year")}
                >
                  Year
                </Button>
              </div>

              <div className="flex items-center space-x-2 px-2 py-1 border rounded-md">
                <Switch
                  id="comparison-mode"
                  checked={comparisonMode}
                  onCheckedChange={setComparisonMode}
                />
                <Label
                  htmlFor="comparison-mode"
                  className="text-sm cursor-pointer"
                >
                  Compare with previous {timeRange}
                </Label>
              </div>

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm">
                    <Download className="h-4 w-4 mr-2" />
                    Export
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem
                    onClick={handleExportFullReport}
                    className="report-button"
                  >
                    <FileText className="h-4 w-4 mr-2" />
                    Export Full Report (PDF)
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <ReportGenerator
                      restaurantName={restaurantName}
                      restaurantId={localStorage.getItem("restaurantId") || ""}
                      orderData={orderData}
                      menuItemData={menuItemData}
                      reviewData={[]}
                      customerMetrics={{
                        newCustomers:
                          customerMetrics.find(
                            (item) => item.name === "New Customers"
                          )?.value || 0,
                        returningCustomers:
                          customerMetrics.find(
                            (item) => item.name === "Returning Customers"
                          )?.value || 0,
                        averageOrderValue: averageOrderValue,
                        customerRetentionRate: customerRetentionRate,
                        orderFrequency: orderFrequency,
                      }}
                      orderChartRef={orderChartRef}
                      revenueChartRef={revenueChartRef}
                      menuChartRef={menuChartRef}
                    />
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="metrics">
          <TabsList className="mb-6">
            <TabsTrigger value="metrics">Key Metrics</TabsTrigger>
            <TabsTrigger value="orders">Orders</TabsTrigger>
            <TabsTrigger value="revenue">Revenue</TabsTrigger>
            <TabsTrigger value="menu">Popular Items</TabsTrigger>
            <TabsTrigger value="reservations">Reservations</TabsTrigger>
            <TabsTrigger value="rewards">Rewards</TabsTrigger>
            <TabsTrigger
              value="forecasting"
              className="flex items-center gap-1"
            >
              <TrendingUp className="h-4 w-4" />
              <span>Forecasting</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="metrics" className="space-y-6">
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              {/* Average Order Value Card */}
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">
                    Average Order Value
                  </CardTitle>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    className="h-4 w-4 text-muted-foreground"
                  >
                    <path d="M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6" />
                  </svg>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {averageOrderValue.toFixed(2)} AZN
                  </div>
                  {comparisonMode && (
                    <p
                      className={`text-xs ${
                        averageOrderValue > prevAverageOrderValue
                          ? "text-green-500"
                          : "text-red-500"
                      }`}
                    >
                      {averageOrderValue > prevAverageOrderValue ? "↑" : "↓"}
                      {Math.abs(
                        ((averageOrderValue - prevAverageOrderValue) /
                          (prevAverageOrderValue || 1)) *
                          100
                      ).toFixed(1)}
                      % from previous {timeRange}
                    </p>
                  )}
                  <p className="text-xs text-muted-foreground">
                    Average amount spent per order
                  </p>
                </CardContent>
              </Card>

              {/* Customer Retention Rate Card */}
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">
                    Customer Retention
                  </CardTitle>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    className="h-4 w-4 text-muted-foreground"
                  >
                    <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
                    <circle cx="9" cy="7" r="4" />
                    <path d="M22 21v-2a4 4 0 0 0-3-3.87" />
                    <path d="M16 3.13a4 4 0 0 1 0 7.75" />
                  </svg>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {customerRetentionRate.toFixed(1)}%
                  </div>
                  {comparisonMode && (
                    <p
                      className={`text-xs ${
                        customerRetentionRate > prevCustomerRetentionRate
                          ? "text-green-500"
                          : "text-red-500"
                      }`}
                    >
                      {customerRetentionRate > prevCustomerRetentionRate
                        ? "↑"
                        : "↓"}
                      {Math.abs(
                        customerRetentionRate - prevCustomerRetentionRate
                      ).toFixed(1)}
                      % from previous {timeRange}
                    </p>
                  )}
                  <p className="text-xs text-muted-foreground">
                    Percentage of returning customers
                  </p>
                </CardContent>
              </Card>

              {/* Order Frequency Card */}
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">
                    Order Frequency
                  </CardTitle>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    className="h-4 w-4 text-muted-foreground"
                  >
                    <rect width="20" height="14" x="2" y="5" rx="2" />
                    <path d="M2 10h20" />
                  </svg>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {orderFrequency.toFixed(1)}
                  </div>
                  {comparisonMode && (
                    <p
                      className={`text-xs ${
                        orderFrequency > prevOrderFrequency
                          ? "text-green-500"
                          : "text-red-500"
                      }`}
                    >
                      {orderFrequency > prevOrderFrequency ? "↑" : "↓"}
                      {Math.abs(
                        ((orderFrequency - prevOrderFrequency) /
                          (prevOrderFrequency || 1)) *
                          100
                      ).toFixed(1)}
                      % from previous {timeRange}
                    </p>
                  )}
                  <p className="text-xs text-muted-foreground">
                    Average orders per customer
                  </p>
                </CardContent>
              </Card>

              {/* Revenue Growth Card */}
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">
                    Revenue Growth
                  </CardTitle>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    className="h-4 w-4 text-muted-foreground"
                  >
                    <path d="M22 12h-4l-3 9L9 3l-3 9H2" />
                  </svg>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {revenueGrowth.toFixed(1)}%
                  </div>
                  <p
                    className={`text-xs ${
                      revenueGrowth >= 0 ? "text-green-500" : "text-red-500"
                    }`}
                  >
                    {revenueGrowth >= 0 ? "↑" : "↓"} Compared to previous{" "}
                    {timeRange}
                  </p>
                  <p className="text-xs text-muted-foreground">
                    Change in total revenue
                  </p>
                </CardContent>
              </Card>
            </div>

            {/* Customer Segments Chart */}
            <Card>
              <CardHeader>
                <CardTitle>Customer Segments</CardTitle>
                <CardDescription>
                  Breakdown of new vs. returning customers
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  {customerMetrics.length > 0 ? (
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={customerMetrics}
                          cx="50%"
                          cy="50%"
                          labelLine={true}
                          label={({ name, percent }) =>
                            `${name}: ${(percent * 100).toFixed(0)}%`
                          }
                          outerRadius={120}
                          fill="#8884d8"
                          dataKey="value"
                        >
                          <Cell fill="#0088FE" />
                          <Cell fill="#00C49F" />
                        </Pie>
                        <Tooltip
                          formatter={(value) => [`${value} customers`, "Count"]}
                        />
                        <Legend />
                      </PieChart>
                    </ResponsiveContainer>
                  ) : (
                    <div className="flex items-center justify-center h-full text-muted-foreground">
                      No customer data available for the selected time period
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Top Customers Table */}
            <Card>
              <CardHeader>
                <CardTitle>Top Customers</CardTitle>
                <CardDescription>
                  Your highest spending customers
                </CardDescription>
              </CardHeader>
              <CardContent>
                {topCustomers.length > 0 ? (
                  <div className="rounded-md border">
                    <table className="w-full">
                      <thead>
                        <tr className="bg-muted/50">
                          <th className="p-2 text-left font-medium">
                            Customer
                          </th>
                          <th className="p-2 text-left font-medium">Orders</th>
                          <th className="p-2 text-left font-medium">
                            Total Spent
                          </th>
                          <th className="p-2 text-left font-medium">
                            Avg. Order
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        {topCustomers.map((customer, index) => (
                          <tr
                            key={customer.userId}
                            className={index % 2 === 0 ? "bg-muted/20" : ""}
                          >
                            <td className="p-2">{customer.customerName}</td>
                            <td className="p-2">{customer.orderCount}</td>
                            <td className="p-2">
                              {customer.totalSpent.toFixed(2)} AZN
                            </td>
                            <td className="p-2">
                              {(
                                customer.totalSpent / customer.orderCount
                              ).toFixed(2)}{" "}
                              AZN
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                ) : (
                  <div className="flex items-center justify-center h-[200px] text-muted-foreground">
                    No customer data available for the selected time period
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="orders" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Order Trends</CardTitle>
                <CardDescription>Number of orders over time</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex justify-end mb-2 gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() =>
                      exportAsCSV(
                        orderData as unknown as Record<string, unknown>[],
                        ORDER_HEADERS,
                        ORDER_LABELS,
                        "order_trends"
                      )
                    }
                  >
                    <FileSpreadsheet className="h-4 w-4 mr-2" />
                    CSV
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() =>
                      exportAsPDF(
                        orderData as unknown as Record<string, unknown>[],
                        ORDER_HEADERS,
                        ORDER_LABELS,
                        "order_trends",
                        "Order Trends",
                        `${
                          timeRange.charAt(0).toUpperCase() + timeRange.slice(1)
                        } Report`
                      )
                    }
                  >
                    <FileText className="h-4 w-4 mr-2" />
                    PDF
                  </Button>
                </div>
                <div className="h-[400px]" ref={orderChartRef}>
                  {orderData.length > 0 ? (
                    <ResponsiveContainer width="100%" height="100%">
                      <LineChart
                        data={orderData}
                        margin={{
                          top: 5,
                          right: 30,
                          left: 20,
                          bottom: 5,
                        }}
                      >
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="date" />
                        <YAxis />
                        <Tooltip
                          formatter={(value, name) => {
                            if (name === "Orders (Previous)") {
                              return [`${value} (Previous)`, "Orders"];
                            }
                            return [value, name];
                          }}
                          labelFormatter={(label) => `Date: ${label}`}
                        />
                        <Legend />
                        <Line
                          type="monotone"
                          dataKey="count"
                          name="Orders"
                          stroke="#8884d8"
                          activeDot={{ r: 8 }}
                          strokeWidth={2}
                        />
                        {comparisonMode && prevOrderData.length > 0 && (
                          <Line
                            type="monotone"
                            data={prevOrderData.map((item) => ({
                              ...item,
                              date:
                                orderData.find(
                                  (_, i) => i === prevOrderData.indexOf(item)
                                )?.date || item.date,
                            }))}
                            dataKey="count"
                            name="Orders (Previous)"
                            stroke="#82ca9d"
                            strokeDasharray="5 5"
                            activeDot={{ r: 6 }}
                          />
                        )}
                      </LineChart>
                    </ResponsiveContainer>
                  ) : (
                    <div className="flex items-center justify-center h-full text-muted-foreground">
                      No order data available for the selected time period
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="revenue" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Revenue Analysis</CardTitle>
                <CardDescription>Revenue trends over time</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex justify-end mb-2 gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() =>
                      exportAsCSV(
                        revenueData as unknown as Record<string, unknown>[],
                        ORDER_HEADERS,
                        ORDER_LABELS,
                        "revenue_analysis"
                      )
                    }
                  >
                    <FileSpreadsheet className="h-4 w-4 mr-2" />
                    CSV
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() =>
                      exportAsPDF(
                        revenueData as unknown as Record<string, unknown>[],
                        ORDER_HEADERS,
                        ORDER_LABELS,
                        "revenue_analysis",
                        "Revenue Analysis",
                        `${
                          timeRange.charAt(0).toUpperCase() + timeRange.slice(1)
                        } Report`
                      )
                    }
                  >
                    <FileText className="h-4 w-4 mr-2" />
                    PDF
                  </Button>
                </div>
                <div className="h-[400px]" ref={revenueChartRef}>
                  {revenueData.length > 0 ? (
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart
                        data={revenueData}
                        margin={{
                          top: 5,
                          right: 30,
                          left: 20,
                          bottom: 5,
                        }}
                      >
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="date" />
                        <YAxis />
                        <Tooltip
                          formatter={(value, name) => {
                            if (name === "Previous Revenue (AZN)") {
                              return [`${value} AZN (Previous)`, "Revenue"];
                            }
                            return [
                              `${value} AZN`,
                              name === "Revenue (AZN)" ? "Revenue" : name,
                            ];
                          }}
                          labelFormatter={(label) => `Date: ${label}`}
                        />
                        <Legend />
                        <Bar
                          dataKey="revenue"
                          name="Revenue (AZN)"
                          fill="#82ca9d"
                        />
                        {comparisonMode && prevRevenueData.length > 0 && (
                          <Bar
                            dataKey="revenue"
                            name="Previous Revenue (AZN)"
                            fill="#8884d8"
                            // Convert data to the format expected by Recharts
                            // We need to use the same data array but override the dataKey
                            // This is a workaround for Recharts type issues
                          />
                        )}
                      </BarChart>
                    </ResponsiveContainer>
                  ) : (
                    <div className="flex items-center justify-center h-full text-muted-foreground">
                      No revenue data available for the selected time period
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="menu" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Popular Menu Items</CardTitle>
                <CardDescription>Most ordered items</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex justify-end mb-2 gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() =>
                      exportAsCSV(
                        menuItemData as unknown as Record<string, unknown>[],
                        MENU_ITEM_HEADERS,
                        MENU_ITEM_LABELS,
                        "popular_items"
                      )
                    }
                  >
                    <FileSpreadsheet className="h-4 w-4 mr-2" />
                    CSV
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() =>
                      exportAsPDF(
                        menuItemData as unknown as Record<string, unknown>[],
                        MENU_ITEM_HEADERS,
                        MENU_ITEM_LABELS,
                        "popular_items",
                        "Popular Menu Items"
                      )
                    }
                  >
                    <FileText className="h-4 w-4 mr-2" />
                    PDF
                  </Button>
                </div>
                <div className="h-[400px]" ref={menuChartRef}>
                  {menuItemData.length > 0 ? (
                    <ResponsiveContainer width="100%" height="100%">
                      {comparisonMode && prevMenuItemData.length > 0 ? (
                        // Combined chart for comparison mode
                        <BarChart
                          data={menuItemData.map((item) => {
                            const prevItem = prevMenuItemData.find(
                              (p) => p.name === item.name
                            );
                            return {
                              ...item,
                              prevCount: prevItem ? prevItem.count : 0,
                            };
                          })}
                          layout="vertical"
                          margin={{
                            top: 5,
                            right: 30,
                            left: 100,
                            bottom: 5,
                          }}
                        >
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis type="number" />
                          <YAxis
                            type="category"
                            dataKey="name"
                            tick={{ fontSize: 12 }}
                          />
                          <Tooltip
                            formatter={(value, name) => {
                              if (name === "Previous Period") {
                                return [`${value} orders (Previous)`, name];
                              }
                              return [`${value} orders`, name];
                            }}
                          />
                          <Legend />
                          <Bar
                            dataKey="count"
                            name="Current Period"
                            fill="#8884d8"
                          />
                          <Bar
                            dataKey="prevCount"
                            name="Previous Period"
                            fill="#82ca9d"
                          />
                        </BarChart>
                      ) : (
                        // Regular chart for non-comparison mode
                        <BarChart
                          data={menuItemData}
                          layout="vertical"
                          margin={{
                            top: 5,
                            right: 30,
                            left: 100,
                            bottom: 5,
                          }}
                        >
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis type="number" />
                          <YAxis
                            type="category"
                            dataKey="name"
                            tick={{ fontSize: 12 }}
                          />
                          <Tooltip
                            formatter={(value) => [`${value} orders`, "Orders"]}
                          />
                          <Legend />
                          <Bar dataKey="count" name="Orders" fill="#8884d8" />
                        </BarChart>
                      )}
                    </ResponsiveContainer>
                  ) : (
                    <div className="flex items-center justify-center h-full text-muted-foreground">
                      No menu item data available
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="reservations" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Reservation Status</CardTitle>
                <CardDescription>
                  Distribution of reservation statuses
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex justify-end mb-2 gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() =>
                      exportAsCSV(
                        reservationData as unknown as Record<string, unknown>[],
                        RESERVATION_HEADERS,
                        RESERVATION_LABELS,
                        "reservation_status"
                      )
                    }
                  >
                    <FileSpreadsheet className="h-4 w-4 mr-2" />
                    CSV
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() =>
                      exportAsPDF(
                        reservationData as unknown as Record<string, unknown>[],
                        RESERVATION_HEADERS,
                        RESERVATION_LABELS,
                        "reservation_status",
                        "Reservation Status"
                      )
                    }
                  >
                    <FileText className="h-4 w-4 mr-2" />
                    PDF
                  </Button>
                </div>
                <div className="h-[400px]" ref={reservationChartRef}>
                  {reservationData.length > 0 &&
                  reservationData.some((item) => item.value > 0) ? (
                    <ResponsiveContainer width="100%" height="100%">
                      {comparisonMode &&
                      prevReservationData.length > 0 &&
                      prevReservationData.some((item) => item.value > 0) ? (
                        // Comparison view with bar chart
                        <BarChart
                          data={reservationData.map((item) => {
                            const prevItem = prevReservationData.find(
                              (p) => p.name === item.name
                            );
                            return {
                              name: item.name,
                              current: item.value,
                              previous: prevItem ? prevItem.value : 0,
                            };
                          })}
                          margin={{
                            top: 20,
                            right: 30,
                            left: 20,
                            bottom: 5,
                          }}
                        >
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="name" />
                          <YAxis />
                          <Tooltip
                            formatter={(value, name) => {
                              if (name === "Previous Period") {
                                return [
                                  `${value} reservations (Previous)`,
                                  name,
                                ];
                              }
                              return [`${value} reservations`, name];
                            }}
                          />
                          <Legend />
                          <Bar
                            dataKey="current"
                            name="Current Period"
                            fill="#8884d8"
                          />
                          <Bar
                            dataKey="previous"
                            name="Previous Period"
                            fill="#82ca9d"
                          />
                        </BarChart>
                      ) : (
                        // Regular pie chart for non-comparison mode
                        <PieChart>
                          <Pie
                            data={reservationData}
                            cx="50%"
                            cy="50%"
                            labelLine={true}
                            label={({ name, percent }) =>
                              `${name}: ${(percent * 100).toFixed(0)}%`
                            }
                            outerRadius={150}
                            fill="#8884d8"
                            dataKey="value"
                          >
                            {reservationData.map((_, index) => (
                              <Cell
                                key={`cell-${index}`}
                                fill={COLORS[index % COLORS.length]}
                              />
                            ))}
                          </Pie>
                          <Tooltip
                            formatter={(value, name) => [
                              `${value} reservations`,
                              name,
                            ]}
                          />
                          <Legend />
                        </PieChart>
                      )}
                    </ResponsiveContainer>
                  ) : (
                    <div className="flex items-center justify-center h-full text-muted-foreground">
                      No reservation data available for the selected time period
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="rewards" className="space-y-6">
            <RewardAnalytics
              restaurantId={localStorage.getItem("restaurantId") || ""}
            />
          </TabsContent>

          <TabsContent value="forecasting" className="space-y-6">
            <ForecastingTab
              orders={orders}
              reservations={reservations}
              parseOrderDate={parseOrderDate}
              parseReservationDate={parseReservationDate}
            />
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};
