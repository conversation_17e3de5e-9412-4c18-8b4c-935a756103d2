import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { format, isPast, addMinutes } from "date-fns";
import { tr } from "date-fns/locale";
import { Order, ClientDetails } from "@/types/dashboard";
import { useEffect, useRef, useState } from "react";
import { Printer, Calendar, Clock, Copy, Check } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { ScheduledOrderCountdown } from "@/components/ui/scheduled-order-countdown";
import { CouponUsageDisplay } from "@/components/orders/CouponUsageDisplay";

interface OrdersTabProps {
  orders: Order[];
  userRole: "client" | "restaurant" | null;
  customerDetails: { [key: string]: ClientDetails };
  updateOrderStatus: (
    orderId: string,
    newStatus: Order["status"]
  ) => Promise<void>;
  highlightedOrderId?: string | null;
  onPrintReceipt: (order: Order) => void;
}

const CustomerProfileDialog = ({
  customerId,
  customerDetails,
  orders,
  open,
  onOpenChange,
}: {
  customerId: string;
  customerDetails: { [key: string]: ClientDetails };
  orders: Order[];
  open: boolean;
  onOpenChange: (open: boolean) => void;
}) => {
  const customerOrders = orders.filter((order) => order.userId === customerId);
  const customer = customerDetails[customerId];

  // Calculate customer statistics
  const totalSpent = customerOrders.reduce(
    (sum, order) =>
      sum + (typeof order.totalPrice === "number" ? order.totalPrice : 0),
    0
  );
  const averageOrderValue =
    customerOrders.length > 0 ? totalSpent / customerOrders.length : 0;
  const favoriteItems = customerOrders
    .flatMap((order) => order.items)
    .reduce((acc, item) => {
      acc[item.name] = (acc[item.name] || 0) + item.quantity;
      return acc;
    }, {} as Record<string, number>);

  const topItems = Object.entries(favoriteItems)
    .sort(([, a], [, b]) => b - a)
    .slice(0, 5);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Customer Profile</DialogTitle>
          <DialogDescription>
            Detailed information about {customer?.firstName}{" "}
            {customer?.lastName}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Customer Details */}
          <div className="grid grid-cols-2 gap-4 p-4 bg-muted/50 rounded-lg">
            <div>
              <h3 className="font-semibold">Contact Information</h3>
              <p className="text-sm text-muted-foreground mt-2">
                Name: {customer?.firstName} {customer?.lastName}
              </p>
              {/* Phone and username removed */}
            </div>
            <div>
              <h3 className="font-semibold">Order Statistics</h3>
              <p className="text-sm text-muted-foreground mt-2">
                Total Orders: {customerOrders.length}
              </p>
              <p className="text-sm text-muted-foreground">
                Total Spent: {totalSpent.toFixed(2)} AZN
              </p>
              <p className="text-sm text-muted-foreground">
                Average Order: {averageOrderValue.toFixed(2)} AZN
              </p>
            </div>
          </div>

          {/* Favorite Items */}
          <div>
            <h3 className="font-semibold mb-3">Most Ordered Items</h3>
            <div className="space-y-2">
              {topItems.map(([itemName, quantity]) => (
                <div
                  key={itemName}
                  className="flex justify-between items-center p-2 bg-muted/30 rounded"
                >
                  <span>{itemName}</span>
                  <Badge variant="secondary">{quantity}x ordered</Badge>
                </div>
              ))}
            </div>
          </div>

          {/* Order History */}
          <div>
            <h3 className="font-semibold mb-3">Order History</h3>
            <div className="space-y-4">
              {customerOrders.map((order) => (
                <div
                  key={order.orderId}
                  className="border p-4 rounded-lg space-y-2"
                >
                  <div className="flex justify-between items-start">
                    <div>
                      <p className="font-medium">
                        Order #{order.orderId.slice(0, 8)}
                      </p>
                      <p className="text-sm text-muted-foreground">
                        {(() => {
                          try {
                            let dateObj;
                            if (order.orderDate instanceof Date) {
                              dateObj = order.orderDate;
                            } else if (
                              typeof order.orderDate === "object" &&
                              order.orderDate !== null
                            ) {
                              // Check for Firestore Timestamp with toDate method
                              const timestampWithMethod = order.orderDate as {
                                toDate?: () => Date;
                              };
                              if (
                                typeof timestampWithMethod.toDate === "function"
                              ) {
                                dateObj = timestampWithMethod.toDate();
                              } else {
                                // Check for Firestore Timestamp-like object
                                const timestamp = order.orderDate as {
                                  seconds?: number;
                                  nanoseconds?: number;
                                };
                                if (
                                  timestamp.seconds &&
                                  timestamp.nanoseconds
                                ) {
                                  // Handle Firestore timestamp format
                                  dateObj = new Date(timestamp.seconds * 1000);
                                } else {
                                  // Try to convert other objects to date
                                  try {
                                    dateObj = new Date(
                                      order.orderDate as unknown as
                                        | string
                                        | number
                                    );
                                  } catch {
                                    return "Invalid date";
                                  }
                                }
                              }
                            } else if (typeof order.orderDate === "string") {
                              dateObj = new Date(order.orderDate);
                            } else if (typeof order.orderDate === "number") {
                              dateObj = new Date(order.orderDate);
                            } else {
                              return "Invalid date";
                            }

                            // Validate the date
                            if (isNaN(dateObj.getTime())) {
                              return "Invalid date";
                            }

                            return format(dateObj, "PPPp", { locale: tr });
                          } catch (error) {
                            console.error("Date formatting error:", error);
                            return "Invalid date";
                          }
                        })()}
                      </p>
                    </div>
                    <Badge>{order.status}</Badge>
                  </div>
                  <div>
                    <p className="text-sm font-medium">Items:</p>
                    <ul className="text-sm text-muted-foreground">
                      {order.items.map((item, index) => (
                        <li key={index} className="flex justify-between">
                          <span>
                            {item.quantity}x {item.name}
                          </span>
                          <span>
                            {typeof item.price === "number"
                              ? (item.price * item.quantity).toFixed(2)
                              : item.price * item.quantity}{" "}
                            AZN
                          </span>
                        </li>
                      ))}
                    </ul>
                  </div>
                  <div className="flex justify-between items-center pt-2 border-t">
                    <span className="text-sm">Table: {order.tableName}</span>
                    <span className="font-medium">
                      Total:{" "}
                      {order.totalPrice ? order.totalPrice.toFixed(2) : "0.00"}{" "}
                      AZN
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export const OrdersTab = ({
  orders,
  userRole,
  customerDetails,
  updateOrderStatus,
  highlightedOrderId,
  onPrintReceipt,
}: OrdersTabProps) => {
  const highlightedRowRef = useRef<HTMLTableRowElement>(null);
  const [selectedCustomerId, setSelectedCustomerId] = useState<string | null>(
    null
  );
  const [copiedOrderId, setCopiedOrderId] = useState<string | null>(null);

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text).then(() => {
      setCopiedOrderId(text);
      setTimeout(() => setCopiedOrderId(null), 2000);
    });
  };
  const [selectedOrderForDetails, setSelectedOrderForDetails] = useState<
    string | null
  >(null);
  const [activeTab, setActiveTab] = useState<string>("all");

  // Filter orders based on the active tab
  const filteredOrders = orders.filter((order) => {
    if (activeTab === "all") return true;
    if (activeTab === "scheduled") return order.isScheduled;
    if (activeTab === "regular") return !order.isScheduled;
    return true;
  });

  // Sort orders: scheduled future orders first, then by date
  const sortedOrders = [...filteredOrders].sort((a, b) => {
    // First sort by scheduled status
    if (a.isScheduled && !b.isScheduled) return -1;
    if (!a.isScheduled && b.isScheduled) return 1;

    // If both are scheduled, sort by scheduled date
    if (a.isScheduled && b.isScheduled && a.scheduledFor && b.scheduledFor) {
      const aDate = a.scheduledFor.toDate();
      const bDate = b.scheduledFor.toDate();

      // Future scheduled orders first
      const now = new Date();
      const aIsFuture = aDate > now;
      const bIsFuture = bDate > now;

      if (aIsFuture && !bIsFuture) return -1;
      if (!aIsFuture && bIsFuture) return 1;

      // Then by date (ascending for future, descending for past)
      if (aIsFuture && bIsFuture) {
        return aDate.getTime() - bDate.getTime(); // Ascending for future
      } else {
        return bDate.getTime() - aDate.getTime(); // Descending for past
      }
    }

    // Otherwise sort by order date (most recent first)
    const aDate = a.orderDate.toDate();
    const bDate = b.orderDate.toDate();
    return bDate.getTime() - aDate.getTime();
  });

  // Count upcoming scheduled orders
  const upcomingScheduledOrdersCount = orders.filter(
    (order) =>
      order.isScheduled &&
      order.scheduledFor &&
      !isPast(order.scheduledFor.toDate()) &&
      order.status === "pending"
  ).length;

  useEffect(() => {
    if (highlightedOrderId && highlightedRowRef.current) {
      highlightedRowRef.current.scrollIntoView({
        behavior: "smooth",
        block: "center",
      });
    }
  }, [highlightedOrderId]);

  return (
    <Card>
      <CardHeader>
        <CardTitle>
          {userRole === "client" ? "My Orders" : "Restaurant Orders"}
        </CardTitle>
        <CardDescription>
          {userRole === "client"
            ? "View your past and scheduled orders."
            : "Manage your restaurant's orders."}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs
          defaultValue="all"
          value={activeTab}
          onValueChange={setActiveTab}
          className="mb-6"
        >
          <TabsList>
            <TabsTrigger value="all">All Orders</TabsTrigger>
            <TabsTrigger value="regular">Regular Orders</TabsTrigger>
            <TabsTrigger value="scheduled" className="relative">
              Scheduled Orders
              {upcomingScheduledOrdersCount > 0 && (
                <span className="absolute -top-1 -right-1 bg-primary text-primary-foreground text-xs rounded-full w-5 h-5 flex items-center justify-center">
                  {upcomingScheduledOrdersCount}
                </span>
              )}
            </TabsTrigger>
          </TabsList>
        </Tabs>

        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[100px]">Order ID</TableHead>
              {userRole === "client" && <TableHead>Restaurant</TableHead>}
              <TableHead>Date</TableHead>
              <TableHead>Total</TableHead>
              <TableHead>Table</TableHead>
              <TableHead>Status</TableHead>
              {userRole === "restaurant" && <TableHead>Customer</TableHead>}
              <TableHead>Actions</TableHead>
              <TableHead>Details</TableHead>
              <TableHead>Print</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {sortedOrders.map((order) => (
              <TableRow
                key={order.orderId}
                ref={
                  order.orderId === highlightedOrderId
                    ? highlightedRowRef
                    : null
                }
                className={
                  order.orderId === highlightedOrderId
                    ? "bg-primary/10 animate-pulse"
                    : ""
                }
              >
                <TableCell>{order.orderId}</TableCell>
                {userRole === "client" && (
                  <TableCell>{order.restaurantName}</TableCell>
                )}
                <TableCell>
                  <div>
                    {(() => {
                      try {
                        let dateObj;
                        if (order.orderDate instanceof Date) {
                          dateObj = order.orderDate;
                        } else if (
                          typeof order.orderDate === "object" &&
                          order.orderDate !== null
                        ) {
                          // Check for Firestore Timestamp with toDate method
                          const timestampWithMethod = order.orderDate as {
                            toDate?: () => Date;
                          };
                          if (
                            typeof timestampWithMethod.toDate === "function"
                          ) {
                            dateObj = timestampWithMethod.toDate();
                          } else {
                            // Check for Firestore Timestamp-like object
                            const timestamp = order.orderDate as {
                              seconds?: number;
                              nanoseconds?: number;
                            };
                            if (timestamp.seconds && timestamp.nanoseconds) {
                              // Handle Firestore timestamp format
                              dateObj = new Date(timestamp.seconds * 1000);
                            } else {
                              // Try to convert other objects to date
                              try {
                                dateObj = new Date(
                                  order.orderDate as unknown as string | number
                                );
                              } catch {
                                return "Invalid date";
                              }
                            }
                          }
                        } else if (typeof order.orderDate === "string") {
                          dateObj = new Date(order.orderDate);
                        } else if (typeof order.orderDate === "number") {
                          dateObj = new Date(order.orderDate);
                        } else {
                          return "Invalid date";
                        }

                        // Validate the date
                        if (isNaN(dateObj.getTime())) {
                          return "Invalid date";
                        }

                        return format(dateObj, "PPP", { locale: tr });
                      } catch (error) {
                        console.error("Date formatting error:", error);
                        return "Invalid date";
                      }
                    })()}
                  </div>

                  {/* Show scheduled date if this is a scheduled order */}
                  {order.isScheduled && order.scheduledFor && (
                    <div className="mt-1">
                      {order.status === "pending" &&
                      !isPast(order.scheduledFor.toDate()) ? (
                        <ScheduledOrderCountdown
                          scheduledTime={order.scheduledFor.toDate()}
                        />
                      ) : (
                        <div className="flex items-center gap-1 text-xs text-muted-foreground">
                          <Clock className="h-3 w-3" />
                          <span>
                            Scheduled:{" "}
                            {format(order.scheduledFor.toDate(), "PPP 'at' p", {
                              locale: tr,
                            })}
                          </span>
                        </div>
                      )}
                    </div>
                  )}
                </TableCell>
                <TableCell>
                  {typeof order.totalPrice === "number"
                    ? order.totalPrice.toFixed(2)
                    : order.totalPrice}{" "}
                  AZN
                </TableCell>
                <TableCell>{order.tableName || "Not assigned"}</TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <span>{order.status}</span>
                    {order.isScheduled && (
                      <Badge
                        variant="outline"
                        className="flex items-center gap-1 text-xs"
                      >
                        <Calendar className="h-3 w-3" />
                        Scheduled
                      </Badge>
                    )}
                  </div>
                </TableCell>
                {userRole === "restaurant" && (
                  <TableCell>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setSelectedCustomerId(order.userId)}
                      className="flex items-center gap-2"
                    >
                      {customerDetails[order.userId] ? (
                        <>
                          {customerDetails[order.userId].firstName}{" "}
                          {customerDetails[order.userId].lastName}
                        </>
                      ) : (
                        "Loading..."
                      )}
                    </Button>
                  </TableCell>
                )}
                <TableCell>
                  {/* Always show action buttons regardless of userRole */}
                  {order.status === "pending" && (
                    <>
                      {order.tableId ? (
                        <>
                          {/* Accept Order button is always available */}
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() =>
                              updateOrderStatus(order.orderId, "preparing")
                            }
                            className="mr-2"
                          >
                            Accept Order
                          </Button>
                          <Button
                            variant="destructive"
                            size="sm"
                            onClick={() =>
                              updateOrderStatus(order.orderId, "cancelled")
                            }
                          >
                            Cancel
                          </Button>
                        </>
                      ) : (
                        <Button
                          variant="destructive"
                          size="sm"
                          onClick={() =>
                            updateOrderStatus(order.orderId, "cancelled")
                          }
                        >
                          No Tables Available
                        </Button>
                      )}
                    </>
                  )}
                  {order.status === "preparing" && (
                    <>
                      {/* Check if this is a scheduled order that's not ready to be processed */}
                      {order.isScheduled &&
                      order.scheduledFor &&
                      isPast(addMinutes(order.scheduledFor.toDate(), -10)) ===
                        false ? (
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <div className="inline-flex">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  className="opacity-70"
                                  disabled
                                >
                                  <span className="flex items-center gap-1">
                                    <Clock className="h-3 w-3" />
                                    Mark as Ready
                                  </span>
                                </Button>
                              </div>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p className="max-w-xs">
                                This is a scheduled order for{" "}
                                {format(
                                  order.scheduledFor.toDate(),
                                  "PPP 'at' p"
                                )}
                                . You can mark it as ready 10 minutes before the
                                scheduled time.
                              </p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      ) : (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() =>
                            updateOrderStatus(order.orderId, "ready")
                          }
                        >
                          Mark as Ready
                        </Button>
                      )}
                    </>
                  )}
                  {order.status === "ready" && (
                    <>
                      {/* Check if this is a scheduled order that's not ready to be processed */}
                      {order.isScheduled &&
                      order.scheduledFor &&
                      isPast(addMinutes(order.scheduledFor.toDate(), -10)) ===
                        false ? (
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <div className="inline-flex">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  className="opacity-70"
                                  disabled
                                >
                                  <span className="flex items-center gap-1">
                                    <Clock className="h-3 w-3" />
                                    Complete Order
                                  </span>
                                </Button>
                              </div>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p className="max-w-xs">
                                This is a scheduled order for{" "}
                                {format(
                                  order.scheduledFor.toDate(),
                                  "PPP 'at' p"
                                )}
                                . You can complete it 10 minutes before the
                                scheduled time.
                              </p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      ) : (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() =>
                            updateOrderStatus(order.orderId, "completed")
                          }
                        >
                          Complete Order
                        </Button>
                      )}
                    </>
                  )}
                </TableCell>
                <TableCell>
                  <Dialog
                    open={selectedOrderForDetails === order.orderId}
                    onOpenChange={(open) =>
                      setSelectedOrderForDetails(open ? order.orderId : null)
                    }
                  >
                    <DialogTrigger asChild>
                      <Button variant="ghost" size="sm">
                        View Details
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="sm:max-w-md w-[95vw] max-w-[450px]">
                      <DialogHeader>
                        <DialogTitle>Order Details</DialogTitle>
                        <DialogDescription>
                          <div className="flex items-start gap-2">
                            <div>
                              <span className="font-medium">Order ID:</span>{" "}
                              <span className="break-all text-xs block mt-1 max-w-[350px]">
                                {order.orderId}
                              </span>
                            </div>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-7 px-2 mt-1"
                              onClick={(e) => {
                                e.preventDefault();
                                copyToClipboard(order.orderId);
                              }}
                            >
                              {copiedOrderId === order.orderId ? (
                                <Check className="h-3.5 w-3.5 text-green-500" />
                              ) : (
                                <Copy className="h-3.5 w-3.5" />
                              )}
                            </Button>
                          </div>
                        </DialogDescription>
                      </DialogHeader>
                      <div className="space-y-4">
                        <div>
                          <strong>
                            {userRole === "client"
                              ? "Restaurant:"
                              : "Customer:"}
                          </strong>{" "}
                          {userRole === "client"
                            ? order.restaurantName
                            : customerDetails[order.userId]
                            ? `${customerDetails[order.userId].firstName} ${
                                customerDetails[order.userId].lastName
                              }`
                            : "Loading customer details..."}
                        </div>
                        {/* Phone number removed */}
                        <div>
                          <strong>Date:</strong>{" "}
                          {(() => {
                            try {
                              let dateObj;
                              if (order.orderDate instanceof Date) {
                                dateObj = order.orderDate;
                              } else if (
                                typeof order.orderDate === "object" &&
                                order.orderDate !== null
                              ) {
                                // Check for Firestore Timestamp with toDate method
                                const timestampWithMethod = order.orderDate as {
                                  toDate?: () => Date;
                                };
                                if (
                                  typeof timestampWithMethod.toDate ===
                                  "function"
                                ) {
                                  dateObj = timestampWithMethod.toDate();
                                } else {
                                  // Check for Firestore Timestamp-like object
                                  const timestamp = order.orderDate as {
                                    seconds?: number;
                                    nanoseconds?: number;
                                  };
                                  if (
                                    timestamp.seconds &&
                                    timestamp.nanoseconds
                                  ) {
                                    // Handle Firestore timestamp format
                                    dateObj = new Date(
                                      timestamp.seconds * 1000
                                    );
                                  } else {
                                    // Try to convert other objects to date
                                    try {
                                      dateObj = new Date(
                                        order.orderDate as unknown as
                                          | string
                                          | number
                                      );
                                    } catch {
                                      return "Invalid date";
                                    }
                                  }
                                }
                              } else if (typeof order.orderDate === "string") {
                                dateObj = new Date(order.orderDate);
                              } else if (typeof order.orderDate === "number") {
                                dateObj = new Date(order.orderDate);
                              } else {
                                return "Invalid date";
                              }

                              // Validate the date
                              if (isNaN(dateObj.getTime())) {
                                return "Invalid date";
                              }

                              return format(dateObj, "PPPpp", { locale: tr });
                            } catch (error) {
                              console.error("Date formatting error:", error);
                              return "Invalid date";
                            }
                          })()}
                        </div>
                        <div>
                          <strong>Table:</strong>{" "}
                          {order.tableName || "Not assigned"}
                        </div>
                        <div>
                          <strong>Status:</strong> {order.status}
                        </div>

                        {/* Scheduled Order Information */}
                        {order.isScheduled && order.scheduledFor && (
                          <div className="mt-2 p-3 bg-muted/30 rounded-md border">
                            <div className="flex items-center gap-2 mb-2">
                              <Calendar className="h-4 w-4 text-primary" />
                              <strong>Scheduled Order</strong>
                            </div>
                            <div className="flex items-center gap-2 text-sm">
                              <Clock className="h-4 w-4 text-muted-foreground" />
                              <span>
                                {format(
                                  order.scheduledFor.toDate(),
                                  "PPP 'at' p",
                                  { locale: tr }
                                )}
                              </span>
                            </div>
                            <p className="text-xs text-muted-foreground mt-2">
                              {isPast(order.scheduledFor.toDate())
                                ? "This order has been processed"
                                : "This order will be processed at the scheduled time"}
                            </p>
                          </div>
                        )}
                        <div>
                          <strong>Items:</strong>
                          <ul className="mt-2 space-y-2">
                            {order.items.map((item, index) => (
                              <li
                                key={index}
                                className="flex justify-between items-center"
                              >
                                <span>
                                  {item.quantity}x {item.name}
                                </span>
                                <span>
                                  {typeof item.price === "number"
                                    ? (item.price * item.quantity).toFixed(2)
                                    : item.price * item.quantity}{" "}
                                  AZN
                                </span>
                              </li>
                            ))}
                          </ul>
                        </div>
                        {order.notes && (
                          <div>
                            <strong>Notes:</strong>
                            <p className="mt-1">{order.notes}</p>
                          </div>
                        )}

                        {/* Coupon Usage Display */}
                        <CouponUsageDisplay
                          originalAmount={order.originalAmount}
                          discountAmount={order.discountAmount}
                          appliedCouponCode={order.appliedCouponCode}
                          appliedRewardCode={order.appliedRewardCode}
                          affectedItemId={order.affectedItemId}
                          couponUsed={order.couponUsed}
                          totalPrice={
                            typeof order.totalPrice === "number"
                              ? order.totalPrice
                              : parseFloat(String(order.totalPrice))
                          }
                          orderItems={order.items?.map((item) => ({
                            itemId: item.itemId || item.id,
                            name: item.name,
                            price: item.price,
                            quantity: item.quantity,
                          }))}
                          className="my-3"
                        />

                        <div className="flex justify-between items-center font-semibold">
                          <span>Total:</span>
                          <span>
                            {typeof order.totalPrice === "number"
                              ? order.totalPrice.toFixed(2)
                              : order.totalPrice}{" "}
                            AZN
                          </span>
                        </div>
                      </div>
                    </DialogContent>
                  </Dialog>
                </TableCell>
                <TableCell>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onPrintReceipt(order)}
                  >
                    <Printer className="h-4 w-4" />
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>

      {selectedCustomerId && customerDetails[selectedCustomerId] && (
        <CustomerProfileDialog
          customerId={selectedCustomerId}
          customerDetails={customerDetails}
          orders={orders}
          open={!!selectedCustomerId}
          onOpenChange={(open) => !open && setSelectedCustomerId(null)}
        />
      )}
    </Card>
  );
};
