import React from "react";
import { Badge } from "@/components/ui/badge";
import { Coins } from "lucide-react";

interface PointsBalanceProps {
  points: number;
  className?: string;
  showIcon?: boolean;
  variant?: "default" | "outline" | "secondary";
  size?: "sm" | "default" | "lg";
}

export const PointsBalance: React.FC<PointsBalanceProps> = ({
  points,
  className = "",
  showIcon = true,
  variant = "default",
  size = "default",
}) => {
  const formatPoints = (points: number) => {
    if (points >= 1000) {
      return `${(points / 1000).toFixed(1)}k`;
    }
    return points.toString();
  };

  const getSizeClasses = () => {
    switch (size) {
      case "sm":
        return "text-xs px-2 py-1";
      case "lg":
        return "text-base px-3 py-2";
      default:
        return "text-sm px-2.5 py-1.5";
    }
  };

  return (
    <Badge
      variant={variant}
      className={`${getSizeClasses()} ${className} flex items-center gap-1.5 font-medium`}
    >
      {showIcon && <Coins className="h-3 w-3" />}
      <span>{formatPoints(points)} pts</span>
    </Badge>
  );
};
