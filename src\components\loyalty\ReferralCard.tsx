import React, { useState, useEffect } from "react";
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Skeleton } from "@/components/ui/skeleton";
import { LoyaltyStatus, Referral } from "@/types/loyalty";
import { loyaltyService } from "@/services/LoyaltyService";
import { useAuth } from "@/providers/AuthProvider";
import { Users, Copy, Check, Send, Share2 } from "lucide-react";
import { toast } from "sonner";
import { collection, query, getDocs, orderBy } from "firebase/firestore";
import { firestore } from "@/config/firebase";

export const ReferralCard: React.FC = () => {
  const { user } = useAuth();
  const [loyaltyStatus, setLoyaltyStatus] = useState<LoyaltyStatus | null>(
    null
  );
  const [referrals, setReferrals] = useState<Referral[]>([]);
  const [loading, setLoading] = useState(true);
  const [copied, setCopied] = useState(false);
  const [linkCopied, setLinkCopied] = useState(false);
  const [referralEmail, setReferralEmail] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [referralLink, setReferralLink] = useState("");

  // Create a function to fetch referrals that can be called multiple times
  const fetchReferrals = async (userId: string) => {
    try {
      // Fetch referrals
      const referralsQuery = query(
        collection(firestore, "clients", userId, "referrals"),
        orderBy("createdAt", "desc")
      );

      const referralsSnapshot = await getDocs(referralsQuery);
      const referralsData = referralsSnapshot.docs.map(
        (doc) => doc.data() as Referral
      );
      setReferrals(referralsData);
    } catch (error) {
      console.error("Error fetching referrals:", error);
    }
  };

  useEffect(() => {
    const fetchLoyaltyStatus = async () => {
      if (!user) return;

      setLoading(true);
      try {
        const status = await loyaltyService.getLoyaltyStatus(user.uid);
        setLoyaltyStatus(status);

        // Generate referral link if status exists
        if (status) {
          const link = loyaltyService.generateReferralLink(status.referralCode);
          setReferralLink(link);

          // Fetch referrals
          await fetchReferrals(user.uid);
        }
      } catch (error) {
        console.error("Error fetching loyalty status:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchLoyaltyStatus();

    // Set up an interval to refresh referrals every 30 seconds
    const refreshInterval = setInterval(() => {
      if (user) {
        fetchReferrals(user.uid);
      }
    }, 30000);

    // Clean up interval on component unmount
    return () => clearInterval(refreshInterval);
  }, [user]);

  // Helper function to safely copy text to clipboard with fallback
  const copyToClipboard = (text: string): boolean => {
    try {
      // Modern clipboard API
      if (navigator.clipboard && navigator.clipboard.writeText) {
        navigator.clipboard.writeText(text);
        return true;
      }

      // Fallback for older browsers
      const textArea = document.createElement("textarea");
      textArea.value = text;
      textArea.style.position = "fixed"; // Avoid scrolling to bottom
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();

      const successful = document.execCommand("copy");
      document.body.removeChild(textArea);
      return successful;
    } catch (err) {
      console.error("Failed to copy text: ", err);
      return false;
    }
  };

  const handleCopyReferralCode = () => {
    if (!loyaltyStatus) return;

    const success = copyToClipboard(loyaltyStatus.referralCode);
    if (success) {
      setCopied(true);
      toast.success("Referral code copied to clipboard!");

      setTimeout(() => {
        setCopied(false);
      }, 2000);
    } else {
      toast.error(
        "Failed to copy to clipboard. Please try manually selecting and copying the code."
      );
    }
  };

  const handleCopyReferralLink = () => {
    if (!referralLink) return;

    const success = copyToClipboard(referralLink);
    if (success) {
      setLinkCopied(true);
      toast.success("Referral link copied to clipboard!");

      setTimeout(() => {
        setLinkCopied(false);
      }, 2000);
    } else {
      toast.error(
        "Failed to copy to clipboard. Please try manually selecting and copying the link."
      );
    }
  };

  const handleShareReferralLink = async () => {
    if (!referralLink) return;

    try {
      // Check if Web Share API is available
      if (navigator.share) {
        try {
          await navigator.share({
            title: "Join Qonai with my referral link",
            text: "Use my referral link to join Qonai and we both get bonus points!",
            url: referralLink,
          });
          toast.success("Referral link shared successfully!");
          return;
        } catch (error) {
          console.error("Error sharing:", error);
          // Continue to fallback if sharing fails
        }
      }

      // Fallback to copy if sharing fails or is not available
      handleCopyReferralLink();
    } catch (error) {
      console.error("Error in share handler:", error);
      toast.error("Could not share the link. Please try copying it manually.");
    }
  };

  const handleSendReferral = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user || !loyaltyStatus || !referralEmail) return;

    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(referralEmail)) {
      toast.error("Please enter a valid email address");
      return;
    }

    setIsSubmitting(true);
    try {
      const referralId = await loyaltyService.createReferral(
        user.uid,
        referralEmail
      );

      if (referralId) {
        toast.success(`Referral invitation sent to ${referralEmail}`);
        setReferralEmail("");

        // Refresh referrals using our reusable function
        await fetchReferrals(user.uid);
      } else {
        toast.error(
          "This email has already been referred or is already a user"
        );
      }
    } catch (error) {
      console.error("Error sending referral:", error);
      toast.error("Failed to send referral. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>
            <Skeleton className="h-8 w-48" />
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Skeleton className="h-20 w-full" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-4 w-3/4" />
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!loyaltyStatus) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Users className="mr-2 h-5 w-5 text-primary" />
            Referrals
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground">
            Join our loyalty program to start referring friends and earning
            bonus points!
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Users className="mr-2 h-5 w-5 text-primary" />
          Refer Friends
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div>
            <p className="text-sm text-muted-foreground mb-2">
              Share your referral code or link with friends and earn{" "}
              {loyaltyService.pointsConfig.referralPoints} points for each
              friend who signs up! Your friend will also receive{" "}
              {loyaltyService.pointsConfig.referredUserPoints} bonus points.
            </p>

            <div className="space-y-4 mt-4">
              <div>
                <Label htmlFor="referral-code">Your Referral Code</Label>
                <div className="flex mt-1">
                  <Input
                    id="referral-code"
                    value={loyaltyStatus.referralCode}
                    readOnly
                    className="rounded-r-none"
                  />
                  <Button
                    type="button"
                    variant="secondary"
                    className="rounded-l-none"
                    onClick={handleCopyReferralCode}
                  >
                    {copied ? (
                      <Check className="h-4 w-4" />
                    ) : (
                      <Copy className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </div>

              <div>
                <Label htmlFor="referral-link">Referral Link</Label>
                <div className="flex mt-1">
                  <Input
                    id="referral-link"
                    value={referralLink}
                    readOnly
                    className="rounded-r-none text-xs"
                  />
                  <Button
                    type="button"
                    variant="secondary"
                    className="rounded-l-none rounded-r-none px-2"
                    onClick={handleCopyReferralLink}
                  >
                    {linkCopied ? (
                      <Check className="h-4 w-4" />
                    ) : (
                      <Copy className="h-4 w-4" />
                    )}
                  </Button>
                  <Button
                    type="button"
                    variant="default"
                    className="rounded-l-none px-2"
                    onClick={handleShareReferralLink}
                  >
                    <Share2 className="h-4 w-4" />
                  </Button>
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  Share this link directly with friends or on social media
                </p>
              </div>
            </div>
          </div>

          <div className="border-t pt-4">
            <form onSubmit={handleSendReferral}>
              <Label htmlFor="referral-email">Invite a Friend</Label>
              <div className="flex mt-1">
                <Input
                  id="referral-email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={referralEmail}
                  onChange={(e) => setReferralEmail(e.target.value)}
                  className="rounded-r-none"
                  required
                />
                <Button
                  type="submit"
                  className="rounded-l-none"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? (
                    "Sending..."
                  ) : (
                    <>
                      <Send className="h-4 w-4 mr-2" />
                      Send
                    </>
                  )}
                </Button>
              </div>
            </form>
          </div>

          {referrals.length > 0 && (
            <div className="border-t pt-4">
              <div className="flex justify-between items-center mb-2">
                <h3 className="text-sm font-medium">Your Referrals</h3>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 px-2 text-xs"
                  onClick={() => user && fetchReferrals(user.uid)}
                >
                  Refresh
                </Button>
              </div>
              <div className="space-y-2">
                {referrals.slice(0, 5).map((referral) => (
                  <div
                    key={referral.id}
                    className="text-sm flex justify-between items-center"
                  >
                    <span className="truncate max-w-[200px]">
                      {referral.referredEmail}
                    </span>
                    <span
                      className={`text-xs px-2 py-1 rounded-full ${
                        referral.status === "completed"
                          ? "bg-green-100 text-green-800"
                          : referral.status === "expired"
                          ? "bg-red-100 text-red-800"
                          : "bg-yellow-100 text-yellow-800"
                      }`}
                    >
                      {referral.status.charAt(0).toUpperCase() +
                        referral.status.slice(1)}
                    </span>
                  </div>
                ))}
                {referrals.length > 5 && (
                  <p className="text-xs text-muted-foreground text-center">
                    +{referrals.length - 5} more referrals
                  </p>
                )}
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
