import { firestore } from '@/config/firebase';
import { collection, getDocs, doc, updateDoc } from 'firebase/firestore';

// Sample cuisine types to assign to restaurants
const cuisineTypes = [
  'Italian',
  'Japanese',
  'American',
  'Mexican',
  'Chinese',
  'Indian',
  'Thai',
  'Mediterranean',
  'French',
  'Turkish',
  'Azerbaijani',
  'Greek',
  'Spanish',
  'Korean',
  'Vietnamese'
];

// Function to update restaurant cuisine types
export async function updateRestaurantCuisines() {
  try {
    // Get all restaurants
    const restaurantsRef = collection(firestore, 'restaurants');
    const querySnapshot = await getDocs(restaurantsRef);
    
    // Update each restaurant with a random cuisine type
    const updatePromises = querySnapshot.docs.map(async (docSnapshot) => {
      const restaurantId = docSnapshot.id;
      const restaurantData = docSnapshot.data();
      
      // Skip if already has a cuisine type
      if (restaurantData.cuisineType) {
        return;
      }
      
      // Assign a random cuisine type
      const randomIndex = Math.floor(Math.random() * cuisineTypes.length);
      const cuisineType = cuisineTypes[randomIndex];
      
      // Update the restaurant document
      await updateDoc(doc(firestore, 'restaurants', restaurantId), {
        cuisineType: cuisineType
      });
    });
    
    // Wait for all updates to complete
    await Promise.all(updatePromises);
    
    return { success: true, message: 'All restaurants updated with cuisine types' };
  } catch (error) {
    console.error('Error updating restaurant cuisines:', error);
    return { success: false, message: 'Error updating restaurant cuisines', error };
  }
}
