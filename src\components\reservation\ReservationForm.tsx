import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { User } from "firebase/auth";
import type { Table } from "@/components/reservation/types";
import { useEffect, useMemo } from "react";

interface ReservationInput {
  customerName: string;
  customerPhone: string;
  date: string;
  arrivalTime: string;
  departureTime?: string;
  partySize: number;
  selectedTableId: string;
}

interface ReservationFormProps {
  newReservation: ReservationInput;
  setNewReservation: (value: React.SetStateAction<ReservationInput>) => void;
  selectedTableId: string;
  handleReservation: () => void;
  user: User | null;
  tables: Table[];
}

const ReservationForm = ({
  newReservation,
  setNewReservation,
  selectedTableId,
  handleReservation,
  user,
  tables,
}: ReservationFormProps) => {
  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    let processedValue: string | number = value;

    if (name === "partySize") {
      processedValue = parseInt(value, 10) || 1;
      processedValue = Math.max(1, processedValue);
      const numericValue =
        typeof processedValue === "number"
          ? processedValue
          : parseInt(value, 10) || 1;
      setNewReservation((prev) => ({
        ...prev,
        partySize: numericValue,
        selectedTableId: "",
      }));
      return;
    } else if (name === "date") {
      setNewReservation((prev) => ({
        ...prev,
        selectedTableId: "",
      }));
    } else if (name === "arrivalTime") {
      setNewReservation((prev) => ({
        ...prev,
        selectedTableId: "",
      }));
    }

    setNewReservation((prev) => ({
      ...prev,
      [name]: processedValue,
    }));
  };

  useEffect(() => {
    if (user && !newReservation.customerName && user.displayName) {
      setNewReservation((prev) => ({
        ...prev,
        customerName: user.displayName || "",
      }));
    }
    if (user && !newReservation.customerPhone && user.phoneNumber) {
      setNewReservation((prev) => ({
        ...prev,
        customerPhone: user.phoneNumber || "",
      }));
    }
  }, [
    user,
    newReservation.customerName,
    newReservation.customerPhone,
    setNewReservation,
  ]);

  const selectedTableDetails = useMemo(() => {
    if (!selectedTableId) return null;
    return tables.find((t) => t.id === selectedTableId);
  }, [selectedTableId, tables]);

  return (
    <form
      onSubmit={(e) => {
        e.preventDefault();
        handleReservation();
      }}
      className="space-y-4"
    >
      {!user && (
        <p className="text-sm text-yellow-600">
          Login or Register to save your reservations.
        </p>
      )}
      <div>
        <Label htmlFor="customerName">Your Name</Label>
        <Input
          id="customerName"
          name="customerName"
          type="text"
          value={newReservation.customerName}
          onChange={handleInputChange}
          required
          placeholder="Enter your full name"
        />
      </div>
      <div>
        <Label htmlFor="customerPhone">Phone Number</Label>
        <Input
          id="customerPhone"
          name="customerPhone"
          type="tel"
          value={newReservation.customerPhone}
          onChange={handleInputChange}
          required
          placeholder="Enter your phone number"
        />
      </div>
      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="date">Date</Label>
          <Input
            id="date"
            name="date"
            type="date"
            value={newReservation.date}
            min={new Date().toLocaleDateString("en-CA")}
            onChange={handleInputChange}
            required
          />
        </div>
        <div>
          <Label htmlFor="arrivalTime">Arrival Time</Label>
          <Input
            id="arrivalTime"
            name="arrivalTime"
            type="time"
            value={newReservation.arrivalTime}
            step="1800"
            onChange={handleInputChange}
            required
          />
        </div>
      </div>
      <div>
        <Label htmlFor="partySize">Party Size</Label>
        <Input
          id="partySize"
          name="partySize"
          type="number"
          value={newReservation.partySize}
          onChange={handleInputChange}
          required
          min="1"
          placeholder="Number of guests"
        />
      </div>
      {selectedTableDetails && (
        <div className="p-3 bg-blue-100 border border-blue-300 rounded-md text-sm">
          Selected Table:{" "}
          <span className="font-semibold">{selectedTableDetails.name}</span>{" "}
          (Capacity: {selectedTableDetails.capacity})
        </div>
      )}
      {!selectedTableId && (
        <div className="p-3 bg-gray-100 border border-gray-300 rounded-md text-sm text-gray-600">
          Please select an available table from the layout map.
        </div>
      )}
      <Button
        type="submit"
        className="w-full"
        disabled={!selectedTableId || !user}
      >
        {user ? "Submit Reservation" : "Login to Reserve"}
      </Button>
    </form>
  );
};

export default ReservationForm;
