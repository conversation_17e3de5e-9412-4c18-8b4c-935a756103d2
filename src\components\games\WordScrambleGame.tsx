import React, { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { gameService } from "@/services/GameService";
import { ArrowLeft, Timer, Trophy, HelpCircle } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Progress } from "@/components/ui/progress";

interface WordScrambleGameProps {
  onBack: () => void;
  onComplete: () => void;
  userId: string;
}

export const WordScrambleGame: React.FC<WordScrambleGameProps> = ({
  onBack,
  onComplete,
  userId,
}) => {
  const [gameStarted, setGameStarted] = useState(false);
  const [gameCompleted, setGameCompleted] = useState(false);
  const [words, setWords] = useState<
    { word: string; hint: string; scrambled: string }[]
  >([]);
  const [currentWordIndex, setCurrentWordIndex] = useState(0);
  const [userInput, setUserInput] = useState("");
  const [timer, setTimer] = useState(0);
  const [timerRunning, setTimerRunning] = useState(false);
  const [showHint, setShowHint] = useState(false);
  const [result, setResult] = useState<{
    success: boolean;
    pointsEarned: number;
    message: string;
  } | null>(null);
  const [correctWords, setCorrectWords] = useState(0);

  // Initialize game
  useEffect(() => {
    const scrambledWords = gameService.getWordScrambleWords(5);
    setWords(scrambledWords);
  }, []);

  // Timer effect
  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (timerRunning) {
      interval = setInterval(() => {
        setTimer((prevTimer) => prevTimer + 1);
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [timerRunning]);

  // Format time as MM:SS
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, "0")}:${secs
      .toString()
      .padStart(2, "0")}`;
  };

  // Start game
  const handleStartGame = () => {
    setGameStarted(true);
    setTimerRunning(true);
  };

  // Check user's answer
  const checkAnswer = () => {
    const currentWord = words[currentWordIndex];
    const isCorrect = userInput.toUpperCase() === currentWord.word;

    if (isCorrect) {
      setCorrectWords((prev) => prev + 1);
    }

    // Move to next word or end game
    if (currentWordIndex < words.length - 1) {
      setCurrentWordIndex((prev) => prev + 1);
      setUserInput("");
      setShowHint(false);
    } else {
      endGame();
    }
  };

  // End game and submit result
  const endGame = async () => {
    setTimerRunning(false);
    setGameCompleted(true);

    try {
      const gameResult = await gameService.submitWordScrambleResult(
        userId,
        correctWords,
        words.length,
        timer
      );
      setResult(gameResult);
      if (gameResult.success && gameResult.pointsEarned > 0) {
        onComplete();
      }
    } catch (error) {
      console.error("Error submitting game result:", error);
      setResult({
        success: false,
        pointsEarned: 0,
        message: "An error occurred. Please try again.",
      });
    }
  };

  // Skip current word
  const skipWord = () => {
    if (currentWordIndex < words.length - 1) {
      setCurrentWordIndex((prev) => prev + 1);
      setUserInput("");
      setShowHint(false);
    } else {
      endGame();
    }
  };

  // Toggle hint visibility
  const toggleHint = () => {
    setShowHint(!showHint);
  };

  return (
    <Card className="w-full">
      <CardContent className="p-6">
        <div className="flex justify-between items-center mb-6">
          <Button
            variant="ghost"
            size="sm"
            onClick={onBack}
            className="flex items-center"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Games
          </Button>
          {gameStarted && !gameCompleted && (
            <div className="flex items-center">
              <Timer className="h-4 w-4 mr-2 text-muted-foreground" />
              <span className="text-lg font-medium">{formatTime(timer)}</span>
            </div>
          )}
        </div>

        <div className="text-center mb-6">
          <h2 className="text-2xl font-bold mb-2">Word Scramble</h2>
          <p className="text-muted-foreground">
            Unscramble the letters to find food-related words!
          </p>
        </div>

        {!gameStarted ? (
          <div className="text-center py-8">
            <p className="mb-6">
              Unscramble the letters to form food-related words. The faster you
              solve, the more points you earn!
            </p>
            <Button onClick={handleStartGame} size="lg">
              Start Game
            </Button>
          </div>
        ) : gameCompleted ? (
          <div className="text-center py-8">
            <div className="mb-6">
              <Trophy className="h-16 w-16 mx-auto text-primary mb-4" />
              <h3 className="text-xl font-bold mb-2">Game Completed!</h3>
              <p className="text-muted-foreground mb-2">
                Time: {formatTime(timer)}
              </p>
              <p className="text-muted-foreground">
                Words Solved: {correctWords}/{words.length}
              </p>
              {result && (
                <Alert
                  className={`mt-4 ${
                    result.success ? "bg-primary/10" : "bg-destructive/10"
                  }`}
                >
                  <AlertTitle>
                    {result.success ? "Success!" : "Oops!"}
                  </AlertTitle>
                  <AlertDescription>{result.message}</AlertDescription>
                  {result.pointsEarned > 0 && (
                    <div className="mt-2 font-medium">
                      +{result.pointsEarned} points earned!
                    </div>
                  )}
                </Alert>
              )}
            </div>
            <Button onClick={onBack}>Return to Games</Button>
          </div>
        ) : (
          <div>
            <div className="mb-6">
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm font-medium">Progress</span>
                <span className="text-sm">
                  {currentWordIndex + 1} / {words.length}
                </span>
              </div>
              <Progress
                value={((currentWordIndex + 1) / words.length) * 100}
                className="h-2"
              />
            </div>

            <div className="bg-muted p-6 rounded-lg mb-6 text-center">
              <h3 className="text-2xl font-bold mb-4 tracking-widest">
                {words[currentWordIndex]?.scrambled}
              </h3>
              {showHint && (
                <p className="text-sm text-muted-foreground mb-4">
                  Hint: {words[currentWordIndex]?.hint}
                </p>
              )}
            </div>

            <div className="space-y-4">
              <div className="flex gap-2">
                <Input
                  value={userInput}
                  onChange={(e) => setUserInput(e.target.value)}
                  placeholder="Type your answer..."
                  className="flex-1"
                  onKeyDown={(e) => {
                    if (e.key === "Enter" && userInput.trim()) {
                      checkAnswer();
                    }
                  }}
                />
                <Button
                  variant="outline"
                  size="icon"
                  onClick={toggleHint}
                  title="Show hint"
                >
                  <HelpCircle className="h-4 w-4" />
                </Button>
              </div>

              <div className="grid grid-cols-2 gap-3">
                <Button variant="outline" onClick={skipWord}>
                  Skip
                </Button>
                <Button onClick={checkAnswer} disabled={!userInput.trim()}>
                  Submit
                </Button>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
