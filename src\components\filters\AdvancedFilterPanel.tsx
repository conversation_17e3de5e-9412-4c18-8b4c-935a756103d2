import { useState } from "react";
import { FilterPanel } from "./FilterPanel";
import { PriceRangeFilter } from "./PriceRangeFilter";
import { DietaryFilter } from "./DietaryFilter";
import { AllergenFilter } from "./AllergenFilter";
import { NutritionFilter } from "./NutritionFilter";
import { FeaturesFilter } from "./FeaturesFilter";
import { DeliveryTimeFilter } from "./DeliveryTimeFilter";
import { ToggleFilter } from "./ToggleFilter";
import { Button } from "@/components/ui/button";
import { FilterChip, FilterChips } from "./FilterChips";
import { FilterOptions } from "@/services/RestaurantFilterService";

interface AdvancedFilterPanelProps {
  filters: FilterOptions;
  onChange: (filters: FilterOptions) => void;
  facets?: {
    categories: Record<string, number>;
    cuisines: Record<string, number>;
    dietary: Record<string, number>;
    allergens: Record<string, number>;
    healthLabels: Record<string, number>;
    features: Record<string, number>;
    priceRanges: Record<string, number>;
  };
  onApply: () => void;
  onReset: () => void;
}

export function AdvancedFilterPanel({
  filters,
  onChange,
  facets,
  onApply,
  onReset,
}: AdvancedFilterPanelProps) {
  // Local state for filters before applying
  const [localFilters, setLocalFilters] = useState<FilterOptions>(filters);

  // Active filter chips
  const [activeFilters, setActiveFilters] = useState<FilterChip[]>(
    getActiveFilters(filters)
  );

  // Update local filters
  const handleFilterChange = <T extends keyof FilterOptions>(
    key: T,
    value: FilterOptions[T]
  ) => {
    setLocalFilters((prev) => ({
      ...prev,
      [key]: value,
    }));
  };

  // Apply filters
  const handleApplyFilters = () => {
    onChange(localFilters);
    setActiveFilters(getActiveFilters(localFilters));
    onApply();
  };

  // Reset filters
  const handleResetFilters = () => {
    const resetFilters: FilterOptions = {
      priceRanges: [],
      dietary: [],
      allergens: [],
      healthLabels: [],
      nutritionFilters: undefined,
      features: [],
      deliveryTime: [10, 60],
      isOpenOnly: false,
      parkingAvailable: false,
    };
    setLocalFilters(resetFilters);
    onChange(resetFilters);
    setActiveFilters([]);
    onReset();
  };

  // Remove a single filter
  const handleRemoveFilter = (filter: FilterChip) => {
    const newLocalFilters = { ...localFilters };

    if (filter.group === "priceRange") {
      newLocalFilters.priceRanges = (newLocalFilters.priceRanges || []).filter(
        (price) => price !== filter.id
      );
    } else if (filter.group === "dietary") {
      newLocalFilters.dietary = (newLocalFilters.dietary || []).filter(
        (diet) => diet !== filter.id
      );
    } else if (filter.group === "allergen") {
      newLocalFilters.allergens = (newLocalFilters.allergens || []).filter(
        (allergen) => allergen !== filter.id
      );
    } else if (filter.group === "healthLabel") {
      newLocalFilters.healthLabels = (
        newLocalFilters.healthLabels || []
      ).filter((label) => label !== filter.id);
    } else if (filter.group === "nutrition") {
      // For nutrition filters, we need to remove the specific nutrition filter
      if (newLocalFilters.nutritionFilters) {
        delete newLocalFilters.nutritionFilters[
          filter.id as keyof typeof newLocalFilters.nutritionFilters
        ];

        // If no nutrition filters left, set to undefined
        if (Object.keys(newLocalFilters.nutritionFilters).length === 0) {
          newLocalFilters.nutritionFilters = undefined;
        }
      }
    } else if (filter.group === "feature") {
      newLocalFilters.features = (newLocalFilters.features || []).filter(
        (feature) => feature !== filter.id
      );
    } else if (filter.group === "isOpenOnly") {
      newLocalFilters.isOpenOnly = false;
    } else if (filter.group === "parkingAvailable") {
      newLocalFilters.parkingAvailable = false;
    }

    setLocalFilters(newLocalFilters);
    onChange(newLocalFilters);
    setActiveFilters(getActiveFilters(newLocalFilters));
  };

  return (
    <div className="space-y-4">
      <FilterChips
        filters={activeFilters}
        onRemove={handleRemoveFilter}
        onClearAll={handleResetFilters}
      />

      <FilterPanel title="Price Range" defaultOpen={true}>
        <PriceRangeFilter
          value={localFilters.priceRanges || []}
          onChange={(value) => handleFilterChange("priceRanges", value)}
          counts={facets?.priceRanges}
        />
      </FilterPanel>

      <FilterPanel title="Dietary Options" defaultOpen={true}>
        <DietaryFilter
          value={localFilters.dietary || []}
          onChange={(value) => handleFilterChange("dietary", value)}
          counts={facets?.dietary}
        />
      </FilterPanel>

      <FilterPanel title="Allergen Exclusions" defaultOpen={false}>
        <AllergenFilter
          value={localFilters.allergens || []}
          onChange={(value) => handleFilterChange("allergens", value)}
          counts={facets?.allergens}
        />
      </FilterPanel>

      <FilterPanel title="Nutrition Information" defaultOpen={false}>
        <NutritionFilter
          value={localFilters.nutritionFilters || {}}
          healthLabels={localFilters.healthLabels || []}
          onChange={(value) => handleFilterChange("nutritionFilters", value)}
          onHealthLabelsChange={(value) =>
            handleFilterChange("healthLabels", value)
          }
        />
      </FilterPanel>

      <FilterPanel title="Restaurant Features" defaultOpen={false}>
        <FeaturesFilter
          value={localFilters.features || []}
          onChange={(value) => handleFilterChange("features", value)}
          counts={facets?.features}
        />
      </FilterPanel>

      <FilterPanel title="Delivery Time" defaultOpen={false}>
        <DeliveryTimeFilter
          value={localFilters.deliveryTime || [10, 60]}
          onChange={(value) => handleFilterChange("deliveryTime", value)}
        />
      </FilterPanel>

      <FilterPanel title="Additional Filters" defaultOpen={false}>
        <div className="space-y-4">
          <ToggleFilter
            label="Open Now"
            value={localFilters.isOpenOnly || false}
            onChange={(value) => handleFilterChange("isOpenOnly", value)}
            description="Show only restaurants that are currently open"
          />

          <ToggleFilter
            label="Parking Available"
            value={localFilters.parkingAvailable || false}
            onChange={(value) => handleFilterChange("parkingAvailable", value)}
            description="Show only restaurants with parking facilities"
          />
        </div>
      </FilterPanel>

      <div className="flex gap-2 pt-4 mt-4 border-t">
        <Button
          variant="outline"
          className="flex-1 h-12"
          onClick={handleResetFilters}
          size="lg"
        >
          Clear All
        </Button>
        <Button
          className="flex-1 h-12 bg-primary"
          onClick={handleApplyFilters}
          size="lg"
        >
          Apply Filters
        </Button>
      </div>
    </div>
  );
}

// Helper function to convert filters to filter chips
function getActiveFilters(filters: FilterOptions): FilterChip[] {
  const activeFilters: FilterChip[] = [];

  // Add price range filters
  filters.priceRanges?.forEach((price) => {
    let displayPrice = "";

    // Map price ranges to labels with actual price ranges
    if (price === "M") {
      displayPrice = "Under 15₼";
    } else if (price === "MM") {
      displayPrice = "15₼ to 30₼";
    } else if (price === "MMM") {
      displayPrice = "30₼ to 50₼";
    } else if (price === "MMMM") {
      displayPrice = "Over 50₼";
    } else if (price === "$") {
      displayPrice = "Under 15₼";
    } // For backward compatibility
    else if (price === "$$") {
      displayPrice = "15₼ to 30₼";
    } // For backward compatibility
    else if (price === "$$$") {
      displayPrice = "30₼ to 50₼";
    } // For backward compatibility
    else if (price === "$$$$") {
      displayPrice = "Over 50₼";
    } // For backward compatibility
    else if (price === "₼") {
      displayPrice = "Under 15₼";
    } // For backward compatibility
    else if (price === "₼₼") {
      displayPrice = "15₼ to 30₼";
    } // For backward compatibility
    else if (price === "₼₼₼") {
      displayPrice = "30₼ to 50₼";
    } // For backward compatibility
    else if (price === "₼₼₼₼") {
      displayPrice = "Over 50₼";
    } // For backward compatibility
    else {
      displayPrice = price;
    }

    activeFilters.push({
      id: price,
      label: `Price: ${displayPrice}`,
      group: "priceRange",
    });
  });

  // Add dietary filters
  filters.dietary?.forEach((diet) => {
    activeFilters.push({
      id: diet,
      label: `Dietary: ${diet}`,
      group: "dietary",
    });
  });

  // Add allergen filters
  filters.allergens?.forEach((allergen) => {
    activeFilters.push({
      id: allergen,
      label: `Exclude: ${allergen}`,
      group: "allergen",
    });
  });

  // Add health label filters
  filters.healthLabels?.forEach((label) => {
    activeFilters.push({
      id: label,
      label: `Health: ${label}`,
      group: "healthLabel",
    });
  });

  // Add nutrition filters
  if (filters.nutritionFilters) {
    // Add calories filter
    if (filters.nutritionFilters.calories) {
      activeFilters.push({
        id: "calories",
        label: `Calories: ${filters.nutritionFilters.calories[0]}-${filters.nutritionFilters.calories[1]} kcal`,
        group: "nutrition",
      });
    }

    // Add protein filter
    if (filters.nutritionFilters.protein) {
      activeFilters.push({
        id: "protein",
        label: `Protein: ${filters.nutritionFilters.protein[0]}-${filters.nutritionFilters.protein[1]}g`,
        group: "nutrition",
      });
    }

    // Add carbs filter
    if (filters.nutritionFilters.carbs) {
      activeFilters.push({
        id: "carbs",
        label: `Carbs: ${filters.nutritionFilters.carbs[0]}-${filters.nutritionFilters.carbs[1]}g`,
        group: "nutrition",
      });
    }

    // Add fat filter
    if (filters.nutritionFilters.fat) {
      activeFilters.push({
        id: "fat",
        label: `Fat: ${filters.nutritionFilters.fat[0]}-${filters.nutritionFilters.fat[1]}g`,
        group: "nutrition",
      });
    }

    // Add sodium filter
    if (filters.nutritionFilters.sodium) {
      activeFilters.push({
        id: "sodium",
        label: `Sodium: ${filters.nutritionFilters.sodium[0]}-${filters.nutritionFilters.sodium[1]}mg`,
        group: "nutrition",
      });
    }
  }

  // Add feature filters
  filters.features?.forEach((feature) => {
    activeFilters.push({
      id: feature,
      label: `Feature: ${feature}`,
      group: "feature",
    });
  });

  // Add toggle filters
  if (filters.isOpenOnly) {
    activeFilters.push({
      id: "isOpenOnly",
      label: "Open Now",
      group: "isOpenOnly",
    });
  }

  if (filters.parkingAvailable) {
    activeFilters.push({
      id: "parkingAvailable",
      label: "Parking Available",
      group: "parkingAvailable",
    });
  }

  // Add delivery time filter if it's not the default
  if (
    filters.deliveryTime &&
    (filters.deliveryTime[0] !== 10 || filters.deliveryTime[1] !== 60)
  ) {
    activeFilters.push({
      id: "deliveryTime",
      label: `Delivery: ${filters.deliveryTime[0]}-${filters.deliveryTime[1]} min`,
      group: "deliveryTime",
    });
  }

  return activeFilters;
}
