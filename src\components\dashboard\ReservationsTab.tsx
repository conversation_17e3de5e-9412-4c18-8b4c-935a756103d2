import { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { format } from "date-fns";
import {
  Table as TableType,
  Reservation,
  ClientDetails,
  ReservationTimer,
} from "@/types/dashboard";
import { TableCard } from "./TableCard";
import { Badge } from "@/components/ui/badge";

interface ReservationsTabProps {
  reservations: Reservation[];
  userRole: "client" | "restaurant" | null;
  tables: TableType[];
  getTableStatusColor: (tableId: string) => string;
  getTableAnalytics?: (tableId: string) => {
    totalReservations: number;
    pendingCount: number;
    confirmedCount: number;
    cancelledCount: number;
    noShowCount: number;
    completedCount: number;
    utilization: number;
  };
  canCancelReservation?: (reservation: Reservation) => boolean;
  handleDeleteReservation: (reservationId: string) => Promise<void>;
  handleReservationActive?: (reservationId: string) => Promise<void>;
  handleReservationComplete?: (reservationId: string) => Promise<void>;
  handleNoShow?: (reservationId: string) => Promise<void>;
  activeTimers?: ReservationTimer[];
  formatElapsedTime?: (seconds: number) => string;
  customerDetails: { [key: string]: ClientDetails };
  handleReservationStatus?: (
    reservationId: string,
    status: Reservation["status"]
  ) => Promise<void>;
}

export const ReservationsTab = ({
  tables,
  reservations,
  customerDetails,
  userRole,
  getTableAnalytics = () => ({
    totalReservations: 0,
    pendingCount: 0,
    confirmedCount: 0,
    cancelledCount: 0,
    noShowCount: 0,
    completedCount: 0,
    utilization: 0,
  }),
  getTableStatusColor,
  formatElapsedTime = (seconds) =>
    `${Math.floor(seconds / 60)}:${(seconds % 60).toString().padStart(2, "0")}`,
  activeTimers = [],
  handleReservationActive = async () => {},
  handleReservationComplete = async () => {},
  handleNoShow = async () => {},
  handleReservationStatus,
  handleDeleteReservation,
  canCancelReservation,
}: ReservationsTabProps) => {
  const [selectedTable, setSelectedTable] = useState<TableType | null>(null);

  const getStatusBadgeVariant = (status: Reservation["status"]) => {
    switch (status) {
      case "pending":
        return "secondary" as const;
      case "confirmed":
      case "active":
        return "secondary" as const;
      case "completed":
        return "default" as const;
      case "cancelled":
      case "no-show":
        return "destructive" as const;
      default:
        return "secondary" as const;
    }
  };

  const getStatusText = (status: Reservation["status"]) => {
    switch (status) {
      case "pending":
        return "Pending Approval";
      case "confirmed":
        return "Confirmed";
      case "active":
        return "Seated";
      case "completed":
        return "Completed";
      case "cancelled":
        return "Cancelled";
      case "no-show":
        return "No Show";
      default:
        return status;
    }
  };

  const canMarkAsNoShow = (reservation: Reservation) => {
    if (reservation.status !== "confirmed") return false;

    const now = new Date();
    const reservationTime = new Date(
      `${reservation.date}T${reservation.arrivalTime}`
    );
    const timeDiff = (now.getTime() - reservationTime.getTime()) / (1000 * 60);

    return timeDiff >= 15 && timeDiff <= 45;
  };

  const getNoShowButtonTooltip = (reservation: Reservation) => {
    if (reservation.status !== "confirmed") return "";

    const now = new Date();
    const reservationTime = new Date(
      `${reservation.date}T${reservation.arrivalTime}`
    );
    const timeDiff = (now.getTime() - reservationTime.getTime()) / (1000 * 60);

    if (timeDiff < 15) {
      return `Can mark as no-show after ${Math.ceil(15 - timeDiff)} minutes`;
    }
    if (timeDiff > 45) {
      return "Reservation has been automatically marked as no-show";
    }
    return "Mark as no-show";
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>
          {userRole === "client" ? "My Reservations" : "Manage Reservations"}
        </CardTitle>
        <CardDescription>
          {userRole === "client"
            ? "View your reservation history"
            : "Handle incoming reservation requests and manage tables"}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-8">
          {/* Only show tables section for restaurant users */}
          {userRole === "restaurant" && (
            <div>
              <h3 className="text-lg font-medium mb-4">Restaurant Tables</h3>
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                {tables.map((table) => (
                  <TableCard
                    key={table.id}
                    table={table}
                    reservations={reservations}
                    getTableStatusColor={getTableStatusColor}
                    onSelect={setSelectedTable}
                  />
                ))}
              </div>
            </div>
          )}

          <div>
            <h3 className="text-lg font-medium mb-4">
              {userRole === "client"
                ? "Reservation History"
                : "All Reservations"}
            </h3>
            <div className="rounded-lg border">
              <Table>
                <TableHeader>
                  <TableRow className="bg-muted/50">
                    <TableHead className="font-medium">Date & Time</TableHead>
                    <TableHead className="font-medium">Customer</TableHead>
                    <TableHead className="font-medium">
                      Table & Party Size
                    </TableHead>
                    <TableHead className="font-medium">Status</TableHead>
                    <TableHead className="font-medium text-right">
                      Actions
                    </TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {reservations
                    .sort((a, b) => {
                      const dateA = new Date(`${a.date}T${a.arrivalTime}`);
                      const dateB = new Date(`${b.date}T${b.arrivalTime}`);
                      return dateB.getTime() - dateA.getTime();
                    })
                    .map((reservation) => {
                      const reservationTime = new Date(
                        `${reservation.date}T${reservation.arrivalTime}`
                      );
                      const now = new Date();
                      const timeDiff =
                        (now.getTime() - reservationTime.getTime()) /
                        (1000 * 60);

                      // Auto mark as no-show after 45 minutes
                      if (reservation.status === "confirmed" && timeDiff > 45) {
                        handleNoShow?.(reservation.id);
                      }

                      const customer = customerDetails[reservation.userId];

                      return (
                        <TableRow
                          key={reservation.id}
                          id={`reservation-${reservation.id}`}
                          className="hover:bg-muted/50"
                        >
                          <TableCell>
                            <div className="space-y-1">
                              <div className="font-medium">
                                {format(
                                  new Date(reservation.date),
                                  "MMM d, yyyy"
                                )}
                              </div>
                              <div className="text-sm text-muted-foreground">
                                {format(
                                  new Date(
                                    `${reservation.date}T${reservation.arrivalTime}`
                                  ),
                                  "HH:mm"
                                )}
                                {reservation.departureTime &&
                                  ` - ${format(
                                    new Date(
                                      `${reservation.date}T${reservation.departureTime}`
                                    ),
                                    "HH:mm"
                                  )}`}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            {userRole === "restaurant" ? (
                              customer ? (
                                <div className="space-y-1">
                                  <div className="font-medium">
                                    {customer.firstName} {customer.lastName}
                                  </div>
                                  <div className="text-sm text-muted-foreground">
                                    {customer.phone}
                                  </div>
                                </div>
                              ) : (
                                "-"
                              )
                            ) : (
                              <div className="space-y-1">
                                <div className="font-medium">
                                  {reservation.restaurantName}
                                </div>
                                <div className="text-sm text-muted-foreground">
                                  {tables.find(
                                    (t) => t.id === reservation.tableId
                                  )?.name || "-"}
                                </div>
                              </div>
                            )}
                          </TableCell>
                          <TableCell>
                            <div className="space-y-1">
                              <div className="font-medium">
                                {userRole === "restaurant" ? (
                                  <>
                                    Table{" "}
                                    {tables.find(
                                      (t) => t.id === reservation.tableId
                                    )?.name || "-"}
                                  </>
                                ) : (
                                  `${reservation.partySize} people`
                                )}
                              </div>
                              <div className="text-sm text-muted-foreground">
                                {userRole === "restaurant"
                                  ? `${reservation.partySize} people`
                                  : "Reserved Table"}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              <Badge
                                variant={getStatusBadgeVariant(
                                  reservation.status
                                )}
                              >
                                {getStatusText(reservation.status)}
                              </Badge>
                              {reservation.status === "active" && (
                                <span className="text-xs text-muted-foreground">
                                  (
                                  {formatElapsedTime(
                                    activeTimers.find(
                                      (t) => t.reservationId === reservation.id
                                    )?.elapsedTime || 0
                                  )}
                                  )
                                </span>
                              )}
                            </div>
                          </TableCell>
                          <TableCell className="text-right">
                            <div className="flex justify-end items-center gap-2">
                              {userRole === "restaurant" ? (
                                <>
                                  {reservation.status === "pending" && (
                                    <>
                                      <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={() =>
                                          handleReservationStatus?.(
                                            reservation.id,
                                            "confirmed"
                                          )
                                        }
                                        className="bg-green-50 text-green-600 hover:bg-green-100 hover:text-green-700"
                                      >
                                        Accept
                                      </Button>
                                      <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={() =>
                                          handleReservationStatus?.(
                                            reservation.id,
                                            "cancelled"
                                          )
                                        }
                                        className="bg-red-50 text-red-600 hover:bg-red-100 hover:text-red-700"
                                      >
                                        Decline
                                      </Button>
                                    </>
                                  )}
                                  {reservation.status === "confirmed" && (
                                    <>
                                      <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={() =>
                                          handleReservationActive?.(
                                            reservation.id
                                          )
                                        }
                                        className="bg-blue-50 text-blue-600 hover:bg-blue-100 hover:text-blue-700"
                                      >
                                        Seat
                                      </Button>
                                      <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={() =>
                                          handleNoShow?.(reservation.id)
                                        }
                                        disabled={!canMarkAsNoShow(reservation)}
                                        title={getNoShowButtonTooltip(
                                          reservation
                                        )}
                                        className="bg-red-50 text-red-600 hover:bg-red-100 hover:text-red-700"
                                      >
                                        No-Show
                                      </Button>
                                    </>
                                  )}
                                  {reservation.status === "active" && (
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      onClick={() =>
                                        handleReservationComplete?.(
                                          reservation.id
                                        )
                                      }
                                      className="bg-green-50 text-green-600 hover:bg-green-100 hover:text-green-700"
                                    >
                                      Complete
                                    </Button>
                                  )}
                                </>
                              ) : (
                                <>
                                  {(reservation.status === "pending" ||
                                    reservation.status === "confirmed") &&
                                    (!canCancelReservation ||
                                      canCancelReservation(reservation)) && (
                                      <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={() =>
                                          handleReservationStatus?.(
                                            reservation.id,
                                            "cancelled"
                                          )
                                        }
                                        className="bg-red-50 text-red-600 hover:bg-red-100 hover:text-red-700"
                                      >
                                        Cancel Reservation
                                      </Button>
                                    )}
                                </>
                              )}
                              {(reservation.status === "cancelled" ||
                                reservation.status === "no-show" ||
                                reservation.status === "completed" ||
                                new Date(
                                  `${reservation.date}T${reservation.arrivalTime}`
                                ) < new Date()) && (
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() =>
                                    handleDeleteReservation(reservation.id)
                                  }
                                  className="bg-red-50 text-red-600 hover:bg-red-100 hover:text-red-700"
                                >
                                  Delete
                                </Button>
                              )}
                            </div>
                          </TableCell>
                        </TableRow>
                      );
                    })}
                </TableBody>
              </Table>
            </div>
          </div>
        </div>
      </CardContent>

      {/* Table Details Dialog */}
      <Dialog
        open={!!selectedTable}
        onOpenChange={() => setSelectedTable(null)}
      >
        <DialogContent className="max-w-3xl" hideCloseButton>
          <DialogHeader>
            <DialogTitle>{selectedTable?.name} Details</DialogTitle>
            <DialogDescription>
              Capacity: {selectedTable?.capacity} people
            </DialogDescription>
          </DialogHeader>
          {selectedTable && (
            <div className="space-y-6">
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                {Object.entries(getTableAnalytics(selectedTable.id)).map(
                  ([key, value]) => (
                    <div key={key} className="p-4 bg-muted rounded-lg">
                      <p className="text-sm font-medium capitalize">
                        {key.replace(/([A-Z])/g, " $1").trim()}
                      </p>
                      <p className="text-2xl font-bold">
                        {typeof value === "number" && key === "utilization"
                          ? `${(value as number).toFixed(1)}%`
                          : String(value)}
                      </p>
                    </div>
                  )
                )}
              </div>

              <div className="space-y-4">
                <h4 className="text-lg font-medium">Reservation History</h4>
                <div className="max-h-[300px] overflow-y-auto space-y-2">
                  {reservations
                    .filter((r) => r.tableId === selectedTable.id)
                    .sort(
                      (a, b) =>
                        new Date(`${b.date}T${b.arrivalTime}`).getTime() -
                        new Date(`${a.date}T${a.arrivalTime}`).getTime()
                    )
                    .map((res) => {
                      const customer = customerDetails[res.userId];
                      return (
                        <div
                          key={res.id}
                          className="flex justify-between items-start p-4 hover:bg-muted/50 rounded-lg border"
                        >
                          <div className="space-y-1">
                            <p className="font-medium">
                              {format(
                                new Date(`${res.date}T${res.arrivalTime}`),
                                "PPP p"
                              )}
                            </p>
                            <p className="text-sm text-muted-foreground">
                              {customer ? (
                                <>
                                  <span className="font-medium">
                                    {res.customerName ||
                                      `${customer.firstName} ${customer.lastName}`}
                                  </span>
                                  <br />
                                  Phone: {res.customerPhone || customer.phone}
                                </>
                              ) : (
                                "Loading customer details..."
                              )}
                            </p>
                            <p className="text-sm text-muted-foreground">
                              Party of {res.partySize}
                              {res.departureTime && (
                                <>
                                  {" "}
                                  · Until{" "}
                                  {format(
                                    new Date(
                                      `${res.date}T${res.departureTime}`
                                    ),
                                    "p"
                                  )}
                                </>
                              )}
                            </p>
                          </div>
                          <Badge variant={getStatusBadgeVariant(res.status)}>
                            {res.status}
                          </Badge>
                        </div>
                      );
                    })}
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </Card>
  );
};
