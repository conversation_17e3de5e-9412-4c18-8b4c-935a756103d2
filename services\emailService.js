/**
 * Email service for sending various types of emails
 */
const { transporter, defaultSender } = require("../config/nodemailer");
const { db } = require("../config/firebase");
const emailTemplates = require("../constants/emailTemplates");
const crypto = require("crypto");
const logger = require("../utils/logger");

/**
 * Send an email and log it to Firestore
 * @param {Object} options - Email options
 * @param {string} options.to - Recipient email
 * @param {string} options.subject - Email subject
 * @param {string} options.html - Email HTML content
 * @param {string} options.text - Email plain text content (optional)
 * @param {string} options.from - Sender email (optional, defaults to configured sender)
 * @param {string} options.type - Email type for logging (e.g., 'newsletter', 'notification')
 * @param {boolean} options.useQueue - Whether to use the email queue (default: true)
 * @param {number} options.priority - Email priority (1-5, where 1 is highest) (optional)
 * @param {Object} options.metadata - Additional metadata for tracking (optional)
 * @returns {Promise<boolean>} - Success status
 */
const sendEmail = async (options) => {
  try {
    const {
      to,
      subject,
      html,
      text,
      from = defaultSender,
      type = "general",
      useQueue = true,
      priority = 3,
      metadata = {},
    } = options;

    // Validate email address format
    if (!validateEmail(to)) {
      logger.warn(`Invalid email address format: ${to}`);
      return false;
    }

    // Use email queue for better deliverability
    if (useQueue) {
      const emailQueue = require("./emailQueue");

      // Add to queue with priority
      await emailQueue.queueEmail({
        to,
        subject,
        html,
        text: text || "",
        from,
        type,
        priority,
        metadata,
        createdAt: new Date(),
      });

      logger.info(`Email queued for: ${to}`, { subject, type, priority });
      return true;
    }
    // Direct sending (for urgent emails or testing)
    else {
      // Send email directly
      const info = await transporter.sendMail({
        from,
        to,
        subject,
        text: text || "",
        html,
        priority: priority === 1 ? "high" : priority === 5 ? "low" : "normal",
      });

      // Log email to Firestore
      await db.collection("emailLogs").add({
        to,
        subject,
        type,
        messageId: info.messageId,
        timestamp: new Date(),
        status: "sent",
        metadata,
      });

      logger.info(`Email sent directly: ${info.messageId}`, {
        to,
        subject,
        type,
      });
      return true;
    }
  } catch (error) {
    logger.error("Error sending email:", {
      error: error.message,
      code: error.code,
      response: error.response,
      responseCode: error.responseCode,
      command: error.command,
      to: options.to,
      subject: options.subject,
    });

    // Log failed email attempt
    try {
      await db.collection("emailLogs").add({
        to: options.to,
        subject: options.subject,
        type: options.type || "general",
        timestamp: new Date(),
        status: "failed",
        error: error.message,
        errorCode: error.code,
        errorResponse: error.response,
      });
    } catch (logError) {
      logger.error("Error logging email failure:", logError);
    }

    return false;
  }
};

/**
 * Validate email address format
 * @param {string} email - Email address to validate
 * @returns {boolean} - Whether the email is valid
 */
const validateEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * Send OTP verification email
 * @param {string} email - Recipient email
 * @param {string} name - Recipient name
 * @returns {Promise<{success: boolean, otp: string}>} - Success status and OTP
 */
const sendOtpVerificationEmail = async (email, name = "") => {
  // Generate OTP
  const otp = crypto.randomInt(100000, 999999).toString();

  // Create email content
  const html = emailTemplates.getOtpVerificationTemplate(name, otp);

  // Send email
  const success = await sendEmail({
    to: email,
    subject: "Verify Your Subscription",
    html,
    type: "verification",
  });

  return { success, otp };
};

/**
 * Send subscription confirmation email
 * @param {string} email - Recipient email
 * @param {string} name - Recipient name
 * @param {string} unsubscribeToken - Token for unsubscribing
 * @param {boolean} isResubscribe - Whether this is a resubscription
 * @returns {Promise<boolean>} - Success status
 */
const sendSubscriptionConfirmationEmail = async (
  email,
  name,
  unsubscribeToken,
  isResubscribe = false
) => {
  // Create unsubscribe link
  const unsubscribeLink = `https://api.qonai.me/newsletter/unsubscribe?token=${unsubscribeToken}`;

  // Create email content
  const html = emailTemplates.getSubscriptionConfirmedTemplate(
    name,
    unsubscribeLink,
    isResubscribe
  );

  // Send email
  return await sendEmail({
    to: email,
    subject: isResubscribe
      ? "Welcome Back to Our Newsletter!"
      : "Subscription Confirmed",
    html,
    type: "subscription",
  });
};

/**
 * Send unsubscription confirmation email
 * @param {string} email - Recipient email
 * @returns {Promise<boolean>} - Success status
 */
const sendUnsubscriptionConfirmationEmail = async (email) => {
  // Create email content
  const html = emailTemplates.getUnsubscriptionConfirmedTemplate();

  // Send email
  return await sendEmail({
    to: email,
    subject: "Unsubscription Confirmed",
    html,
    type: "unsubscription",
  });
};

/**
 * Send notification email
 * @param {Object} options - Notification options
 * @returns {Promise<boolean>} - Success status
 */
const sendNotificationEmail = async (options) => {
  try {
    const {
      to,
      subject,
      body,
      recipientName,
      type,
      data,
      priority = 2,
    } = options;

    // Validate required fields
    if (!to || !subject) {
      logger.warn("Missing required fields for notification email", {
        to: to || "missing",
        subject: subject || "missing",
      });
      return false;
    }

    // Default HTML content
    let htmlContent = body;

    // If type is provided, use appropriate template
    if (type && data) {
      switch (type) {
        case "order":
          htmlContent = emailTemplates.getOrderEmailTemplate({
            recipientName,
            orderDetails: data.orderDetails,
            restaurantName: data.restaurantName,
            status: data.status,
            orderId: data.orderId,
          });
          break;
        case "reservation":
          htmlContent = emailTemplates.getReservationEmailTemplate({
            recipientName,
            reservationDetails: data.reservationDetails,
            restaurantName: data.restaurantName,
            status: data.status,
            reservationId: data.reservationId,
          });
          break;
        case "referral":
          htmlContent = emailTemplates.getReferralEmailTemplate({
            referrerName: data.referrerName,
            referralCode: data.referralCode,
            referralLink: data.referralLink,
            bonusPoints: data.bonusPoints,
          });
          break;
        default:
          logger.info(`Using default template for notification type: ${type}`);
      }
    }

    // Send email with appropriate priority
    return await sendEmail({
      to,
      subject,
      html: htmlContent,
      type: type || "notification",
      priority,
      metadata: {
        notificationType: type,
        recipientName,
        dataId: data?.orderId || data?.reservationId || null,
      },
    });
  } catch (error) {
    logger.error("Error sending notification email:", {
      error: error.message,
      to: options.to,
      subject: options.subject,
      type: options.type,
    });
    return false;
  }
};

module.exports = {
  sendEmail,
  sendOtpVerificationEmail,
  sendSubscriptionConfirmationEmail,
  sendUnsubscriptionConfirmationEmail,
  sendNotificationEmail,
};
