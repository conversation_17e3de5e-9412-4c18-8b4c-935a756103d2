import { Link } from "react-router-dom";
import { XCircle } from "lucide-react";

const Failed = () => {
  return (
    <div className="min-h-screen w-full flex items-center justify-center bg-white">
      <div className="text-center p-8 max-w-md">
        <div className="flex justify-center mb-6">
          <XCircle className="w-24 h-24 text-red-500" />
        </div>
        <h1 className="text-4xl font-bold text-red-700 mb-4">
          Transaction Failed
        </h1>
        <p className="text-gray-600 mb-8">
          We apologize, but something went wrong with your transaction. Please
          try again or contact support if the issue persists.
        </p>
        <div className="space-y-4">
          <Link
            to="/"
            className="inline-flex items-center px-6 py-3 text-base font-medium text-white bg-red-600 rounded-lg hover:bg-red-700 transition-colors duration-200 w-full justify-center"
          >
            Return to Home
          </Link>
          <button
            onClick={() => window.history.back()}
            className="inline-flex items-center px-6 py-3 text-base font-medium text-red-700 bg-red-100 rounded-lg hover:bg-red-200 transition-colors duration-200 w-full justify-center"
          >
            Try Again
          </button>
        </div>
      </div>
    </div>
  );
};

export default Failed;
