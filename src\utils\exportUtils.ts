import { saveAs } from 'file-saver';
import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';
import { format } from 'date-fns';

// Helper function to convert data to CSV format
export const convertToCSV = <T extends Record<string, unknown>>(data: T[], headers: string[], headerLabels: string[]): string => {
  // Create CSV header row
  let csv = headerLabels.join(',') + '\n';

  // Add data rows
  data.forEach(item => {
    const row = headers.map(header => {
      // Handle special cases like dates or numbers
      const value = item[header];
      if (value instanceof Date) {
        return `"${format(value, 'yyyy-MM-dd')}"`;
      } else if (typeof value === 'string') {
        // Escape quotes and wrap in quotes to handle commas in text
        return `"${value.replace(/"/g, '""')}"`;
      } else {
        return value;
      }
    });
    csv += row.join(',') + '\n';
  });

  return csv;
};

// Export data as CSV file
export const exportAsCSV = <T extends Record<string, unknown>>(data: T[], headers: string[], headerLabels: string[], filename: string): void => {
  const csv = convertToCSV(data, headers, headerLabels);
  const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
  saveAs(blob, `${filename}_${format(new Date(), 'yyyy-MM-dd')}.csv`);
};

// Export data as PDF file
export const exportAsPDF = <T extends Record<string, unknown>>(
  data: T[],
  headers: string[],
  headerLabels: string[],
  filename: string,
  title: string,
  subtitle?: string,
  // logo parameter is not used
  // logo?: string
): void => {
  // Create new PDF document
  const doc = new jsPDF();
  const pageWidth = doc.internal.pageSize.getWidth();

  // Add title
  doc.setFontSize(18);
  doc.text(title, pageWidth / 2, 20, { align: 'center' });

  // Add subtitle if provided
  if (subtitle) {
    doc.setFontSize(12);
    doc.text(subtitle, pageWidth / 2, 30, { align: 'center' });
  }

  // Add date
  doc.setFontSize(10);
  doc.text(`Generated on: ${format(new Date(), 'PPP')}`, pageWidth / 2, subtitle ? 40 : 30, { align: 'center' });

  // Format data for the table
  const tableData = data.map(item => {
    return headers.map(header => {
      const value = item[header];
      if (value instanceof Date) {
        return format(value, 'yyyy-MM-dd');
      } else if (typeof value === 'number' || typeof value === 'string') {
        return value;
      } else {
        return String(value || '');
      }
    });
  }) as string[][];

  // Add table
  autoTable(doc, {
    head: [headerLabels],
    body: tableData,
    startY: subtitle ? 50 : 40,
    theme: 'grid',
    styles: {
      fontSize: 10,
      cellPadding: 3,
      lineColor: [200, 200, 200],
      lineWidth: 0.1,
    },
    headStyles: {
      fillColor: [66, 66, 66],
      textColor: 255,
      fontStyle: 'bold',
    },
    alternateRowStyles: {
      fillColor: [245, 245, 245],
    },
  });

  // Save the PDF
  doc.save(`${filename}_${format(new Date(), 'yyyy-MM-dd')}.pdf`);
};

// Export chart as PDF
export const exportChartAsPDF = (
  chartRef: React.RefObject<HTMLDivElement>,
  title: string,
  subtitle?: string,
  filename?: string
): void => {
  if (!chartRef.current) return;

  // Create new PDF document
  const doc = new jsPDF('landscape');
  const pageWidth = doc.internal.pageSize.getWidth();
  // pageHeight is not used in this function
  // const pageHeight = doc.internal.pageSize.getHeight();

  // Add title
  doc.setFontSize(18);
  doc.text(title, pageWidth / 2, 20, { align: 'center' });

  // Add subtitle if provided
  if (subtitle) {
    doc.setFontSize(12);
    doc.text(subtitle, pageWidth / 2, 30, { align: 'center' });
  }

  // Add date
  doc.setFontSize(10);
  doc.text(`Generated on: ${format(new Date(), 'PPP')}`, pageWidth / 2, subtitle ? 40 : 30, { align: 'center' });

  // Convert chart to image and add to PDF
  const canvas = document.createElement('canvas');
  canvas.width = chartRef.current.offsetWidth * 2;
  canvas.height = chartRef.current.offsetHeight * 2;
  const ctx = canvas.getContext('2d');

  if (ctx) {
    // Set scale for better quality
    ctx.scale(2, 2);

    // Use html2canvas approach with foreignObject
    const svgData = new XMLSerializer().serializeToString(chartRef.current.querySelector('svg')!);
    const img = new Image();
    const svgBlob = new Blob([svgData], { type: 'image/svg+xml;charset=utf-8' });
    const url = URL.createObjectURL(svgBlob);

    img.onload = () => {
      ctx.drawImage(img, 0, 0);
      URL.revokeObjectURL(url);

      const imgData = canvas.toDataURL('image/png');

      // Calculate dimensions to fit the page while maintaining aspect ratio
      const imgWidth = pageWidth - 40; // 20mm margin on each side
      const imgHeight = (chartRef.current!.offsetHeight / chartRef.current!.offsetWidth) * imgWidth;

      // Add the image to the PDF
      doc.addImage(imgData, 'PNG', 20, subtitle ? 50 : 40, imgWidth, imgHeight);

      // Save the PDF
      doc.save(`${filename || title}_${format(new Date(), 'yyyy-MM-dd')}.pdf`);
    };

    img.src = url;
  }
};

// Export all analytics data as PDF report
interface ChartData {
  date: string;
  count: number;
  revenue: number;
}

interface MenuItemData {
  name: string;
  count: number;
}

interface ReservationData {
  name: string;
  value: number;
}

interface TopCustomer {
  userId: string;
  customerName: string;
  totalSpent: number;
  orderCount: number;
}

interface CustomerMetric {
  name: string;
  value: number;
}

export const exportAnalyticsReport = (
  orderData: ChartData[],
  // Revenue data is used in the PDF generation but not in this function directly
  // We need to keep this parameter for compatibility with existing code
  _revenueData: ChartData[],
  menuItemData: MenuItemData[],
  reservationData: ReservationData[],
  restaurantName: string,
  timeRange: string,
  comparisonData?: {
    orders: ChartData[],
    revenue: ChartData[],
    menuItems: MenuItemData[],
    reservations: ReservationData[]
  },
  advancedMetrics?: {
    averageOrderValue: number,
    customerRetentionRate: number,
    orderFrequency: number,
    revenueGrowth: number,
    topCustomers: TopCustomer[],
    customerMetrics: CustomerMetric[],
    prevAverageOrderValue?: number,
    prevCustomerRetentionRate?: number,
    prevOrderFrequency?: number
  }
): void => {
  // Create new PDF document
  const doc = new jsPDF();
  const pageWidth = doc.internal.pageSize.getWidth();

  // Add title
  doc.setFontSize(20);
  doc.text(`${restaurantName} Analytics Report`, pageWidth / 2, 20, { align: 'center' });

  // Add subtitle
  doc.setFontSize(14);
  doc.text(`${timeRange} Report`, pageWidth / 2, 30, { align: 'center' });

  // Add date
  doc.setFontSize(10);
  doc.text(`Generated on: ${format(new Date(), 'PPP')}`, pageWidth / 2, 40, { align: 'center' });

  // Add order data table
  doc.setFontSize(12);
  doc.text('Order Trends', 14, 50);

  if (comparisonData) {
    // With comparison data
    const combinedOrderData = orderData.map((item, index) => {
      const prevItem = comparisonData.orders[index] || { count: 0, revenue: 0 };
      return [
        item.date,
        item.count,
        item.revenue.toFixed(2),
        prevItem.count || 0,
        prevItem.revenue ? prevItem.revenue.toFixed(2) : '0.00',
        ((item.count - prevItem.count) / (prevItem.count || 1) * 100).toFixed(1) + '%',
        ((item.revenue - prevItem.revenue) / (prevItem.revenue || 1) * 100).toFixed(1) + '%'
      ];
    });

    autoTable(doc, {
      head: [[
        'Date',
        'Orders',
        'Revenue (AZN)',
        'Previous Orders',
        'Previous Revenue (AZN)',
        'Orders Change %',
        'Revenue Change %'
      ]],
      body: combinedOrderData,
      startY: 55,
      theme: 'grid',
      styles: {
        fontSize: 9,
        cellPadding: 3,
      },
      headStyles: {
        fillColor: [66, 66, 66],
        textColor: 255,
        fontStyle: 'bold',
      },
    });
  } else {
    // Without comparison data
    autoTable(doc, {
      head: [['Date', 'Orders', 'Revenue (AZN)']],
      body: orderData.map(item => [item.date, item.count, item.revenue.toFixed(2)]),
      startY: 55,
      theme: 'grid',
      styles: {
        fontSize: 10,
        cellPadding: 3,
      },
      headStyles: {
        fillColor: [66, 66, 66],
        textColor: 255,
        fontStyle: 'bold',
      },
    });
  }

  // Add popular menu items table
  doc.addPage();
  doc.setFontSize(12);
  doc.text('Popular Menu Items', 14, 20);

  if (comparisonData && comparisonData.menuItems.length > 0) {
    // With comparison data
    const combinedMenuData = menuItemData.map(item => {
      const prevItem = comparisonData.menuItems.find(p => p.name === item.name);
      const prevCount = prevItem ? prevItem.count : 0;
      const change = prevCount > 0 ? ((item.count - prevCount) / prevCount * 100).toFixed(1) + '%' : 'N/A';
      return [item.name, item.count, prevCount, change];
    });

    autoTable(doc, {
      head: [['Item Name', 'Orders', 'Previous Orders', 'Change %']],
      body: combinedMenuData,
      startY: 25,
      theme: 'grid',
      styles: {
        fontSize: 10,
        cellPadding: 3,
      },
      headStyles: {
        fillColor: [66, 66, 66],
        textColor: 255,
        fontStyle: 'bold',
      },
    });
  } else {
    // Without comparison data
    autoTable(doc, {
      head: [['Item Name', 'Orders']],
      body: menuItemData.map(item => [item.name, item.count]),
      startY: 25,
      theme: 'grid',
      styles: {
        fontSize: 10,
        cellPadding: 3,
      },
      headStyles: {
        fillColor: [66, 66, 66],
        textColor: 255,
        fontStyle: 'bold',
      },
    });
  }

  // Add reservation status table
  doc.setFontSize(12);
  // @ts-expect-error - jsPDF-AutoTable adds this property
  doc.text('Reservation Status', 14, doc.lastAutoTable?.finalY ? doc.lastAutoTable.finalY + 20 : 120);

  if (comparisonData && comparisonData.reservations.length > 0) {
    // With comparison data
    const combinedReservationData = reservationData.map(item => {
      const prevItem = comparisonData.reservations.find(p => p.name === item.name);
      const prevValue = prevItem ? prevItem.value : 0;
      const change = prevValue > 0 ? ((item.value - prevValue) / prevValue * 100).toFixed(1) + '%' : 'N/A';
      return [item.name, item.value, prevValue, change];
    });

    autoTable(doc, {
      head: [['Status', 'Count', 'Previous Count', 'Change %']],
      body: combinedReservationData,
      // @ts-expect-error - jsPDF-AutoTable adds this property
      startY: doc.lastAutoTable?.finalY ? doc.lastAutoTable.finalY + 25 : 125,
      theme: 'grid',
      styles: {
        fontSize: 10,
        cellPadding: 3,
      },
      headStyles: {
        fillColor: [66, 66, 66],
        textColor: 255,
        fontStyle: 'bold',
      },
    });
  } else {
    // Without comparison data
    autoTable(doc, {
      head: [['Status', 'Count']],
      body: reservationData.map(item => [item.name, item.value]),
      // @ts-expect-error - jsPDF-AutoTable adds this property
      startY: doc.lastAutoTable?.finalY ? doc.lastAutoTable.finalY + 25 : 125,
      theme: 'grid',
      styles: {
        fontSize: 10,
        cellPadding: 3,
      },
      headStyles: {
        fillColor: [66, 66, 66],
        textColor: 255,
        fontStyle: 'bold',
      },
    });
  }

  // Add summary statistics
  doc.addPage();
  doc.setFontSize(14);
  doc.text('Summary Statistics', pageWidth / 2, 20, { align: 'center' });

  // Calculate summary statistics
  const totalOrders = orderData.reduce((sum, item) => sum + item.count, 0);
  const totalRevenue = orderData.reduce((sum, item) => sum + item.revenue, 0);
  const calculatedAOV = totalOrders > 0 ? totalRevenue / totalOrders : 0;

  // Use advanced metrics if provided, otherwise use calculated values
  const aov = advancedMetrics?.averageOrderValue || calculatedAOV;
  const retentionRate = advancedMetrics?.customerRetentionRate || 0;
  const frequency = advancedMetrics?.orderFrequency || 0;
  const growth = advancedMetrics?.revenueGrowth || 0;

  let summaryData = [];

  if (comparisonData) {
    // With comparison data
    const prevTotalOrders = comparisonData.orders.reduce((sum, item) => sum + item.count, 0);
    const prevTotalRevenue = comparisonData.orders.reduce((sum, item) => sum + item.revenue, 0);
    const prevCalculatedAOV = prevTotalOrders > 0 ? prevTotalRevenue / prevTotalOrders : 0;

    // Use advanced metrics if provided, otherwise use calculated values
    const prevAOV = advancedMetrics?.prevAverageOrderValue || prevCalculatedAOV;
    const prevRetentionRate = advancedMetrics?.prevCustomerRetentionRate || 0;
    const prevFrequency = advancedMetrics?.prevOrderFrequency || 0;

    const orderChange = prevTotalOrders > 0 ? ((totalOrders - prevTotalOrders) / prevTotalOrders * 100).toFixed(1) + '%' : 'N/A';
    const revenueChange = prevTotalRevenue > 0 ? ((totalRevenue - prevTotalRevenue) / prevTotalRevenue * 100).toFixed(1) + '%' : 'N/A';
    const aovChange = prevAOV > 0 ? ((aov - prevAOV) / prevAOV * 100).toFixed(1) + '%' : 'N/A';
    const retentionChange = prevRetentionRate > 0 ? ((retentionRate - prevRetentionRate) / prevRetentionRate * 100).toFixed(1) + '%' : 'N/A';
    const frequencyChange = prevFrequency > 0 ? ((frequency - prevFrequency) / prevFrequency * 100).toFixed(1) + '%' : 'N/A';

    summaryData = [
      ['Total Orders (Current)', totalOrders.toString()],
      ['Total Orders (Previous)', prevTotalOrders.toString()],
      ['Orders Change', orderChange],
      ['Total Revenue (Current)', `${totalRevenue.toFixed(2)} AZN`],
      ['Total Revenue (Previous)', `${prevTotalRevenue.toFixed(2)} AZN`],
      ['Revenue Change', revenueChange],
      ['Revenue Growth', `${growth.toFixed(1)}%`],
      ['Average Order Value (Current)', `${aov.toFixed(2)} AZN`],
      ['Average Order Value (Previous)', `${prevAOV.toFixed(2)} AZN`],
      ['AOV Change', aovChange],
      ['Customer Retention Rate (Current)', `${retentionRate.toFixed(1)}%`],
      ['Customer Retention Rate (Previous)', `${prevRetentionRate.toFixed(1)}%`],
      ['Retention Change', retentionChange],
      ['Order Frequency (Current)', frequency.toFixed(1)],
      ['Order Frequency (Previous)', prevFrequency.toFixed(1)],
      ['Frequency Change', frequencyChange],
      ['Most Popular Item (Current)', menuItemData.length > 0 ? menuItemData[0].name : 'N/A'],
      ['Most Popular Item (Previous)', comparisonData.menuItems.length > 0 ? comparisonData.menuItems[0].name : 'N/A'],
      ['Comparison Period', `Current vs Previous ${timeRange}`],
    ];
  } else {
    // Without comparison data
    summaryData = [
      ['Total Orders', totalOrders.toString()],
      ['Total Revenue', `${totalRevenue.toFixed(2)} AZN`],
      ['Average Order Value', `${aov.toFixed(2)} AZN`],
      ['Customer Retention Rate', `${retentionRate.toFixed(1)}%`],
      ['Order Frequency', frequency.toFixed(1)],
      ['Revenue Growth', `${growth.toFixed(1)}%`],
      ['Most Popular Item', menuItemData.length > 0 ? menuItemData[0].name : 'N/A'],
      ['Time Period', timeRange],
    ];
  }

  // Add top customers table if available
  if (advancedMetrics?.topCustomers && advancedMetrics.topCustomers.length > 0) {
    doc.addPage();
    doc.setFontSize(14);
    doc.text('Top Customers', pageWidth / 2, 20, { align: 'center' });

    const topCustomersData = advancedMetrics.topCustomers.map(customer => [
      customer.customerName,
      customer.orderCount,
      `${customer.totalSpent.toFixed(2)} AZN`,
      `${(customer.totalSpent / customer.orderCount).toFixed(2)} AZN`
    ]);

    autoTable(doc, {
      head: [['Customer', 'Orders', 'Total Spent', 'Avg. Order']],
      body: topCustomersData,
      startY: 30,
      theme: 'grid',
      styles: {
        fontSize: 10,
        cellPadding: 3,
      },
      headStyles: {
        fillColor: [66, 66, 66],
        textColor: 255,
        fontStyle: 'bold',
      },
    });
  }

  autoTable(doc, {
    body: summaryData,
    startY: 30,
    theme: 'plain',
    styles: {
      fontSize: 12,
      cellPadding: 5,
    },
    columnStyles: {
      0: { fontStyle: 'bold' },
    },
  });

  // Save the PDF
  doc.save(`${restaurantName}_Analytics_Report_${format(new Date(), 'yyyy-MM-dd')}.pdf`);
};
