import React, { useState, useMemo } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { RewardCard } from "./RewardCard";
import { RewardRedemptionCard } from "./RewardRedemptionCard";
import { RewardFiltersComponent, RewardFilters } from "./RewardFilters";
import {
  useAvailableRewardsWithRestaurantInfo,
  useRedeemedRewards,
  useRedeemReward,
} from "@/lib/react-query/hooks/useRewards";
import { useAuth } from "@/providers/AuthProvider";
import { Gift, AlertCircle } from "lucide-react";
import { Reward } from "@/types/rewards";
import { applyRewardFilters } from "@/utils/rewardFilters";

interface RewardsMarketplaceProps {
  userPoints: number;
  onRewardRedeemed?: () => void;
}

export const RewardsMarketplace: React.FC<RewardsMarketplaceProps> = ({
  userPoints,
  onRewardRedeemed,
}) => {
  const { user } = useAuth();
  const [selectedReward, setSelectedReward] = useState<Reward | null>(null);
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [successDialogOpen, setSuccessDialogOpen] = useState(false);
  const [redemptionCode, setRedemptionCode] = useState<string | null>(null);
  const [filters, setFilters] = useState<RewardFilters>({});

  const {
    data: availableRewards = [],
    isLoading: isLoadingRewards,
    isError: isRewardsError,
  } = useAvailableRewardsWithRestaurantInfo(user?.uid);

  const {
    data: redeemedRewards = [],
    isLoading: isLoadingRedeemed,
    isError: isRedeemedError,
  } = useRedeemedRewards(user?.uid);

  const { mutate: redeemReward, isPending: isRedeeming } = useRedeemReward();

  // Apply filters to available rewards
  const filteredRewards = useMemo(() => {
    return applyRewardFilters(availableRewards, filters);
  }, [availableRewards, filters]);

  const handleRedeemClick = (rewardId: string) => {
    const reward = filteredRewards.find((r) => r.id === rewardId);
    if (reward) {
      setSelectedReward(reward);
      setConfirmDialogOpen(true);
    }
  };

  const handleConfirmRedeem = () => {
    if (!selectedReward || !user) return;

    redeemReward(
      { userId: user.uid, rewardId: selectedReward.id },
      {
        onSuccess: (code) => {
          setRedemptionCode(code);
          setConfirmDialogOpen(false);
          setSuccessDialogOpen(true);

          if (onRewardRedeemed) {
            onRewardRedeemed();
          }
        },
      }
    );
  };

  const closeSuccessDialog = () => {
    setSuccessDialogOpen(false);
    setRedemptionCode(null);
    setSelectedReward(null);
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center text-xl">
            <Gift className="mr-3 h-6 w-6 text-primary" />
            Rewards Marketplace
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          <Tabs defaultValue="available">
            <TabsList className="w-full mb-6">
              <TabsTrigger value="available" className="flex-1 text-sm">
                Available Rewards ({filteredRewards.length})
              </TabsTrigger>
              <TabsTrigger value="redeemed" className="flex-1 text-sm">
                Your Rewards
              </TabsTrigger>
            </TabsList>

            <TabsContent value="available" className="mt-0">
              <div className="space-y-6">
                {/* Filters at the top */}
                <RewardFiltersComponent
                  rewards={availableRewards}
                  filters={filters}
                  onFiltersChange={setFilters}
                />

                {/* Rewards Grid */}
                <div>
                  {isLoadingRewards ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                      {[...Array(6)].map((_, i) => (
                        <Card key={i} className="overflow-hidden">
                          <div className="h-40">
                            <Skeleton className="h-full w-full" />
                          </div>
                          <CardContent className="p-4">
                            <Skeleton className="h-5 w-3/4 mb-3" />
                            <Skeleton className="h-4 w-1/2 mb-2" />
                            <Skeleton className="h-3 w-full mb-2" />
                            <Skeleton className="h-3 w-full mb-4" />
                            <Skeleton className="h-9 w-full" />
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  ) : isRewardsError ? (
                    <div className="text-center py-12">
                      <AlertCircle className="h-12 w-12 text-destructive mx-auto mb-4" />
                      <h3 className="text-lg font-medium mb-2">
                        Failed to load rewards
                      </h3>
                      <p className="text-muted-foreground">
                        Please try again later or refresh the page.
                      </p>
                    </div>
                  ) : availableRewards.length === 0 ? (
                    <div className="text-center py-12">
                      <Gift className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                      <h3 className="text-lg font-medium mb-2">
                        No rewards available
                      </h3>
                      <p className="text-muted-foreground">
                        Earn more points to unlock exciting rewards!
                      </p>
                    </div>
                  ) : filteredRewards.length === 0 ? (
                    <div className="text-center py-12">
                      <Gift className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                      <h3 className="text-lg font-medium mb-2">
                        No matching rewards
                      </h3>
                      <p className="text-muted-foreground">
                        Try adjusting your filters to see more rewards.
                      </p>
                    </div>
                  ) : (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                      {filteredRewards.map((reward) => (
                        <RewardCard
                          key={reward.id}
                          reward={reward}
                          userPoints={userPoints}
                          onRedeem={handleRedeemClick}
                          isLoading={isRedeeming}
                        />
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </TabsContent>

            <TabsContent value="redeemed" className="mt-0">
              {isLoadingRedeemed ? (
                <div className="space-y-4">
                  {[...Array(2)].map((_, i) => (
                    <Card key={i} className="overflow-hidden">
                      <CardContent className="p-5">
                        <Skeleton className="h-5 w-3/4 mb-3" />
                        <Skeleton className="h-4 w-1/2 mb-3" />
                        <Skeleton className="h-10 w-full mb-3" />
                        <Skeleton className="h-3 w-full" />
                      </CardContent>
                    </Card>
                  ))}
                </div>
              ) : isRedeemedError ? (
                <div className="text-center py-12">
                  <AlertCircle className="h-12 w-12 text-destructive mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-2">
                    Failed to load your rewards
                  </h3>
                  <p className="text-muted-foreground">
                    Please try again later or refresh the page.
                  </p>
                </div>
              ) : redeemedRewards.length === 0 ? (
                <div className="text-center py-12">
                  <Gift className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-2">No rewards yet</h3>
                  <p className="text-muted-foreground">
                    You haven't redeemed any rewards yet. Redeem points for
                    exciting rewards!
                  </p>
                </div>
              ) : (
                <ScrollArea className="h-[500px] pr-4">
                  <div className="space-y-4">
                    {redeemedRewards.map((redemption) => (
                      <RewardRedemptionCard
                        key={redemption.id}
                        redemption={redemption}
                      />
                    ))}
                  </div>
                </ScrollArea>
              )}
            </TabsContent>
          </Tabs>

          {/* Confirmation Dialog */}
          <Dialog open={confirmDialogOpen} onOpenChange={setConfirmDialogOpen}>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Confirm Reward Redemption</DialogTitle>
                <DialogDescription>
                  Are you sure you want to redeem this reward? This will deduct{" "}
                  {selectedReward?.pointsCost} points from your balance.
                </DialogDescription>
              </DialogHeader>

              {selectedReward && (
                <div className="py-4">
                  <h3 className="font-medium">{selectedReward.name}</h3>
                  <p className="text-sm text-muted-foreground mt-1">
                    {selectedReward.description}
                  </p>

                  {selectedReward.termsAndConditions && (
                    <div className="mt-3 p-3 bg-muted rounded-md text-xs">
                      <strong>Terms & Conditions:</strong>{" "}
                      {selectedReward.termsAndConditions}
                    </div>
                  )}

                  <div className="mt-4 p-3 bg-muted rounded-md text-center">
                    <p className="text-sm font-medium">Cost</p>
                    <p className="text-xl font-bold">
                      {selectedReward.pointsCost} points
                    </p>
                  </div>
                </div>
              )}

              <DialogFooter>
                <Button
                  variant="outline"
                  onClick={() => setConfirmDialogOpen(false)}
                  disabled={isRedeeming}
                >
                  Cancel
                </Button>
                <Button onClick={handleConfirmRedeem} disabled={isRedeeming}>
                  {isRedeeming ? "Processing..." : "Confirm Redemption"}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          {/* Success Dialog */}
          <Dialog open={successDialogOpen} onOpenChange={setSuccessDialogOpen}>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Reward Redeemed Successfully!</DialogTitle>
                <DialogDescription>
                  You have successfully redeemed {selectedReward?.name}. Your
                  redemption code is:
                </DialogDescription>
              </DialogHeader>

              <div className="py-4">
                <div className="p-4 bg-muted rounded-md text-center">
                  <p className="text-xl font-mono font-bold">
                    {redemptionCode}
                  </p>
                </div>

                <p className="text-sm text-muted-foreground mt-4">
                  This code has been saved to your account. You can view it
                  anytime in the "Your Rewards" tab.
                </p>
              </div>

              <DialogFooter>
                <Button onClick={closeSuccessDialog}>Done</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </CardContent>
      </Card>
    </div>
  );
};
