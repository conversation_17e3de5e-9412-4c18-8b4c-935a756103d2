import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import {
  collection,
  query,
  where,
  getDocs,
  getDoc,
  doc,
  updateDoc,
  onSnapshot,
  limit,
  startAfter,
  orderBy,
  DocumentData,
  QueryDocumentSnapshot,
} from "firebase/firestore";
import { firestore } from "@/config/firebase";
import { Restaurant } from "@/types/restaurant";
import { queryKeys, CACHE_TIME, STALE_TIME } from "../index";
import { useEffect, useState } from "react";
import { FilterOptions } from "@/services/RestaurantFilterService";
import { RestaurantFilterService } from "@/services/RestaurantFilterService";
import { toast } from "sonner";

// Number of restaurants to fetch per page
const RESTAURANTS_PER_PAGE = 20;

/**
 * Hook to fetch all restaurants with caching
 */
export function useAllRestaurants() {
  return useQuery({
    queryKey: queryKeys.restaurants.all,
    queryFn: async () => {
      try {
        const result = await RestaurantFilterService.getAllRestaurants();
        return result;
      } catch (error) {
        console.error("Error fetching all restaurants:", error);
        toast.error("Failed to load restaurant data");
        throw error;
      }
    },
    staleTime: STALE_TIME,
    gcTime: CACHE_TIME,
  });
}

/**
 * Hook to fetch filtered restaurants with caching
 */
export function useFilteredRestaurants(filterOptions: FilterOptions) {
  return useQuery({
    queryKey: queryKeys.restaurants.filtered(
      filterOptions as unknown as Record<string, unknown>
    ),
    queryFn: async () => {
      try {
        const result = await RestaurantFilterService.getFilteredRestaurants(
          filterOptions
        );
        return {
          restaurants: result.restaurants,
          facets: result.facets,
          totalCount: result.totalCount,
        };
      } catch (error) {
        console.error("Error fetching filtered restaurants:", error);
        toast.error("Failed to load restaurant data");
        throw error;
      }
    },
    staleTime: STALE_TIME,
    gcTime: CACHE_TIME,
  });
}

/**
 * Hook to fetch a single restaurant by ID with caching
 */
export function useRestaurant(restaurantId: string | undefined) {
  return useQuery({
    queryKey: queryKeys.restaurants.detail(restaurantId || ""),
    queryFn: async () => {
      if (!restaurantId) {
        throw new Error("Restaurant ID is required");
      }

      const restaurantRef = doc(firestore, "restaurants", restaurantId);
      const restaurantSnap = await getDoc(restaurantRef);

      if (!restaurantSnap.exists()) {
        throw new Error("Restaurant not found");
      }

      return { id: restaurantSnap.id, ...restaurantSnap.data() } as Restaurant;
    },
    enabled: !!restaurantId,
    staleTime: STALE_TIME,
    gcTime: CACHE_TIME,
  });
}

/**
 * Hook to fetch a single restaurant by username with caching
 */
export function useRestaurantByUsername(username: string | undefined) {
  return useQuery({
    queryKey: ["restaurant_by_username", username],
    queryFn: async () => {
      if (!username) {
        throw new Error("Restaurant username is required");
      }

      const restaurantsRef = collection(firestore, "restaurants");
      const q = query(
        restaurantsRef,
        where("username", "==", username),
        where("isActive", "==", true)
      );

      const querySnapshot = await getDocs(q);

      if (querySnapshot.empty) {
        throw new Error("Restaurant not found");
      }

      const restaurantDoc = querySnapshot.docs[0];
      return { id: restaurantDoc.id, ...restaurantDoc.data() } as Restaurant;
    },
    enabled: !!username,
    staleTime: STALE_TIME,
    gcTime: CACHE_TIME,
  });
}

/**
 * Hook to fetch restaurants with real-time updates
 */
export function useRealtimeRestaurants(pageSize = RESTAURANTS_PER_PAGE) {
  const [restaurants, setRestaurants] = useState<Restaurant[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [lastDoc, setLastDoc] =
    useState<QueryDocumentSnapshot<DocumentData> | null>(null);
  const [hasMore, setHasMore] = useState(true);

  // Initial fetch
  useEffect(() => {
    setLoading(true);
    setError(null);

    const restaurantsRef = collection(firestore, "restaurants");
    const q = query(
      restaurantsRef,
      where("isActive", "==", true),
      orderBy("restaurantName"),
      limit(pageSize)
    );

    const unsubscribe = onSnapshot(
      q,
      (snapshot) => {
        const fetchedRestaurants: Restaurant[] = [];
        snapshot.forEach((doc) => {
          fetchedRestaurants.push({ id: doc.id, ...doc.data() } as Restaurant);
        });

        setRestaurants(fetchedRestaurants);
        setLastDoc(snapshot.docs[snapshot.docs.length - 1] || null);
        setHasMore(snapshot.docs.length === pageSize);
        setLoading(false);
      },
      (err) => {
        console.error("Error fetching restaurants:", err);
        setError(err as Error);
        setLoading(false);
      }
    );

    return () => unsubscribe();
  }, [pageSize]);

  // Function to load more restaurants
  const loadMore = async () => {
    if (!lastDoc || !hasMore) return;

    try {
      const restaurantsRef = collection(firestore, "restaurants");
      const q = query(
        restaurantsRef,
        where("isActive", "==", true),
        orderBy("restaurantName"),
        startAfter(lastDoc),
        limit(pageSize)
      );

      const snapshot = await getDocs(q);
      const newRestaurants: Restaurant[] = [];

      snapshot.forEach((doc) => {
        newRestaurants.push({ id: doc.id, ...doc.data() } as Restaurant);
      });

      setRestaurants((prev) => [...prev, ...newRestaurants]);
      setLastDoc(snapshot.docs[snapshot.docs.length - 1] || null);
      setHasMore(snapshot.docs.length === pageSize);
    } catch (err) {
      console.error("Error loading more restaurants:", err);
      setError(err as Error);
    }
  };

  return { restaurants, loading, error, hasMore, loadMore };
}

/**
 * Hook to update a restaurant with optimistic updates
 */
export function useUpdateRestaurant() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      restaurantId,
      data,
    }: {
      restaurantId: string;
      data: Partial<Restaurant>;
    }) => {
      const restaurantRef = doc(firestore, "restaurants", restaurantId);
      await updateDoc(restaurantRef, data);
      return { id: restaurantId, ...data };
    },
    onMutate: async ({ restaurantId, data }) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({
        queryKey: queryKeys.restaurants.detail(restaurantId),
      });

      // Snapshot the previous value
      const previousRestaurant = queryClient.getQueryData<Restaurant>(
        queryKeys.restaurants.detail(restaurantId)
      );

      // Optimistically update to the new value
      if (previousRestaurant) {
        queryClient.setQueryData(queryKeys.restaurants.detail(restaurantId), {
          ...previousRestaurant,
          ...data,
        });
      }

      return { previousRestaurant };
    },
    onError: (_, { restaurantId }, context) => {
      // If the mutation fails, use the context returned from onMutate to roll back
      if (context?.previousRestaurant) {
        queryClient.setQueryData(
          queryKeys.restaurants.detail(restaurantId),
          context.previousRestaurant
        );
      }
    },
    onSettled: (_, __, { restaurantId }) => {
      // Always refetch after error or success to make sure our local data is correct
      queryClient.invalidateQueries({
        queryKey: queryKeys.restaurants.detail(restaurantId),
      });
      queryClient.invalidateQueries({ queryKey: queryKeys.restaurants.all });
    },
  });
}
