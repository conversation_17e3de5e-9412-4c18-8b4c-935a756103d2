import { useState, useEffect } from "react";
import { useParams } from "react-router-dom";
import { doc, getDoc } from "firebase/firestore";
import { firestore } from "@/config/firebase";
import { Loading } from "@/components/ui/loading";
import { format } from "date-fns";
import { tr } from "date-fns/locale";
import { Order } from "@/types/dashboard";
import { toast } from "sonner";
import { Button } from "@/components/ui/button";
import { Printer, Share2, Copy, Check } from "lucide-react";

import { ShareableCard } from "@/components/ui/shareable-card";
import { getOrderShareUrl, generateHashtags } from "@/utils/shareUtils";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { MetaTags } from "@/components/ui/meta-tags";

export const Receipt = () => {
  const { restaurantId, orderId } = useParams();
  const [order, setOrder] = useState<Order | null>(null);
  const [loading, setLoading] = useState(true);
  const [copiedOrderId, setCopiedOrderId] = useState<boolean>(false);

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text).then(() => {
      setCopiedOrderId(true);
      setTimeout(() => setCopiedOrderId(false), 2000);
    });
  };

  useEffect(() => {
    const fetchOrder = async () => {
      if (!restaurantId || !orderId) return;

      try {
        const orderRef = doc(
          firestore,
          "restaurants",
          restaurantId,
          "orders",
          orderId
        );
        const orderDoc = await getDoc(orderRef);

        if (!orderDoc.exists()) {
          toast.error("Order not found");
          return;
        }

        setOrder({ orderId: orderDoc.id, ...orderDoc.data() } as Order);
      } catch (error) {
        console.error("Error fetching order:", error);
        toast.error("Error loading order");
      } finally {
        setLoading(false);
      }
    };

    fetchOrder();
  }, [restaurantId, orderId]);

  const handlePrint = () => {
    window.print();
  };

  if (loading) return <Loading />;
  if (!order) return <div>Order not found</div>;

  return (
    <div className="max-w-[80mm] mx-auto p-4 text-sm">
      {/* Add meta tags for social sharing */}
      <MetaTags
        title={`Order Receipt from ${
          order.restaurantName || "Qonai Restaurant"
        } | Qonai Food Ordering`}
        description={`Order #${order.orderId.slice(0, 8)} - ${
          order.items.length
        } items - Total: ${
          typeof order.totalPrice === "number"
            ? order.totalPrice.toFixed(2)
            : order.totalPrice
        } AZN`}
        imageUrl="/restaurant.png"
        url={getOrderShareUrl(restaurantId || "", orderId || "")}
        type="food"
      />
      <style>
        {`@media print {
          body * {
            visibility: hidden;
          }
          .receipt, .receipt * {
            visibility: visible;
          }
          .receipt {
            position: relative;
            left: 50%;
            transform: translateX(-50%);
            width: 80mm;
          }
        }`}
      </style>
      <div className="receipt">
        <div className="text-center mb-6">
          <h1 className="text-xl font-bold">{order.restaurantName}</h1>
          <div className="flex items-center justify-center gap-1 mt-1">
            <p className="text-xs">Receipt #{order.orderId.slice(0, 8)}</p>
            <Button
              variant="ghost"
              size="sm"
              className="h-5 w-5 p-0"
              onClick={() => copyToClipboard(order.orderId)}
            >
              {copiedOrderId ? (
                <Check className="h-3 w-3 text-green-500" />
              ) : (
                <Copy className="h-3 w-3 text-gray-500" />
              )}
              <span className="sr-only">Copy full order ID</span>
            </Button>
          </div>
          <p className="text-xs mt-1">
            {format(order.orderDate.toDate(), "d MMM yyyy HH:mm:ss", {
              locale: tr,
            })}
          </p>
        </div>

        <div className="border-t border-b py-4 my-4">
          <div className="space-y-2">
            <div className="flex justify-between font-medium">
              <span>Status</span>
              <span>{order.status}</span>
            </div>
            {order.tableName && (
              <div className="flex justify-between">
                <span>Table</span>
                <span>{order.tableName}</span>
              </div>
            )}
          </div>
        </div>

        <div className="space-y-2 mb-4">
          {order.items.map((item, index) => (
            <div key={index} className="flex justify-between">
              <div>
                <span>
                  {item.quantity}x {item.name}
                </span>
              </div>
              <span>
                {typeof item.price === "number"
                  ? (item.price * item.quantity).toFixed(2)
                  : item.price * item.quantity}{" "}
                AZN
              </span>
            </div>
          ))}
        </div>

        <div className="border-t pt-4">
          <div className="flex justify-between font-bold">
            <span>Total</span>
            <span>
              {typeof order.totalPrice === "number"
                ? order.totalPrice.toFixed(2)
                : order.totalPrice}{" "}
              AZN
            </span>
          </div>
        </div>

        <div className="text-center mt-8 text-xs">
          <p>Thank you for your business!</p>
          <p>{format(new Date(), "d MMM yyyy")}</p>
        </div>

        <div className="flex justify-between items-center mt-4">
          <Dialog>
            <DialogTrigger asChild>
              <Button variant="outline" size="sm">
                <Share2 className="h-4 w-4 mr-2" />
                Share Receipt
              </Button>
            </DialogTrigger>
            <DialogContent
              className="sm:max-w-md w-[95vw] max-w-[450px]"
              hideCloseButton
            >
              <DialogHeader className="mb-4">
                <DialogTitle className="text-xl">
                  Share Order Receipt
                </DialogTitle>
                <DialogDescription>
                  Share your order from{" "}
                  {order.restaurantName || "Qonai Restaurant"} with friends and
                  family
                </DialogDescription>
              </DialogHeader>

              <ShareableCard
                title={`Order from ${
                  order.restaurantName || "Qonai Restaurant"
                }`}
                description={`Order #${order.orderId.slice(0, 8)} - ${
                  order.items.length
                } items - Total: ${
                  typeof order.totalPrice === "number"
                    ? order.totalPrice.toFixed(2)
                    : order.totalPrice
                } AZN`}
                imageUrl="/restaurant.png"
                url={getOrderShareUrl(restaurantId || "", orderId || "")}
                hashtags={generateHashtags(["Qonai", "Food", "Order"])}
              >
                <div className="space-y-2 mt-2 pt-2 border-t">
                  <div className="text-sm text-muted-foreground">
                    <strong>Status:</strong>{" "}
                    {order.status
                      ? order.status.charAt(0).toUpperCase() +
                        order.status.slice(1)
                      : "Completed"}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    <strong>Date:</strong>{" "}
                    {order.orderDate &&
                    typeof order.orderDate.toDate === "function"
                      ? format(order.orderDate.toDate(), "d MMM yyyy", {
                          locale: tr,
                        })
                      : format(new Date(), "d MMM yyyy", { locale: tr })}
                  </div>
                  {order.items &&
                    order.items.slice(0, 2).map((item, index) => (
                      <div key={index} className="text-sm flex justify-between">
                        <span>
                          {item.quantity || 1}x {item.name || "Item"}
                        </span>
                        <span>
                          {typeof item.price === "number" && item.quantity
                            ? (item.price * item.quantity).toFixed(2)
                            : (item.price || 0) * (item.quantity || 1)}{" "}
                          AZN
                        </span>
                      </div>
                    ))}
                  {order.items && order.items.length > 2 && (
                    <div className="text-xs text-muted-foreground">
                      +{order.items.length - 2} more items
                    </div>
                  )}
                </div>
              </ShareableCard>
            </DialogContent>
          </Dialog>

          <Button onClick={handlePrint} variant="outline" size="sm">
            <Printer className="h-4 w-4 mr-2" />
            Print Receipt
          </Button>
        </div>
      </div>
    </div>
  );
};
