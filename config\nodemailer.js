/**
 * Nodemailer configuration for sending emails
 */
const nodemailer = require("nodemailer");
const dotenv = require("dotenv");
const logger = require("../utils/logger");

// Load environment variables
dotenv.config();

// Configure nodemailer
let transporter;

// Email provider options
const EMAIL_PROVIDERS = {
  GMAIL: "gmail",
  MAILTRAP: "mailtrap",
  SMTP: "smtp",
  SENDGRID: "sendgrid",
};

// Get email provider from environment or default to Gmail
const emailProvider = process.env.EMAIL_PROVIDER || EMAIL_PROVIDERS.GMAIL;

// Configure transporter based on provider
switch (emailProvider.toLowerCase()) {
  case EMAIL_PROVIDERS.MAILTRAP:
    // Mailtrap configuration for testing
    transporter = nodemailer.createTransport({
      host: "sandbox.smtp.mailtrap.io",
      port: 2525,
      secure: false,
      auth: {
        user: process.env.MAILTRAP_USER,
        pass: process.env.MAILTRAP_PASS,
      },
      debug: process.env.NODE_ENV !== "production",
      logger: process.env.NODE_ENV !== "production",
      // Connection pool for better performance
      pool: true,
      maxConnections: 5,
      rateDelta: 1000,
      rateLimit: 5,
    });
    logger.info("Using Mailtrap for email testing");
    break;

  case EMAIL_PROVIDERS.SENDGRID:
    // SendGrid configuration
    transporter = nodemailer.createTransport({
      host: "smtp.sendgrid.net",
      port: 587,
      secure: false,
      auth: {
        user: "apikey",
        pass: process.env.SENDGRID_API_KEY,
      },
      // Connection pool for better performance
      pool: true,
      maxConnections: 10,
      rateDelta: 1000,
      rateLimit: 10,
    });
    logger.info("Using SendGrid for email sending");
    break;

  case EMAIL_PROVIDERS.SMTP:
    // Custom SMTP configuration
    transporter = nodemailer.createTransport({
      host: process.env.SMTP_HOST,
      port: parseInt(process.env.SMTP_PORT || "587"),
      secure: process.env.SMTP_SECURE === "true",
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASSWORD,
      },
      // Connection pool for better performance
      pool: true,
      maxConnections: 5,
      rateDelta: 1000,
      rateLimit: 5,
    });
    logger.info(
      `Using custom SMTP (${process.env.SMTP_HOST}) for email sending`
    );
    break;

  case EMAIL_PROVIDERS.GMAIL:
  default:
    // Gmail configuration for production
    transporter = nodemailer.createTransport({
      host: "smtp.gmail.com",
      port: 465,
      secure: true, // use SSL
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASSWORD,
      },
      debug: process.env.NODE_ENV !== "production",
      logger: process.env.NODE_ENV !== "production",
      // Connection pool for better performance
      pool: true,
      maxConnections: 5,
      rateDelta: 1000,
      rateLimit: 5,
    });
    logger.info("Using Gmail for email sending");
    break;
}

// Verify transporter connection
transporter.verify((error, success) => {
  if (error) {
    logger.error("Email transporter verification failed:", {
      error: error.message,
    });
  } else {
    logger.info("Email transporter ready to send messages");
  }
});

module.exports = {
  transporter,
  defaultSender: process.env.EMAIL_FROM || process.env.EMAIL_USER,
  EMAIL_PROVIDERS,
};
