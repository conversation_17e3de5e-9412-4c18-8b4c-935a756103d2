import { useState, useEffect } from "react";
import { firestore } from "@/config/firebase";
import {
  collection,
  query,
  getDocs,
  orderBy,
  limit,
  DocumentData,
  QueryDocumentSnapshot,
} from "firebase/firestore";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { RestaurantFollower } from "@/types/followers";
import { Loading } from "@/components/ui/loading";
import { getFollowerCount } from "@/utils/followerUtils";

interface RestaurantFollowersProps {
  restaurantId: string;
}

export const RestaurantFollowers = ({ restaurantId }: RestaurantFollowersProps) => {
  const [followers, setFollowers] = useState<RestaurantFollower[]>([]);
  const [loading, setLoading] = useState(true);
  const [totalCount, setTotalCount] = useState(0);

  useEffect(() => {
    const fetchFollowers = async () => {
      try {
        setLoading(true);
        const followersRef = collection(firestore, "restaurants", restaurantId, "followers");
        const followersQuery = query(
          followersRef,
          orderBy("createdAt", "desc"),
          limit(10)
        );

        const querySnapshot = await getDocs(followersQuery);

        // Get total count using the utility function
        const count = await getFollowerCount(restaurantId);
        setTotalCount(count);

        const followersList: RestaurantFollower[] = [];

        querySnapshot.forEach((doc: QueryDocumentSnapshot<DocumentData>) => {
          const data = doc.data() as Omit<RestaurantFollower, "userId">;
          followersList.push({
            userId: doc.id,
            ...data,
          } as RestaurantFollower);
        });

        setFollowers(followersList);
      } catch (error) {
        console.error("Error fetching followers:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchFollowers();
  }, [restaurantId]);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-xl font-semibold">Followers ({totalCount})</CardTitle>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="flex justify-center py-8">
            <Loading type="default" />
          </div>
        ) : followers.length === 0 ? (
          <div className="text-center py-6 text-muted-foreground">
            <p>No followers yet.</p>
          </div>
        ) : (
          <div className="grid gap-4">
            {followers.map((follower) => (
              <div
                key={follower.userId}
                className="flex items-center justify-between p-3 rounded-lg border border-border/50"
              >
                <div className="flex items-center gap-3">
                  <div>
                    <h4 className="font-medium">{follower.username}</h4>
                    <p className="text-sm text-muted-foreground">
                      {(() => {
                        try {
                          if (follower.createdAt instanceof Date) {
                            return follower.createdAt.toLocaleDateString();
                          } else if (follower.createdAt && typeof follower.createdAt.toDate === 'function') {
                            return follower.createdAt.toDate().toLocaleDateString();
                          } else if (follower.createdAt) {
                            return new Date((follower.createdAt as unknown) as string | number).toLocaleDateString();
                          }
                          return 'Unknown date';
                        } catch (error) {
                          console.error('Error formatting date:', error);
                          return 'Unknown date';
                        }
                      })()}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};
