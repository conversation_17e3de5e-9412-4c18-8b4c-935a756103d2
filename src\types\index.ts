export interface WorkingHours {
  day: string;
  openTime: string;
  closeTime: string;
}

export interface MenuItem {
  id: string;
  itemId?: string; // For compatibility
  name: string;
  description: string;
  price: number;
  imageUrl?: string;
  restaurantId?: string;
  category: string;
  available: boolean;
  ingredients?: string[];
  dietary?: string[];
  spicyLevel?: "mild" | "medium" | "hot" | "extra hot";
  allergens?: string[];
  calories?: number;
  preparationTime?: string;
  isSignatureDish?: boolean;
  isSeasonalDish?: boolean;

  // Additional properties for compatibility with RecommendationService
  restaurantName?: string;
  restaurantUsername?: string;
  dietaryInfo?: string[];
  cuisineType?: string;
  orderCount?: number;
}

export interface RestaurantMenu {
  items: MenuItem[];
  categories: string[];
  specialOffers?: MenuItem[];
  dailySpecials?: MenuItem[];
}

export interface Restaurant {
  id: string;
  restaurantName: string;
  description: string;
  rating?: number;
  cuisines?: string[];
  priceRange?: string;
  location?: {
    latitude: number;
    longitude: number;
    address: string;
  };
  username: string;
  address: string;
  atmosphere: string;
  autoUpdateStatus: boolean;
  categories: string[];
  dietary: string[];
  dressCode: string;
  email: string;
  features: string[];
  isActive: boolean;
  isOpen: boolean;
  languages: string[];
  noiseLevel: string;
  parkingAvailable: boolean;
  paymentMethods: string[];
  phone: string;
  reservationPolicy: string;
  services: string[];
  specialties: string[];
  totalCapacity: number;
  workingHours: WorkingHours[];
  images: string[];
  menu: RestaurantMenu;
  settings?: {
    theme: string;
    notifications: boolean;
    autoConfirmReservations: boolean;
  };
  reservations?: {
    maxPartySize: number;
    minNoticeTime: number;
    depositRequired: boolean;
    cancellationPolicy: string;
  };

  // Additional properties for compatibility with RecommendationService
  name?: string;
  uid?: string;
  imageUrl?: string;
  cuisineType?: string;
  dietaryOptions?: string[];
}

export interface Order {
  orderId: string;
  restaurantId: string;
  userId: string;
  items: {
    itemId: string;
    name: string;
    price: number;
    quantity: number;
    notes?: string;
  }[];
  status: string;
  totalAmount: number;
  orderDate: Date;
  updatedAt?: Date;
  deliveryAddress?: string;
  paymentMethod: string;
  specialInstructions?: string;
  contactPhone?: string;
  scheduledFor?: Date;
  restaurantName?: string;
  userName?: string;
}
