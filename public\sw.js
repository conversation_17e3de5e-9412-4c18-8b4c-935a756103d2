// Enhanced Service Worker for SaleX App with Offline Support

const CACHE_NAME = 'salex-cache-v2';
const STATIC_CACHE_NAME = 'salex-static-v2';
const DYNAMIC_CACHE_NAME = 'salex-dynamic-v2';
const API_CACHE_NAME = 'salex-api-v2';

// Assets to cache on install
const STATIC_ASSETS = [
  '/',
  '/index.html',
  '/notification.mp3',
  '/manifest.json',
  '/favicon.ico',
  '/assets/index.css',
  '/assets/index.js',
  '/offline.html' // Fallback page for when offline
];

// API routes to cache
const API_ROUTES = [
  '/api/restaurants',
  '/api/categories',
  '/api/featured'
];

// Install event - cache static assets
self.addEventListener('install', (event) => {

  // Skip waiting to activate the new service worker immediately
  self.skipWaiting();

  event.waitUntil(
    Promise.all([
      // Cache static assets
      caches.open(STATIC_CACHE_NAME)
        .then((cache) => {
          return Promise.allSettled(
            STATIC_ASSETS.map(url =>
              cache.add(url).catch(error => {
                console.warn(`Failed to cache ${url}:`, error);
                return null;
              })
            )
          );
        }),

      // Pre-cache API routes if possible
      caches.open(API_CACHE_NAME)
        .then((cache) => {
          return Promise.allSettled(
            API_ROUTES.map(url =>
              fetch(url)
                .then(response => {
                  if (response.ok) {
                    return cache.put(url, response);
                  }
                  return null;
                })
                .catch(error => {
                  console.warn(`Failed to pre-cache API route ${url}:`, error);
                  return null;
                })
            )
          );
        })
    ])
  );
});

// Activate event - clean up old caches and claim clients
self.addEventListener('activate', (event) => {

  // Claim clients to take control immediately
  event.waitUntil(clients.claim());

  // Clean up old caches
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          if (
            cacheName !== STATIC_CACHE_NAME &&
            cacheName !== DYNAMIC_CACHE_NAME &&
            cacheName !== API_CACHE_NAME
          ) {
            return caches.delete(cacheName);
          }
        })
      );
    })
  );
});

// Helper function to determine if a request is for an API
const isApiRequest = (request) => {
  const url = new URL(request.url);
  return url.pathname.startsWith('/api/');
};

// Helper function to determine if a request is for a static asset
const isStaticAsset = (request) => {
  const url = new URL(request.url);
  return STATIC_ASSETS.some(asset => url.pathname.endsWith(asset));
};

// Helper function to determine if a request is for an image
const isImageRequest = (request) => {
  return request.destination === 'image';
};

// Fetch event with improved caching strategy
self.addEventListener('fetch', (event) => {
  const request = event.request;
  const url = new URL(request.url);

  // Skip cross-origin requests
  if (url.origin !== location.origin && !url.origin.includes('firebaseapp.com')) {
    return;
  }

  // Handle API requests - Cache then network with fallback
  if (isApiRequest(request)) {
    event.respondWith(
      caches.open(API_CACHE_NAME).then((cache) => {
        return fetch(request)
          .then((networkResponse) => {
            // Cache the fresh response
            if (networkResponse.ok) {
              cache.put(request, networkResponse.clone());
            }
            return networkResponse;
          })
          .catch(() => {
            // If offline, try to return from cache
            return cache.match(request)
              .then((cacheResponse) => {
                if (cacheResponse) {
                  return cacheResponse;
                }
                // If not in cache, return a generic offline response for API
                return new Response(
                  JSON.stringify({
                    error: 'You are offline. This data is not available offline.'
                  }),
                  {
                    headers: { 'Content-Type': 'application/json' },
                    status: 503,
                    statusText: 'Service Unavailable'
                  }
                );
              });
          });
      })
    );
    return;
  }

  // Handle static assets - Cache first, then network
  if (isStaticAsset(request)) {
    event.respondWith(
      caches.match(request)
        .then((cacheResponse) => {
          return cacheResponse || fetch(request)
            .then((networkResponse) => {
              return caches.open(STATIC_CACHE_NAME)
                .then((cache) => {
                  cache.put(request, networkResponse.clone());
                  return networkResponse;
                });
            })
            .catch(() => {
              // Return offline page for navigation requests
              if (request.mode === 'navigate') {
                return caches.match('/offline.html');
              }
              return new Response('Not available offline', {
                status: 503,
                statusText: 'Service Unavailable'
              });
            });
        })
    );
    return;
  }

  // Handle images - Cache first with network fallback
  if (isImageRequest(request)) {
    event.respondWith(
      caches.match(request)
        .then((cacheResponse) => {
          return cacheResponse || fetch(request)
            .then((networkResponse) => {
              return caches.open(DYNAMIC_CACHE_NAME)
                .then((cache) => {
                  // Cache the image for future use
                  cache.put(request, networkResponse.clone());
                  return networkResponse;
                });
            })
            .catch(() => {
              // Return a placeholder image if offline
              return new Response('Image not available offline', {
                status: 503,
                statusText: 'Service Unavailable'
              });
            });
        })
    );
    return;
  }

  // Default strategy for other requests - Network first with cache fallback
  event.respondWith(
    fetch(request)
      .then((networkResponse) => {
        // Cache successful responses for future offline use
        if (networkResponse.ok) {
          const responseToCache = networkResponse.clone();
          caches.open(DYNAMIC_CACHE_NAME)
            .then((cache) => {
              cache.put(request, responseToCache);
            });
        }
        return networkResponse;
      })
      .catch(() => {
        // If network fails, try to serve from cache
        return caches.match(request)
          .then((cacheResponse) => {
            if (cacheResponse) {
              return cacheResponse;
            }

            // Return offline page for navigation requests
            if (request.mode === 'navigate') {
              return caches.match('/offline.html');
            }

            // For other requests, return a simple offline response
            return new Response('Content not available offline', {
              status: 503,
              statusText: 'Service Unavailable'
            });
          });
      })
  );
});

// Push event - handle push notifications
self.addEventListener('push', (event) => {
  let data = {};
  if (event.data) {
    data = event.data.json();
  }

  const options = {
    body: data.message || 'New notification',
    icon: '/favicon.ico',
    badge: '/favicon.ico',
    data: {
      url: data.url || '/'
    },
    vibrate: [100, 50, 100],
    actions: [
      {
        action: 'open',
        title: 'Open'
      }
    ]
  };

  event.waitUntil(
    self.registration.showNotification(data.title || 'SaleX Notification', options)
  );
});

// Notification click event - open the app
self.addEventListener('notificationclick', (event) => {
  event.notification.close();

  if (event.action === 'open' || event.action === '') {
    const urlToOpen = event.notification.data.url || '/';

    event.waitUntil(
      clients.matchAll({
        type: 'window',
        includeUncontrolled: true
      })
      .then((windowClients) => {
        // Check if there is already a window/tab open with the target URL
        for (let i = 0; i < windowClients.length; i++) {
          const client = windowClients[i];
          // If so, focus it
          if (client.url === urlToOpen && 'focus' in client) {
            return client.focus();
          }
        }
        // If not, open a new window/tab
        if (clients.openWindow) {
          return clients.openWindow(urlToOpen);
        }
      })
    );
  }
});
