import { Badge } from "@/components/ui/badge";
import { Users } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";
import { useFollowerCount } from "@/hooks/useFollowerCount";

interface FollowerCountProps {
  restaurantId: string;
  variant?: "default" | "secondary" | "outline" | "destructive" | "ghost";
  className?: string;
  /** Whether to show the Users icon. Defaults to true. */
  showIcon?: boolean;
  /** Whether to show the text "followers" alongside the count. Defaults to false. */
  showLabel?: boolean;
  /** If true, uses StaticBadge to prevent hover/active states. Defaults to false. */
  isStatic?: boolean;
  /** Customizes the loading skeleton size. Defaults to h-5 w-16. */
  skeletonClassName?: string;
}

/**
 * Displays the real-time follower count for a given restaurant ID.
 * Can be styled as a Badge or a static text element.
 */
export const FollowerCount = ({
  restaurantId,
  variant = "secondary",
  isStatic = false,
  className = "",
  showIcon = true,
  showLabel = false,
  skeletonClassName = "h-5 w-16",
}: FollowerCountProps) => {
  const { count, loading } = useFollowerCount(restaurantId);

  const formatCount = (num: number): string => {
    if (num >= 1_000_000) {
      return `${(num / 1_000_000).toFixed(1).replace(/\.0$/, "")}M`;
    } else if (num >= 1_000) {
      return `${(num / 1_000).toFixed(1).replace(/\.0$/, "")}K`;
    }
    return num.toString();
  };

  const formattedCount = formatCount(count);

  if (loading) {
    return <Skeleton className={`${skeletonClassName} rounded-md`} />;
  }

  const labelText = count === 1 ? "follower" : "followers";

  // If isStatic is true, just return the number without badge styling
  if (isStatic) {
    return (
      <span className={className}>
        {formattedCount}
      </span>
    );
  }

  // Otherwise return the badge version
  return (
    <Badge
      variant={variant}
      className={`inline-flex items-center gap-1 text-xs font-medium flex-shrink-0 ${className}`}
      title={`${count.toLocaleString()} ${labelText}`}
    >
      {showIcon && <Users className="h-3 w-3" />}
      <span>{formattedCount}</span>
      {showLabel && <span className="hidden sm:inline">{labelText}</span>}{" "}
      {/* Optionally hide full label on small screens */}
    </Badge>
  );
};
