import React, { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { gameService } from "@/services/GameService";
import { ArrowLeft, Trophy, HelpCircle, ChefHat } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";

interface RecipeChallengeGameProps {
  onBack: () => void;
  onComplete: () => void;
  userId: string;
}

interface Recipe {
  ingredients: string[];
  dish: string;
  hint: string;
}

export const RecipeChallengeGame: React.FC<RecipeChallengeGameProps> = ({
  onBack,
  onComplete,
  userId,
}) => {
  const [gameStarted, setGameStarted] = useState(false);
  const [gameCompleted, setGameCompleted] = useState(false);
  const [recipes, setRecipes] = useState<Recipe[]>([]);
  const [currentRecipeIndex, setCurrentRecipeIndex] = useState(0);
  const [userInput, setUserInput] = useState("");
  const [showHint, setShowHint] = useState(false);
  const [result, setResult] = useState<{
    success: boolean;
    pointsEarned: number;
    message: string;
  } | null>(null);
  const [correctGuesses, setCorrectGuesses] = useState(0);
  const [showAnswer, setShowAnswer] = useState(false);

  // Initialize game
  useEffect(() => {
    const challengeRecipes = gameService.getRecipeChallenges(3);
    setRecipes(challengeRecipes);
  }, []);

  // Start game
  const handleStartGame = () => {
    setGameStarted(true);
  };

  // Check user's answer
  const checkAnswer = () => {
    const currentRecipe = recipes[currentRecipeIndex];
    // Case insensitive comparison and allow partial matches
    const isCorrect =
      userInput.toLowerCase().includes(currentRecipe.dish.toLowerCase()) ||
      currentRecipe.dish.toLowerCase().includes(userInput.toLowerCase());

    if (isCorrect) {
      setCorrectGuesses((prev) => prev + 1);
    }

    setShowAnswer(true);
  };

  // Move to next recipe
  const nextRecipe = () => {
    if (currentRecipeIndex < recipes.length - 1) {
      setCurrentRecipeIndex((prev) => prev + 1);
      setUserInput("");
      setShowHint(false);
      setShowAnswer(false);
    } else {
      endGame();
    }
  };

  // End game and submit result
  const endGame = async () => {
    setGameCompleted(true);

    try {
      const gameResult = await gameService.submitRecipeChallengeResult(
        userId,
        correctGuesses,
        recipes.length
      );
      setResult(gameResult);
      if (gameResult.success && gameResult.pointsEarned > 0) {
        onComplete();
      }
    } catch (error) {
      console.error("Error submitting game result:", error);
      setResult({
        success: false,
        pointsEarned: 0,
        message: "An error occurred. Please try again.",
      });
    }
  };

  // Toggle hint visibility
  const toggleHint = () => {
    setShowHint(!showHint);
  };

  return (
    <Card className="w-full">
      <CardContent className="p-6">
        <div className="flex justify-between items-center mb-6">
          <Button
            variant="ghost"
            size="sm"
            onClick={onBack}
            className="flex items-center"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Games
          </Button>
        </div>

        <div className="text-center mb-6">
          <h2 className="text-2xl font-bold mb-2">Recipe Challenge</h2>
          <p className="text-muted-foreground">
            Guess what dish can be made with the given ingredients!
          </p>
        </div>

        {!gameStarted ? (
          <div className="text-center py-8">
            <p className="mb-6">
              Look at the ingredients and guess what dish can be made. Test your
              culinary knowledge!
            </p>
            <Button onClick={handleStartGame} size="lg">
              Start Game
            </Button>
          </div>
        ) : gameCompleted ? (
          <div className="text-center py-8">
            <div className="mb-6">
              <Trophy className="h-16 w-16 mx-auto text-primary mb-4" />
              <h3 className="text-xl font-bold mb-2">Game Completed!</h3>
              <p className="text-muted-foreground">
                Correct Guesses: {correctGuesses}/{recipes.length}
              </p>
              {result && (
                <Alert
                  className={`mt-4 ${
                    result.success ? "bg-primary/10" : "bg-destructive/10"
                  }`}
                >
                  <AlertTitle>
                    {result.success ? "Success!" : "Oops!"}
                  </AlertTitle>
                  <AlertDescription>{result.message}</AlertDescription>
                  {result.pointsEarned > 0 && (
                    <div className="mt-2 font-medium">
                      +{result.pointsEarned} points earned!
                    </div>
                  )}
                </Alert>
              )}
            </div>
            <Button onClick={onBack}>Return to Games</Button>
          </div>
        ) : (
          <div>
            <div className="mb-6">
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm font-medium">Progress</span>
                <span className="text-sm">
                  {currentRecipeIndex + 1} / {recipes.length}
                </span>
              </div>
              <Progress
                value={((currentRecipeIndex + 1) / recipes.length) * 100}
                className="h-2"
              />
            </div>

            <div className="bg-muted p-6 rounded-lg mb-6">
              <div className="flex items-center mb-4">
                <ChefHat className="h-5 w-5 mr-2 text-primary" />
                <h3 className="font-medium">Ingredients:</h3>
              </div>
              <div className="flex flex-wrap gap-2 mb-4">
                {recipes[currentRecipeIndex]?.ingredients.map(
                  (ingredient, index) => (
                    <Badge key={index} variant="outline" className="px-3 py-1">
                      {ingredient}
                    </Badge>
                  )
                )}
              </div>
              {showHint && (
                <p className="text-sm text-muted-foreground mt-4 p-2 bg-background rounded">
                  Hint: {recipes[currentRecipeIndex]?.hint}
                </p>
              )}
            </div>

            {showAnswer ? (
              <div className="space-y-4">
                <div className="p-4 bg-primary/10 rounded-lg text-center">
                  <p className="text-sm mb-2">The dish was:</p>
                  <p className="text-xl font-bold">
                    {recipes[currentRecipeIndex]?.dish}
                  </p>
                  <p className="text-sm mt-2">
                    {correctGuesses > currentRecipeIndex
                      ? "You got it right!"
                      : "Better luck next time!"}
                  </p>
                </div>
                <Button className="w-full" onClick={nextRecipe}>
                  {currentRecipeIndex < recipes.length - 1
                    ? "Next Recipe"
                    : "Finish Game"}
                </Button>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="flex gap-2">
                  <Input
                    value={userInput}
                    onChange={(e) => setUserInput(e.target.value)}
                    placeholder="What dish is this?"
                    className="flex-1"
                    onKeyDown={(e) => {
                      if (e.key === "Enter" && userInput.trim()) {
                        checkAnswer();
                      }
                    }}
                  />
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={toggleHint}
                    title="Show hint"
                  >
                    <HelpCircle className="h-4 w-4" />
                  </Button>
                </div>

                <Button
                  className="w-full"
                  onClick={checkAnswer}
                  disabled={!userInput.trim()}
                >
                  Submit Guess
                </Button>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};
