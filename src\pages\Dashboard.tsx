// Dashboard.tsx
import { useState, useEffect, useCallback } from "react";
import { useAuth } from "@/providers/AuthProvider";
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Loading } from "@/components/ui/loading";
import { toast } from "sonner";
import {
  collection,
  query,
  orderBy,
  onSnapshot,
  doc,
  updateDoc,
  getDoc,
  deleteDoc,
  FirestoreError,
  Timestamp,
  serverTimestamp,
  setDoc,
} from "firebase/firestore";
import { firestore } from "@/config/firebase";
import { OrdersTab } from "@/components/dashboard/OrdersTab";
import { MenuTab } from "@/components/dashboard/MenuTab";
import { ReservationsTab } from "@/components/dashboard/ReservationsTab";
import { OrderHistoryTab } from "@/components/dashboard/OrderHistoryTab";
import { NotificationsPanel } from "@/components/dashboard/NotificationsPanel";
import { AnalyticsTab } from "@/components/dashboard/AnalyticsTab";
import { RewardsManagement } from "@/components/restaurant/RewardsManagement";
import { NotificationSettings } from "@/components/restaurant/NotificationSettings";
import {
  Order,
  ClientDetails,
  MenuItem,
  Table,
  Reservation,
  ReservationTimer,
} from "@/types/dashboard";
import { v4 as uuidv4 } from "uuid";
import { notificationService } from "@/services/NotificationService";
import { restaurantNotificationService } from "@/services/RestaurantNotificationService";
import { orderSchedulingService } from "@/services/OrderSchedulingService";
import { loyaltyService } from "@/services/LoyaltyService";
import { convertToServiceOrder } from "@/utils/orderUtils";
import { format } from "date-fns";
import {
  ShoppingBag,
  Calendar,
  Menu,
  BarChart,
  Gift,
  Bell,
} from "lucide-react";

export const Dashboard = () => {
  const { user, loading, error, clearError } = useAuth();
  const [userRole, setUserRole] = useState<"client" | "restaurant" | null>(
    null
  );
  const [orders, setOrders] = useState<Order[]>([]);
  const [customerDetails, setCustomerDetails] = useState<{
    [key: string]: ClientDetails;
  }>({});
  const [menuItems, setMenuItems] = useState<MenuItem[]>([]);
  const [tables, setTables] = useState<Table[]>([]);
  const [reservations, setReservations] = useState<Reservation[]>([]);
  const [activeTimers, setActiveTimers] = useState<ReservationTimer[]>([]);
  const [activeTab, setActiveTab] = useState("orders");
  const [selectedOrderId, setSelectedOrderId] = useState<string | null>(null);
  // Removed local notifications state - now using AuthProvider

  const fetchMenuItems = useCallback(async () => {
    if (!user || userRole !== "restaurant") return;

    try {
      const restaurantRef = doc(firestore, "restaurants", user.uid);
      const restaurantSnap = await getDoc(restaurantRef);

      if (!restaurantSnap.exists()) {
        toast.error("Restaurant not found");
        return;
      }
      const menuRef = collection(restaurantRef, "menu");
      const q = query(menuRef, orderBy("category"));

      const unsubscribe = onSnapshot(
        q,
        (querySnapshot) => {
          const items: MenuItem[] = [];
          querySnapshot.forEach((doc) => {
            items.push({ itemId: doc.id, ...doc.data() } as MenuItem);
          });
          setMenuItems(items);
        },
        (error) => {
          const firestoreError = error as FirestoreError;
          toast.error(firestoreError.message);
          console.error("Error fetching menu items:", error);
        }
      );

      return () => unsubscribe();
    } catch (error: unknown) {
      const firestoreError = error as FirestoreError;
      toast.error(firestoreError.message);
      console.error("Error in fetchMenuItems:", error);
    }
  }, [user, userRole]);

  const fetchCustomerDetails = async (userId: string) => {
    try {
      const customerRef = doc(firestore, "clients", userId);
      const customerSnap = await getDoc(customerRef);

      if (customerSnap.exists()) {
        const data = customerSnap.data() as ClientDetails;
        setCustomerDetails((prev) => ({
          ...prev,
          [userId]: data,
        }));
      }
    } catch (err) {
      console.error("Error fetching customer details:", err);
    }
  };

  const fetchUserRole = useCallback(async () => {
    if (!user) return;

    try {
      const userDocRef = doc(firestore, "users", user.uid);
      const userDocSnap = await getDoc(userDocRef);

      if (userDocSnap.exists()) {
        const data = userDocSnap.data();
        setUserRole(data.role);
      } else {
        console.error("User document not found.");
      }
    } catch (err: unknown) {
      const firebaseError = err as FirestoreError;
      toast.error(firebaseError.message);
      console.error("Error fetching user role:", err);
    }
  }, [user]);

  const fetchOrders = useCallback(async () => {
    if (!user || !userRole) return;

    try {
      let q;
      if (userRole === "client") {
        // Get orders from client's orders subcollection
        const clientRef = doc(firestore, "clients", user.uid);
        const ordersRef = collection(clientRef, "orders");
        q = query(ordersRef, orderBy("orderDate", "desc"));
      } else if (userRole === "restaurant") {
        // Get orders from restaurant's orders subcollection
        const restaurantRef = doc(firestore, "restaurants", user.uid);
        const ordersRef = collection(restaurantRef, "orders");
        q = query(ordersRef, orderBy("orderDate", "desc"));
      } else {
        return;
      }

      const unsubscribe = onSnapshot(
        q,
        (querySnapshot) => {
          const fetchedOrders: Order[] = [];
          querySnapshot.forEach((doc) => {
            fetchedOrders.push({
              orderId: doc.id,
              ...doc.data(),
            } as Order);
          });
          setOrders(fetchedOrders);
        },
        (error) => {
          const firestoreError = error as FirestoreError;
          toast.error(firestoreError.message);
          console.error("Error fetching orders:", error);
        }
      );

      return () => unsubscribe();
    } catch (error: unknown) {
      const firestoreError = error as FirestoreError;
      toast.error(firestoreError.message);
      console.error("Error in fetchOrders:", error);
    }
  }, [user, userRole]);

  useEffect(() => {
    fetchUserRole();
  }, [fetchUserRole]);

  useEffect(() => {
    if (userRole) {
      fetchOrders();
    }
  }, [userRole, fetchOrders]);

  useEffect(() => {
    if (userRole === "restaurant") {
      const uniqueUserIds = new Set([
        ...orders.map((order) => order.userId),
        ...reservations.map((res) => res.userId),
      ]);

      uniqueUserIds.forEach((userId) => {
        if (!customerDetails[userId]) {
          fetchCustomerDetails(userId);
        }
      });
    }
  }, [userRole, orders, reservations, customerDetails]);

  useEffect(() => {
    if (userRole === "restaurant") {
      fetchMenuItems();
    }
  }, [userRole, fetchMenuItems]);

  useEffect(() => {
    if (!user || !userRole) return;

    const fetchReservations = async () => {
      try {
        let unsubscribe;

        if (userRole === "restaurant") {
          // For restaurants, get all reservations from their collection
          const restaurantRef = doc(firestore, "restaurants", user.uid);
          const reservationsRef = collection(restaurantRef, "reservations");
          const q = query(reservationsRef, orderBy("date", "desc"));

          unsubscribe = onSnapshot(q, (snapshot) => {
            const reservationsData = snapshot.docs.map((doc) => ({
              id: doc.id,
              ...doc.data(),
            })) as Reservation[];
            setReservations(reservationsData);
          });
        } else {
          // For clients, get their reservations from their subcollection
          const clientRef = doc(firestore, "clients", user.uid);
          const reservationsRef = collection(clientRef, "reservations");
          const q = query(reservationsRef, orderBy("date", "desc"));

          unsubscribe = onSnapshot(q, (snapshot) => {
            const reservationsData = snapshot.docs.map((doc) => ({
              id: doc.id,
              ...doc.data(),
            })) as Reservation[];
            setReservations(reservationsData);
          });
        }

        return () => unsubscribe?.();
      } catch (error) {
        console.error("Error fetching reservations:", error);
        toast.error("Failed to fetch reservations");
      }
    };

    fetchReservations();
  }, [user, userRole]);

  useEffect(() => {
    const timerInterval = setInterval(() => {
      setActiveTimers((currentTimers) =>
        currentTimers.map((timer) => ({
          ...timer,
          elapsedTime: Math.floor(
            (new Date().getTime() - timer.startTime.getTime()) / 1000
          ),
        }))
      );
    }, 1000);

    return () => clearInterval(timerInterval);
  }, []);

  useEffect(() => {
    if (error) {
      toast.error(error.message);
      clearError();
    }
  }, [error, clearError]);

  // Notification handling moved to AuthProvider

  // Add this after the reservations useEffect
  useEffect(() => {
    if (!user || userRole !== "restaurant") return;

    const fetchTables = async () => {
      try {
        const restaurantRef = doc(firestore, "restaurants", user.uid);
        const settingsDoc = await getDoc(
          doc(restaurantRef, "settings", "config")
        );

        if (settingsDoc.exists()) {
          const settings = settingsDoc.data();
          setTables(settings.tables || []);
        }
      } catch (error) {
        console.error("Error fetching tables:", error);
        toast.error("Failed to fetch tables");
      }
    };

    fetchTables();
  }, [user, userRole]);

  // Initialize restaurant notifications
  useEffect(() => {
    if (!user || userRole !== "restaurant") return;

    // Check if auto-initialization is enabled
    const autoInit = localStorage.getItem(`notification_auto_init_${user.uid}`);
    if (autoInit === "true") {
      restaurantNotificationService
        .initializeRestaurantNotifications({
          restaurantId: user.uid,
          enableSound: true,
          enableBrowserNotifications: true,
          enableEmailNotifications: true,
        })
        .catch((error) => {
          console.error(
            "Error auto-initializing restaurant notifications:",
            error
          );
        });
    }

    // Cleanup on unmount
    return () => {
      restaurantNotificationService.cleanup(user.uid);
    };
  }, [user, userRole]);

  const formatElapsedTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;
    return `${hours.toString().padStart(2, "0")}:${minutes
      .toString()
      .padStart(2, "0")}:${remainingSeconds.toString().padStart(2, "0")}`;
  };

  const getTableStatusColor = (tableId: string) => {
    const today = new Date().toISOString().split("T")[0];
    const now = new Date().getTime();

    const activeReservation = reservations.find((res) => {
      if (res.tableId !== tableId || res.date !== today) return false;
      if (
        res.status !== "confirmed" &&
        res.status !== "pending" &&
        res.status !== "active"
      )
        return false;

      const resStart = new Date(`${res.date}T${res.arrivalTime}`).getTime();
      const resEnd = res.departureTime
        ? new Date(`${res.date}T${res.departureTime}`).getTime()
        : resStart + 2 * 60 * 60 * 1000;

      return now >= resStart && now < resEnd;
    });

    if (!activeReservation) return "bg-white";
    if (activeReservation.status === "active")
      return "bg-green-100 animate-pulse";
    return activeReservation.status === "confirmed"
      ? "bg-green-100"
      : "bg-yellow-100";
  };

  const getTableAnalytics = (tableId: string) => {
    // Filter reservations for this table
    const tableReservations = reservations.filter(
      (res) => res.tableId === tableId
    );

    // Count reservations by status
    const pendingCount = tableReservations.filter(
      (res) => res.status === "pending"
    ).length;
    const confirmedCount = tableReservations.filter(
      (res) => res.status === "confirmed"
    ).length;
    const cancelledCount = tableReservations.filter(
      (res) => res.status === "cancelled"
    ).length;
    const noShowCount = tableReservations.filter(
      (res) => res.status === "no-show"
    ).length;
    const completedCount = tableReservations.filter(
      (res) => res.status === "completed"
    ).length;
    const activeCount = tableReservations.filter(
      (res) => res.status === "active"
    ).length;

    // Calculate total reservations (excluding cancelled)
    const totalReservations =
      pendingCount +
      confirmedCount +
      noShowCount +
      completedCount +
      activeCount;

    // Calculate utilization rate
    // This is a simplified calculation - in a real app, you might want to consider
    // the actual hours the table was occupied vs. available hours
    const completedAndActiveReservations = completedCount + activeCount;
    const utilization =
      totalReservations > 0
        ? (completedAndActiveReservations / totalReservations) * 100
        : 0;

    return {
      totalReservations,
      pendingCount,
      confirmedCount,
      cancelledCount,
      noShowCount,
      completedCount,
      utilization,
    };
  };

  const canCancelReservation = (reservation: Reservation) => {
    if (reservation.status !== "confirmed") return false;

    const reservationDateTime = new Date(
      `${reservation.date}T${reservation.arrivalTime}`
    );
    const now = new Date();
    const oneMinuteAfterReservation = new Date(
      reservationDateTime.getTime() + 1000 * 60 * 35
    );

    return now > oneMinuteAfterReservation;
  };

  const handleDeleteReservation = async (reservationId: string) => {
    if (!user || !userRole) return;

    try {
      let reservationData: Reservation;

      if (userRole === "restaurant") {
        // Restoran koleksiyonundan rezervasyonu al
        const restaurantRef = doc(firestore, "restaurants", user.uid);
        const reservationRef = doc(
          restaurantRef,
          "reservations",
          reservationId
        );
        const reservationDoc = await getDoc(reservationRef);

        if (!reservationDoc.exists()) {
          toast.error("Reservation not found");
          return;
        }

        reservationData = reservationDoc.data() as Reservation;

        // Restoranın rezervasyonunu sil
        await deleteDoc(reservationRef);

        // Müşterinin rezervasyonunu sil
        const clientRef = doc(firestore, "clients", reservationData.userId);
        const clientReservationRef = doc(
          clientRef,
          "reservations",
          reservationId
        );
        await deleteDoc(clientReservationRef);
      } else {
        // Müşteri koleksiyonundan rezervasyonu al
        const clientRef = doc(firestore, "clients", user.uid);
        const clientReservationRef = doc(
          clientRef,
          "reservations",
          reservationId
        );
        const clientReservationDoc = await getDoc(clientReservationRef);

        if (!clientReservationDoc.exists()) {
          toast.error("Reservation not found");
          return;
        }

        reservationData = clientReservationDoc.data() as Reservation;

        // Müşterinin rezervasyonunu sil
        await deleteDoc(clientReservationRef);

        // Restoranın rezervasyonunu sil
        const restaurantRef = doc(
          firestore,
          "restaurants",
          reservationData.restaurantId
        );
        const restaurantReservationRef = doc(
          restaurantRef,
          "reservations",
          reservationId
        );
        await deleteDoc(restaurantReservationRef);
      }

      // Karşı tarafa bildirim gönder
      if (userRole === "restaurant") {
        await createReservationNotification(
          reservationData.userId,
          "client",
          "reservation_deleted",
          "Reservation Deleted",
          `Your reservation for ${format(
            new Date(reservationData.date),
            "PPP"
          )} at ${
            reservationData.arrivalTime
          } has been deleted by the restaurant.`,
          reservationId
        );
      } else {
        await createReservationNotification(
          reservationData.restaurantId,
          "restaurant",
          "reservation_deleted",
          "Reservation Deleted",
          `A reservation for ${format(
            new Date(reservationData.date),
            "PPP"
          )} at ${
            reservationData.arrivalTime
          } has been deleted by the customer.`,
          reservationId
        );
      }

      toast.success("Reservation deleted successfully");
    } catch (error) {
      console.error("Error deleting reservation:", error);
      toast.error("Failed to delete reservation");
    }
  };

  const handleViewReservation = (reservationId: string) => {
    setActiveTab("reservations");
    setTimeout(() => {
      const element = document.getElementById(`reservation-${reservationId}`);
      element?.scrollIntoView({ behavior: "smooth", block: "center" });
    }, 100);
  };

  // Add notification creation for reservation status changes
  const createReservationNotification = async (
    recipientId: string,
    recipientRole: "client" | "restaurant",
    type: string,
    title: string,
    message: string,
    reservationId: string,
    reservationData?: Reservation
  ) => {
    try {
      // Create database notification
      await notificationService.createDatabaseNotification(
        recipientId,
        recipientRole,
        type,
        title,
        message,
        { reservationId }
      );

      // Send email notification if we have reservation data
      if (reservationData && recipientRole === "client") {
        try {
          const recipientDetails = customerDetails[recipientId];
          if (recipientDetails?.email) {
            const restaurantDetails = await getDoc(
              doc(firestore, "restaurants", reservationData.restaurantId)
            );
            const restaurantName = restaurantDetails.exists()
              ? (restaurantDetails.data() as { restaurantName?: string })
                  ?.restaurantName || "Restaurant"
              : "Restaurant";

            // E-posta gönderme işlemini async olarak başlat ve beklemeden devam et
            notificationService
              .sendEmailNotification({
                to: recipientDetails.email,
                subject: title,
                body: "", // Will be generated from template
                recipientName: `${recipientDetails.firstName} ${recipientDetails.lastName}`,
                type: "reservation",
                data: {
                  reservationDetails: {
                    date: format(new Date(reservationData.date), "PPP"),
                    arrivalTime: reservationData.arrivalTime,
                    partySize: reservationData.partySize,
                    notes: reservationData.notes,
                  },
                  restaurantName,
                  status: reservationData.status,
                  reservationId,
                },
              })
              .then(() => {})
              .catch((err) => {
                console.error("Error in email notification:", err);
              });
          }
        } catch (emailError) {
          console.error(
            "Error preparing reservation email notification:",
            emailError
          );
          // E-posta hatası bildirim oluşturma işlemini etkilememelidir
        }
      }
    } catch (error) {
      console.error("Error creating reservation notification:", error);
    }
  };

  // Update handleReservationStatus to include notifications
  const handleReservationStatus = async (
    reservationId: string,
    newStatus: Reservation["status"]
  ) => {
    if (!user) return;

    try {
      let reservationData: Reservation;
      let reservationRef;

      if (userRole === "restaurant") {
        // Restaurant cancelling/confirming a reservation
        const restaurantRef = doc(firestore, "restaurants", user.uid);
        reservationRef = doc(restaurantRef, "reservations", reservationId);
        const reservationDoc = await getDoc(reservationRef);

        if (!reservationDoc.exists()) {
          toast.error("Reservation not found");
          return;
        }
        reservationData = reservationDoc.data() as Reservation;
      } else {
        // Client cancelling their own reservation
        const clientRef = doc(firestore, "clients", user.uid);
        const clientReservationRef = doc(
          clientRef,
          "reservations",
          reservationId
        );
        const clientReservationDoc = await getDoc(clientReservationRef);

        if (!clientReservationDoc.exists()) {
          toast.error("Reservation not found");
          return;
        }
        reservationData = clientReservationDoc.data() as Reservation;

        // Also update in restaurant's collection
        const restaurantRef = doc(
          firestore,
          "restaurants",
          reservationData.restaurantId
        );
        reservationRef = doc(restaurantRef, "reservations", reservationId);
      }

      // Update restaurant's reservation
      await updateDoc(reservationRef, {
        status: newStatus,
        updatedAt: serverTimestamp(),
      });

      // Update client's reservation copy
      const clientRef = doc(firestore, "clients", reservationData.userId);
      const clientReservationRef = doc(
        clientRef,
        "reservations",
        reservationId
      );
      await setDoc(
        clientReservationRef,
        {
          ...reservationData,
          status: newStatus,
          updatedAt: serverTimestamp(),
          id: reservationId,
        },
        { merge: true }
      );

      // Create notification for the client
      await createReservationNotification(
        reservationData.userId,
        "client",
        `reservation_${newStatus}`,
        `Reservation ${newStatus}`,
        `Your reservation for ${format(
          new Date(reservationData.date),
          "PPP"
        )} at ${reservationData.arrivalTime} has been ${newStatus}.`,
        reservationId,
        reservationData
      );

      // If client is cancelling, create notification for the restaurant
      if (userRole === "client" && newStatus === "cancelled") {
        await createReservationNotification(
          reservationData.restaurantId,
          "restaurant",
          "reservation_cancelled",
          "Reservation Cancelled",
          `A reservation for ${format(
            new Date(reservationData.date),
            "PPP"
          )} at ${
            reservationData.arrivalTime
          } has been cancelled by the customer.`,
          reservationId
        );
      }

      toast.success(`Reservation ${newStatus}`);
    } catch (error) {
      console.error("Error updating reservation:", error);
      toast.error("Failed to update reservation");
    }
  };

  // Update handleReservationActive to include notifications
  const handleReservationActive = async (reservationId: string) => {
    if (!user) return;

    try {
      const reservation = reservations.find((res) => res.id === reservationId);
      if (!reservation) {
        toast.error("Reservation not found");
        return;
      }

      const now = new Date();
      const reservationDate = new Date(
        `${reservation.date}T${reservation.arrivalTime}`
      );
      const reservationEndTime = reservation.departureTime
        ? new Date(`${reservation.date}T${reservation.departureTime}`)
        : new Date(reservationDate.getTime() + 2 * 60 * 60 * 1000);

      if (now < reservationDate) {
        toast.error("Cannot mark as seated before reservation time");
        return;
      }

      if (now > reservationEndTime) {
        toast.error("Reservation time has passed");
        return;
      }

      const restaurantRef = doc(firestore, "restaurants", user.uid);
      const reservationRef = doc(restaurantRef, "reservations", reservationId);

      const arrivedAt = new Date();
      // Update restaurant's reservation
      await updateDoc(reservationRef, {
        status: "active",
        arrivedAt: arrivedAt,
        updatedAt: serverTimestamp(),
      });

      // Update client's reservation copy
      const clientRef = doc(firestore, "clients", reservation.userId);
      const clientReservationRef = doc(
        clientRef,
        "reservations",
        reservationId
      );
      await setDoc(
        clientReservationRef,
        {
          ...reservation,
          status: "active",
          arrivedAt: arrivedAt,
          updatedAt: serverTimestamp(),
          id: reservationId,
        },
        { merge: true }
      );

      // Create notification for the client
      await createReservationNotification(
        reservation.userId,
        "client",
        "reservation_confirmed",
        "Table Seated",
        `You have been seated at your table. Enjoy your meal!`,
        reservationId,
        reservation
      );

      setActiveTimers((current) => [
        ...current,
        {
          reservationId,
          startTime: arrivedAt,
          elapsedTime: 0,
        },
      ]);

      toast.success("Reservation marked as active");
    } catch (error) {
      console.error("Error updating reservation:", error);
      toast.error("Failed to update reservation");
    }
  };

  // Update handleNoShow to include notifications
  const handleNoShow = async (reservationId: string) => {
    if (!user) return;

    try {
      const restaurantRef = doc(firestore, "restaurants", user.uid);
      const reservationRef = doc(restaurantRef, "reservations", reservationId);
      const reservationDoc = await getDoc(reservationRef);

      if (!reservationDoc.exists()) {
        toast.error("Reservation not found");
        return;
      }

      const reservationData = reservationDoc.data() as Reservation;

      // Update restaurant's reservation
      await updateDoc(reservationRef, {
        status: "no-show",
        updatedAt: serverTimestamp(),
      });

      // Update client's reservation copy
      const clientRef = doc(firestore, "clients", reservationData.userId);
      const clientReservationRef = doc(
        clientRef,
        "reservations",
        reservationId
      );
      await setDoc(
        clientReservationRef,
        {
          ...reservationData,
          status: "no-show",
          updatedAt: serverTimestamp(),
          id: reservationId,
        },
        { merge: true }
      );

      // Create notification for the client
      await createReservationNotification(
        reservationData.userId,
        "client",
        "reservation_noshow",
        "Missed Reservation",
        `You missed your reservation for ${format(
          new Date(reservationData.date),
          "PPP"
        )} at ${
          reservationData.arrivalTime
        }. Please contact the restaurant if you need to reschedule.`,
        reservationId,
        reservationData
      );

      toast.success("Reservation marked as no-show");
    } catch (error) {
      console.error("Error updating reservation:", error);
      toast.error("Failed to update reservation");
    }
  };

  const handleReservationComplete = async (reservationId: string) => {
    if (!user) return;

    try {
      const restaurantRef = doc(firestore, "restaurants", user.uid);
      const reservationRef = doc(restaurantRef, "reservations", reservationId);
      const reservationDoc = await getDoc(reservationRef);

      if (!reservationDoc.exists()) {
        toast.error("Reservation not found");
        return;
      }

      const reservationData = reservationDoc.data() as Reservation;
      const timer = activeTimers.find((t) => t.reservationId === reservationId);

      // Update restaurant's reservation
      await updateDoc(reservationRef, {
        status: "completed",
        duration: timer ? timer.elapsedTime : undefined,
        updatedAt: serverTimestamp(),
      });

      // Update client's reservation copy
      const clientRef = doc(firestore, "clients", reservationData.userId);
      const clientReservationRef = doc(
        clientRef,
        "reservations",
        reservationId
      );
      await setDoc(
        clientReservationRef,
        {
          ...reservationData,
          status: "completed",
          duration: timer ? timer.elapsedTime : undefined,
          updatedAt: serverTimestamp(),
          id: reservationId,
        },
        { merge: true }
      );

      setActiveTimers((current) =>
        current.filter((t) => t.reservationId !== reservationId)
      );

      toast.success("Reservation marked as completed");
    } catch (error) {
      console.error("Error updating reservation:", error);
      toast.error("Failed to update reservation");
    }
  };

  const updateOrderStatus = async (
    orderId: string,
    newStatus: Order["status"]
  ) => {
    try {
      // Get the order first to get the userId
      const orderRef = doc(
        firestore,
        "restaurants",
        user!.uid,
        "orders",
        orderId
      );
      const orderDoc = await getDoc(orderRef);

      if (!orderDoc.exists()) {
        toast.error("Order not found");
        return;
      }

      const orderData = orderDoc.data() as Order;

      // Check if this is a scheduled order and if it can be updated
      if (orderData.isScheduled) {
        // Convert dashboard order to service order
        const serviceOrder = convertToServiceOrder(orderData);
        const { canUpdate, message } =
          await orderSchedulingService.canUpdateScheduledOrder(
            serviceOrder,
            newStatus
          );

        if (!canUpdate) {
          toast.error(message || "This scheduled order cannot be updated yet");
          return;
        }
      }

      // If the order is pending and doesn't have a table assigned, cancel it
      if (orderData.status === "pending" && !orderData.tableId) {
        const newStatus = "cancelled";

        // Update restaurant's order
        await updateDoc(orderRef, {
          status: newStatus,
        });

        // Update client's order
        const clientOrderRef = doc(
          firestore,
          "clients",
          orderData.userId,
          "orders",
          orderId
        );
        await updateDoc(clientOrderRef, {
          status: newStatus,
        });

        // Add notification for the client
        const notificationId = uuidv4();
        const clientNotificationRef = doc(
          firestore,
          "clients",
          orderData.userId,
          "notifications",
          notificationId
        );
        await setDoc(clientNotificationRef, {
          id: notificationId,
          type: "order_cancelled",
          title: "Order Cancelled",
          message: "Your order has been cancelled due to no available tables.",
          orderId: orderId,
          recipientId: orderData.userId,
          read: false,
          createdAt: serverTimestamp(),
        });

        toast.error("Order cancelled: No tables available");
        return;
      }

      // Notify customer about order status change
      let notificationTitle = "";
      let notificationMessage = "";

      switch (newStatus) {
        case "preparing":
          notificationTitle = "Order Accepted";
          notificationMessage =
            "Your order is now being prepared. You've earned loyalty points for this order!";
          break;
        case "ready":
          notificationTitle = "Order Ready";
          notificationMessage = "Your order is ready for pickup at your table.";
          break;
        case "completed":
          notificationTitle = "Order Completed";
          notificationMessage =
            "Thank you for dining with us! Your loyalty points have been awarded.";
          break;
        case "cancelled":
          notificationTitle = "Order Cancelled";
          notificationMessage = orderData.tableId
            ? "Your order has been cancelled."
            : "Your order has been cancelled due to no available tables.";
          break;
      }

      // Create database notification
      await notificationService.createDatabaseNotification(
        orderData.userId,
        "client",
        `order_${newStatus}`,
        notificationTitle,
        notificationMessage,
        { orderId }
      );

      // Send email notification
      try {
        const recipientDetails = customerDetails[orderData.userId];
        if (recipientDetails?.email) {
          const restaurantDetails = await getDoc(
            doc(firestore, "restaurants", orderData.restaurantId)
          );
          const restaurantName = restaurantDetails.exists()
            ? (restaurantDetails.data() as { restaurantName?: string })
                ?.restaurantName || "Restaurant"
            : "Restaurant";

          // E-posta gönderme işlemini async olarak başlat ve beklemeden devam et
          notificationService
            .sendEmailNotification({
              to: recipientDetails.email,
              subject: notificationTitle,
              body: "", // Will be generated from template
              recipientName: `${recipientDetails.firstName} ${recipientDetails.lastName}`,
              type: "order",
              data: {
                orderDetails: orderData,
                restaurantName,
                status: newStatus,
                orderId,
              },
            })
            .then(() => {})
            .catch((err) => {
              console.error("Error in email notification:", err);
            });
        }
      } catch (emailError) {
        console.error("Error preparing email notification:", emailError);
        // E-posta hatası sipariş güncelleme işlemini etkilememelidir
      }

      // Update restaurant's order
      await updateDoc(orderRef, {
        status: newStatus,
      });

      // Update client's order
      const clientOrderRef = doc(
        firestore,
        "clients",
        orderData.userId,
        "orders",
        orderId
      );
      await updateDoc(clientOrderRef, {
        status: newStatus,
      });

      // Award loyalty points when order is accepted (preparing) or completed
      if (
        (newStatus === "preparing" || newStatus === "completed") &&
        orderData.userId
      ) {
        try {
          console.log(
            `Attempting to award loyalty points for ${newStatus} order: userId=${
              orderData.userId
            }, orderId=${orderId}, amount=${orderData.totalPrice || 0}`
          );

          const pointsAwarded = await loyaltyService.awardOrderPoints(
            orderData.userId,
            orderId,
            orderData.totalPrice || 0
          );

          if (pointsAwarded) {
            console.log(`Points awarded successfully for ${newStatus} order`);

            // Only show toast for completed orders to avoid confusion
            if (newStatus === "completed") {
              toast.success("Customer earned loyalty points for this order!");
            }
          }
        } catch (loyaltyError) {
          console.error(
            `Error awarding loyalty points for ${newStatus} order:`,
            loyaltyError
          );
          // Don't show error to user, as the order status was still updated successfully
        }
      }

      toast.success("Order status updated successfully");
    } catch (error) {
      console.error("Error updating order status:", error);
      toast.error("Failed to update order status");
    }
  };

  const handleViewOrder = (orderId: string) => {
    setActiveTab("orders");
    setSelectedOrderId(orderId);
    // You could implement scrolling to the specific order here
  };

  const printReceipt = (order: Order) => {
    const receiptWindow = window.open("", "_blank");
    if (!receiptWindow) return;

    const formatDate = (timestamp: Timestamp | Date | null) => {
      if (!timestamp) return new Date().toLocaleString();
      if ("toDate" in timestamp) return timestamp.toDate().toLocaleString();
      return timestamp.toLocaleString();
    };

    // Generate receipt URL
    const receiptUrl = `${window.location.origin}/receipts/${order.restaurantId}/${order.orderId}`;

    // Ensure restaurant name is not undefined
    const restaurantName = order.restaurantName || "Qonai Restaurant";

    const receiptContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>Receipt - Order ${order.orderId}</title>
        <script src="https://cdn.jsdelivr.net/npm/qrcode-generator@1.4.4/qrcode.min.js"></script>
        <link rel="preconnect" href="https://fonts.googleapis.com">
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
        <style>
          :root {
            --primary-color: #1e293b;
            --secondary-color: #64748b;
            --accent-color: #f59e0b;
            --background-color: #ffffff;
            --border-color: #e2e8f0;
            --text-color: #1e293b;
            --text-muted: #64748b;
          }

          * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
          }

          body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            width: 300px;
            margin: 0 auto;
            padding: 20px;
            color: var(--text-color);
            background-color: #f8fafc;
          }

          .receipt-container {
            background-color: var(--background-color);
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.1);
            padding: 24px;
            position: relative;
            overflow: hidden;
          }

          .receipt-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 6px;
            background-color: var(--accent-color);
          }

          .logo {
            text-align: center;
            margin-bottom: 16px;
          }

          .logo svg {
            width: 48px;
            height: 48px;
            color: var(--accent-color);
          }

          .header {
            text-align: center;
            margin-bottom: 24px;
          }

          .header h2 {
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 4px;
            color: var(--primary-color);
          }

          .header p {
            font-size: 13px;
            color: var(--secondary-color);
            margin: 2px 0;
          }

          .divider {
            height: 1px;
            background-color: var(--border-color);
            margin: 16px 0;
          }

          .dashed-divider {
            height: 1px;
            background-image: linear-gradient(to right, var(--border-color) 50%, transparent 50%);
            background-size: 8px 1px;
            background-repeat: repeat-x;
            margin: 16px 0;
          }

          .order-info {
            background-color: #f8fafc;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 16px;
          }

          .order-info-row {
            display: flex;
            justify-content: space-between;
            font-size: 13px;
            margin-bottom: 8px;
          }

          .order-info-row:last-child {
            margin-bottom: 0;
          }

          .order-info-label {
            font-weight: 500;
            color: var(--secondary-color);
          }

          .order-info-value {
            font-weight: 600;
            color: var(--primary-color);
          }

          .items-header {
            display: flex;
            justify-content: space-between;
            font-size: 14px;
            font-weight: 600;
            color: var(--secondary-color);
            margin-bottom: 12px;
            padding: 0 4px;
          }

          .item {
            display: flex;
            justify-content: space-between;
            margin: 8px 0;
            font-size: 14px;
          }

          .item-name {
            flex: 1;
            padding-right: 8px;
          }

          .item-quantity {
            font-weight: 500;
            margin-right: 8px;
            color: var(--secondary-color);
          }

          .item-price {
            font-weight: 600;
            text-align: right;
            min-width: 70px;
          }

          .subtotal {
            display: flex;
            justify-content: space-between;
            font-size: 14px;
            margin: 12px 0;
          }

          .total {
            display: flex;
            justify-content: space-between;
            font-size: 16px;
            font-weight: 700;
            margin: 16px 0;
            color: var(--primary-color);
          }

          .qr-code {
            text-align: center;
            margin: 24px 0;
            padding: 16px;
            background-color: #f8fafc;
            border-radius: 8px;
          }

          .qr-code img {
            max-width: 100%;
            height: auto;
          }

          .qr-code p {
            font-size: 12px;
            color: var(--secondary-color);
            margin-top: 8px;
          }

          .footer {
            text-align: center;
            margin-top: 24px;
            padding-top: 16px;
            border-top: 1px solid var(--border-color);
          }

          .footer p {
            font-size: 13px;
            color: var(--secondary-color);
            margin: 4px 0;
          }

          .footer .thank-you {
            font-size: 14px;
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 8px;
          }

          .print-button {
            display: block;
            width: 100%;
            margin-top: 24px;
            padding: 12px;
            background-color: var(--accent-color);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: background-color 0.2s;
          }

          .print-button:hover {
            background-color: #e69009;
          }

          @media print {
            body {
              width: 100%;
              max-width: 300px;
              background-color: white;
              padding: 0;
            }

            .receipt-container {
              box-shadow: none;
              padding: 16px;
            }

            .no-print {
              display: none;
            }
          }
        </style>
      </head>
      <body>
        <div class="receipt-container">
          <div class="logo">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M12 2L2 7l10 5 10-5-10-5z"></path>
              <path d="M2 17l10 5 10-5"></path>
              <path d="M2 12l10 5 10-5"></path>
            </svg>
          </div>

          <div class="header">
            <h2>${restaurantName}</h2>
            <p>Order #${order.orderId.substring(0, 8)}</p>
            <p>${formatDate(order.orderDate)}</p>
          </div>

          <div class="divider"></div>

          <div class="order-info">
            <div class="order-info-row">
              <span class="order-info-label">Status:</span>
              <span class="order-info-value">${
                order.status.charAt(0).toUpperCase() + order.status.slice(1)
              }</span>
            </div>
            ${
              order.tableName
                ? `
            <div class="order-info-row">
              <span class="order-info-label">Table:</span>
              <span class="order-info-value">${order.tableName}</span>
            </div>
            `
                : ""
            }
            <div class="order-info-row">
              <span class="order-info-label">Payment Method:</span>
              <span class="order-info-value">Card</span>
            </div>
          </div>

          <div class="items-header">
            <span>Item</span>
            <span>Price</span>
          </div>

          <div class="items-list">
            ${order.items
              .map(
                (item) => `
              <div class="item">
                <div class="item-name">
                  <span class="item-quantity">${item.quantity}x</span>
                  ${item.name}
                </div>
                <div class="item-price">${(item.price * item.quantity).toFixed(
                  2
                )} AZN</div>
              </div>
            `
              )
              .join("")}
          </div>

          <div class="dashed-divider"></div>

          <div class="subtotal">
            <span>Subtotal</span>
            <span>${
              order.originalAmount
                ? order.originalAmount.toFixed(2)
                : typeof order.totalPrice === "number"
                ? order.totalPrice.toFixed(2)
                : order.totalPrice
            } AZN</span>
          </div>

          ${
            order.discountAmount && order.discountAmount > 0
              ? `
          <div class="subtotal" style="color: #16a34a;">
            <span>Discount ${
              order.appliedRewardCode
                ? `(${order.appliedRewardCode})`
                : order.appliedCouponCode
                ? `(${order.appliedCouponCode})`
                : ""
            }</span>
            <span>-${order.discountAmount.toFixed(2)} AZN</span>
          </div>`
              : ""
          }

          <div class="subtotal">
            <span>Tax (0%)</span>
            <span>0.00 AZN</span>
          </div>

          <div class="divider"></div>

          <div class="total">
            <span>Total</span>
            <span>${
              typeof order.totalPrice === "number"
                ? order.totalPrice.toFixed(2)
                : order.totalPrice
            } AZN</span>
          </div>

          ${
            order.discountAmount && order.discountAmount > 0
              ? `
          <div style="margin-top: 10px; padding: 8px; background-color: #f0f9ff; border: 1px solid #0ea5e9; border-radius: 4px; text-align: center;">
            <div style="color: #0369a1; font-size: 12px; font-weight: 600;">
              💰 You saved ${order.discountAmount.toFixed(2)} AZN!
            </div>
          </div>`
              : ""
          }

          <div class="qr-code">
            <div id="qrcode"></div>
            <p>Scan to view receipt online</p>
          </div>

          <div class="footer">
            <p class="thank-you">Thank you for your order!</p>
            <p>Visit us again soon</p>
            <p>${format(new Date(), "d MMMM yyyy")}</p>
          </div>

          <div class="no-print">
            <button onclick="window.print();window.close();" class="print-button">
              Print Receipt
            </button>
          </div>
        </div>

        <script>
          // Generate QR code with the receipt URL
          var typeNumber = 0;
          var errorCorrectionLevel = 'L';
          var qr = qrcode(typeNumber, errorCorrectionLevel);
          qr.addData('${receiptUrl}');
          qr.make();
          document.getElementById('qrcode').innerHTML = qr.createImgTag(5);
        </script>
      </body>
      </html>
    `;

    receiptWindow.document.open();
    receiptWindow.document.write(receiptContent);
    receiptWindow.document.close();
  };

  if (loading) {
    return <Loading />;
  }

  return (
    <div className="mx-auto sm:p-2 lg:p-4">
      {loading ? (
        <Loading />
      ) : error ? (
        <div className="text-center">
          <p className="text-red-500">
            {error ? error.toString() : "An error occurred"}
          </p>
          <button
            onClick={clearError}
            className="mt-4 px-4 py-2 bg-primary text-white rounded-md"
          >
            Try Again
          </button>
        </div>
      ) : !user ? (
        <div className="text-center">
          <p>Please log in to access the dashboard.</p>
        </div>
      ) : (
        <>
          <div className="flex justify-between items-center mb-8">
            <h1 className="text-3xl font-bold">
              {userRole === "client"
                ? "Client Dashboard"
                : "Restaurant Dashboard"}
            </h1>
            <NotificationsPanel
              onViewOrder={handleViewOrder}
              onViewReservation={handleViewReservation}
            />
          </div>

          <Tabs
            defaultValue="orders"
            value={activeTab}
            onValueChange={setActiveTab}
          >
            <TabsList
              className={`mb-8 grid ${
                userRole === "client"
                  ? "grid-cols-2"
                  : "grid-cols-2 md:grid-cols-6"
              } gap-2`}
            >
              {userRole === "client" && (
                <>
                  <TabsTrigger
                    value="orders"
                    className="flex items-center justify-center gap-1 w-full col-span-1"
                  >
                    <ShoppingBag className="h-4 w-4" />
                    <span>Order History</span>
                  </TabsTrigger>
                  <TabsTrigger
                    value="reservations"
                    className="flex items-center justify-center gap-1 w-full col-span-1"
                  >
                    <Calendar className="h-4 w-4" />
                    <span>Reservations</span>
                  </TabsTrigger>
                </>
              )}
              {userRole === "restaurant" && (
                <>
                  <TabsTrigger
                    value="orders"
                    className="flex items-center justify-center gap-1 w-full"
                  >
                    <ShoppingBag className="h-4 w-4" />
                    <span>Orders</span>
                  </TabsTrigger>
                  <TabsTrigger
                    value="menu"
                    className="flex items-center justify-center gap-1 w-full"
                  >
                    <Menu className="h-4 w-4" />
                    <span>Menu</span>
                  </TabsTrigger>
                  <TabsTrigger
                    value="reservations"
                    className="flex items-center justify-center gap-1 w-full"
                  >
                    <Calendar className="h-4 w-4" />
                    <span>Reservations</span>
                  </TabsTrigger>
                  <TabsTrigger
                    value="analytics"
                    className="flex items-center justify-center gap-1 w-full"
                  >
                    <BarChart className="h-4 w-4" />
                    <span>Analytics</span>
                  </TabsTrigger>
                  <TabsTrigger
                    value="rewards"
                    className="flex items-center justify-center gap-1 w-full"
                  >
                    <Gift className="h-4 w-4" />
                    <span>Rewards</span>
                  </TabsTrigger>
                  <TabsTrigger
                    value="notifications"
                    className="flex items-center justify-center gap-1 w-full"
                  >
                    <Bell className="h-4 w-4" />
                    <span>Notifications</span>
                  </TabsTrigger>
                </>
              )}
            </TabsList>

            {userRole === "client" && (
              <>
                <TabsContent value="orders">
                  <OrderHistoryTab
                    orders={orders}
                    onPrintReceipt={printReceipt}
                  />
                </TabsContent>
                <TabsContent value="reservations">
                  <ReservationsTab
                    reservations={reservations}
                    userRole={userRole}
                    tables={tables}
                    getTableStatusColor={getTableStatusColor}
                    getTableAnalytics={getTableAnalytics}
                    canCancelReservation={canCancelReservation}
                    handleDeleteReservation={handleDeleteReservation}
                    handleReservationStatus={handleReservationStatus}
                    customerDetails={{}}
                  />
                </TabsContent>
              </>
            )}

            {userRole === "restaurant" && (
              <>
                <TabsContent value="orders">
                  <OrdersTab
                    orders={orders}
                    userRole={userRole}
                    customerDetails={customerDetails}
                    updateOrderStatus={updateOrderStatus}
                    highlightedOrderId={selectedOrderId}
                    onPrintReceipt={printReceipt}
                  />
                </TabsContent>
                <TabsContent value="menu">
                  <MenuTab menuItems={menuItems} />
                </TabsContent>
                <TabsContent value="reservations">
                  <ReservationsTab
                    reservations={reservations}
                    userRole={userRole}
                    tables={tables}
                    getTableStatusColor={getTableStatusColor}
                    getTableAnalytics={getTableAnalytics}
                    canCancelReservation={canCancelReservation}
                    handleDeleteReservation={handleDeleteReservation}
                    handleReservationStatus={handleReservationStatus}
                    handleReservationActive={handleReservationActive}
                    handleReservationComplete={handleReservationComplete}
                    handleNoShow={handleNoShow}
                    activeTimers={activeTimers}
                    formatElapsedTime={formatElapsedTime}
                    customerDetails={customerDetails}
                  />
                </TabsContent>
                <TabsContent value="analytics">
                  <AnalyticsTab
                    orders={orders}
                    menuItems={menuItems}
                    reservations={reservations}
                  />
                </TabsContent>
                <TabsContent value="rewards">
                  <RewardsManagement restaurantId={user.uid} />
                </TabsContent>
                <TabsContent value="notifications">
                  <NotificationSettings restaurantId={user.uid} />
                </TabsContent>
              </>
            )}
          </Tabs>
        </>
      )}
    </div>
  );
};
