import { useState } from "react";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { format } from "date-fns";
import { Order, OrderItem } from "@/types/dashboard";
import { CouponUsageDisplay } from "@/components/orders/CouponUsageDisplay";
import {
  ShoppingBag,
  Clock,
  Calendar,
  ChevronDown,
  ChevronUp,
  Printer,
  Copy,
  Check,
} from "lucide-react";

interface OrderHistoryTabProps {
  orders: Order[];
  onPrintReceipt: (order: Order) => void;
}

export const OrderHistoryTab = ({
  orders,
  onPrintReceipt,
}: OrderHistoryTabProps) => {
  const [expandedOrder, setExpandedOrder] = useState<string | null>(null);
  const [openDialog, setOpenDialog] = useState<string | null>(null);
  const [copiedOrderId, setCopiedOrderId] = useState<string | null>(null);

  const toggleOrderExpansion = (orderId: string) => {
    setExpandedOrder(expandedOrder === orderId ? null : orderId);
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text).then(() => {
      setCopiedOrderId(text);
      setTimeout(() => setCopiedOrderId(null), 2000);
    });
  };

  const getStatusColor = (status: Order["status"]) => {
    switch (status) {
      case "pending":
        return "bg-yellow-100 text-yellow-800";
      case "preparing":
        return "bg-blue-100 text-blue-800";
      case "ready":
        return "bg-green-100 text-green-800";
      case "cancelled":
        return "bg-red-100 text-red-800";
      case "completed":
        return "bg-green-100 text-green-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getStatusIcon = (status: Order["status"]) => {
    switch (status) {
      case "pending":
        return <Clock className="h-4 w-4 mr-1" />;
      case "preparing":
        return <ShoppingBag className="h-4 w-4 mr-1" />;
      case "ready":
        return <ShoppingBag className="h-4 w-4 mr-1" />;
      case "cancelled":
        return <ShoppingBag className="h-4 w-4 mr-1" />;
      case "completed":
        return <ShoppingBag className="h-4 w-4 mr-1" />;
      default:
        return <ShoppingBag className="h-4 w-4 mr-1" />;
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Order History</CardTitle>
        <CardDescription>
          View your past food orders and their details
        </CardDescription>
      </CardHeader>
      <CardContent>
        {orders.length === 0 ? (
          <div className="text-center py-10">
            <ShoppingBag className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium mb-2">No orders yet</h3>
            <p className="text-muted-foreground mb-6">
              Your order history will appear here once you place an order
            </p>
          </div>
        ) : (
          <div className="space-y-6">
            {orders.map((order) => (
              <div
                key={order.orderId}
                className="border rounded-lg overflow-hidden"
              >
                <div
                  className="flex justify-between items-center p-4 cursor-pointer hover:bg-muted/50"
                  onClick={() => toggleOrderExpansion(order.orderId)}
                >
                  <div className="flex items-center gap-4">
                    <div className="hidden sm:flex h-10 w-10 rounded-full bg-primary/10 items-center justify-center">
                      <ShoppingBag className="h-5 w-5 text-primary" />
                    </div>
                    <div>
                      <h3 className="font-medium">
                        Order from {order.restaurantName}
                      </h3>
                      <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <Calendar className="h-3.5 w-3.5" />
                        <span>
                          {(() => {
                            try {
                              let dateObj;
                              if (order.orderDate instanceof Date) {
                                dateObj = order.orderDate;
                              } else if (
                                typeof order.orderDate === "object" &&
                                order.orderDate !== null
                              ) {
                                // Check for Firestore Timestamp with toDate method
                                const timestampWithMethod = order.orderDate as {
                                  toDate?: () => Date;
                                };
                                if (
                                  typeof timestampWithMethod.toDate ===
                                  "function"
                                ) {
                                  dateObj = timestampWithMethod.toDate();
                                } else {
                                  // Check for Firestore Timestamp-like object
                                  const timestamp = order.orderDate as {
                                    seconds?: number;
                                    nanoseconds?: number;
                                  };
                                  if (
                                    timestamp.seconds &&
                                    timestamp.nanoseconds
                                  ) {
                                    // Handle Firestore timestamp format
                                    dateObj = new Date(
                                      timestamp.seconds * 1000
                                    );
                                  } else {
                                    // Try to convert other objects to date
                                    try {
                                      dateObj = new Date(
                                        order.orderDate as unknown as
                                          | string
                                          | number
                                      );
                                    } catch {
                                      return "Invalid date";
                                    }
                                  }
                                }
                              } else if (typeof order.orderDate === "string") {
                                dateObj = new Date(order.orderDate);
                              } else if (typeof order.orderDate === "number") {
                                dateObj = new Date(order.orderDate);
                              } else {
                                return "Invalid date";
                              }

                              // Validate the date
                              if (isNaN(dateObj.getTime())) {
                                return "Invalid date";
                              }

                              return format(dateObj, "MMM d, yyyy • h:mm a");
                            } catch (error) {
                              console.error("Date formatting error:", error);
                              return "Invalid date";
                            }
                          })()}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <Badge className={getStatusColor(order.status)}>
                      {getStatusIcon(order.status)}
                      {order.status.charAt(0).toUpperCase() +
                        order.status.slice(1)}
                    </Badge>
                    <span className="font-medium">
                      {typeof order.totalPrice === "number"
                        ? order.totalPrice.toFixed(2)
                        : order.totalPrice}{" "}
                      AZN
                    </span>
                    {expandedOrder === order.orderId ? (
                      <ChevronUp className="h-5 w-5 text-muted-foreground" />
                    ) : (
                      <ChevronDown className="h-5 w-5 text-muted-foreground" />
                    )}
                  </div>
                </div>

                {expandedOrder === order.orderId && (
                  <div className="p-4 border-t bg-muted/30">
                    <h4 className="font-medium mb-2">Order Items</h4>
                    <div className="space-y-2 mb-4">
                      {order.items.map((item: OrderItem, index: number) => (
                        <div
                          key={index}
                          className="flex justify-between items-center py-2 border-b border-dashed last:border-0"
                        >
                          <div className="flex items-center gap-2">
                            <span className="font-medium">
                              {item.quantity}x
                            </span>
                            <span>{item.name}</span>
                          </div>
                          <span>
                            {(item.price * item.quantity).toFixed(2)} AZN
                          </span>
                        </div>
                      ))}
                    </div>

                    {/* Coupon Usage Display */}
                    <CouponUsageDisplay
                      originalAmount={order.originalAmount}
                      discountAmount={order.discountAmount}
                      appliedCouponCode={order.appliedCouponCode}
                      appliedRewardCode={order.appliedRewardCode}
                      affectedItemId={order.affectedItemId}
                      couponUsed={order.couponUsed}
                      totalPrice={
                        typeof order.totalPrice === "number"
                          ? order.totalPrice
                          : parseFloat(String(order.totalPrice))
                      }
                      orderItems={order.items?.map((item) => ({
                        itemId: item.itemId || item.id,
                        name: item.name,
                        price: item.price,
                        quantity: item.quantity,
                      }))}
                      className="mb-4"
                    />

                    <div className="flex justify-between font-medium">
                      <span>Total</span>
                      <span>
                        {typeof order.totalPrice === "number"
                          ? order.totalPrice.toFixed(2)
                          : order.totalPrice}{" "}
                        AZN
                      </span>
                    </div>

                    {order.notes && (
                      <div className="mt-4 p-3 bg-muted rounded-md">
                        <h4 className="font-medium text-sm mb-1">
                          Order Notes
                        </h4>
                        <p className="text-sm text-muted-foreground">
                          {order.notes}
                        </p>
                      </div>
                    )}

                    <div className="mt-4 flex justify-end gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => onPrintReceipt(order)}
                        className="flex items-center gap-1"
                      >
                        <Printer className="h-4 w-4" />
                        Print Receipt
                      </Button>
                      <Dialog
                        open={openDialog === order.orderId}
                        onOpenChange={(open) =>
                          setOpenDialog(open ? order.orderId : null)
                        }
                      >
                        <DialogTrigger asChild>
                          <Button variant="outline" size="sm">
                            View Full Details
                          </Button>
                        </DialogTrigger>
                        <DialogContent className="sm:max-w-md w-[95vw] max-w-[450px]">
                          <DialogHeader>
                            <DialogTitle>Order Details</DialogTitle>
                            <DialogDescription>
                              <div className="flex items-start gap-2">
                                <div>
                                  <span className="font-medium">Order ID:</span>{" "}
                                  <span className="break-all text-xs block mt-1 max-w-[350px]">
                                    {order.orderId}
                                  </span>
                                </div>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="h-7 px-2 mt-1"
                                  onClick={(e) => {
                                    e.preventDefault();
                                    copyToClipboard(order.orderId);
                                  }}
                                >
                                  {copiedOrderId === order.orderId ? (
                                    <Check className="h-3.5 w-3.5 text-green-500" />
                                  ) : (
                                    <Copy className="h-3.5 w-3.5" />
                                  )}
                                </Button>
                              </div>
                            </DialogDescription>
                          </DialogHeader>
                          <div className="space-y-4">
                            <div>
                              <h4 className="font-medium mb-1">Restaurant</h4>
                              <p>{order.restaurantName}</p>
                            </div>
                            <div>
                              <h4 className="font-medium mb-1">Order Date</h4>
                              <p>
                                {(() => {
                                  try {
                                    let dateObj;
                                    if (order.orderDate instanceof Date) {
                                      dateObj = order.orderDate;
                                    } else if (
                                      typeof order.orderDate === "object" &&
                                      order.orderDate !== null
                                    ) {
                                      // Check for Firestore Timestamp with toDate method
                                      const timestampWithMethod =
                                        order.orderDate as {
                                          toDate?: () => Date;
                                        };
                                      if (
                                        typeof timestampWithMethod.toDate ===
                                        "function"
                                      ) {
                                        dateObj = timestampWithMethod.toDate();
                                      } else {
                                        // Check for Firestore Timestamp-like object
                                        const timestamp = order.orderDate as {
                                          seconds?: number;
                                          nanoseconds?: number;
                                        };
                                        if (
                                          timestamp.seconds &&
                                          timestamp.nanoseconds
                                        ) {
                                          // Handle Firestore timestamp format
                                          dateObj = new Date(
                                            timestamp.seconds * 1000
                                          );
                                        } else {
                                          // Try to convert other objects to date
                                          try {
                                            dateObj = new Date(
                                              order.orderDate as unknown as
                                                | string
                                                | number
                                            );
                                          } catch {
                                            return "Invalid date";
                                          }
                                        }
                                      }
                                    } else if (
                                      typeof order.orderDate === "string"
                                    ) {
                                      dateObj = new Date(order.orderDate);
                                    } else if (
                                      typeof order.orderDate === "number"
                                    ) {
                                      dateObj = new Date(order.orderDate);
                                    } else {
                                      return "Invalid date";
                                    }

                                    // Validate the date
                                    if (isNaN(dateObj.getTime())) {
                                      return "Invalid date";
                                    }

                                    return format(dateObj, "PPPp");
                                  } catch (error) {
                                    console.error(
                                      "Date formatting error:",
                                      error
                                    );
                                    return "Invalid date";
                                  }
                                })()}
                              </p>
                            </div>
                            <div>
                              <h4 className="font-medium mb-1">Status</h4>
                              <Badge className={getStatusColor(order.status)}>
                                {getStatusIcon(order.status)}
                                {order.status.charAt(0).toUpperCase() +
                                  order.status.slice(1)}
                              </Badge>
                            </div>
                            <div>
                              <h4 className="font-medium mb-1">Items</h4>
                              <div className="space-y-2">
                                {order.items.map(
                                  (item: OrderItem, index: number) => (
                                    <div
                                      key={index}
                                      className="flex justify-between items-center py-2 border-b border-dashed last:border-0"
                                    >
                                      <div>
                                        <p className="font-medium">
                                          {item.name} x {item.quantity}
                                        </p>
                                        {item.notes && (
                                          <p className="text-sm text-muted-foreground">
                                            Note: {item.notes}
                                          </p>
                                        )}
                                      </div>
                                      <span>
                                        {(item.price * item.quantity).toFixed(
                                          2
                                        )}{" "}
                                        AZN
                                      </span>
                                    </div>
                                  )
                                )}
                              </div>
                            </div>
                            {order.notes && (
                              <div>
                                <h4 className="font-medium mb-1">
                                  Order Notes
                                </h4>
                                <p className="text-muted-foreground">
                                  {order.notes}
                                </p>
                              </div>
                            )}

                            {/* Coupon Usage Display */}
                            <CouponUsageDisplay
                              originalAmount={order.originalAmount}
                              discountAmount={order.discountAmount}
                              appliedCouponCode={order.appliedCouponCode}
                              appliedRewardCode={order.appliedRewardCode}
                              affectedItemId={order.affectedItemId}
                              couponUsed={order.couponUsed}
                              totalPrice={
                                typeof order.totalPrice === "number"
                                  ? order.totalPrice
                                  : parseFloat(String(order.totalPrice))
                              }
                              orderItems={order.items?.map((item) => ({
                                itemId: item.itemId || item.id,
                                name: item.name,
                                price: item.price,
                                quantity: item.quantity,
                              }))}
                              className="my-3"
                            />

                            <div className="flex justify-between font-medium text-lg pt-2 border-t">
                              <span>Total</span>
                              <span>
                                {typeof order.totalPrice === "number"
                                  ? order.totalPrice.toFixed(2)
                                  : order.totalPrice}{" "}
                                AZN
                              </span>
                            </div>
                          </div>
                          <DialogFooter>
                            <Button
                              type="button"
                              onClick={() => setOpenDialog(null)}
                            >
                              Close
                            </Button>
                          </DialogFooter>
                        </DialogContent>
                      </Dialog>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};
