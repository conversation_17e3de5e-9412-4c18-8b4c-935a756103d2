export interface WorkingHours {
  day:
    | "monday"
    | "tuesday"
    | "wednesday"
    | "thursday"
    | "friday"
    | "saturday"
    | "sunday";
  isOpen: boolean;
  openTime: string;
  closeTime: string;
}

export interface Restaurant {
  id: string;
  restaurantName: string;
  name?: string; // Added for compatibility with RestaurantRecommendation
  address: string;
  rating: number;
  reviewCount?: number; // Number of reviews
  weightedRating?: number; // Calculated weighted rating
  combinedScore?: number; // Combined score for sorting
  imageUrl?: string;
  isOpen: boolean;
  categories: string[];
  cuisines: string[];
  phone: string;
  uid: string;
  username: string;
  location?: {
    latitude: number;
    longitude: number;
  };
  distance?: number;
  autoUpdateStatus: boolean;
  workingHours: WorkingHours[];
  description: string;
  seating?: {
    indoor: boolean;
    outdoor: boolean;
    totalCapacity?: number;
  };
  features?: string[];
  atmosphere?: string[];
  dietary?: string[];
  noiseLevel?: string;
  dressCode?: string;
  parkingAvailable: boolean;
  services?: string[];
  specialties?: string[];
  reservationPolicy?: string;
  paymentMethods?: string[];
  certifications?: string[];
  languages?: string[];
  priceRange?: string; // Price range indicator ($, $$, $$$, $$$$)
  estimatedDeliveryTime?: number; // Estimated delivery time in minutes
  menu?: MenuItem[]; // Menu items for the restaurant
}

export interface MenuItem {
  id?: string; // For backward compatibility
  itemId: string;
  name: string;
  description: string;
  price: number;
  category: string;
  imageUrl?: string;
  available: boolean;
  ingredients?: string[];
  dietary: string[];
  spicyLevel?: "mild" | "medium" | "hot" | "extra hot";
  allergens?: string[];

  // Basic nutrition information
  calories?: number;
  servingSize?: string;
  servingsPerItem?: number;

  // Macronutrients
  protein?: number; // in grams
  carbs?: number; // in grams
  fat?: number; // in grams

  // Detailed nutrition information
  fiber?: number; // in grams
  sugar?: number; // in grams
  sodium?: number; // in milligrams
  cholesterol?: number; // in milligrams

  // Additional nutrition information
  vitamins?: {
    vitaminA?: number;
    vitaminC?: number;
    vitaminD?: number;
    calcium?: number;
    iron?: number;
  };

  // Health labels
  healthLabels?: string[]; // e.g., "Low Fat", "High Protein", "Low Sodium"

  preparationTime?: string;
  isSignatureDish?: boolean;
  isSeasonalDish?: boolean;

  // Promotion fields
  isSpecialOffer?: boolean;
  isChefRecommendation?: boolean;
  isPopular?: boolean;
  isLimitedTimeOffer?: boolean;
  promotionEndDate?: Date;
  promotionDescription?: string;
  discountPercentage?: number;

  restaurantId: string; // ID of the restaurant this menu item belongs to
  restaurantUsername?: string; // Username of the restaurant
  restaurantName?: string; // Name of the restaurant
}

export interface Review {
  id: string;
  userId: string;
  userName: string;
  rating: number;
  comment: string;
  createdAt: Date;
  updatedAt?: Date;
  restaurantId: string;
  restaurantName: string;
  userAvatarUrl?: string;
}

export interface EditingReview {
  id: string;
  rating: number;
  comment: string;
}
