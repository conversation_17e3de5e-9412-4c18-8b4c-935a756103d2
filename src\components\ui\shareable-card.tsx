import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { SocialShareButtons } from "./social-share-buttons";
import { toast } from "sonner";
import { Link2 } from "lucide-react";
import { Separator } from "@/components/ui/separator";

export interface ShareableCardProps {
  title: string;
  description?: string;
  imageUrl?: string;
  url: string;
  hashtags?: string[];
  children?: React.ReactNode;
  className?: string;
}

export const ShareableCard = ({
  title = "Order Receipt",
  description,
  imageUrl = "/restaurant.png", // Default to restaurant.png
  url,
  hashtags = [],
  children,
}: ShareableCardProps) => {
  // Ensure title is not undefined
  const safeTitle = title || "Order Receipt";
  const handleCopyLink = async () => {
    try {
      await navigator.clipboard.writeText(url);
      toast.success("Link copied to clipboard!");
    } catch (error) {
      console.error("Failed to copy link:", error);
      toast.error("Failed to copy link. Please try again.");
    }
  };

  return (
    <div className="space-y-6">
      {/* Preview */}
      <div className="w-full aspect-video overflow-hidden rounded-lg border bg-muted mx-auto max-w-sm">
        {imageUrl ? (
          <img
            src={imageUrl}
            alt={safeTitle}
            className="w-full h-full object-cover"
            referrerPolicy="no-referrer"
            onError={(e) => {
              console.log("Image failed to load:", imageUrl);
              // Try to use restaurant.png as fallback
              e.currentTarget.src = "/restaurant.png";

              // Add a second error handler in case restaurant.png also fails
              e.currentTarget.onerror = () => {
                // Replace the img with a div containing the title if all images fail
                const parent = e.currentTarget.parentNode;
                if (parent) {
                  const fallbackDiv = document.createElement("div");
                  fallbackDiv.className =
                    "w-full h-full flex items-center justify-center bg-muted";

                  const titleSpan = document.createElement("span");
                  titleSpan.className = "text-muted-foreground font-medium";
                  titleSpan.textContent = safeTitle;

                  fallbackDiv.appendChild(titleSpan);
                  parent.replaceChild(fallbackDiv, e.currentTarget);
                }
              };
            }}
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center bg-muted">
            <span className="text-muted-foreground font-medium">
              {safeTitle}
            </span>
          </div>
        )}
      </div>

      {/* Content Preview */}
      <div className="space-y-3 max-w-sm mx-auto">
        <h3 className="text-xl font-bold">{safeTitle}</h3>
        {description && (
          <p className="text-muted-foreground text-sm">{description}</p>
        )}
        {children}
      </div>

      <Separator className="my-4" />

      {/* Share Options */}
      <div className="space-y-6">
        {/* Copy Link Button */}
        <div className="flex justify-center">
          <Button
            variant="outline"
            onClick={handleCopyLink}
            className="flex items-center gap-2 px-4 py-2 h-10 w-full max-w-sm"
          >
            <Link2 className="h-4 w-4" />
            Copy Link
          </Button>
        </div>

        {/* Social Media Buttons */}
        <div className="flex justify-center">
          <SocialShareButtons
            url={url}
            title={safeTitle}
            description={description}
            hashtags={hashtags}
            iconSize={40}
          />
        </div>
      </div>
    </div>
  );
};
