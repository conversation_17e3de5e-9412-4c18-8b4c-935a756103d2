import { Loader2 } from "lucide-react"; // Gerçek spinner ikonu için

interface LoadingProps {
  /** Eğer true ise, tüm ekranı kaplayan bir overlay gösterir. Varsayılan: false */
  fullScreen?: boolean;
  /** Gösterilecek iskelet veya spinner türü. Varsayılan: 'default' */
  type?: "default" | "card" | "profile" | "button";
  /** <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> metni (sadece 'default' türü için anlamlı). */
  text?: string;
  /** Ana konteyner için ek CSS sınıfları. */
  className?: string;
}

export const Loading = ({
  fullScreen = false,
  type = "default",
  text = "Loading...", // Varsayılan bir metin ekleyelim
  className = "",
}: LoadingProps) => {
  // Konteyner sınıflarını belirle
  const baseClasses = "flex items-center justify-center";
  const sizeClasses = fullScreen
    ? "fixed inset-0 z-50 bg-background/80 backdrop-blur-sm" // Tam ekran overlay
    : type === "button"
      ? "w-full h-full" // Button için daha küçük konteyner
      : "w-full h-full min-h-[150px]"; // Diğer tipler için konteyner içi

  const renderContent = () => {
    switch (type) {
      case "card":
        return (
          <div className="w-full max-w-sm rounded-lg border bg-card p-6 space-y-4 overflow-hidden">
            {/* Kart Resmi Alanı İskeleti */}
            <div className="aspect-video w-full bg-muted rounded-md animate-pulse" />
            {/* Başlık İskeleti */}
            <div className="h-5 w-3/4 bg-muted rounded-md animate-pulse" />
            {/* Açıklama Satırları İskeleti */}
            <div className="space-y-2">
              <div className="h-4 w-full bg-muted/70 rounded animate-pulse" />
              <div className="h-4 w-5/6 bg-muted/70 rounded animate-pulse" />
            </div>
            {/* Buton İskeleti */}
            <div className="h-9 w-1/3 bg-muted rounded-md animate-pulse mt-4" />
          </div>
        );
      case "profile":
        return (
          <div className="w-full max-w-lg rounded-lg border bg-card p-6 space-y-6 overflow-hidden">
            {/* Avatar ve Başlık İskeleti */}
            <div className="flex items-center space-x-4">
              <div className="h-16 w-16 bg-muted rounded-full animate-pulse flex-shrink-0" />
              <div className="space-y-2 flex-grow">
                <div className="h-6 w-1/2 bg-muted rounded-md animate-pulse" />
                <div className="h-4 w-3/4 bg-muted/70 rounded animate-pulse" />
              </div>
            </div>
            {/* Form Alanları İskeleti */}
            <div className="space-y-5">
              {[1, 2, 3].map((i) => (
                <div key={i} className="space-y-2">
                  <div className="h-4 w-1/4 bg-muted/50 rounded animate-pulse" />
                  <div className="h-9 w-full bg-muted/70 rounded-md animate-pulse" />
                </div>
              ))}
              {/* Buton İskeleti */}
              <div className="h-10 w-1/3 bg-muted rounded-md animate-pulse pt-2" />
            </div>
          </div>
        );
      case "button":
        return (
          <div className="flex items-center justify-center">
            <div className="flex space-x-1">
              <div className="h-2 w-2 bg-white rounded-full animate-bounce" style={{ animationDelay: "0ms" }} />
              <div className="h-2 w-2 bg-white rounded-full animate-bounce" style={{ animationDelay: "150ms" }} />
              <div className="h-2 w-2 bg-white rounded-full animate-bounce" style={{ animationDelay: "300ms" }} />
            </div>
          </div>
        );
      // Default: Spinner ve Metin
      default:
        return (
          <div className="flex flex-col items-center justify-center gap-3 text-center p-4">
            <Loader2 className="h-8 w-8 text-primary animate-spin" />
            {text && (
              <p className="text-sm font-medium text-muted-foreground">
                {text}
              </p>
            )}
          </div>
        );
    }
  };

  return (
    <div
      className={`${baseClasses} ${sizeClasses} ${className}`}
      role="status" // Erişilebilirlik için rol belirt
      aria-live="polite" // Ekran okuyuculara durumun değiştiğini bildirir
      aria-label={text || "Content is loading"} // Yükleniyor metnini etiket olarak ekle
    >
      {renderContent()}
    </div>
  );
};
