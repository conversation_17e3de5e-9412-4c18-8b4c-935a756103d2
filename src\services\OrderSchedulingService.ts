import {
  collection,
  query,
  where,
  getDocs,
  updateDoc,
  doc,
  Timestamp,
  getDoc,
  orderBy,
} from "firebase/firestore";
import { firestore } from "@/config/firebase";
import { Order } from "@/types/order";
import { notificationService } from "./NotificationService";
import { format } from "date-fns";

class OrderSchedulingService {
  // Fetch upcoming scheduled orders for a restaurant
  async getUpcomingScheduledOrders(restaurantId: string): Promise<Order[]> {
    try {
      const now = Timestamp.now();
      const ordersRef = collection(
        firestore,
        "restaurants",
        restaurantId,
        "orders"
      );
      const scheduledOrdersQuery = query(
        ordersRef,
        where("isScheduled", "==", true),
        where("scheduledFor", ">", now),
        where("status", "==", "pending"),
        orderBy("scheduledFor", "asc")
      );

      const querySnapshot = await getDocs(scheduledOrdersQuery);
      return querySnapshot.docs.map(
        (doc) =>
          ({
            id: doc.id,
            ...doc.data(),
          } as Order)
      );
    } catch (error) {
      console.error("Error fetching upcoming scheduled orders:", error);
      return [];
    }
  }

  // Fetch upcoming scheduled orders for a user
  async getUserScheduledOrders(userId: string): Promise<Order[]> {
    try {
      const now = Timestamp.now();
      const ordersRef = collection(firestore, "clients", userId, "orders");
      const scheduledOrdersQuery = query(
        ordersRef,
        where("isScheduled", "==", true),
        where("scheduledFor", ">", now),
        orderBy("scheduledFor", "asc")
      );

      const querySnapshot = await getDocs(scheduledOrdersQuery);
      return querySnapshot.docs.map(
        (doc) =>
          ({
            id: doc.id,
            ...doc.data(),
          } as Order)
      );
    } catch (error) {
      console.error("Error fetching user scheduled orders:", error);
      return [];
    }
  }

  // Process orders that are due to be fulfilled
  async processScheduledOrders(): Promise<number> {
    try {
      const now = Timestamp.now();
      // Add a small buffer (5 minutes) to account for processing delays
      const bufferTime = new Date(now.toMillis() + 5 * 60 * 1000);
      const bufferTimestamp = Timestamp.fromDate(bufferTime);

      // Query all restaurants for scheduled orders that are due
      const restaurantsRef = collection(firestore, "restaurants");
      const restaurantsSnapshot = await getDocs(restaurantsRef);

      let processedCount = 0;

      for (const restaurantDoc of restaurantsSnapshot.docs) {
        const restaurantId = restaurantDoc.id;
        const ordersRef = collection(
          firestore,
          "restaurants",
          restaurantId,
          "orders"
        );
        const dueOrdersQuery = query(
          ordersRef,
          where("isScheduled", "==", true),
          where("scheduledFor", "<=", bufferTimestamp),
          where("status", "==", "pending")
        );

        const dueOrdersSnapshot = await getDocs(dueOrdersQuery);

        for (const orderDoc of dueOrdersSnapshot.docs) {
          const orderId = orderDoc.id;
          const orderData = orderDoc.data() as Order;

          // Update order status to processing
          await updateDoc(
            doc(firestore, "restaurants", restaurantId, "orders", orderId),
            {
              status: "preparing",
              updatedAt: now,
            }
          );

          // Update client's order copy
          await updateDoc(
            doc(firestore, "clients", orderData.userId, "orders", orderId),
            {
              status: "preparing",
              updatedAt: now,
            }
          );

          // Send notification to restaurant
          await this.notifyRestaurantAboutScheduledOrder(restaurantId, orderId);

          // Send notification to customer
          await this.notifyCustomerAboutScheduledOrder(
            orderData.userId,
            orderId,
            orderData,
            restaurantId
          );

          processedCount++;
        }
      }

      return processedCount;
    } catch (error) {
      console.error("Error processing scheduled orders:", error);
      return 0;
    }
  }

  // Send reminder notifications for upcoming scheduled orders
  async sendScheduledOrderReminders(): Promise<number> {
    try {
      const now = Timestamp.now();
      // Set reminder time to 30 minutes before scheduled time
      const reminderTime = new Date(now.toMillis() + 30 * 60 * 1000);
      const reminderEndTime = new Date(reminderTime.getTime() + 5 * 60 * 1000); // 5 minute window

      const reminderStartTimestamp = Timestamp.fromDate(reminderTime);
      const reminderEndTimestamp = Timestamp.fromDate(reminderEndTime);

      // Query all restaurants for scheduled orders that need reminders
      const restaurantsRef = collection(firestore, "restaurants");
      const restaurantsSnapshot = await getDocs(restaurantsRef);

      let reminderCount = 0;

      for (const restaurantDoc of restaurantsSnapshot.docs) {
        const restaurantId = restaurantDoc.id;
        const restaurantData = restaurantDoc.data();
        const restaurantName = restaurantData.restaurantName || "Restaurant";

        const ordersRef = collection(
          firestore,
          "restaurants",
          restaurantId,
          "orders"
        );
        const reminderOrdersQuery = query(
          ordersRef,
          where("isScheduled", "==", true),
          where("scheduledFor", ">=", reminderStartTimestamp),
          where("scheduledFor", "<=", reminderEndTimestamp),
          where("reminderSent", "==", false),
          where("status", "==", "pending")
        );

        const reminderOrdersSnapshot = await getDocs(reminderOrdersQuery);

        for (const orderDoc of reminderOrdersSnapshot.docs) {
          const orderId = orderDoc.id;
          const orderData = orderDoc.data() as Order;

          // Update reminder sent flag
          await updateDoc(
            doc(firestore, "restaurants", restaurantId, "orders", orderId),
            {
              reminderSent: true,
            }
          );

          // Update client's order copy
          await updateDoc(
            doc(firestore, "clients", orderData.userId, "orders", orderId),
            {
              reminderSent: true,
            }
          );

          // Get customer details
          const customerDoc = await getDoc(
            doc(firestore, "clients", orderData.userId)
          );
          if (customerDoc.exists()) {
            const customerData = customerDoc.data();

            // Send notification to customer
            await notificationService.createDatabaseNotification(
              orderData.userId,
              "client",
              "order_reminder",
              "Order Reminder",
              `Your scheduled order at ${restaurantName} will be processed in 30 minutes.`,
              { orderId }
            );

            // Send email notification if email exists
            if (customerData.email) {
              notificationService
                .sendEmailNotification({
                  to: customerData.email,
                  subject: "Your Scheduled Order Reminder",
                  body: "",
                  recipientName: `${customerData.firstName} ${customerData.lastName}`,
                  type: "order",
                  data: {
                    orderDetails: orderData,
                    restaurantName,
                    status: "reminder",
                    orderId,
                  },
                })
                .catch((err) => {
                  console.error("Error in reminder email notification:", err);
                });
            }
          }

          reminderCount++;
        }
      }

      return reminderCount;
    } catch (error) {
      console.error("Error sending scheduled order reminders:", error);
      return 0;
    }
  }

  // Notify restaurant about a scheduled order that's ready to be processed
  private async notifyRestaurantAboutScheduledOrder(
    restaurantId: string,
    orderId: string
    // Order data not used in this method
  ): Promise<void> {
    try {
      // Create notification in restaurant's notifications collection
      await notificationService.createDatabaseNotification(
        restaurantId,
        "restaurant",
        "scheduled_order_ready",
        "Scheduled Order Ready",
        `A scheduled order is now ready to be prepared.`,
        { orderId }
      );
    } catch (error) {
      console.error("Error notifying restaurant about scheduled order:", error);
    }
  }

  // Notify customer that their scheduled order is being processed
  private async notifyCustomerAboutScheduledOrder(
    userId: string,
    orderId: string,
    orderData: Order,
    restaurantId: string
  ): Promise<void> {
    try {
      // Get restaurant details
      const restaurantDoc = await getDoc(
        doc(firestore, "restaurants", restaurantId)
      );
      const restaurantName = restaurantDoc.exists()
        ? (restaurantDoc.data() as { restaurantName?: string })
            ?.restaurantName || "Restaurant"
        : "Restaurant";

      // Create notification in customer's notifications collection
      await notificationService.createDatabaseNotification(
        userId,
        "client",
        "scheduled_order_processing",
        "Scheduled Order Processing",
        `Your scheduled order at ${restaurantName} is now being prepared.`,
        { orderId }
      );

      // Get customer details for email notification
      const customerDoc = await getDoc(doc(firestore, "clients", userId));
      if (customerDoc.exists()) {
        const customerData = customerDoc.data();

        // Send email notification if email exists
        if (customerData.email) {
          notificationService
            .sendEmailNotification({
              to: customerData.email,
              subject: "Your Scheduled Order is Being Prepared",
              body: "",
              recipientName: `${customerData.firstName} ${customerData.lastName}`,
              type: "order",
              data: {
                orderDetails: orderData,
                restaurantName,
                status: "preparing",
                orderId,
              },
            })
            .catch((err) => {
              console.error(
                "Error in scheduled order email notification:",
                err
              );
            });
        }
      }
    } catch (error) {
      console.error("Error notifying customer about scheduled order:", error);
    }
  }

  // Check if a scheduled order can be updated to a new status
  async canUpdateScheduledOrder(
    order: Order,
    newStatus: Order["status"]
  ): Promise<{ canUpdate: boolean; message?: string }> {
    try {
      // If the order is not scheduled, it can be updated
      if (!order.isScheduled || !order.scheduledFor) {
        return { canUpdate: true };
      }

      const now = new Date();
      const scheduledTime = order.scheduledFor.toDate();

      // Calculate time difference in minutes
      const timeDifferenceMs = scheduledTime.getTime() - now.getTime();
      const timeDifferenceMinutes = Math.floor(timeDifferenceMs / (1000 * 60));

      // Special case: Cancel can always be done
      if (newStatus === "cancelled") {
        return { canUpdate: true };
      }

      // Special case: Accept Order (preparing) can be done at any time
      if (newStatus === "preparing" && order.status === "pending") {
        return { canUpdate: true };
      }

      // For other status changes (ready, completed), only allow if:
      // 1. The scheduled time has passed, or
      // 2. The scheduled time is within 10 minutes
      if (timeDifferenceMinutes <= 10 || timeDifferenceMs <= 0) {
        return { canUpdate: true };
      }

      // Otherwise, don't allow status changes
      return {
        canUpdate: false,
        message: `This order is scheduled for ${format(
          scheduledTime,
          "PPP 'at' p"
        )}. You can only mark it as ready or completed 10 minutes before the scheduled time.`,
      };
    } catch (error) {
      console.error("Error checking if scheduled order can be updated:", error);
      return {
        canUpdate: false,
        message: "Failed to check if order can be updated",
      };
    }
  }

  // Check if a restaurant is accepting scheduled orders for a specific time
  async isSchedulingAvailable(
    restaurantId: string,
    scheduledTime: Date
  ): Promise<{ available: boolean; message?: string }> {
    try {
      // Get restaurant details
      const restaurantDoc = await getDoc(
        doc(firestore, "restaurants", restaurantId)
      );
      if (!restaurantDoc.exists()) {
        return { available: false, message: "Restaurant not found" };
      }

      const restaurantData = restaurantDoc.data();

      // Check if restaurant has scheduling enabled
      if (restaurantData.schedulingEnabled === false) {
        return {
          available: false,
          message: "This restaurant does not accept scheduled orders",
        };
      }

      // Check if the scheduled time is within the restaurant's operating hours
      // This would require additional logic to check working hours

      // Check if the restaurant has too many scheduled orders for that time slot
      const oneHourBefore = new Date(scheduledTime.getTime() - 60 * 60 * 1000);
      const oneHourAfter = new Date(scheduledTime.getTime() + 60 * 60 * 1000);

      const ordersRef = collection(
        firestore,
        "restaurants",
        restaurantId,
        "orders"
      );
      const existingOrdersQuery = query(
        ordersRef,
        where("isScheduled", "==", true),
        where("scheduledFor", ">=", Timestamp.fromDate(oneHourBefore)),
        where("scheduledFor", "<=", Timestamp.fromDate(oneHourAfter)),
        where("status", "==", "pending")
      );

      const existingOrdersSnapshot = await getDocs(existingOrdersQuery);

      // If there are too many orders in that time slot (e.g., more than 5), reject
      const maxOrdersPerHour = restaurantData.maxScheduledOrdersPerHour || 5;
      if (existingOrdersSnapshot.size >= maxOrdersPerHour) {
        return {
          available: false,
          message:
            "This time slot is fully booked. Please select a different time.",
        };
      }

      return { available: true };
    } catch (error) {
      console.error("Error checking scheduling availability:", error);
      return {
        available: false,
        message: "Failed to check scheduling availability",
      };
    }
  }
}

export const orderSchedulingService = new OrderSchedulingService();
