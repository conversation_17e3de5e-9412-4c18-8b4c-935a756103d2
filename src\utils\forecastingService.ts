/**
 * ForecastingService - Utility for generating forecasts based on historical data
 */

interface DataPoint {
  date: string;
  value: number;
}

interface ForecastResult {
  data: DataPoint[];
  confidence: number;
  algorithm: string;
  r2?: number; // R-squared value for regression models
}

/**
 * Performs linear regression on historical data and forecasts future values
 *
 * @param historicalData Array of historical data points
 * @param forecastDays Number of days to forecast
 * @returns Forecast result with predicted data points
 */
export function linearRegressionForecast(
  historicalData: DataPoint[],
  forecastDays: number
): ForecastResult {
  if (historicalData.length < 5) {
    throw new Error("Insufficient data for forecasting. Need at least 5 data points.");
  }

  // Convert dates to numerical x values (days since first date)
  const firstDate = new Date(historicalData[0].date);
  const xValues: number[] = [];
  const yValues: number[] = [];

  historicalData.forEach((point) => {
    const date = new Date(point.date);
    const daysDiff = Math.floor((date.getTime() - firstDate.getTime()) / (1000 * 60 * 60 * 24));
    xValues.push(daysDiff);
    yValues.push(point.value);
  });

  // Calculate linear regression parameters
  const n = xValues.length;
  const sumX = xValues.reduce((sum, x) => sum + x, 0);
  const sumY = yValues.reduce((sum, y) => sum + y, 0);
  const sumXY = xValues.reduce((sum, x, i) => sum + x * yValues[i], 0);
  const sumXX = xValues.reduce((sum, x) => sum + x * x, 0);

  const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
  const intercept = (sumY - slope * sumX) / n;

  // Calculate R-squared
  const yMean = sumY / n;
  const totalSumOfSquares = yValues.reduce((sum, y) => sum + Math.pow(y - yMean, 2), 0);
  const predictedValues = xValues.map(x => slope * x + intercept);
  const residualSumOfSquares = yValues.reduce((sum, y, i) => sum + Math.pow(y - predictedValues[i], 2), 0);
  const r2 = 1 - (residualSumOfSquares / totalSumOfSquares);

  // Generate forecast data
  const lastDate = new Date(historicalData[historicalData.length - 1].date);
  const forecastData: DataPoint[] = [];

  for (let i = 1; i <= forecastDays; i++) {
    const forecastDate = new Date(lastDate);
    forecastDate.setDate(forecastDate.getDate() + i);

    const x = xValues[xValues.length - 1] + i;
    const forecastValue = slope * x + intercept;

    // Ensure forecast values are non-negative
    const adjustedValue = Math.max(0, forecastValue);

    forecastData.push({
      date: forecastDate.toISOString().split('T')[0],
      value: Math.round(adjustedValue * 100) / 100, // Round to 2 decimal places
    });
  }

  // Calculate confidence based on R-squared
  // Higher R-squared means higher confidence
  const confidence = Math.min(Math.max(r2 * 100, 30), 95); // Limit between 30% and 95%

  return {
    data: forecastData,
    confidence: Math.round(confidence),
    algorithm: 'Linear Regression',
    r2: Math.round(r2 * 100) / 100,
  };
}

/**
 * Performs moving average forecast on historical data
 *
 * @param historicalData Array of historical data points
 * @param forecastDays Number of days to forecast
 * @param windowSize Size of the moving average window
 * @returns Forecast result with predicted data points
 */
export function movingAverageForecast(
  historicalData: DataPoint[],
  forecastDays: number,
  windowSize: number = 7
): ForecastResult {
  if (historicalData.length < windowSize) {
    throw new Error(`Insufficient data for forecasting. Need at least ${windowSize} data points.`);
  }

  // Calculate moving average for the historical data
  const movingAverages: number[] = [];
  for (let i = windowSize - 1; i < historicalData.length; i++) {
    let sum = 0;
    for (let j = 0; j < windowSize; j++) {
      sum += historicalData[i - j].value;
    }
    movingAverages.push(sum / windowSize);
  }

  // Generate forecast data
  const lastDate = new Date(historicalData[historicalData.length - 1].date);
  const forecastData: DataPoint[] = [];

  // Use the last few values to predict future values
  const lastValues = historicalData.slice(-windowSize).map(point => point.value);

  for (let i = 1; i <= forecastDays; i++) {
    const forecastDate = new Date(lastDate);
    forecastDate.setDate(forecastDate.getDate() + i);

    // Calculate the moving average for the forecast
    const sum = lastValues.reduce((acc, val) => acc + val, 0);
    const average = sum / windowSize;

    // Add the forecast to the data
    forecastData.push({
      date: forecastDate.toISOString().split('T')[0],
      value: Math.round(average * 100) / 100, // Round to 2 decimal places
    });

    // Update the last values for the next forecast
    lastValues.shift();
    lastValues.push(average);
  }

  // Calculate confidence based on the stability of the data
  // More stable data = higher confidence
  const values = historicalData.map(point => point.value);
  const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
  const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
  const stdDev = Math.sqrt(variance);
  const coefficientOfVariation = mean > 0 ? stdDev / mean : 1;

  // Lower coefficient of variation means more stable data
  const confidence = Math.min(Math.max(100 - coefficientOfVariation * 100, 30), 90);

  return {
    data: forecastData,
    confidence: Math.round(confidence),
    algorithm: 'Moving Average',
  };
}

/**
 * Performs weighted moving average forecast on historical data
 *
 * @param historicalData Array of historical data points
 * @param forecastDays Number of days to forecast
 * @param weights Array of weights for the weighted average
 * @returns Forecast result with predicted data points
 */
export function weightedMovingAverageForecast(
  historicalData: DataPoint[],
  forecastDays: number,
  weights: number[] = [0.5, 0.3, 0.2]
): ForecastResult {
  if (historicalData.length < weights.length) {
    throw new Error(`Insufficient data for forecasting. Need at least ${weights.length} data points.`);
  }

  // Ensure weights sum to 1
  const sumWeights = weights.reduce((sum, weight) => sum + weight, 0);
  const normalizedWeights = weights.map(weight => weight / sumWeights);

  // Generate forecast data
  const lastDate = new Date(historicalData[historicalData.length - 1].date);
  const forecastData: DataPoint[] = [];

  // Use the last few values to predict future values
  const lastValues = historicalData.slice(-normalizedWeights.length).map(point => point.value);

  for (let i = 1; i <= forecastDays; i++) {
    const forecastDate = new Date(lastDate);
    forecastDate.setDate(forecastDate.getDate() + i);

    // Calculate the weighted average for the forecast
    let weightedSum = 0;
    for (let j = 0; j < normalizedWeights.length; j++) {
      weightedSum += lastValues[lastValues.length - 1 - j] * normalizedWeights[j];
    }

    // Add the forecast to the data
    forecastData.push({
      date: forecastDate.toISOString().split('T')[0],
      value: Math.round(weightedSum * 100) / 100, // Round to 2 decimal places
    });

    // Update the last values for the next forecast
    lastValues.shift();
    lastValues.push(weightedSum);
  }

  // Calculate confidence based on the stability of the data
  // More stable data = higher confidence
  const values = historicalData.map(point => point.value);
  const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
  const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
  const stdDev = Math.sqrt(variance);
  const coefficientOfVariation = mean > 0 ? stdDev / mean : 1;

  // Lower coefficient of variation means more stable data
  const confidence = Math.min(Math.max(100 - coefficientOfVariation * 100, 40), 92);

  return {
    data: forecastData,
    confidence: Math.round(confidence),
    algorithm: 'Weighted Moving Average',
  };
}

/**
 * Performs exponential smoothing forecast on historical data
 *
 * @param historicalData Array of historical data points
 * @param forecastDays Number of days to forecast
 * @param alpha Smoothing factor (0 < alpha < 1)
 * @returns Forecast result with predicted data points
 */
export function exponentialSmoothingForecast(
  historicalData: DataPoint[],
  forecastDays: number,
  alpha: number = 0.3
): ForecastResult {
  if (historicalData.length < 3) {
    throw new Error("Insufficient data for forecasting. Need at least 3 data points.");
  }

  // Ensure alpha is between 0 and 1
  alpha = Math.max(0.1, Math.min(0.9, alpha));

  // Calculate exponential smoothing for the historical data
  const smoothedValues: number[] = [historicalData[0].value];

  for (let i = 1; i < historicalData.length; i++) {
    const smoothed = alpha * historicalData[i].value + (1 - alpha) * smoothedValues[i - 1];
    smoothedValues.push(smoothed);
  }

  // Generate forecast data
  const lastDate = new Date(historicalData[historicalData.length - 1].date);
  const forecastData: DataPoint[] = [];

  // Use the last smoothed value as the starting point for forecasting
  const lastSmoothedValue = smoothedValues[smoothedValues.length - 1];

  for (let i = 1; i <= forecastDays; i++) {
    const forecastDate = new Date(lastDate);
    forecastDate.setDate(forecastDate.getDate() + i);

    // In simple exponential smoothing, all future forecasts equal the last smoothed value
    forecastData.push({
      date: forecastDate.toISOString().split('T')[0],
      value: Math.round(lastSmoothedValue * 100) / 100, // Round to 2 decimal places
    });
  }

  // Calculate mean absolute percentage error (MAPE) for confidence
  let sumPercentageError = 0;
  let countValidPoints = 0;

  for (let i = 1; i < historicalData.length; i++) {
    const actual = historicalData[i].value;
    const forecast = smoothedValues[i - 1];

    if (actual !== 0) {
      sumPercentageError += Math.abs((actual - forecast) / actual);
      countValidPoints++;
    }
  }

  const mape = countValidPoints > 0 ? sumPercentageError / countValidPoints : 0;
  const confidence = Math.min(Math.max(100 - mape * 100, 35), 93);

  return {
    data: forecastData,
    confidence: Math.round(confidence),
    algorithm: 'Exponential Smoothing',
  };
}

/**
 * Performs seasonal forecast on historical data
 *
 * @param historicalData Array of historical data points
 * @param forecastDays Number of days to forecast
 * @param seasonalPeriod Length of the seasonal period (e.g., 7 for weekly patterns)
 * @returns Forecast result with predicted data points
 */
export function seasonalForecast(
  historicalData: DataPoint[],
  forecastDays: number,
  seasonalPeriod: number = 7
): ForecastResult {
  if (historicalData.length < seasonalPeriod * 2) {
    throw new Error(`Insufficient data for seasonal forecasting. Need at least ${seasonalPeriod * 2} data points.`);
  }

  // Calculate seasonal indices
  const seasons = Math.floor(historicalData.length / seasonalPeriod);
  const seasonalIndices: number[] = Array(seasonalPeriod).fill(0);

  // Calculate average for each season
  for (let i = 0; i < seasonalPeriod; i++) {
    let sum = 0;
    let count = 0;

    for (let j = 0; j < seasons; j++) {
      const index = j * seasonalPeriod + i;
      if (index < historicalData.length) {
        sum += historicalData[index].value;
        count++;
      }
    }

    seasonalIndices[i] = count > 0 ? sum / count : 0;
  }

  // Normalize seasonal indices
  const avgIndex = seasonalIndices.reduce((sum, val) => sum + val, 0) / seasonalPeriod;
  const normalizedIndices = seasonalIndices.map(index => avgIndex > 0 ? index / avgIndex : 1);

  // Generate forecast data
  const lastDate = new Date(historicalData[historicalData.length - 1].date);
  const forecastData: DataPoint[] = [];

  // Calculate trend using linear regression
  const xValues: number[] = [];
  const yValues: number[] = [];

  historicalData.forEach((point, index) => {
    xValues.push(index);
    yValues.push(point.value);
  });

  const n = xValues.length;
  const sumX = xValues.reduce((sum, x) => sum + x, 0);
  const sumY = yValues.reduce((sum, y) => sum + y, 0);
  const sumXY = xValues.reduce((sum, x, i) => sum + x * yValues[i], 0);
  const sumXX = xValues.reduce((sum, x) => sum + x * x, 0);

  const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
  const intercept = (sumY - slope * sumX) / n;

  // Generate forecasts
  for (let i = 1; i <= forecastDays; i++) {
    const forecastDate = new Date(lastDate);
    forecastDate.setDate(forecastDate.getDate() + i);

    const x = xValues.length + i - 1;
    const trend = slope * x + intercept;

    // Apply seasonal index
    const seasonIndex = (historicalData.length + i - 1) % seasonalPeriod;
    const seasonalFactor = normalizedIndices[seasonIndex];
    const forecastValue = trend * seasonalFactor;

    forecastData.push({
      date: forecastDate.toISOString().split('T')[0],
      value: Math.max(0, Math.round(forecastValue * 100) / 100), // Ensure non-negative and round
    });
  }

  // Calculate confidence based on the consistency of seasonal patterns
  const seasonalVariance = normalizedIndices.reduce((sum, val) => sum + Math.pow(val - 1, 2), 0) / seasonalPeriod;
  const confidence = Math.min(Math.max(85 - seasonalVariance * 100, 40), 94);

  return {
    data: forecastData,
    confidence: Math.round(confidence),
    algorithm: 'Seasonal Forecast',
  };
}

/**
 * Generates a forecast based on historical data using the best algorithm
 *
 * @param historicalData Array of historical data points
 * @param forecastDays Number of days to forecast
 * @returns The best forecast result
 */
export function generateBestForecast(
  historicalData: DataPoint[],
  forecastDays: number
): ForecastResult {
  // Ensure we have enough data
  if (historicalData.length < 7) {
    throw new Error("Insufficient data for forecasting. Need at least 7 data points.");
  }

  try {
    // Try different forecasting methods and pick the one with highest confidence
    const forecasts: ForecastResult[] = [];

    try {
      forecasts.push(linearRegressionForecast(historicalData, forecastDays));
    } catch (e) {
      console.warn("Linear regression forecast failed:", e);
    }

    try {
      forecasts.push(movingAverageForecast(historicalData, forecastDays));
    } catch (e) {
      console.warn("Moving average forecast failed:", e);
    }

    try {
      forecasts.push(weightedMovingAverageForecast(historicalData, forecastDays));
    } catch (e) {
      console.warn("Weighted moving average forecast failed:", e);
    }

    try {
      forecasts.push(exponentialSmoothingForecast(historicalData, forecastDays));
    } catch (e) {
      console.warn("Exponential smoothing forecast failed:", e);
    }

    // Only try seasonal forecast if we have enough data
    if (historicalData.length >= 14) {
      try {
        forecasts.push(seasonalForecast(historicalData, forecastDays));
      } catch (e) {
        console.warn("Seasonal forecast failed:", e);
      }
    }

    // Sort by confidence and return the best one
    forecasts.sort((a, b) => b.confidence - a.confidence);

    return forecasts[0] || linearRegressionForecast(historicalData, forecastDays);
  } catch (error) {
    console.error("All forecasting methods failed:", error);
    throw new Error("Unable to generate forecast. Please try with more data.");
  }
}
