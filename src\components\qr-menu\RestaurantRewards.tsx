import React from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { useRestaurantRewards } from "@/lib/react-query/hooks/useRewards";
import { Reward } from "@/types/rewards";
import { useMenuItems } from "@/lib/react-query/hooks/useMenu";
import { useAuth } from "@/providers/AuthProvider";
import { Gift, Tag, Clock, Utensils, Star } from "lucide-react";
import { format } from "date-fns";

interface RestaurantRewardsProps {
  restaurantId: string;
  userPoints?: number;
  onRewardSelect?: (rewardId: string) => void;
}

export const RestaurantRewards: React.FC<RestaurantRewardsProps> = ({
  restaurantId,
  userPoints = 0,
  onRewardSelect,
}) => {
  const { user } = useAuth();
  const {
    data: rewards = [],
    isLoading,
    isError,
  } = useRestaurantRewards(restaurantId);
  const { data: menuItems = [] } = useMenuItems(restaurantId);

  // Helper function to get menu item name by ID
  const getMenuItemName = (menuItemId?: string) => {
    if (!menuItemId) return null;
    const menuItem = menuItems.find((item) => item.itemId === menuItemId);
    return menuItem?.name || "Unknown Item";
  };

  // Filter only active rewards
  const activeRewards = rewards.filter(
    (reward) =>
      reward.isActive &&
      (!reward.expiresAt || reward.expiresAt.toDate() > new Date()) &&
      (!reward.availableQuantity || reward.availableQuantity > 0)
  );

  if (isLoading) {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle className="flex items-center">
            <Gift className="mr-2 h-5 w-5 text-primary" />
            <Skeleton className="h-6 w-32" />
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            {[...Array(4)].map((_, i) => (
              <Skeleton key={i} className="h-32 w-full" />
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (isError || activeRewards.length === 0) {
    return null; // Don't show anything if there are no rewards or error
  }

  const getRewardTypeIcon = (type: string) => {
    switch (type) {
      case "discount":
        return <Tag className="h-4 w-4" />;
      case "freeItem":
        return <Utensils className="h-4 w-4" />;
      case "freeDelivery":
        return <Gift className="h-4 w-4" />;
      default:
        return <Star className="h-4 w-4" />;
    }
  };

  const getRewardTypeLabel = (reward: Reward) => {
    switch (reward.type) {
      case "discount":
        return `${reward.discountValue}${
          reward.discountType === "percentage" ? "%" : "$"
        } Off`;
      case "freeItem":
        return "Free Item";
      case "freeDelivery":
        return "Free Delivery";
      case "vipAccess":
        return "VIP Access";
      default:
        return "Special Offer";
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center">
          <Gift className="mr-2 h-5 w-5 text-primary" />
          Loyalty Rewards
        </CardTitle>
        {user && (
          <div className="text-sm text-muted-foreground">
            Your Points:{" "}
            <span className="font-semibold text-primary">{userPoints}</span>
          </div>
        )}
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          {activeRewards.map((reward) => {
            const hasEnoughPoints = userPoints >= reward.pointsCost;
            const canRedeem = hasEnoughPoints && user;

            return (
              <Card
                key={reward.id}
                className={`relative overflow-hidden transition-all duration-200 ${
                  canRedeem
                    ? "border-primary/50 hover:border-primary hover:shadow-md cursor-pointer"
                    : "opacity-75"
                }`}
                onClick={() => canRedeem && onRewardSelect?.(reward.id)}
              >
                <CardContent className="p-4">
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex items-center">
                      {getRewardTypeIcon(reward.type)}
                      <Badge
                        variant={canRedeem ? "default" : "secondary"}
                        className="ml-2"
                      >
                        {getRewardTypeLabel(reward)}
                      </Badge>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-semibold text-primary">
                        {reward.pointsCost} pts
                      </div>
                    </div>
                  </div>

                  <h3 className="font-medium text-sm mb-1 line-clamp-1">
                    {reward.name}
                  </h3>

                  <p className="text-xs text-muted-foreground mb-2 line-clamp-2">
                    {reward.description}
                  </p>

                  {/* Show menu item information if applicable */}
                  {reward.menuItemId && (
                    <div className="text-xs text-primary/80 mb-2 font-medium">
                      {reward.type === "discount" ? "For: " : "Item: "}
                      {getMenuItemName(reward.menuItemId)}
                    </div>
                  )}

                  {reward.expiresAt && (
                    <div className="flex items-center text-xs text-muted-foreground mb-2">
                      <Clock className="h-3 w-3 mr-1" />
                      Expires{" "}
                      {format(reward.expiresAt.toDate(), "MMM dd, yyyy")}
                    </div>
                  )}

                  {reward.availableQuantity && (
                    <div className="text-xs text-muted-foreground mb-2">
                      Only {reward.availableQuantity} left
                    </div>
                  )}

                  {user ? (
                    <Button
                      size="sm"
                      className="w-full text-xs h-7"
                      disabled={!canRedeem}
                      variant={canRedeem ? "default" : "secondary"}
                    >
                      {hasEnoughPoints
                        ? "Redeem"
                        : `Need ${reward.pointsCost - userPoints} more pts`}
                    </Button>
                  ) : (
                    <Button
                      size="sm"
                      variant="outline"
                      className="w-full text-xs h-7"
                      disabled
                    >
                      Login to Redeem
                    </Button>
                  )}
                </CardContent>
              </Card>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
};
