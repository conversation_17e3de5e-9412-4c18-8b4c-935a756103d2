import { firestore } from "@/config/firebase";
import { collection, query, getDocs } from "firebase/firestore";
import { Restaurant } from "@/types";
import { FollowedRestaurant } from "@/types/followers";

/**
 * Fetches the IDs of restaurants followed by a user
 * @param userId The ID of the user
 * @returns Array of restaurant IDs that the user follows
 */
export const getFollowedRestaurantIds = async (userId: string): Promise<string[]> => {
  if (!userId) return [];

  try {
    const followingRef = collection(firestore, "clients", userId, "following");
    const querySnapshot = await getDocs(query(followingRef));

    return querySnapshot.docs.map(doc => doc.id);
  } catch (error) {
    console.error("Error fetching followed restaurant IDs:", error);
    return [];
  }
};

/**
 * Fetches detailed information about restaurants followed by a user
 * @param userId The ID of the user
 * @returns Array of FollowedRestaurant objects
 */
export const getFollowedRestaurants = async (userId: string): Promise<FollowedRestaurant[]> => {
  if (!userId) return [];

  try {
    const followingRef = collection(firestore, "clients", userId, "following");
    const querySnapshot = await getDocs(query(followingRef));

    const followedRestaurants: FollowedRestaurant[] = [];

    querySnapshot.forEach(doc => {
      const data = doc.data() as Omit<FollowedRestaurant, "restaurantId">;
      followedRestaurants.push({
        restaurantId: doc.id,
        ...data,
      } as FollowedRestaurant);
    });

    return followedRestaurants;
  } catch (error) {
    console.error("Error fetching followed restaurants:", error);
    return [];
  }
};

/**
 * Checks if a user is following a specific restaurant
 * @param userId The ID of the user
 * @param restaurantId The ID of the restaurant
 * @returns Boolean indicating if the user follows the restaurant
 */
export const isFollowingRestaurant = async (userId: string, restaurantId: string): Promise<boolean> => {
  if (!userId || !restaurantId) return false;

  try {
    const followingIds = await getFollowedRestaurantIds(userId);
    return followingIds.includes(restaurantId);
  } catch (error) {
    console.error("Error checking if following restaurant:", error);
    return false;
  }
};

/**
 * Filters a list of restaurants to only include those followed by a user
 * @param restaurants Array of restaurants to filter
 * @param followedIds Array of restaurant IDs that the user follows
 * @returns Filtered array of restaurants
 */
export const filterFollowedRestaurants = (
  restaurants: Restaurant[],
  followedIds: string[]
): Restaurant[] => {
  if (!followedIds.length) return [];

  return restaurants.filter(restaurant => followedIds.includes(restaurant.id));
};
