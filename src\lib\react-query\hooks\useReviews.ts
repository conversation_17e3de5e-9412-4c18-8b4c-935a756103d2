import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import {
  collection,
  query,
  where,
  getDocs,
  doc,
  onSnapshot,
  orderBy,
  limit,
  writeBatch,
  serverTimestamp,
  FirestoreError,
  Timestamp,
} from "firebase/firestore";
import { firestore } from "@/config/firebase";
import { Review } from "@/types/restaurant";
import { queryKeys, CACHE_TIME, STALE_TIME } from "../index";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import { calculateRatingStats } from "@/utils/reviewUtils";

/**
 * Hook to fetch reviews for a restaurant with caching
 */
export function useReviews(restaurantId: string | undefined) {
  return useQuery({
    queryKey: queryKeys.restaurants.reviews(restaurantId || ""),
    queryFn: async () => {
      if (!restaurantId) {
        throw new Error("Restaurant ID is required");
      }

      const reviewsRef = collection(
        doc(firestore, "restaurants", restaurantId),
        "reviews"
      );
      const q = query(reviewsRef, orderBy("createdAt", "desc"));
      const querySnapshot = await getDocs(q);

      const reviews: Review[] = [];
      querySnapshot.forEach((doc) => {
        const data = doc.data();
        reviews.push({
          id: doc.id,
          ...data,
          createdAt:
            data.createdAt instanceof Timestamp
              ? data.createdAt.toDate()
              : new Date(data.createdAt),
          updatedAt:
            data.updatedAt instanceof Timestamp
              ? data.updatedAt.toDate()
              : data.updatedAt
              ? new Date(data.updatedAt)
              : undefined,
        } as Review);
      });

      return reviews;
    },
    enabled: !!restaurantId,
    staleTime: STALE_TIME,
    gcTime: CACHE_TIME,
  });
}

/**
 * Hook to fetch featured reviews with caching
 */
export function useFeaturedReviews(count = 5) {
  return useQuery({
    queryKey: queryKeys.reviews.featured,
    queryFn: async () => {
      const reviewsRef = collection(firestore, "reviews");
      const q = query(
        reviewsRef,
        where("rating", ">=", 4),
        orderBy("rating", "desc"),
        orderBy("createdAt", "desc"),
        limit(count)
      );

      const querySnapshot = await getDocs(q);

      const reviews: Review[] = [];
      querySnapshot.forEach((doc) => {
        const data = doc.data();
        reviews.push({
          id: doc.id,
          ...data,
          createdAt:
            data.createdAt instanceof Timestamp
              ? data.createdAt.toDate()
              : new Date(data.createdAt),
          updatedAt:
            data.updatedAt instanceof Timestamp
              ? data.updatedAt.toDate()
              : data.updatedAt
              ? new Date(data.updatedAt)
              : undefined,
        } as Review);
      });

      return reviews;
    },
    staleTime: STALE_TIME,
    gcTime: CACHE_TIME,
  });
}

/**
 * Hook to fetch reviews with real-time updates
 */
export function useRealtimeReviews(restaurantId: string | undefined) {
  const [reviews, setReviews] = useState<Review[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<FirestoreError | null>(null);

  useEffect(() => {
    if (!restaurantId) {
      setIsLoading(false);
      setReviews([]);
      setError(null);
      return;
    }

    setIsLoading(true);
    setError(null);

    const reviewsRef = collection(
      doc(firestore, "restaurants", restaurantId),
      "reviews"
    );
    const q = query(reviewsRef, orderBy("createdAt", "desc"));

    const unsubscribe = onSnapshot(
      q,
      (snapshot) => {
        const fetchedReviews = snapshot.docs.map((doc) => {
          const data = doc.data();
          return {
            id: doc.id,
            ...data,
            createdAt:
              data.createdAt instanceof Timestamp
                ? data.createdAt.toDate()
                : new Date(data.createdAt),
            updatedAt:
              data.updatedAt instanceof Timestamp
                ? data.updatedAt.toDate()
                : data.updatedAt
                ? new Date(data.updatedAt)
                : undefined,
          } as Review;
        });

        setReviews(fetchedReviews);
        setIsLoading(false);
      },
      (err: FirestoreError) => {
        console.error("Error fetching reviews:", err);
        setError(err);
        toast.error("Failed to load reviews");
        setIsLoading(false);
      }
    );

    return () => unsubscribe();
  }, [restaurantId]);

  return { reviews, setReviews, isLoading, error };
}

/**
 * Hook to add a review with optimistic updates
 */
export function useAddReview() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      restaurantId,
      userId,
      userName,
      userAvatarUrl,
      rating,
      comment,
      restaurantName,
    }: Omit<Review, "id" | "createdAt" | "updatedAt">) => {
      // Create the review reference directly when needed
      const timestamp = serverTimestamp();

      const reviewData = {
        userId,
        userName,
        userAvatarUrl: userAvatarUrl || null, // Use null instead of undefined
        rating,
        comment: comment.trim(),
        restaurantId,
        restaurantName,
        createdAt: timestamp,
        updatedAt: timestamp,
      };

      // Use a batch write for atomicity
      const batch = writeBatch(firestore);

      // Add to restaurant's subcollection
      const newReviewRef = doc(
        collection(firestore, "restaurants", restaurantId, "reviews")
      );
      batch.set(newReviewRef, reviewData);

      // Add to global collection if rating is high
      if (rating >= 4) {
        // Use the same ID for the global review document
        const globalReviewRef = doc(firestore, "reviews", newReviewRef.id);
        batch.set(globalReviewRef, reviewData);
      }

      // Get current reviews to calculate new rating
      const reviewsRef = collection(
        doc(firestore, "restaurants", restaurantId),
        "reviews"
      );
      const reviewsSnapshot = await getDocs(reviewsRef);

      const currentReviews: Review[] = [];
      reviewsSnapshot.forEach((doc) => {
        const data = doc.data();
        currentReviews.push({
          id: doc.id,
          ...data,
          createdAt:
            data.createdAt instanceof Timestamp
              ? data.createdAt.toDate()
              : new Date(data.createdAt),
          updatedAt:
            data.updatedAt instanceof Timestamp
              ? data.updatedAt.toDate()
              : data.updatedAt
              ? new Date(data.updatedAt)
              : undefined,
        } as Review);
      });

      // Add the new review to the array for calculation
      const tempReviews = [
        ...currentReviews,
        {
          id: newReviewRef.id,
          ...reviewData,
          createdAt: new Date(),
          updatedAt: new Date(),
        } as Review,
      ];

      // Calculate new rating and review count
      const { averageRating, reviewCount } = calculateRatingStats(tempReviews);

      // Update the restaurant document with the new rating and review count
      const restaurantRef = doc(firestore, "restaurants", restaurantId);
      batch.update(restaurantRef, {
        rating: averageRating,
        reviewCount: reviewCount,
      });

      await batch.commit();

      return {
        id: newReviewRef.id,
        ...reviewData,
        createdAt: new Date(),
        updatedAt: new Date(),
      } as Review;
    },
    onMutate: async (newReview) => {
      const { restaurantId } = newReview;

      // Cancel any outgoing refetches
      await queryClient.cancelQueries({
        queryKey: queryKeys.restaurants.reviews(restaurantId),
      });

      // Snapshot the previous values
      const previousReviews = queryClient.getQueryData<Review[]>(
        queryKeys.restaurants.reviews(restaurantId)
      );

      const previousRestaurant = queryClient.getQueryData(
        queryKeys.restaurants.detail(restaurantId)
      );

      // Create a temporary ID for optimistic update
      const tempId = `temp_${Date.now()}`;

      // Create optimistic review
      const optimisticReview: Review = {
        id: tempId,
        ...newReview,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      // Optimistically update reviews
      if (previousReviews) {
        queryClient.setQueryData(queryKeys.restaurants.reviews(restaurantId), [
          optimisticReview,
          ...previousReviews,
        ]);
      }

      return { previousReviews, previousRestaurant, tempId };
    },
    onError: (_, newReview, context) => {
      const { restaurantId } = newReview;

      // If the mutation fails, use the context returned from onMutate to roll back
      if (context?.previousReviews) {
        queryClient.setQueryData(
          queryKeys.restaurants.reviews(restaurantId),
          context.previousReviews
        );
      }

      if (context?.previousRestaurant) {
        queryClient.setQueryData(
          queryKeys.restaurants.detail(restaurantId),
          context.previousRestaurant
        );
      }

      toast.error("Failed to submit review");
    },
    onSuccess: () => {
      toast.success("Review submitted successfully!");
    },
    onSettled: (_, __, newReview) => {
      const { restaurantId } = newReview;

      // Always refetch after error or success to make sure our local data is correct
      queryClient.invalidateQueries({
        queryKey: queryKeys.restaurants.reviews(restaurantId),
      });
      queryClient.invalidateQueries({
        queryKey: queryKeys.restaurants.detail(restaurantId),
      });
      queryClient.invalidateQueries({
        queryKey: queryKeys.reviews.featured,
      });
    },
  });
}
