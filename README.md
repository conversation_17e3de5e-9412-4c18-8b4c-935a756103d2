# Qonai - Restaurant Marketplace Platform

## Overview

Qonai is a modern web application that connects users with local restaurants. The platform allows users to discover restaurants, browse menus, place orders, make reservations, and leave reviews. For restaurant owners, Qonai provides tools to manage their online presence, menu items, and customer interactions.

## Features

### For Customers

- **Restaurant Discovery**: Browse and search for restaurants by location, cuisine, or category
- **Menu Browsing**: View restaurant menus with detailed item descriptions and prices
- **Online Ordering**: Place food orders directly through the platform
- **Reservations**: Make table reservations at participating restaurants
- **Reviews & Ratings**: Leave reviews and ratings for restaurants
- **User Profiles**: Manage personal information and order history
- **AI Chat Assistant**: Get recommendations and help through the integrated AI chat

### For Restaurants

- **Restaurant Profiles**: Create and manage detailed restaurant profiles
- **Menu Management**: Add, edit, and organize menu items
- **Order Processing**: Receive and manage customer orders
- **Reservation Management**: Handle customer reservations
- **Analytics Dashboard**: View performance metrics and customer data
- **Working Hours**: Set and display restaurant operating hours

## Technology Stack

- **Frontend**: React 18 with TypeScript
- **Build Tool**: Vite
- **Styling**: TailwindCSS with shadcn/ui components
- **State Management**: React Query
- **Authentication & Database**: Firebase (Auth, Firestore)
- **Routing**: React Router
- **Maps & Location**: Leaflet, React-Leaflet
- **Payments**: PayPal integration
- **AI Integration**: Google Generative AI
- **Animations**: Framer Motion
- **Notifications**: Sonner toast notifications

## Getting Started

### Prerequisites

- Node.js (v16 or higher)
- npm or yarn
- Firebase account

### Installation

1. Clone the repository

   ```bash
   git clone https://github.com/alyvofficial/Qonai.git
   cd Qonai
   ```

2. Install dependencies

   ```bash
   npm install
   # or
   yarn
   ```

3. Create a `.env` file in the root directory with your Firebase configuration:

   ```
   VITE_APP_FIREBASE_API_KEY=your_api_key
   VITE_APP_FIREBASE_AUTH_DOMAIN=your_auth_domain
   VITE_APP_FIREBASE_PROJECT_ID=your_project_id
   VITE_APP_FIREBASE_STORAGE_BUCKET=your_storage_bucket
   VITE_APP_FIREBASE_MESSAGING_SENDER_ID=your_messaging_sender_id
   VITE_APP_FIREBASE_APP_ID=your_app_id
   VITE_APP_FIREBASE_MEASUREMENT_ID=your_measurement_id
   ```

4. Start the development server

   ```bash
   npm run dev
   # or
   yarn dev
   ```

5. Open your browser and navigate to `http://localhost:5173`

### Building for Production

```bash
npm run build
# or
yarn build
```

## Project Structure

- `/src`: Source code
  - `/components`: Reusable UI components
  - `/pages`: Application pages
  - `/providers`: Context providers
  - `/config`: Configuration files
  - `/hooks`: Custom React hooks
  - `/types`: TypeScript type definitions
  - `/utils`: Utility functions
  - `/services`: Service modules

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the ISC License - see the LICENSE file for details.

## Contact

Project Link: [https://github.com/alyvofficial/Qonai](https://github.com/alyvofficial/Qonai)
