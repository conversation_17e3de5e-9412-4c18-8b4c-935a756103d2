# Qonai Newsletter API

A secure API for managing newsletter subscriptions, sending notifications, and handling email campaigns.

## Features

- Newsletter subscription management
- Email notifications with priority-based queuing
- Multiple email provider support (Gmail, SendGrid, SMTP, Mailtrap)
- Secure authentication
- Rate limiting with exponential backoff
- Input validation and sanitization
- Comprehensive logging and monitoring
- Error handling with retry mechanisms

## Installation

1. Clone the repository
2. Install dependencies:

```bash
npm install
```

3. Create a `.env` file with the following variables (see `.env.example` for a complete template):

```
# Server Configuration
PORT=3000
NODE_ENV=development

# Firebase Configuration
FIREBASE_TYPE=service_account
FIREBASE_PROJECT_ID=your_project_id
FIREBASE_PRIVATE_KEY_ID=your_private_key_id
FIREBASE_PRIVATE_KEY=your_private_key
FIREBASE_CLIENT_EMAIL=your_client_email
FIREBASE_CLIENT_ID=your_client_id
FIREBASE_AUTH_URI=https://accounts.google.com/o/oauth2/auth
FIREBASE_TOKEN_URI=https://oauth2.googleapis.com/token
FIREBASE_AUTH_CERT_URL=https://www.googleapis.com/oauth2/v1/certs
FIREBASE_CLIENT_CERT_URL=your_client_cert_url
FIREBASE_UNIVERSE_DOMAIN=googleapis.com

# Email Configuration
# Options: gmail, mailtrap, smtp, sendgrid
EMAIL_PROVIDER=gmail
EMAIL_FROM=<EMAIL>
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your_app_password

# Mailtrap Configuration (for testing)
MAILTRAP_USER=your_mailtrap_user
MAILTRAP_PASS=your_mailtrap_password

# SendGrid Configuration (if using SendGrid)
SENDGRID_API_KEY=your_sendgrid_api_key

# Custom SMTP Configuration (if using custom SMTP)
SMTP_HOST=smtp.example.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=your_smtp_user
SMTP_PASSWORD=your_smtp_password

# Authentication
JWT_SECRET=your_jwt_secret
JWT_EXPIRES_IN=24h
API_KEY=your_api_key
LEGACY_API_KEY=your_legacy_api_key
```

4. Start the server:

```bash
npm start
```

## API Endpoints

### Newsletter Management

#### Subscribe to Newsletter

```
POST /newsletter/subscribe
```

Request body:

```json
{
  "email": "<EMAIL>",
  "name": "John Doe",
  "preferences": {
    "marketing": true,
    "notifications": true,
    "frequency": "weekly"
  }
}
```

#### Verify Subscription

```
POST /newsletter/verify
```

Request body:

```json
{
  "email": "<EMAIL>",
  "otp": "123456"
}
```

#### Unsubscribe from Newsletter

```
GET /newsletter/unsubscribe?token=your_unsubscribe_token
```

#### List Subscribers

```
GET /newsletter/subscribers
```

Headers:

```
X-API-Key: your_api_key
```

### Email Notifications

#### Send Notification Email

```
POST /notification/send-notification-email
```

Headers:

```
X-API-Key: your_api_key
```

Request body:

```json
{
  "to": "<EMAIL>",
  "subject": "Your Order Confirmation",
  "body": "Thank you for your order!",
  "recipientName": "John Doe",
  "type": "order",
  "data": {
    "orderId": "123456",
    "orderTotal": 99.99
  }
}
```

## Security Features

### Authentication

The API uses multiple authentication methods:

1. **API Key Authentication**: For service-to-service communication
2. **JWT Authentication**: For secure token-based authentication
3. **Firebase Authentication**: For user authentication

### Rate Limiting

Rate limits are applied to prevent abuse:

- General API: 100 requests per 15 minutes
- Email sending: 20 emails per hour
- Subscription attempts: 5 per 24 hours

### Input Validation

All input is validated and sanitized using Joi and XSS:

- Email validation
- String pattern validation
- XSS protection
- Input sanitization

### CORS Protection

CORS is configured to only allow requests from trusted origins:

- Production: qonai.me, salex-2025.firebaseapp.com, api.qonai.me
- Development: localhost origins

## Logging

Comprehensive logging is implemented for security monitoring:

- Authentication events
- Rate limit violations
- API requests
- Error logging

## Maintenance

### Dependencies

- express: Web framework
- firebase-admin: Firebase integration
- nodemailer: Email sending with multiple provider support
- joi: Input validation
- xss: XSS protection
- express-rate-limit: Rate limiting
- winston: Logging and monitoring
- helmet: Security headers

### Email Queue Management

The API includes a robust email queue system with:

- Priority-based processing (highest to lowest)
- Exponential backoff for failed emails
- Connection pooling for better performance
- Support for multiple email providers
- Automatic retry mechanism

### Performance Optimizations

- Connection pooling for email sending
- Rate limiting to prevent service abuse
- Batch processing for bulk emails
- Scheduled cleanup tasks for database maintenance

### Troubleshooting

1. **401 Unauthorized**: Check API key or JWT token
2. **429 Too Many Requests**: Rate limit exceeded
3. **400 Bad Request**: Invalid input data
4. **Email Delivery Issues**: Check email provider configuration and logs
5. **Queue Processing**: Monitor queue statistics with `/api/email/queue-stats` endpoint

## License

MIT
