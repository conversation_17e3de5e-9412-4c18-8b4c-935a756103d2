import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { RESTAURANT_OPTIONS } from "@/types";
import { ScrollArea } from "@/components/ui/scroll-area";
import { AlertCircle } from "lucide-react";

interface AllergenFilterProps {
  value: string[];
  onChange: (value: string[]) => void;
  counts?: Record<string, number>;
}

export function AllergenFilter({
  value,
  onChange,
  counts,
}: AllergenFilterProps) {
  const handleCheckboxChange = (
    checked: boolean | "indeterminate",
    option: string
  ) => {
    if (checked === "indeterminate") return;

    if (checked) {
      onChange([...value, option]);
    } else {
      onChange(value.filter((item) => item !== option));
    }
  };

  return (
    <div className="space-y-2">
      <div className="flex items-center gap-1 mb-2">
        <p className="text-sm font-medium">Exclude Allergens</p>
        <AlertCircle className="h-3.5 w-3.5 text-red-500" />
      </div>
      <p className="text-xs text-muted-foreground mb-2">
        Select allergens you want to exclude from results
      </p>
      <ScrollArea className="h-[200px] pr-4">
        <div className="space-y-2">
          {RESTAURANT_OPTIONS.allergens.map((option) => (
            <div key={option} className="flex items-center space-x-2">
              <Checkbox
                id={`allergen-${option}`}
                checked={value.includes(option)}
                onCheckedChange={(checked) =>
                  handleCheckboxChange(checked, option)
                }
              />
              <Label
                htmlFor={`allergen-${option}`}
                className="text-sm flex-1 cursor-pointer"
              >
                {option}
              </Label>
              {counts && counts[option] !== undefined && (
                <span className="text-xs text-muted-foreground">
                  ({counts[option]})
                </span>
              )}
            </div>
          ))}
        </div>
      </ScrollArea>
    </div>
  );
}
