/**
 * Calculates the distance between two geographic coordinates using the Haversine formula
 * @param lat1 Latitude of the first point in decimal degrees
 * @param lon1 Longitude of the first point in decimal degrees
 * @param lat2 Latitude of the second point in decimal degrees
 * @param lon2 Longitude of the second point in decimal degrees
 * @returns Distance in kilometers
 */
export const calculateDistance = (
  lat1: number,
  lon1: number,
  lat2: number,
  lon2: number
): number => {
  if (
    !Number.isFinite(lat1) ||
    !Number.isFinite(lon1) ||
    !Number.isFinite(lat2) ||
    !Number.isFinite(lon2)
  )
    return Infinity;
    
  // Earth's radius in kilometers
  const R = 6371;
  
  // Convert latitude and longitude from degrees to radians
  const dLat = ((lat2 - lat1) * Math.PI) / 180;
  const dLon = ((lon2 - lon1) * Math.PI) / 180;
  
  // Haversine formula
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos((lat1 * Math.PI) / 180) *
      Math.cos((lat2 * Math.PI) / 180) *
      Math.sin(dLon / 2) *
      Math.sin(dLon / 2);
      
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  
  // Distance in kilometers
  return R * c;
};

/**
 * Formats a distance in kilometers to a human-readable string
 * @param distance Distance in kilometers
 * @returns Formatted distance string (e.g., "2.5 km" or "500 m")
 */
export const formatDistance = (distance: number): string => {
  if (!Number.isFinite(distance)) return "Unknown distance";
  
  if (distance < 1) {
    // Convert to meters for distances less than 1 km
    const meters = Math.round(distance * 1000);
    return `${meters} m`;
  } else {
    // Round to one decimal place for distances in kilometers
    return `${distance.toFixed(1)} km`;
  }
};

/**
 * Checks if a location is within a specified radius of another location
 * @param centerLat Latitude of the center point
 * @param centerLon Longitude of the center point
 * @param pointLat Latitude of the point to check
 * @param pointLon Longitude of the point to check
 * @param radiusKm Radius in kilometers
 * @returns True if the point is within the radius, false otherwise
 */
export const isWithinRadius = (
  centerLat: number,
  centerLon: number,
  pointLat: number,
  pointLon: number,
  radiusKm: number
): boolean => {
  const distance = calculateDistance(centerLat, centerLon, pointLat, pointLon);
  return distance <= radiusKm;
};

/**
 * Gets the current user location using the browser's geolocation API
 * @returns Promise that resolves to the user's location or rejects with an error
 */
export const getCurrentLocation = (): Promise<{latitude: number; longitude: number}> => {
  return new Promise((resolve, reject) => {
    if (!navigator.geolocation) {
      reject(new Error("Geolocation is not supported by this browser."));
      return;
    }
    
    navigator.geolocation.getCurrentPosition(
      (position) => {
        resolve({
          latitude: position.coords.latitude,
          longitude: position.coords.longitude
        });
      },
      (error) => {
        reject(error);
      }
    );
  });
};
