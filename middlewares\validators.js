/**
 * Request validation middleware using Jo<PERSON>
 * Enhanced with sanitization, detailed error reporting, and security features
 */
const Joi = require("joi");
const logger = require("../utils/logger");
const xss = require("xss");

// Default Joi validation options
const defaultValidationOptions = {
  abortEarly: false, // Return all errors, not just the first one
  stripUnknown: true, // Remove unknown fields
  escapeHtml: true, // Escape HTML in string inputs
};

/**
 * Sanitize object values to prevent XSS attacks
 * @param {Object} obj - Object to sanitize
 * @returns {Object} - Sanitized object
 */
const sanitizeObject = (obj) => {
  if (!obj || typeof obj !== "object") return obj;

  const sanitized = {};

  for (const [key, value] of Object.entries(obj)) {
    if (typeof value === "string") {
      // Sanitize strings with XSS library
      sanitized[key] = xss(value);
    } else if (value && typeof value === "object" && !Array.isArray(value)) {
      // Recursively sanitize nested objects
      sanitized[key] = sanitizeObject(value);
    } else if (Array.isArray(value)) {
      // Sanitize arrays
      sanitized[key] = value.map((item) =>
        typeof item === "string"
          ? xss(item)
          : typeof item === "object"
          ? sanitizeObject(item)
          : item
      );
    } else {
      // Keep other types as is
      sanitized[key] = value;
    }
  }

  return sanitized;
};

/**
 * Validate and sanitize request body against schema
 * @param {Object} schema - Joi schema
 * @param {Object} options - Validation options
 * @returns {Function} - Express middleware
 */
const validateBody = (schema, options = {}) => {
  return (req, res, next) => {
    // Get client info for logging
    const clientIp = req.headers["x-forwarded-for"] || req.ip || "unknown";
    const path = req.path;

    try {
      // Sanitize input before validation
      const sanitizedBody = sanitizeObject(req.body);

      // Validate with Joi
      const { error, value } = schema.validate(sanitizedBody, {
        ...defaultValidationOptions,
        ...options,
      });

      if (error) {
        // Format detailed error messages
        const errorDetails = error.details.map((detail) => ({
          message: detail.message,
          path: detail.path.join("."),
          type: detail.type,
        }));

        // Log validation errors
        logger.warn(`Validation error on ${path} from ${clientIp}`, {
          ip: clientIp,
          path,
          errors: errorDetails,
        });

        return res.status(400).json({
          error: "Validation failed",
          details: errorDetails,
        });
      }

      // Replace req.body with validated and sanitized data
      req.body = value;
      next();
    } catch (err) {
      logger.error(`Validation exception on ${path} from ${clientIp}`, {
        ip: clientIp,
        path,
        error: err.message,
      });

      return res.status(500).json({ error: "Validation error" });
    }
  };
};

/**
 * Validate and sanitize request query against schema
 * @param {Object} schema - Joi schema
 * @param {Object} options - Validation options
 * @returns {Function} - Express middleware
 */
const validateQuery = (schema, options = {}) => {
  return (req, res, next) => {
    // Get client info for logging
    const clientIp = req.headers["x-forwarded-for"] || req.ip || "unknown";
    const path = req.path;

    try {
      // Sanitize input before validation
      const sanitizedQuery = sanitizeObject(req.query);

      // Validate with Joi
      const { error, value } = schema.validate(sanitizedQuery, {
        ...defaultValidationOptions,
        ...options,
      });

      if (error) {
        // Format detailed error messages
        const errorDetails = error.details.map((detail) => ({
          message: detail.message,
          path: detail.path.join("."),
          type: detail.type,
        }));

        // Log validation errors
        logger.warn(`Query validation error on ${path} from ${clientIp}`, {
          ip: clientIp,
          path,
          errors: errorDetails,
        });

        return res.status(400).json({
          error: "Query validation failed",
          details: errorDetails,
        });
      }

      // Replace req.query with validated and sanitized data
      req.query = value;
      next();
    } catch (err) {
      logger.error(`Query validation exception on ${path} from ${clientIp}`, {
        ip: clientIp,
        path,
        error: err.message,
      });

      return res.status(500).json({ error: "Query validation error" });
    }
  };
};

// Common validation patterns
const patterns = {
  // Alphanumeric with some special chars, no scripts or dangerous patterns
  safeString: /^[a-zA-Z0-9\s.,!?@#$%^&*()_+\-=\[\]{}|:;"'<>,.?/~`]+$/,
  // UUID pattern
  uuid: /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i,
  // OTP pattern (6 digits)
  otp: /^[0-9]{6}$/,
};

// Schema for newsletter subscription
const subscribeSchema = Joi.object({
  email: Joi.string()
    .email()
    .trim()
    .lowercase()
    .required()
    .max(100)
    .message("Email must be valid and not exceed 100 characters"),
  name: Joi.string()
    .trim()
    .required()
    .min(2)
    .max(100)
    .pattern(patterns.safeString)
    .message(
      "Name is required and must contain only safe characters (2-100 characters)"
    ),
  preferences: Joi.object({
    marketing: Joi.boolean().default(true),
    notifications: Joi.boolean().default(true),
    frequency: Joi.string()
      .valid("daily", "weekly", "monthly")
      .default("weekly"),
  }).allow(null),
  source: Joi.string().trim().max(50).allow("", null),
  referrer: Joi.string().trim().max(200).allow("", null),
});

// Schema for OTP verification
const verifyOtpSchema = Joi.object({
  email: Joi.string()
    .email()
    .trim()
    .lowercase()
    .required()
    .max(100)
    .message("Email must be valid and not exceed 100 characters"),
  otp: Joi.string()
    .trim()
    .required()
    .pattern(patterns.otp)
    .message("OTP must be exactly 6 digits"),
  device: Joi.string().trim().max(100).allow("", null),
});

// Schema for notification email
const notificationEmailSchema = Joi.object({
  to: Joi.string()
    .email()
    .trim()
    .lowercase()
    .required()
    .max(100)
    .message("Recipient email must be valid and not exceed 100 characters"),
  subject: Joi.string()
    .trim()
    .required()
    .min(3)
    .max(150)
    .pattern(patterns.safeString)
    .message(
      "Subject must be between 3-150 characters and contain only safe characters"
    ),
  body: Joi.string()
    .allow("", null)
    .max(50000)
    .message("Email body cannot exceed 50,000 characters"),
  recipientName: Joi.string()
    .trim()
    .allow("", null)
    .max(100)
    .pattern(patterns.safeString)
    .message(
      "Recipient name must contain only safe characters and not exceed 100 characters"
    ),
  type: Joi.string()
    .trim()
    .allow("", null)
    .valid(
      "welcome",
      "verification",
      "notification",
      "marketing",
      "order",
      "loyalty",
      "referral",
      "password_reset",
      "reservation"
    ),
  data: Joi.object().allow(null),
  useQueue: Joi.boolean().default(true),
  priority: Joi.string().valid("high", "normal", "low").default("normal"),
  trackOpens: Joi.boolean().default(true),
  trackClicks: Joi.boolean().default(true),
}).or("body", "type");

// Schema for unsubscribe token
const unsubscribeSchema = Joi.object({
  token: Joi.string()
    .trim()
    .required()
    .min(32)
    .max(256)
    .message("Unsubscribe token must be between 32-256 characters"),
  email: Joi.string()
    .email()
    .trim()
    .lowercase()
    .max(100)
    .message("Email must be valid and not exceed 100 characters"),
  all: Joi.boolean().default(false),
});

// Schema for bulk email campaigns
const bulkEmailSchema = Joi.object({
  subject: Joi.string()
    .trim()
    .required()
    .min(3)
    .max(150)
    .pattern(patterns.safeString)
    .message(
      "Subject must be between 3-150 characters and contain only safe characters"
    ),
  content: Joi.string()
    .required()
    .min(50)
    .max(100000)
    .message("Email content must be between 50-100,000 characters"),
  testEmail: Joi.string()
    .email()
    .trim()
    .lowercase()
    .allow(null, "")
    .max(100)
    .message("Test email must be valid and not exceed 100 characters"),
  segmentId: Joi.string()
    .trim()
    .allow(null, "")
    .max(100)
    .message("Segment ID cannot exceed 100 characters"),
  scheduledFor: Joi.date().iso().min("now").allow(null),
  trackingEnabled: Joi.boolean().default(true),
  sender: Joi.string().trim().max(100).allow(null, ""),
});

module.exports = {
  validateBody,
  validateQuery,
  schemas: {
    subscribeSchema,
    verifyOtpSchema,
    notificationEmailSchema,
    unsubscribeSchema,
    bulkEmailSchema,
  },
};
