/**
 * Bulk Email Routes
 *
 * Routes for sending bulk emails and managing email campaigns
 */
const express = require("express");
const router = express.Router();
const { db } = require("../config/firebase");
const emailService = require("../services/emailService");
const emailQueue = require("../services/emailQueue");
const { validateBody, schemas } = require("../middlewares/validators");
const { verifyApiKey } = require("../middlewares/authMiddleware");

/**
 * @route POST /bulk-email/campaign
 * @desc Send a bulk email campaign to subscribers
 * @access Private (requires API key)
 */
router.post(
  "/campaign",
  verifyApiKey,
  validateBody(schemas.bulkEmailSchema),
  async (req, res) => {
    try {
      const { subject, content, testEmail, segmentId } = req.body;

      // Test email
      if (testEmail) {
        await emailService.sendEmail({
          to: testEmail,
          subject,
          html: content,
          type: "campaign-test",
          useQueue: false, // Send test emails immediately
        });

        return res.status(200).json({
          success: true,
          message: "Test email sent successfully",
        });
      }

      // Create campaign record
      const campaignRef = await db.collection("emailCampaigns").add({
        subject,
        createdAt: new Date(),
        status: "queued",
        totalRecipients: 0,
        queued: 0,
        sent: 0,
        failed: 0,
      });

      // Get subscribers
      let subscribersQuery = db
        .collection("subscribers")
        .where("subscribed", "==", true);

      // Apply segmentation if provided
      if (segmentId) {
        const segmentDoc = await db.collection("segments").doc(segmentId).get();
        if (segmentDoc.exists) {
          const segment = segmentDoc.data();
          // Apply segment filters (this is a simplified example)
          if (segment.filters) {
            // Add filters to query based on segment definition
            // This would depend on your segmentation implementation
          }
        }
      }

      const snapshot = await subscribersQuery.get();

      if (snapshot.empty) {
        await campaignRef.update({ status: "completed" });
        return res.status(404).json({
          success: false,
          error: "No active subscribers found",
        });
      }

      const subscribers = snapshot.docs.map((doc) => doc.data());

      // Update campaign with recipient count
      await campaignRef.update({
        totalRecipients: subscribers.length,
        queued: subscribers.length,
      });

      // Queue emails for each subscriber
      let queuedCount = 0;

      for (const subscriber of subscribers) {
        // Personalize content
        const unsubscribeLink = `https://api.qonai.me/newsletter/unsubscribe?token=${subscriber.unsubscribeToken}`;

        const personalizedContent = content
          .replace(/{{name}}/g, subscriber.name || "there")
          .replace(/{{unsubscribeLink}}/g, unsubscribeLink);

        // Queue the email
        await emailQueue.queueEmail({
          to: subscriber.email,
          subject,
          html: personalizedContent,
          type: "campaign",
          metadata: {
            campaignId: campaignRef.id,
            subscriberId: subscriber.id || subscriber.email,
          },
        });

        queuedCount++;
      }

      // Start processing the queue if not already running
      emailQueue.processQueue();

      res.status(200).json({
        success: true,
        message: `Campaign queued successfully. ${queuedCount} emails will be sent.`,
        campaignId: campaignRef.id,
      });
    } catch (error) {
      console.error("Error sending bulk email:", error);
      res.status(500).json({
        success: false,
        error: "Failed to send bulk email campaign",
      });
    }
  }
);

/**
 * @route GET /bulk-email/campaigns
 * @desc Get all email campaigns
 * @access Private (requires API key)
 */
router.get("/campaigns", verifyApiKey, async (req, res) => {
  try {
    const snapshot = await db
      .collection("emailCampaigns")
      .orderBy("createdAt", "desc")
      .get();

    const campaigns = snapshot.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    }));

    res.status(200).json({ campaigns });
  } catch (error) {
    console.error("Error fetching campaigns:", error);
    res.status(500).json({ error: "Failed to fetch campaigns" });
  }
});

/**
 * @route GET /bulk-email/queue/stats
 * @desc Get email queue statistics
 * @access Private (requires API key)
 */
router.get("/queue/stats", verifyApiKey, async (req, res) => {
  try {
    const stats = await emailQueue.getQueueStats();
    res.status(200).json({ stats });
  } catch (error) {
    console.error("Error fetching queue stats:", error);
    res.status(500).json({ error: "Failed to fetch queue statistics" });
  }
});

module.exports = router;
