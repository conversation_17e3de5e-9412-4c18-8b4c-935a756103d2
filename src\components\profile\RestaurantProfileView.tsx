import { RestaurantDetails, WorkingHours } from "@/types";
import { Card } from "@/components/ui/card";
import { to12HourFormat } from "@/utils/timeUtils";
import { calculateDuration } from "@/utils/timeUtils";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Loading } from "../ui/loading";
import { RestaurantFollowers } from "./RestaurantFollowers";

interface RestaurantProfileViewProps {
  userData: RestaurantDetails;
  isSaving: boolean;
}

export const RestaurantProfileView = ({
  userData: restaurantData,
  isSaving,
}: RestaurantProfileViewProps) => {
  return (
    <section className="space-y-8">
      {/* Basic Information */}
      <div className="grid lg:grid-cols-3 md:grid-cols-3 gap-8">
        {/* Left Column - Restaurant Image & Basic Info */}
        <div className="space-y-3">
          {restaurantData.imageUrl && (
            <div className="relative rounded-lg overflow-hidden">
              <img
                src={restaurantData.imageUrl}
                alt={restaurantData.restaurantName}
                className="sm:w-1/2 md:w-1/4 w-full aspect-square object-cover"
                onError={(e) => {
                  e.currentTarget.src = "/placeholder-restaurant.jpg";
                }}
              />
              {isSaving && (
                <div className="absolute inset-0 bg-background/80 flex items-center justify-center">
                  <Loading type="default" />
                </div>
              )}
            </div>
          )}
          <div className="space-y-2">
            <h3 className="text-lg font-semibold">Contact Information</h3>
            <div className="space-y-1 text-sm">
              <p>
                <span className="text-muted-foreground">Email:</span>{" "}
                {restaurantData.email}
              </p>
              <p>
                <span className="text-muted-foreground">Phone:</span>{" "}
                {restaurantData.phone}
              </p>
              <p>
                <span className="text-muted-foreground">Address:</span>{" "}
                {restaurantData.address}
              </p>
            </div>
          </div>
        </div>

        {/* Middle Column - Categories & Features */}
        <div className="space-y-3">
          <div>
            <h3 className="text-lg font-semibold mb-2">Cuisines</h3>
            <div className="flex flex-wrap gap-2">
              {restaurantData.cuisines?.map((cuisine) => (
                <Badge key={cuisine} variant="secondary">
                  {cuisine}
                </Badge>
              ))}
            </div>
          </div>

          <div>
            <h3 className="text-lg font-semibold mb-2">Categories</h3>
            <div className="flex flex-wrap gap-2">
              {restaurantData.categories?.map((category) => (
                <Badge key={category} variant="outline">
                  {category}
                </Badge>
              ))}
            </div>
          </div>

          <div>
            <h3 className="text-lg font-semibold mb-2">Features</h3>
            <div className="flex flex-wrap gap-2">
              {restaurantData.features?.map((feature) => (
                <Badge key={feature} variant="secondary">
                  {feature}
                </Badge>
              ))}
            </div>
          </div>
        </div>

        {/* Right Column - Status & Quick Info */}
        <div className="space-y-3">
          <div>
            <h3 className="text-lg font-semibold mb-2">Status</h3>
            <div className="flex flex-col gap-2">
              <div className="flex items-center gap-2">
                <Badge
                  variant={restaurantData.isOpen ? "default" : "destructive"}
                >
                  {restaurantData.isOpen ? "Open" : "Closed"}
                </Badge>
                <Badge
                  variant={restaurantData.isActive ? "default" : "destructive"}
                >
                  {restaurantData.isActive ? "Active" : "Inactive"}
                </Badge>
              </div>
              <p className="text-sm text-muted-foreground">
                Status updates:{" "}
                {restaurantData.autoUpdateStatus ? "Automatic" : "Manual"}
              </p>
            </div>
          </div>

          <div>
            <h3 className="text-lg font-semibold mb-2">Quick Information</h3>
            <div className="space-y-2 text-sm">
              <div className="flex items-center gap-2">
                <span className="text-muted-foreground">Noise Level:</span>{" "}
                <Badge variant="outline" className="capitalize">
                  {restaurantData.noiseLevel || "Not specified"}
                </Badge>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-muted-foreground">Dress Code:</span>{" "}
                <Badge variant="outline" className="capitalize">
                  {restaurantData.dressCode || "Not specified"}
                </Badge>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-muted-foreground">Reservation:</span>{" "}
                <Badge variant="outline" className="capitalize">
                  {restaurantData.reservationPolicy || "Not specified"}
                </Badge>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-muted-foreground">Parking:</span>{" "}
                <Badge variant="outline">
                  {restaurantData.parkingAvailable
                    ? "Available"
                    : "Not available"}
                </Badge>
              </div>
            </div>
          </div>
        </div>
      </div>

      <Separator />

      {/* Working Hours */}
      <div>
        <h3 className="text-lg font-semibold mb-4">Working Hours</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {restaurantData.workingHours?.map((hours: WorkingHours) => (
            <Card key={hours.day} className="p-4">
              <div className="flex justify-between items-center">
                <span className="font-medium capitalize">{hours.day}</span>
                <Badge variant={hours.isOpen ? "default" : "destructive"}>
                  {hours.isOpen ? "Open" : "Closed"}
                </Badge>
              </div>
              {hours.isOpen && (
                <div className="mt-2 text-sm text-muted-foreground">
                  <p>
                    {to12HourFormat(hours.openTime)} -{" "}
                    {to12HourFormat(hours.closeTime)}
                  </p>
                  <p className="text-xs">
                    ({calculateDuration(hours.openTime, hours.closeTime)} hours)
                  </p>
                </div>
              )}
            </Card>
          ))}
        </div>
      </div>

      <Separator />

      {/* Additional Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        {/* Left Column */}
        <div className="space-y-6">
          <div>
            <h3 className="text-lg font-semibold mb-2">Atmosphere</h3>
            <div className="flex flex-wrap gap-2">
              {restaurantData.atmosphere?.map((atm) => (
                <Badge key={atm} variant="secondary">
                  {atm}
                </Badge>
              ))}
            </div>
          </div>

          <div>
            <h3 className="text-lg font-semibold mb-2">Dietary Options</h3>
            <div className="flex flex-wrap gap-2">
              {restaurantData.dietary?.map((diet) => (
                <Badge key={diet} variant="secondary">
                  {diet}
                </Badge>
              ))}
            </div>
          </div>

          <div>
            <h3 className="text-lg font-semibold mb-2">Services</h3>
            <div className="flex flex-wrap gap-2">
              {restaurantData.services?.map((service) => (
                <Badge key={service} variant="secondary">
                  {service}
                </Badge>
              ))}
            </div>
          </div>
        </div>

        {/* Right Column */}
        <div className="space-y-6">
          <div>
            <h3 className="text-lg font-semibold mb-2">Specialties</h3>
            <div className="flex flex-wrap gap-2">
              {restaurantData.specialties?.map((specialty) => (
                <Badge key={specialty} variant="secondary">
                  {specialty}
                </Badge>
              ))}
            </div>
          </div>

          <div>
            <h3 className="text-lg font-semibold mb-2">Payment Methods</h3>
            <div className="flex flex-wrap gap-2">
              {restaurantData.paymentMethods?.map((method) => (
                <Badge key={method} variant="secondary">
                  {method}
                </Badge>
              ))}
            </div>
          </div>

          <div>
            <h3 className="text-lg font-semibold mb-2">Languages</h3>
            <div className="flex flex-wrap gap-2">
              {restaurantData.languages?.map((lang) => (
                <Badge key={lang} variant="secondary">
                  {lang}
                </Badge>
              ))}
            </div>
          </div>
        </div>
      </div>

      <Separator />

      {/* Seating Information */}
      <div>
        <h3 className="text-lg font-semibold mb-4">Seating Information</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card className="p-4">
            <h4 className="font-medium mb-2">Indoor Seating</h4>
            <Badge
              variant={restaurantData.seating?.indoor ? "default" : "secondary"}
            >
              {restaurantData.seating?.indoor ? "Available" : "Not available"}
            </Badge>
          </Card>
          <Card className="p-4">
            <h4 className="font-medium mb-2">Outdoor Seating</h4>
            <Badge
              variant={
                restaurantData.seating?.outdoor ? "default" : "secondary"
              }
            >
              {restaurantData.seating?.outdoor ? "Available" : "Not available"}
            </Badge>
          </Card>
          <Card className="p-4">
            <h4 className="font-medium mb-2">Total Capacity</h4>
            <p className="text-2xl font-semibold">
              {restaurantData.seating?.totalCapacity || 0}{" "}
              <span className="text-sm text-muted-foreground">seats</span>
            </p>
          </Card>
        </div>
      </div>

      <Separator />

      {/* Followers */}
      <RestaurantFollowers restaurantId={restaurantData.uid} />
    </section>
  );
};
