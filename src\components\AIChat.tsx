import { useState, useRef, useEffect, useCallback } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import * as SelectPrimitive from "@radix-ui/react-select";
import {
  X,
  MessageSquare,
  Loader2,
  Minimize,
  Maximize,
  User,
  Bot,
  SendHorizonal,
  AlertCircle,
} from "lucide-react";
import { MenuRenderer } from "./chat/MenuRenderer";
import { RestaurantListRenderer } from "./chat/RestaurantListRenderer";
import { MarkupRenderer } from "./chat/MarkupRenderer";
import { model } from "@/config/firebase";
import { toast } from "sonner";

// Define the structure for structured data types
// Import types from the renderers
import type { Restaurant } from "./chat/RestaurantListRenderer";
import type { MenuSection } from "./chat/MenuRenderer";

interface RestaurantData {
  type: "restaurant";
  data: Restaurant[]; // Use the proper type
}

interface MenuData {
  type: "menu";
  data: MenuSection[]; // Use the proper type
}

// Union type for message content
type MessageContent = string | RestaurantData | MenuData;

interface Message {
  id: string; // Add unique ID for key prop
  type: "user" | "assistant";
  content: MessageContent;
  timestamp: Date;
}

interface AIChatProps {
  defaultLanguage?: "en" | "az";
}

// Helper to check if content is structured data
function isStructuredData(
  content: MessageContent
): content is RestaurantData | MenuData {
  return typeof content === "object" && content !== null && "type" in content;
}

export const AIChat = ({ defaultLanguage = "en" }: AIChatProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);
  const [query, setQuery] = useState("");
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const [language, setLanguage] = useState<"en" | "az">(defaultLanguage);

  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const endOfMessagesRef = useRef<HTMLDivElement>(null);

  // --- Smooth Scrolling ---
  useEffect(() => {
    endOfMessagesRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]); // Trigger scroll whenever messages change

  // --- Format History for AI Model ---
  // Formats chat history for Google Generative AI
  const formatChatHistory = (msgs: Message[]) => {
    return msgs
      .map((msg) => ({
        role: (msg.type === "user" ? "user" : "model") as "user" | "model",
        parts: [
          {
            text:
              msg.type === "user"
                ? (msg.content as string) // User content is always string
                : isStructuredData(msg.content)
                ? `[Structured ${msg.content.type} data displayed]` // Placeholder for structured data
                : (msg.content as string), // Plain text assistant response
          },
        ],
      }))
      .filter((msg) => msg.parts[0].text && msg.parts[0].text.trim() !== ""); // Ensure content is not empty
  };

  // --- Generate AI Response ---
  const generateAIResponse = useCallback(
    async (
      userQuery: string,
      chatHistory: Array<{ role: string; parts: Array<{ text: string }> }>
    ): Promise<string> => {
      try {
        // Create a comprehensive prompt for restaurant assistance
        const systemPrompt = `You are QonAI, a helpful restaurant assistant for the Qonai platform. You help users with:
- Finding restaurants based on preferences
- Providing menu information
- Making reservations
- Answering questions about cuisine, pricing, and availability
- Giving personalized recommendations

Language preference: ${language === "az" ? "Azerbaijani" : "English"}

Please provide helpful, accurate, and friendly responses about restaurants and dining.`;

        // Combine system prompt with user query
        const fullPrompt = `${systemPrompt}\n\nUser: ${userQuery}`;

        // Start a chat session with history if available
        if (chatHistory.length > 0) {
          const chat = model.startChat({
            history: chatHistory,
          });
          const result = await chat.sendMessage(userQuery);
          const response = result.response;
          return response.text();
        } else {
          // First message, use generateContent
          const result = await model.generateContent(fullPrompt);
          const response = result.response;
          return response.text();
        }
      } catch (error) {
        console.error("AI Generation Error:", error);
        throw new Error("Failed to generate AI response. Please try again.");
      }
    },
    [language]
  );

  // --- Handle Query Submission ---
  const handleSubmit = useCallback(async () => {
    const trimmedQuery = query.trim();
    if (!trimmedQuery) {
      return;
    }

    setIsLoading(true);
    setError(""); // Clear previous errors

    const userMessage: Message = {
      id: `user-${Date.now()}`,
      type: "user",
      content: trimmedQuery,
      timestamp: new Date(),
    };

    // Update messages immediately for responsiveness
    setMessages((prev) => [...prev, userMessage]);
    setQuery(""); // Clear input field

    // Prepare chat history for AI model
    const chatHistory = formatChatHistory(messages);

    try {
      // Generate AI response using Google Generative AI
      const aiResponse = await generateAIResponse(trimmedQuery, chatHistory);

      let assistantContent: MessageContent = aiResponse;

      // Try to parse response for structured data
      try {
        const parsedResponse = JSON.parse(aiResponse);
        if (
          parsedResponse &&
          typeof parsedResponse === "object" &&
          "type" in parsedResponse
        ) {
          // Handle structured data types
          if (
            parsedResponse.type === "menu_list" ||
            parsedResponse.type === "menu"
          ) {
            assistantContent = {
              type: "menu",
              data: parsedResponse.data,
            } as MenuData;
          } else if (
            parsedResponse.type === "restaurant_list" ||
            parsedResponse.type === "restaurant"
          ) {
            assistantContent = {
              type: "restaurant",
              data: parsedResponse.data,
            } as RestaurantData;
          }
        }
      } catch {
        // Response is not JSON, keep as plain text
      }

      const assistantMessage: Message = {
        id: `assistant-${Date.now()}`,
        type: "assistant",
        content: assistantContent,
        timestamp: new Date(),
      };

      setMessages((prev) => [...prev, assistantMessage]);
    } catch (err) {
      const errorMessage =
        err instanceof Error
          ? err.message
          : "An unknown error occurred. Please try again.";
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, [query, messages, generateAIResponse]); // Dependencies for useCallback

  // --- Render Individual Message ---
  const renderMessageContent = (content: MessageContent) => {
    if (isStructuredData(content)) {
      if (content.type === "menu") {
        return <MenuRenderer data={content.data} />;
      } else if (content.type === "restaurant") {
        return <RestaurantListRenderer restaurants={content.data} />;
      }
    } else if (typeof content === "string") {
      // Render plain text using MarkupRenderer
      return <MarkupRenderer content={content} />;
    }
    // Fallback for unknown content types
    return <p>Unsupported message format</p>;
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setQuery(e.target.value);
    if (error) {
      setError(""); // Clear error when user types
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSubmit();
    }
  };

  // --- Component Return ---
  return (
    <>
      {/* Chat Toggle Button */}
      <motion.div
        className="fixed bottom-6 right-6 z-[1000]" // Ensure button is high enough
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        transition={{
          duration: 0.3,
          type: "spring",
          stiffness: 260,
          damping: 20,
        }}
      >
        <Button
          aria-label="Toggle Chat"
          onClick={() => setIsOpen(!isOpen)}
          className="w-14 h-14 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 flex items-center justify-center"
          variant="default"
        >
          <MessageSquare className="w-6 h-6" />
        </Button>
      </motion.div>

      {/* Chat Window */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            key="chat-window"
            initial={{ opacity: 0, y: 30, scale: 0.9 }}
            animate={{
              opacity: 1,
              y: 0,
              scale: 1,
              height: isMinimized ? "auto" : "clamp(400px, 70vh, 650px)", // Responsive height
            }}
            exit={{
              opacity: 0,
              y: 30,
              scale: 0.9,
              transition: { duration: 0.2 },
            }}
            transition={{ duration: 0.3, ease: "easeOut" }}
            className={`fixed right-6 bottom-24 bg-card rounded-xl shadow-2xl z-[999] border flex flex-col overflow-hidden`} // Use bg-card
            style={{
              width: isMinimized ? "320px" : "clamp(360px, 30vw, 420px)",
            }} // Responsive width
          >
            {/* Header */}
            <div className="flex items-center justify-between p-3 border-b bg-muted/40 flex-shrink-0">
              <div className="flex items-center gap-2">
                <div className="w-8 h-8 rounded-full bg-primary/10 text-primary flex items-center justify-center">
                  <Bot className="w-5 h-5" />
                </div>
                <h3 className="font-semibold text-base">QonAI</h3>
              </div>
              <div className="flex gap-1">
                <Button
                  aria-label={isMinimized ? "Maximize Chat" : "Minimize Chat"}
                  variant="ghost"
                  size="icon"
                  className="w-8 h-8 rounded-full hover:bg-muted"
                  onClick={() => setIsMinimized(!isMinimized)}
                >
                  {isMinimized ? (
                    <Maximize className="w-4 h-4" />
                  ) : (
                    <Minimize className="w-4 h-4" />
                  )}
                </Button>
                <Button
                  aria-label="Close Chat"
                  variant="ghost"
                  size="icon"
                  className="w-8 h-8 rounded-full hover:bg-destructive/10 text-destructive"
                  onClick={() => setIsOpen(false)}
                >
                  <X className="w-4 h-4" />
                </Button>
              </div>
            </div>

            {!isMinimized && (
              <>
                {/* Chat Content */}
                <ScrollArea className="flex-1" ref={scrollAreaRef}>
                  <div className="p-4 space-y-4">
                    {messages.length === 0 && !isLoading ? (
                      <div className="flex flex-col items-center justify-center h-full text-center p-6 text-muted-foreground">
                        <Bot className="w-12 h-12 mb-4 text-primary/20" />
                        <h3 className="text-lg font-medium mb-2">
                          Restaurant Assistant
                        </h3>
                        <p className="text-sm max-w-xs">
                          Ask about restaurants, menus, or check availability!
                        </p>
                      </div>
                    ) : (
                      messages.map((message) => (
                        <div
                          key={message.id}
                          className={`flex items-end gap-2 ${
                            message.type === "user"
                              ? "justify-end"
                              : "justify-start"
                          }`}
                        >
                          {message.type === "assistant" && (
                            <div className="w-7 h-7 rounded-full bg-primary text-primary-foreground flex items-center justify-center flex-shrink-0 mb-1">
                              <Bot className="w-4 h-4" />
                            </div>
                          )}
                          <div
                            className={`p-3 rounded-xl shadow-sm max-w-[85%] ${
                              message.type === "user"
                                ? "bg-primary text-primary-foreground rounded-br-none"
                                : "bg-muted text-card-foreground rounded-bl-none"
                            }`}
                          >
                            {renderMessageContent(message.content)}
                          </div>
                          {message.type === "user" && (
                            <div className="w-7 h-7 rounded-full bg-secondary text-secondary-foreground flex items-center justify-center flex-shrink-0 mb-1">
                              <User className="w-4 h-4" />
                            </div>
                          )}
                        </div>
                      ))
                    )}
                    {/* Invisible div to scroll to */}
                    <div ref={endOfMessagesRef} />
                  </div>
                </ScrollArea>

                {/* Loading Indicator */}
                {isLoading && (
                  <div className="flex justify-center items-center p-3 border-t">
                    <Loader2 className="w-5 h-5 animate-spin text-primary" />
                    <span className="ml-2 text-sm text-muted-foreground">
                      Assistant is thinking...
                    </span>
                  </div>
                )}

                {/* Error Message */}
                {error &&
                  !isLoading && ( // Only show error if not loading
                    <div className="m-4 mt-0 px-3 py-2 bg-destructive/10 text-destructive text-xs rounded-lg border border-destructive/20 flex items-center gap-2 flex-shrink-0">
                      <AlertCircle className="w-4 h-4" />
                      <span>{error}</span>
                    </div>
                  )}

                {/* Input Area */}
                <div className="p-3 border-t bg-muted/30 flex-shrink-0">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-xs text-muted-foreground">
                      Language:
                    </span>
                    <Select
                      value={language}
                      onValueChange={(value) =>
                        setLanguage(value as "en" | "az")
                      }
                    >
                      <SelectTrigger className="w-auto h-7 text-xs px-2 bg-background">
                        <SelectValue placeholder="Language" />
                      </SelectTrigger>
                      <SelectPrimitive.Portal>
                        <SelectContent
                          className="z-[1001]"
                          position="popper"
                          sideOffset={5}
                        >
                          <SelectItem value="en">English</SelectItem>
                          <SelectItem value="az">Azərbaycan dili</SelectItem>
                        </SelectContent>
                      </SelectPrimitive.Portal>
                    </Select>
                  </div>
                  <div className="flex gap-2 items-end">
                    <Textarea
                      value={query}
                      onChange={handleInputChange}
                      onKeyDown={handleKeyDown}
                      placeholder="Ask anything..."
                      className="resize-none flex-1 min-h-[40px] max-h-[120px] rounded-lg text-sm bg-background focus-visible:ring-1 focus-visible:ring-ring" // Use bg-background
                      rows={1} // Start with 1 row, auto-expands
                      disabled={isLoading}
                    />
                    <Button
                      aria-label="Send Message"
                      onClick={handleSubmit}
                      disabled={isLoading || !query.trim()}
                      className="shrink-0 w-10 h-10 p-0 rounded-lg"
                      size="icon"
                    >
                      {isLoading ? (
                        <Loader2 className="w-5 h-5 animate-spin" />
                      ) : (
                        <SendHorizonal className="w-5 h-5" />
                      )}
                    </Button>
                  </div>
                </div>
              </>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};
