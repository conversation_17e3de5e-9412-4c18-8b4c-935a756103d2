import { useState, useRef, useEffect, useCallback } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import * as SelectPrimitive from "@radix-ui/react-select";
import {
  X,
  MessageSquare,
  Loader2,
  Minimize,
  Maximize,
  User,
  Bot,
  SendHorizonal,
  AlertCircle,
} from "lucide-react";
import { MenuRenderer } from "./chat/MenuRenderer";
import { RestaurantListRenderer } from "./chat/RestaurantListRenderer";
import { MarkupRenderer } from "./chat/MarkupRenderer";
import { model, firestore } from "@/config/firebase";
import { toast } from "sonner";
import { useAuth } from "@/providers/AuthProvider";
import {
  doc,
  getDoc,
  collection,
  query as firestoreQuery,
  where,
  getDocs,
  orderBy,
  limit,
} from "firebase/firestore";
import { recommendationService } from "@/services/RecommendationService";

// Define the structure for structured data types
// Import types from the renderers
import type { Restaurant } from "./chat/RestaurantListRenderer";
import type { MenuSection } from "./chat/MenuRenderer";
import type { MealPreferences, ClientDetails } from "@/types";
import type { Order } from "@/types/order";
import type { Recommendation } from "@/utils/recommendationUtils";

// Interface for restaurant data with subcollections
interface RestaurantWithSubcollections {
  id: string;
  restaurantName?: string;
  name?: string;
  subcollections?: {
    orders?: Record<string, unknown>[];
    reviews?: Record<string, unknown>[];
    menu?: Record<string, unknown>[];
  };
  [key: string]: unknown;
}

interface RestaurantData {
  type: "restaurant";
  data: Restaurant[]; // Use the proper type
}

interface MenuData {
  type: "menu";
  data: MenuSection[]; // Use the proper type
}

// Union type for message content
type MessageContent = string | RestaurantData | MenuData;

interface Message {
  id: string; // Add unique ID for key prop
  type: "user" | "assistant";
  content: MessageContent;
  timestamp: Date;
}

interface AIChatProps {
  defaultLanguage?: "en" | "az";
}

// Helper to check if content is structured data
function isStructuredData(
  content: MessageContent
): content is RestaurantData | MenuData {
  return typeof content === "object" && content !== null && "type" in content;
}

export const AIChat = ({ defaultLanguage = "en" }: AIChatProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);
  const [query, setQuery] = useState("");
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const [language, setLanguage] = useState<"en" | "az">(defaultLanguage);

  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const endOfMessagesRef = useRef<HTMLDivElement>(null);

  // Get user authentication context
  const { user, userRole } = useAuth();

  // Session-based memory for data caching
  const [sessionData, setSessionData] = useState<{
    personalizedData: Record<string, unknown>;
    restaurantsData: RestaurantWithSubcollections[];
    lastFetched: number;
  } | null>(null);

  // Clear session data on page reload
  useEffect(() => {
    const handleBeforeUnload = () => {
      setSessionData(null);
      setMessages([]);
    };
    window.addEventListener("beforeunload", handleBeforeUnload);
    return () => window.removeEventListener("beforeunload", handleBeforeUnload);
  }, []);

  // --- Smooth Scrolling ---
  useEffect(() => {
    endOfMessagesRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]); // Trigger scroll whenever messages change

  // --- Format History for AI Model ---
  // Formats chat history for Google Generative AI
  const formatChatHistory = (msgs: Message[]) => {
    return msgs
      .map((msg) => ({
        role: (msg.type === "user" ? "user" : "model") as "user" | "model",
        parts: [
          {
            text:
              msg.type === "user"
                ? (msg.content as string) // User content is always string
                : isStructuredData(msg.content)
                ? `[Structured ${msg.content.type} data displayed]` // Placeholder for structured data
                : (msg.content as string), // Plain text assistant response
          },
        ],
      }))
      .filter((msg) => msg.parts[0].text && msg.parts[0].text.trim() !== ""); // Ensure content is not empty
  };

  // --- Fetch User Personalized Data with Subcollections ---
  const fetchUserPersonalizedData = useCallback(async () => {
    if (!user || userRole !== "client") {
      return null;
    }

    // Check if we have cached data from this session
    if (sessionData?.personalizedData && sessionData.lastFetched) {
      const timeSinceLastFetch = Date.now() - sessionData.lastFetched;
      // Use cached data if fetched within the last 5 minutes
      if (timeSinceLastFetch < 5 * 60 * 1000) {
        console.log("Using cached personalized data from session");
        return sessionData.personalizedData;
      }
    }

    try {
      console.log(`Fetching comprehensive data for user UID: ${user.uid}`);

      // Fetch main client document
      const userDoc = await getDoc(doc(firestore, "clients", user.uid));
      let userPreferences: MealPreferences | null = null;
      let favoriteRestaurants: string[] = [];
      let clientData: ClientDetails | null = null;

      if (userDoc.exists()) {
        clientData = userDoc.data() as ClientDetails;
        userPreferences = clientData.mealPreferences || null;
        favoriteRestaurants = clientData.favoriteRestaurants || [];
      }

      // Fetch client subcollections
      // 1. Orders subcollection
      const clientOrdersQuery = firestoreQuery(
        collection(firestore, "clients", user.uid, "orders"),
        orderBy("orderDate", "desc"),
        limit(20)
      );
      const clientOrdersSnapshot = await getDocs(clientOrdersQuery);
      const clientOrders = clientOrdersSnapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
      }));

      // 2. Following subcollection (restaurants user follows)
      const followingQuery = firestoreQuery(
        collection(firestore, "clients", user.uid, "following"),
        orderBy("createdAt", "desc")
      );
      const followingSnapshot = await getDocs(followingQuery);
      const followingRestaurants = followingSnapshot.docs.map((doc) => ({
        restaurantId: doc.id,
        ...doc.data(),
      }));

      // 3. Loyalty data (fetch from main loyalty document instead of subcollection)
      let loyaltyData = null;
      try {
        const loyaltyDoc = await getDoc(
          doc(firestore, "clients", user.uid, "loyalty")
        );
        if (loyaltyDoc.exists()) {
          loyaltyData = loyaltyDoc.data();
        }
      } catch (error) {
        console.log("No loyalty data found for user:", error);
      }

      // Fetch global orders for context (from main orders collection)
      const globalOrdersQuery = firestoreQuery(
        collection(firestore, "orders"),
        where("userId", "==", user.uid),
        orderBy("createdAt", "desc"),
        limit(30)
      );
      const globalOrdersSnapshot = await getDocs(globalOrdersQuery);
      const globalOrders = globalOrdersSnapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
      })) as Order[];

      // Get personalized recommendations
      const recommendations =
        await recommendationService.getPersonalizedRecommendations(
          user.uid,
          10
        );

      const result = {
        userUid: user.uid,
        clientData,
        preferences: userPreferences,
        favoriteRestaurants,
        clientOrders,
        followingRestaurants,
        loyaltyData,
        globalOrders,
        recommendations,
      };

      // Cache the data in session
      setSessionData((prev) => ({
        personalizedData: result,
        restaurantsData: prev?.restaurantsData || [],
        lastFetched: Date.now(),
      }));

      return result;
    } catch (error) {
      console.error("Error fetching personalized data:", error);
      return null;
    }
  }, [user, userRole, sessionData?.personalizedData, sessionData?.lastFetched]);

  // --- Fetch Restaurants with Subcollections ---
  const fetchRestaurantsWithSubcollections = useCallback(async () => {
    // Check if we have cached restaurant data from this session
    if (sessionData?.restaurantsData && sessionData.lastFetched) {
      const timeSinceLastFetch = Date.now() - sessionData.lastFetched;
      // Use cached data if fetched within the last 5 minutes
      if (timeSinceLastFetch < 5 * 60 * 1000) {
        console.log("Using cached restaurant data from session");
        return sessionData.restaurantsData;
      }
    }

    try {
      console.log("Fetching restaurants with subcollections...");

      // Fetch active restaurants
      const restaurantsQuery = firestoreQuery(
        collection(firestore, "restaurants"),
        where("isActive", "==", true),
        limit(20)
      );
      const restaurantsSnapshot = await getDocs(restaurantsQuery);

      const restaurantsWithSubcollections: RestaurantWithSubcollections[] =
        await Promise.all(
          restaurantsSnapshot.docs.map(async (restaurantDoc) => {
            const restaurantData = {
              id: restaurantDoc.id,
              ...restaurantDoc.data(),
            };

            // Fetch restaurant subcollections
            // 1. Orders subcollection
            const restaurantOrdersQuery = firestoreQuery(
              collection(firestore, "restaurants", restaurantDoc.id, "orders"),
              orderBy("orderDate", "desc"),
              limit(10)
            );
            const restaurantOrdersSnapshot = await getDocs(
              restaurantOrdersQuery
            );
            const restaurantOrders = restaurantOrdersSnapshot.docs.map(
              (doc) => ({
                id: doc.id,
                ...doc.data(),
              })
            );

            // 2. Reviews subcollection (if exists)
            const reviewsQuery = firestoreQuery(
              collection(firestore, "restaurants", restaurantDoc.id, "reviews"),
              orderBy("createdAt", "desc"),
              limit(5)
            );
            const reviewsSnapshot = await getDocs(reviewsQuery);
            const reviews = reviewsSnapshot.docs.map((doc) => ({
              id: doc.id,
              ...doc.data(),
            }));

            // 3. Menu subcollection (if exists)
            const menuQuery = firestoreQuery(
              collection(firestore, "restaurants", restaurantDoc.id, "menu"),
              limit(20)
            );
            const menuSnapshot = await getDocs(menuQuery);
            const menuItems = menuSnapshot.docs.map((doc) => ({
              id: doc.id,
              ...doc.data(),
            }));

            return {
              ...restaurantData,
              subcollections: {
                orders: restaurantOrders,
                reviews: reviews,
                menu: menuItems,
              },
            };
          })
        );

      // Cache the restaurant data in session
      setSessionData((prev) => ({
        personalizedData: prev?.personalizedData || {},
        restaurantsData: restaurantsWithSubcollections,
        lastFetched: Date.now(),
      }));

      return restaurantsWithSubcollections;
    } catch (error) {
      console.error("Error fetching restaurants with subcollections:", error);
      return [];
    }
  }, [sessionData?.restaurantsData, sessionData?.lastFetched]);

  // --- Generate AI Response ---
  const generateAIResponse = useCallback(
    async (
      userQuery: string,
      chatHistory: Array<{
        role: "user" | "model";
        parts: Array<{ text: string }>;
      }>
    ): Promise<string> => {
      try {
        // Fetch personalized data for enhanced responses
        const personalizedData = await fetchUserPersonalizedData();

        // Fetch restaurants with subcollections for comprehensive context
        const restaurantsData = await fetchRestaurantsWithSubcollections();

        // Create comprehensive personalized context
        let personalizedContext = "";
        if (personalizedData) {
          const {
            userUid,
            clientData,
            preferences,
            favoriteRestaurants,
            clientOrders,
            followingRestaurants,
            loyaltyData,
            globalOrders,
            recommendations,
          } = personalizedData as {
            userUid: string;
            clientData: ClientDetails | null;
            preferences: MealPreferences | null;
            favoriteRestaurants: string[];
            clientOrders: Order[];
            followingRestaurants: Record<string, unknown>[];
            loyaltyData: Record<string, unknown> | null;
            globalOrders: Order[];
            recommendations: Recommendation[];
          };

          personalizedContext = `\n\nComprehensive User Context:`;
          personalizedContext += `\nUser UID: ${userUid}`;

          // Client profile information
          if (clientData) {
            personalizedContext += `\nUser Profile: ${clientData.firstName} ${clientData.lastName}`;
            personalizedContext += `\nUsername: ${clientData.username}`;
            personalizedContext += `\nPhone: ${clientData.phone}`;
            personalizedContext += `\nEmail: ${clientData.email}`;
          }

          // Dietary preferences and restrictions
          if (preferences) {
            personalizedContext += `\nDietary Preferences: ${
              preferences.preferredCuisines?.join(", ") || "None specified"
            }`;
            personalizedContext += `\nDietary Restrictions: ${
              preferences.dietaryRestrictions?.join(", ") || "None"
            }`;
            personalizedContext += `\nAllergies: ${
              preferences.allergies?.join(", ") || "None"
            }`;
            personalizedContext += `\nPreferred Price Range: ${
              preferences.preferredPriceRange || "Not specified"
            }`;
            personalizedContext += `\nPreferred Atmosphere: ${
              preferences.preferredAtmosphere?.join(", ") || "Any"
            }`;
          }

          // Favorite restaurants
          if (favoriteRestaurants.length > 0) {
            personalizedContext += `\nFavorite Restaurants: ${favoriteRestaurants.length} saved`;
          }

          // Following restaurants
          if (followingRestaurants.length > 0) {
            personalizedContext += `\nFollowing Restaurants: ${followingRestaurants.length} restaurants`;
          }

          // Loyalty data
          if (loyaltyData) {
            personalizedContext += `\nLoyalty Program: Active`;
            const loyaltyRecord = loyaltyData as Record<string, unknown>;
            if (loyaltyRecord.points) {
              personalizedContext += ` (${loyaltyRecord.points} points)`;
            }
          }

          // Client orders from subcollection
          if (clientOrders.length > 0) {
            personalizedContext += `\nClient Orders (Subcollection): ${clientOrders.length} orders`;
          }

          // Global orders
          if (globalOrders.length > 0) {
            personalizedContext += `\nGlobal Orders: ${globalOrders.length} recent orders`;
            // Add details about recent orders
            const recentRestaurants = [
              ...new Set(
                globalOrders
                  .slice(0, 5)
                  .map((order) => order.restaurantId || "Unknown")
              ),
            ];
            personalizedContext += `\nRecently ordered from: ${recentRestaurants.join(
              ", "
            )}`;
          }

          // Loyalty data already handled above

          // Recommendations
          if (recommendations.length > 0) {
            personalizedContext += `\nPersonalized Recommendations Available: ${recommendations.length} restaurants recommended based on user history`;
          }
        }

        // Add restaurant context with subcollections
        let restaurantContext = "";
        if (restaurantsData.length > 0) {
          restaurantContext = `\n\nRestaurant Database Context:`;
          restaurantContext += `\nTotal Active Restaurants: ${restaurantsData.length}`;

          // Add summary of restaurant data
          const totalOrders = restaurantsData.reduce(
            (sum, restaurant) =>
              sum + (restaurant.subcollections?.orders?.length || 0),
            0
          );
          const totalReviews = restaurantsData.reduce(
            (sum, restaurant) =>
              sum + (restaurant.subcollections?.reviews?.length || 0),
            0
          );
          const totalMenuItems = restaurantsData.reduce(
            (sum, restaurant) =>
              sum + (restaurant.subcollections?.menu?.length || 0),
            0
          );

          restaurantContext += `\nTotal Restaurant Orders: ${totalOrders}`;
          restaurantContext += `\nTotal Reviews: ${totalReviews}`;
          restaurantContext += `\nTotal Menu Items: ${totalMenuItems}`;

          // Add top restaurants by order count
          const topRestaurants = restaurantsData
            .sort(
              (a, b) =>
                (b.subcollections?.orders?.length || 0) -
                (a.subcollections?.orders?.length || 0)
            )
            .slice(0, 5)
            .map(
              (r: RestaurantWithSubcollections) =>
                `${r.restaurantName || r.name || "Unknown"} (${
                  r.subcollections?.orders?.length || 0
                } orders)`
            )
            .join(", ");

          if (topRestaurants) {
            restaurantContext += `\nTop Restaurants by Orders: ${topRestaurants}`;
          }
        }

        // Create a comprehensive prompt for restaurant assistance
        const systemPrompt = `You are QonAI, a helpful restaurant assistant for the Qonai platform. You help users with:
- Finding restaurants based on preferences
- Providing menu information
- Making reservations
- Answering questions about cuisine, pricing, and availability
- Giving personalized recommendations

Language preference: ${language === "az" ? "Azerbaijani" : "English"}

${personalizedContext}${restaurantContext}

Please provide helpful, accurate, and friendly responses about restaurants and dining. Use the personalized context and restaurant database to give more relevant recommendations when appropriate.`;

        // Combine system prompt with user query
        const fullPrompt = `${systemPrompt}\n\nUser: ${userQuery}`;

        // Start a chat session with history if available
        if (chatHistory.length > 0) {
          const chat = model.startChat({
            history: chatHistory,
          });
          const result = await chat.sendMessage(userQuery);
          const response = result.response;
          return response.text();
        } else {
          // First message, use generateContent
          const result = await model.generateContent(fullPrompt);
          const response = result.response;
          return response.text();
        }
      } catch (error) {
        console.error("AI Generation Error:", error);
        throw new Error("Failed to generate AI response. Please try again.");
      }
    },
    [language, fetchUserPersonalizedData, fetchRestaurantsWithSubcollections]
  );

  // --- Generate Structured Restaurant Recommendations ---
  const generateRestaurantRecommendations =
    useCallback(async (): Promise<RestaurantData | null> => {
      if (!user || userRole !== "client") {
        return null;
      }

      try {
        const personalizedData = await fetchUserPersonalizedData();
        const typedData = personalizedData as {
          recommendations: Recommendation[];
        };

        if (!personalizedData || typedData.recommendations.length === 0) {
          return null;
        }

        // Convert recommendations to restaurant format
        const restaurants: Restaurant[] = typedData.recommendations
          .filter((rec: Recommendation) => rec.type === "restaurant")
          .map((rec: Recommendation) => ({
            ...(rec.details as Restaurant),
            matchScore: rec.score,
          }));

        if (restaurants.length === 0) {
          return null;
        }

        return {
          type: "restaurant",
          data: restaurants,
        };
      } catch (error) {
        console.error("Error generating restaurant recommendations:", error);
        return null;
      }
    }, [user, userRole, fetchUserPersonalizedData]);

  // --- Handle Query Submission ---
  const handleSubmit = useCallback(async () => {
    const trimmedQuery = query.trim();
    if (!trimmedQuery) {
      return;
    }

    setIsLoading(true);
    setError(""); // Clear previous errors

    const userMessage: Message = {
      id: `user-${Date.now()}`,
      type: "user",
      content: trimmedQuery,
      timestamp: new Date(),
    };

    // Update messages immediately for responsiveness
    setMessages((prev) => [...prev, userMessage]);
    setQuery(""); // Clear input field

    // Prepare chat history for AI model
    const chatHistory = formatChatHistory(messages);

    try {
      // Generate AI response using Google Generative AI
      const aiResponse = await generateAIResponse(trimmedQuery, chatHistory);

      let assistantContent: MessageContent = aiResponse;

      // Try to parse response for structured data
      try {
        const parsedResponse = JSON.parse(aiResponse);
        if (
          parsedResponse &&
          typeof parsedResponse === "object" &&
          "type" in parsedResponse
        ) {
          // Handle structured data types
          if (
            parsedResponse.type === "menu_list" ||
            parsedResponse.type === "menu"
          ) {
            assistantContent = {
              type: "menu",
              data: parsedResponse.data,
            } as MenuData;
          } else if (
            parsedResponse.type === "restaurant_list" ||
            parsedResponse.type === "restaurant"
          ) {
            assistantContent = {
              type: "restaurant",
              data: parsedResponse.data,
            } as RestaurantData;
          }
        }
      } catch {
        // Response is not JSON, keep as plain text
      }

      // Check if the query is asking for recommendations and provide structured data
      const isRecommendationQuery =
        /recommend|suggest|find.*restaurant|best.*restaurant|where.*eat/i.test(
          trimmedQuery
        );

      if (
        isRecommendationQuery &&
        typeof assistantContent === "string" &&
        user &&
        userRole === "client"
      ) {
        const recommendations = await generateRestaurantRecommendations();
        if (recommendations) {
          // Add both text response and structured recommendations
          const assistantTextMessage: Message = {
            id: `assistant-text-${Date.now()}`,
            type: "assistant",
            content: assistantContent,
            timestamp: new Date(),
          };

          const assistantRecommendationsMessage: Message = {
            id: `assistant-recs-${Date.now()}`,
            type: "assistant",
            content: recommendations,
            timestamp: new Date(),
          };

          setMessages((prev) => [
            ...prev,
            assistantTextMessage,
            assistantRecommendationsMessage,
          ]);
          return; // Early return to avoid duplicate message
        }
      }

      const assistantMessage: Message = {
        id: `assistant-${Date.now()}`,
        type: "assistant",
        content: assistantContent,
        timestamp: new Date(),
      };

      setMessages((prev) => [...prev, assistantMessage]);
    } catch (err) {
      const errorMessage =
        err instanceof Error
          ? err.message
          : "An unknown error occurred. Please try again.";
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, [
    query,
    messages,
    generateAIResponse,
    generateRestaurantRecommendations,
    user,
    userRole,
  ]); // Dependencies for useCallback

  // --- Render Individual Message ---
  const renderMessageContent = (content: MessageContent) => {
    if (isStructuredData(content)) {
      if (content.type === "menu") {
        return <MenuRenderer data={content.data} />;
      } else if (content.type === "restaurant") {
        return <RestaurantListRenderer restaurants={content.data} />;
      }
    } else if (typeof content === "string") {
      // Render plain text using MarkupRenderer
      return <MarkupRenderer content={content} />;
    }
    // Fallback for unknown content types
    return <p>Unsupported message format</p>;
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setQuery(e.target.value);
    if (error) {
      setError(""); // Clear error when user types
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSubmit();
    }
  };

  // --- Component Return ---
  return (
    <>
      {/* Chat Toggle Button */}
      <motion.div
        className="fixed bottom-6 right-6 z-[1000]" // Ensure button is high enough
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        transition={{
          duration: 0.3,
          type: "spring",
          stiffness: 260,
          damping: 20,
        }}
      >
        <Button
          aria-label="Toggle Chat"
          onClick={() => setIsOpen(!isOpen)}
          className="w-14 h-14 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 flex items-center justify-center"
          variant="default"
        >
          <MessageSquare className="w-6 h-6" />
        </Button>
      </motion.div>

      {/* Chat Window */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            key="chat-window"
            initial={{ opacity: 0, y: 30, scale: 0.9 }}
            animate={{
              opacity: 1,
              y: 0,
              scale: 1,
              height: isMinimized ? "auto" : "clamp(400px, 70vh, 650px)", // Responsive height
            }}
            exit={{
              opacity: 0,
              y: 30,
              scale: 0.9,
              transition: { duration: 0.2 },
            }}
            transition={{ duration: 0.3, ease: "easeOut" }}
            className={`fixed right-6 bottom-24 bg-card rounded-xl shadow-2xl z-[999] border flex flex-col overflow-hidden`} // Use bg-card
            style={{
              width: isMinimized ? "320px" : "clamp(360px, 30vw, 420px)",
            }} // Responsive width
          >
            {/* Header */}
            <div className="flex items-center justify-between p-3 border-b bg-muted/40 flex-shrink-0">
              <div className="flex items-center gap-2">
                <div className="w-8 h-8 rounded-full bg-primary/10 text-primary flex items-center justify-center">
                  <Bot className="w-5 h-5" />
                </div>
                <h3 className="font-semibold text-base">QonAI</h3>
              </div>
              <div className="flex gap-1">
                <Button
                  aria-label={isMinimized ? "Maximize Chat" : "Minimize Chat"}
                  variant="ghost"
                  size="icon"
                  className="w-8 h-8 rounded-full hover:bg-muted"
                  onClick={() => setIsMinimized(!isMinimized)}
                >
                  {isMinimized ? (
                    <Maximize className="w-4 h-4" />
                  ) : (
                    <Minimize className="w-4 h-4" />
                  )}
                </Button>
                <Button
                  aria-label="Close Chat"
                  variant="ghost"
                  size="icon"
                  className="w-8 h-8 rounded-full hover:bg-destructive/10 text-destructive"
                  onClick={() => setIsOpen(false)}
                >
                  <X className="w-4 h-4" />
                </Button>
              </div>
            </div>

            {!isMinimized && (
              <>
                {/* Chat Content */}
                <ScrollArea className="flex-1" ref={scrollAreaRef}>
                  <div className="p-4 space-y-4">
                    {messages.length === 0 && !isLoading ? (
                      <div className="flex flex-col items-center justify-center h-full text-center p-6 text-muted-foreground">
                        <Bot className="w-12 h-12 mb-4 text-primary/20" />
                        <h3 className="text-lg font-medium mb-2">
                          Restaurant Assistant
                        </h3>
                        <p className="text-sm max-w-xs">
                          Ask about restaurants, menus, or check availability!
                        </p>
                      </div>
                    ) : (
                      messages.map((message) => (
                        <div
                          key={message.id}
                          className={`flex items-end gap-2 ${
                            message.type === "user"
                              ? "justify-end"
                              : "justify-start"
                          }`}
                        >
                          {message.type === "assistant" && (
                            <div className="w-7 h-7 rounded-full bg-primary text-primary-foreground flex items-center justify-center flex-shrink-0 mb-1">
                              <Bot className="w-4 h-4" />
                            </div>
                          )}
                          <div
                            className={`p-3 rounded-xl shadow-sm max-w-[85%] ${
                              message.type === "user"
                                ? "bg-primary text-primary-foreground rounded-br-none"
                                : "bg-muted text-card-foreground rounded-bl-none"
                            }`}
                          >
                            {renderMessageContent(message.content)}
                          </div>
                          {message.type === "user" && (
                            <div className="w-7 h-7 rounded-full bg-secondary text-secondary-foreground flex items-center justify-center flex-shrink-0 mb-1">
                              <User className="w-4 h-4" />
                            </div>
                          )}
                        </div>
                      ))
                    )}
                    {/* Invisible div to scroll to */}
                    <div ref={endOfMessagesRef} />
                  </div>
                </ScrollArea>

                {/* Loading Indicator */}
                {isLoading && (
                  <div className="flex justify-center items-center p-3 border-t">
                    <Loader2 className="w-5 h-5 animate-spin text-primary" />
                    <span className="ml-2 text-sm text-muted-foreground">
                      Assistant is thinking...
                    </span>
                  </div>
                )}

                {/* Error Message */}
                {error &&
                  !isLoading && ( // Only show error if not loading
                    <div className="m-4 mt-0 px-3 py-2 bg-destructive/10 text-destructive text-xs rounded-lg border border-destructive/20 flex items-center gap-2 flex-shrink-0">
                      <AlertCircle className="w-4 h-4" />
                      <span>{error}</span>
                    </div>
                  )}

                {/* Input Area */}
                <div className="p-3 border-t bg-muted/30 flex-shrink-0">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-xs text-muted-foreground">
                      Language:
                    </span>
                    <Select
                      value={language}
                      onValueChange={(value) =>
                        setLanguage(value as "en" | "az")
                      }
                    >
                      <SelectTrigger className="w-auto h-7 text-xs px-2 bg-background">
                        <SelectValue placeholder="Language" />
                      </SelectTrigger>
                      <SelectPrimitive.Portal>
                        <SelectContent
                          className="z-[1001]"
                          position="popper"
                          sideOffset={5}
                        >
                          <SelectItem value="en">English</SelectItem>
                          <SelectItem value="az">Azərbaycan dili</SelectItem>
                        </SelectContent>
                      </SelectPrimitive.Portal>
                    </Select>
                  </div>
                  <div className="flex gap-2 items-end">
                    <Textarea
                      value={query}
                      onChange={handleInputChange}
                      onKeyDown={handleKeyDown}
                      placeholder="Ask anything..."
                      className="resize-none flex-1 min-h-[40px] max-h-[120px] rounded-lg text-sm bg-background focus-visible:ring-1 focus-visible:ring-ring" // Use bg-background
                      rows={1} // Start with 1 row, auto-expands
                      disabled={isLoading}
                    />
                    <Button
                      aria-label="Send Message"
                      onClick={handleSubmit}
                      disabled={isLoading || !query.trim()}
                      className="shrink-0 w-10 h-10 p-0 rounded-lg"
                      size="icon"
                    >
                      {isLoading ? (
                        <Loader2 className="w-5 h-5 animate-spin" />
                      ) : (
                        <SendHorizonal className="w-5 h-5" />
                      )}
                    </Button>
                  </div>
                </div>
              </>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};
