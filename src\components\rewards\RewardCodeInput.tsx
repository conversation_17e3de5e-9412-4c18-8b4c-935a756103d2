import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Loader2, Tag, X, Check } from "lucide-react";
import { rewardsService } from "@/services/RewardsService";
import { RewardRedemption } from "@/types/rewards";
import { toast } from "sonner";

interface RewardCodeInputProps {
  userId: string;
  orderTotal: number;
  cartItems?: Array<{
    item: { itemId: string; name: string; price: number };
    quantity: number;
  }>;
  onRewardApplied: (
    discountAmount: number,
    redemption: RewardRedemption,
    affectedItemId?: string
  ) => void;
  onRewardRemoved: () => void;
  appliedReward?: {
    discountAmount: number;
    redemption: RewardRedemption;
    affectedItemId?: string;
  } | null;
}

export const RewardCodeInput: React.FC<RewardCodeInputProps> = ({
  userId,
  orderTotal,
  cartItems,
  onRewardApplied,
  onRewardRemoved,
  appliedReward,
}) => {
  const [redemptionCode, setRedemptionCode] = useState("");
  const [isValidating, setIsValidating] = useState(false);

  const handleApplyReward = async () => {
    if (!redemptionCode.trim()) {
      toast.error("Please enter a redemption code");
      return;
    }

    setIsValidating(true);

    try {
      const validation = await rewardsService.validateRedemptionCode(
        userId,
        redemptionCode.trim().toUpperCase(),
        orderTotal,
        cartItems
      );

      if (
        validation.isValid &&
        validation.discountAmount !== undefined &&
        validation.redemption
      ) {
        onRewardApplied(
          validation.discountAmount,
          validation.redemption,
          validation.affectedItemId
        );
        setRedemptionCode("");
        if (validation.discountAmount > 0) {
          toast.success(
            `Reward applied! You saved ${validation.discountAmount.toFixed(
              2
            )} AZN`
          );
        } else {
          toast.success("Reward applied successfully!");
        }
      } else {
        let errorMessage = validation.errorMessage || "Invalid redemption code";

        // Provide more specific error messages
        if (errorMessage.includes("minimum")) {
          errorMessage =
            "Order total is below the minimum required for this reward";
        } else if (errorMessage.includes("expired")) {
          errorMessage = "This reward has expired";
        } else if (errorMessage.includes("used")) {
          errorMessage = "This reward has already been used";
        }

        toast.error(errorMessage);
      }
    } catch (error) {
      console.error("Error applying reward:", error);
      toast.error("Failed to apply reward. Please try again.");
    } finally {
      setIsValidating(false);
    }
  };

  const handleRemoveReward = () => {
    onRewardRemoved();
    toast.success("Reward removed");
  };

  const formatRewardType = (reward: {
    type: string;
    discountValue?: number;
    discountType?: string;
  }) => {
    if (reward.type === "discount") {
      if (reward.discountType === "percentage") {
        return `${reward.discountValue}% Discount`;
      } else {
        return `${reward.discountValue} AZN Discount`;
      }
    } else if (reward.type === "freeDelivery") {
      return "Free Delivery";
    } else if (reward.type === "freeItem") {
      return "Free Item";
    } else {
      return "Special Reward";
    }
  };

  if (appliedReward) {
    return (
      <Card className="border-green-200 bg-green-50">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="flex items-center justify-center w-8 h-8 bg-green-100 rounded-full">
                <Check className="h-4 w-4 text-green-600" />
              </div>
              <div>
                <div className="flex items-center space-x-2">
                  <span className="font-medium text-green-800">
                    {appliedReward.redemption.reward?.name}
                  </span>
                  <Badge variant="secondary" className="text-xs">
                    {appliedReward.redemption.reward &&
                      formatRewardType(appliedReward.redemption.reward)}
                  </Badge>
                </div>
                <p className="text-sm text-green-600">
                  Code: {appliedReward.redemption.code} • Saved:{" "}
                  {appliedReward.discountAmount.toFixed(2)} AZN
                  {appliedReward.affectedItemId && (
                    <span className="ml-2 text-xs bg-green-200 px-2 py-1 rounded">
                      Applied to 1 item
                    </span>
                  )}
                </p>
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleRemoveReward}
              className="text-green-700 hover:text-green-800 hover:bg-green-100"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardContent className="p-4">
        <div className="space-y-3">
          <div className="flex items-center space-x-2">
            <Tag className="h-4 w-4 text-muted-foreground" />
            <Label htmlFor="redemption-code" className="text-sm font-medium">
              Have a reward code?
            </Label>
          </div>
          <div className="flex space-x-2">
            <Input
              id="redemption-code"
              placeholder="Enter redemption code (e.g., ABCD-1234-EFGH)"
              value={redemptionCode}
              onChange={(e) => setRedemptionCode(e.target.value.toUpperCase())}
              className="flex-1"
              maxLength={14}
            />
            <Button
              onClick={handleApplyReward}
              disabled={isValidating || !redemptionCode.trim()}
              size="sm"
            >
              {isValidating ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                "Apply"
              )}
            </Button>
          </div>
          <p className="text-xs text-muted-foreground">
            Enter your reward code to apply discounts to your order
          </p>
        </div>
      </CardContent>
    </Card>
  );
};
