import React, { useState, useEffect } from "react";
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { LoyaltyStatus, LoyaltyTier } from "@/types/loyalty";
import { loyaltyService } from "@/services/LoyaltyService";
import { useAuth } from "@/providers/AuthProvider";
import { Award, Star, TrendingUp, Users } from "lucide-react";

// Import tier configuration from LoyaltyService to ensure consistency
import { LOYALTY_TIERS } from "@/services/LoyaltyService";

// Get tier color based on tier ID
const getTierColor = (tierId: string): string => {
  switch (tierId) {
    case "bronze":
      return "bg-amber-700";
    case "silver":
      return "bg-slate-400";
    case "gold":
      return "bg-yellow-500";
    case "platinum":
      return "bg-purple-600";
    default:
      return "bg-gray-500";
  }
};

interface LoyaltyStatusCardProps {
  onJoin?: () => void;
}

export const LoyaltyStatusCard: React.FC<LoyaltyStatusCardProps> = ({
  onJoin,
}) => {
  const { user } = useAuth();
  const [loyaltyStatus, setLoyaltyStatus] = useState<LoyaltyStatus | null>(
    null
  );
  const [loading, setLoading] = useState(true);
  const [currentTier, setCurrentTier] = useState<LoyaltyTier | null>(null);
  const [nextTier, setNextTier] = useState<LoyaltyTier | null>(null);
  const [progress, setProgress] = useState(0);

  useEffect(() => {
    const fetchLoyaltyStatus = async () => {
      if (!user) return;

      setLoading(true);
      try {
        const status = await loyaltyService.getLoyaltyStatus(user.uid);
        setLoyaltyStatus(status);

        if (status) {
          // Find current tier
          const current = LOYALTY_TIERS.find((t) => t.id === status.tier);
          setCurrentTier(current || null);

          // Find next tier
          const currentIndex = LOYALTY_TIERS.findIndex(
            (t) => t.id === status.tier
          );
          if (currentIndex < LOYALTY_TIERS.length - 1) {
            setNextTier(LOYALTY_TIERS[currentIndex + 1]);

            // Calculate progress to next tier
            const currentThreshold = current?.threshold || 0;
            const nextThreshold = LOYALTY_TIERS[currentIndex + 1].threshold;
            const pointsToNextTier = nextThreshold - currentThreshold;
            const pointsEarned = status.lifetimePoints - currentThreshold;
            const progressPercentage = Math.min(
              Math.floor((pointsEarned / pointsToNextTier) * 100),
              100
            );
            setProgress(progressPercentage);
          } else {
            setNextTier(null);
            setProgress(100); // Max tier reached
          }
        }
      } catch (error) {
        console.error("Error fetching loyalty status:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchLoyaltyStatus();
  }, [user]);

  const handleJoinLoyalty = async () => {
    if (!user) return;

    try {
      const success = await loyaltyService.initializeUser(
        user.uid,
        user.email || ""
      );
      if (success) {
        // Refresh loyalty status
        const status = await loyaltyService.getLoyaltyStatus(user.uid);
        setLoyaltyStatus(status);

        if (onJoin) {
          onJoin();
        }
      }
    } catch (error) {
      console.error("Error joining loyalty program:", error);
    }
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>
            <Skeleton className="h-8 w-48" />
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Skeleton className="h-20 w-full" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-3/4" />
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!loyaltyStatus) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Award className="mr-2 h-5 w-5 text-primary" />
            Loyalty Program
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <p className="text-sm text-muted-foreground">
              Join our loyalty program to earn points with every order, review,
              and referral. Redeem points for discounts, free items, and
              exclusive perks!
            </p>
            <Button onClick={handleJoinLoyalty} className="w-full">
              Join Now
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center">
            <Award className="mr-2 h-5 w-5 text-primary" />
            Loyalty Status
          </div>
          <Badge className={`${getTierColor(loyaltyStatus.tier)} text-white`}>
            {currentTier?.name || loyaltyStatus.tier}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <div>
              <p className="text-sm text-muted-foreground">Available Points</p>
              <p className="text-2xl font-bold">{loyaltyStatus.totalPoints}</p>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Lifetime Points</p>
              <p className="text-2xl font-bold">
                {loyaltyStatus.lifetimePoints}
              </p>
            </div>
          </div>

          {nextTier && (
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Progress to {nextTier.name}</span>
                <span>
                  {loyaltyStatus.lifetimePoints} / {nextTier.threshold}
                </span>
              </div>
              <Progress value={progress} className="h-2" />
              <p className="text-xs text-muted-foreground">
                {loyaltyStatus.nextTierPoints} more points needed for{" "}
                {nextTier.name} tier
              </p>
            </div>
          )}

          <div className="grid grid-cols-2 gap-2 mt-4">
            <div className="flex flex-col items-center justify-center p-3 bg-muted rounded-md">
              <TrendingUp className="h-5 w-5 text-primary mb-1" />
              <p className="text-xs font-medium">
                Earn {currentTier?.multiplier}x Points
              </p>
            </div>
            <div className="flex flex-col items-center justify-center p-3 bg-muted rounded-md">
              <Users className="h-5 w-5 text-primary mb-1" />
              <p className="text-xs font-medium">
                {loyaltyStatus.referralCount} Referrals
              </p>
            </div>
          </div>

          <div className="mt-4">
            <p className="text-sm font-medium mb-2">Current Tier Perks</p>
            <ul className="text-xs space-y-1">
              {currentTier?.perks.map((perk, index) => (
                <li key={index} className="flex items-start">
                  <Star className="h-3 w-3 text-primary mr-2 mt-0.5 flex-shrink-0" />
                  <span>{perk}</span>
                </li>
              ))}
            </ul>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
