import { motion } from "framer-motion";
import { RESTAURANT_OPTIONS } from "@/types";
import { useNavigate, useLocation } from "react-router-dom";

export function Categories() {
  const navigate = useNavigate();
  const location = useLocation();

  const categories = RESTAURANT_OPTIONS.categories
    .slice(0, 6)
    .map((category, index) => ({
      id: index + 1,
      name: category,
      icon: ["🍽️", "🍔", "🥗", "🦐", "🥢", "🍕"][index],
    }));

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.3,
      },
    },
  };

  const categoryVariants = {
    hidden: { opacity: 0, scale: 0.8 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: {
        duration: 0.4,
        ease: "easeOut",
      },
    },
  };

  const iconVariants = {
    hover: {
      y: [-2, -8, -2],
      transition: {
        duration: 0.6,
        ease: "easeInOut",
        repeat: Infinity,
      },
    },
  };

  const handleCategoryClick = (categoryName: string) => {
    const searchParams = new URLSearchParams(location.search);
    searchParams.set("category", categoryName);
    navigate(`/restaurants?${searchParams.toString()}`);
    window.scrollTo(0, 0);
  };

  return (
    <section className="py-16 bg-white">
      <div className="mx-auto px-4">
        <motion.h2
          initial={{ opacity: 0, y: -20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-4xl font-bold text-center mb-12"
        >
          Browse Categories
        </motion.h2>
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6"
        >
          {categories.map((category) => (
            <motion.div
              key={category.id}
              variants={categoryVariants}
              whileHover={{
                scale: 1.05,
                backgroundColor: "#fff7ed", // Tailwind orange-50
                transition: { duration: 0.2 },
              }}
              className="flex flex-col items-center p-6 bg-gray-50 rounded-lg cursor-pointer shadow-sm hover:shadow-md transition-shadow"
              onClick={() => handleCategoryClick(category.name)}
            >
              <motion.span
                variants={iconVariants}
                whileHover="hover"
                className="text-4xl mb-4"
              >
                {category.icon}
              </motion.span>
              <motion.h3
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.2 }}
                className="text-lg font-medium"
              >
                {category.name}
              </motion.h3>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  );
}
