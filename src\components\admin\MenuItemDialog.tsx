import { useState, useEffect, FormEvent, ChangeEvent } from "react";
import { Loader2 } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { MultiSelect } from "@/components/ui/multi-select"; // Assuming this is a custom or library component
import { Separator } from "@/components/ui/separator"; // Import Separator
import { MenuItem } from "@/types/menu"; // Using the menu-specific type definition

// --- Constants for Select/MultiSelect Options ---
const ALLERGENS = [
  "Milk",
  "Eggs",
  "Fish",
  "Shellfish",
  "Tree Nuts",
  "Peanuts",
  "Wheat",
  "Soy",
  "Sesame",
  "Mustard",
  "Celery",
  "Lupin",
  "Molluscs",
  "Sulphites",
] as const;

const DIETARY_OPTIONS = [
  "Vegetarian",
  "Vegan",
  "Gluten-Free",
  "Halal",
  "Kosher",
  "Dairy-free",
  "Nut-free",
  "Low-carb",
  "Organic",
  "Raw",
  "Paleo",
  "Keto",
  "Sugar-free",
  "Protein-rich",
  "Diabetic-friendly",
  "Egg-free",
  "Soy-free",
  "Shellfish-free",
  "Fish-free",
  "Wheat-free",
  "Corn-free",
  "Low-sodium",
  "Low-fat",
  "Low-calorie",
  "High-fiber",
  "FODMAP-friendly",
] as const;

const HEALTH_LABELS = [
  "Low Fat",
  "Low Sodium",
  "Low Calorie",
  "Low Carb",
  "High Protein",
  "High Fiber",
  "Sugar Free",
  "No Added Sugar",
  "Whole Grain",
  "Heart Healthy",
  "Immunity Boosting",
  "Antioxidant Rich",
  "Superfood",
  "Locally Sourced",
  "Sustainable",
] as const;

const SPICY_LEVELS = ["none", "mild", "medium", "hot", "extra hot"] as const;

const CATEGORIES = [
  "Appetizers",
  "Soups",
  "Salads",
  "Main Courses",
  "Burgers",
  "Pizza",
  "Pasta",
  "Sandwiches",
  "Seafood",
  "Grilled Dishes",
  "Vegetarian",
  "Vegan",
  "Side Dishes",
  "Desserts",
  "Beverages",
  "Hot Drinks",
  "Cold Drinks",
  "Breakfast",
  "Kids Menu",
  "Special Menu",
] as const;
// --- ---

interface MenuItemDialogProps {
  mode: "add" | "edit";
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (item: Omit<MenuItem, "itemId" | "restaurantId">) => Promise<void>; // Ensure correct type
  isLoading: boolean;
  initialData?: MenuItem;
}

// Default state factory
const createDefaultState = (): Omit<MenuItem, "itemId" | "restaurantId"> => ({
  name: "",
  description: "",
  price: 0,
  category: CATEGORIES[0], // Default to first category
  imageUrl: "",
  available: true,
  dietary: [],
  spicyLevel: SPICY_LEVELS[0], // Default to first spicy level
  allergens: [],
  ingredients: [], // Ensure this is always initialized as an empty array

  // Basic nutrition information
  calories: 0,
  servingSize: "",
  servingsPerItem: 1,

  // Macronutrients
  protein: 0,
  carbs: 0,
  fat: 0,

  // Detailed nutrition information
  fiber: 0,
  sugar: 0,
  sodium: 0,
  cholesterol: 0,

  // Additional nutrition information
  vitamins: {
    vitaminA: 0,
    vitaminC: 0,
    vitaminD: 0,
    calcium: 0,
    iron: 0,
  },

  // Health labels
  healthLabels: [],

  preparationTime: "",
  isSignatureDish: false,
  isSeasonalDish: false,

  // Promotion fields
  isSpecialOffer: false,
  isChefRecommendation: false,
  isPopular: false,
  isLimitedTimeOffer: false,
  promotionEndDate: undefined,
  promotionDescription: "",
  discountPercentage: 0,
});

export const MenuItemDialog = ({
  mode,
  open,
  onOpenChange,
  onSubmit,
  isLoading,
  initialData,
}: MenuItemDialogProps) => {
  const [formData, setFormData] = useState(createDefaultState());
  const [ingredientInput, setIngredientInput] = useState("");

  useEffect(() => {
    if (open && mode === "edit" && initialData) {
      // Make sure ingredients is always an array when editing
      const safeInitialData = {
        ...initialData,
        ingredients: Array.isArray(initialData.ingredients)
          ? initialData.ingredients
          : [],
      };
      setFormData({ ...createDefaultState(), ...safeInitialData }); // Merge initialData with defaults
    } else if (open && mode === "add") {
      setFormData(createDefaultState()); // Reset on open for 'add' mode
    }
    // No reset needed when closing, handled by onOpenChange if required externally
  }, [initialData, mode, open]);

  const handleFormSubmit = async (e: FormEvent) => {
    e.preventDefault(); // Prevent default form submission if wrapped in <form>
    // Basic validation
    if (
      !formData.name ||
      !formData.description ||
      formData.price <= 0 ||
      !formData.category
    ) {
      // Add user feedback here - e.g., using react-toastify or highlighting fields
      console.error("Validation failed: Required fields missing or invalid.");
      return;
    }

    // Ensure all fields are properly formatted for Firestore
    const submissionData = {
      ...formData,
      ingredients: Array.isArray(formData.ingredients)
        ? formData.ingredients
        : [],
      spicyLevel: formData.spicyLevel || SPICY_LEVELS[0], // Use first spicy level if undefined

      // Basic nutrition information
      calories: formData.calories || 0,
      servingSize: formData.servingSize || "",
      servingsPerItem: formData.servingsPerItem || 1,

      // Macronutrients
      protein: formData.protein || 0,
      carbs: formData.carbs || 0,
      fat: formData.fat || 0,

      // Detailed nutrition information
      fiber: formData.fiber || 0,
      sugar: formData.sugar || 0,
      sodium: formData.sodium || 0,
      cholesterol: formData.cholesterol || 0,

      // Additional nutrition information
      vitamins: formData.vitamins || {
        vitaminA: 0,
        vitaminC: 0,
        vitaminD: 0,
        calcium: 0,
        iron: 0,
      },

      // Health labels
      healthLabels: Array.isArray(formData.healthLabels)
        ? formData.healthLabels
        : [],

      preparationTime: formData.preparationTime || "",
      imageUrl: formData.imageUrl || "",

      // Promotion fields
      isSpecialOffer: formData.isSpecialOffer || false,
      isChefRecommendation: formData.isChefRecommendation || false,
      isPopular: formData.isPopular || false,
      isLimitedTimeOffer: formData.isLimitedTimeOffer || false,
      promotionEndDate: formData.promotionEndDate,
      promotionDescription: formData.promotionDescription || "",
      discountPercentage: formData.discountPercentage || 0,
    } as Omit<MenuItem, "itemId" | "restaurantId">;

    await onSubmit(submissionData);
    // Reset only if add mode and successful (handled in parent or via onOpenChange effect)
  };

  const handleInputChange = (
    e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleNumberInputChange = (e: ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    // Allow empty string for optional fields, parse others
    const numValue = value === "" ? undefined : parseInt(value, 10);
    setFormData((prev) => ({
      ...prev,
      [name]: isNaN(numValue as number) ? undefined : numValue,
    }));
  };

  const handlePriceChange = (e: ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target;
    const numValue = value === "" ? 0 : parseFloat(value);
    setFormData((prev) => ({ ...prev, price: isNaN(numValue) ? 0 : numValue }));
  };

  const handleCheckboxChange =
    (
      field:
        | "available"
        | "isSignatureDish"
        | "isSeasonalDish"
        | "isSpecialOffer"
        | "isChefRecommendation"
        | "isPopular"
        | "isLimitedTimeOffer"
    ) =>
    (checked: boolean | "indeterminate") => {
      if (typeof checked === "boolean") {
        setFormData((prev) => ({ ...prev, [field]: checked }));
      }
    };

  const handleSelectChange =
    (field: "category" | "spicyLevel") => (value: string) => {
      setFormData((prev) => ({ ...prev, [field]: value }));
    };

  const handleMultiSelectChange =
    (field: "dietary" | "allergens" | "healthLabels") => (value: string[]) => {
      setFormData((prev) => ({ ...prev, [field]: value }));
    };

  // Functions for handling ingredients as tags
  const addIngredient = () => {
    if (ingredientInput.trim()) {
      const newIngredient = ingredientInput.trim();
      if (!formData.ingredients?.includes(newIngredient)) {
        setFormData((prev) => ({
          ...prev,
          ingredients: [...(prev.ingredients || []), newIngredient],
        }));
      }
      setIngredientInput("");
    }
  };

  const removeIngredient = (index: number) => {
    setFormData((prev) => ({
      ...prev,
      ingredients: prev.ingredients?.filter((_, i) => i !== index) || [],
    }));
  };

  return (
    <Dialog modal={false} open={open} onOpenChange={onOpenChange}>
      {/* Increased max-width, adjusted max-height */}
      <DialogContent
        className="sm:max-w-xl md:max-w-2xl max-h-[90vh] overflow-y-auto p-6"
        hideCloseButton
      >
        <DialogHeader>
          <DialogTitle className="text-xl">
            {mode === "add" ? "Add New Menu Item" : "Edit Menu Item"}
          </DialogTitle>
          <DialogDescription>
            {mode === "add"
              ? "Fill in the details for the new menu item."
              : `Editing: ${initialData?.name || "Menu Item"}`}{" "}
            Fields marked with <span className="text-destructive">*</span> are
            required.
          </DialogDescription>
        </DialogHeader>

        {/* Using form for better semantics, though submit is handled by button */}
        <form onSubmit={handleFormSubmit} className="space-y-6 py-4">
          {/* --- Section 1: Basic Info --- */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold border-b pb-2">
              Basic Information
            </h3>
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="name">
                  Name <span className="text-destructive">*</span>
                </Label>
                <Input
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  placeholder="e.g., Classic Cheeseburger"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="price">
                  Price (AZN) <span className="text-destructive">*</span>
                </Label>
                <Input
                  id="price"
                  name="price"
                  type="number"
                  value={formData.price}
                  onChange={handlePriceChange}
                  step="0.01"
                  min="0"
                  placeholder="e.g., 12.50"
                  required
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="description">
                Description <span className="text-destructive">*</span>
              </Label>
              <Textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                placeholder="Describe the item..."
                required
                className="min-h-[80px]"
              />
            </div>
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="category">
                  Category <span className="text-destructive">*</span>
                </Label>
                <Select
                  name="category"
                  value={formData.category}
                  onValueChange={handleSelectChange("category")}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent className="z-[9999] max-h-[200px]">
                    {CATEGORIES.map((cat) => (
                      <SelectItem key={cat} value={cat}>
                        {cat}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="available">Availability</Label>
                <Select
                  name="available"
                  value={formData.available ? "yes" : "no"}
                  onValueChange={(val) =>
                    handleCheckboxChange("available")(val === "yes")
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Is it available?" />
                  </SelectTrigger>
                  <SelectContent className="z-[9999]">
                    <SelectItem value="yes">Available</SelectItem>
                    <SelectItem value="no">Not Available</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          <Separator className="my-6" />

          {/* --- Section 2: Details --- */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold border-b pb-2">
              Additional Details
            </h3>
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="spicyLevel">Spicy Level</Label>
                <Select
                  name="spicyLevel"
                  value={formData.spicyLevel}
                  onValueChange={handleSelectChange("spicyLevel")}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select spicy level" />
                  </SelectTrigger>
                  <SelectContent className="z-[9999]">
                    {SPICY_LEVELS.map((level) => (
                      <SelectItem key={level} value={level}>
                        {level.charAt(0).toUpperCase() + level.slice(1)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="preparationTime">Prep Time</Label>
                <Input
                  id="preparationTime"
                  name="preparationTime"
                  value={formData.preparationTime || ""}
                  onChange={handleInputChange}
                  placeholder="e.g., 15-20 minutes"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="imageUrl">Image URL</Label>
                <Input
                  id="imageUrl"
                  name="imageUrl"
                  value={formData.imageUrl || ""}
                  onChange={handleInputChange}
                  placeholder="https://..."
                />
              </div>
            </div>
          </div>

          <Separator className="my-6" />

          {/* --- Section 3: Promotions --- */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold border-b pb-2">Promotions</h3>
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="isSpecialOffer"
                    checked={formData.isSpecialOffer}
                    onCheckedChange={handleCheckboxChange("isSpecialOffer")}
                  />
                  <Label htmlFor="isSpecialOffer" className="font-medium">
                    Special Offer
                  </Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="isChefRecommendation"
                    checked={formData.isChefRecommendation}
                    onCheckedChange={handleCheckboxChange(
                      "isChefRecommendation"
                    )}
                  />
                  <Label htmlFor="isChefRecommendation" className="font-medium">
                    Chef's Recommendation
                  </Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="isPopular"
                    checked={formData.isPopular}
                    onCheckedChange={handleCheckboxChange("isPopular")}
                  />
                  <Label htmlFor="isPopular" className="font-medium">
                    Popular Item
                  </Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="isLimitedTimeOffer"
                    checked={formData.isLimitedTimeOffer}
                    onCheckedChange={handleCheckboxChange("isLimitedTimeOffer")}
                  />
                  <Label htmlFor="isLimitedTimeOffer" className="font-medium">
                    Limited Time Offer
                  </Label>
                </div>
              </div>

              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="promotionDescription">
                    Promotion Description
                  </Label>
                  <Textarea
                    id="promotionDescription"
                    name="promotionDescription"
                    value={formData.promotionDescription || ""}
                    onChange={handleInputChange}
                    placeholder="Special offer details..."
                    className="min-h-[80px]"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="discountPercentage">
                    Discount Percentage (%)
                  </Label>
                  <Input
                    id="discountPercentage"
                    name="discountPercentage"
                    type="number"
                    value={formData.discountPercentage || 0}
                    onChange={handleNumberInputChange}
                    min="0"
                    max="100"
                    placeholder="e.g., 15"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="promotionEndDate">Promotion End Date</Label>
                  <Input
                    id="promotionEndDate"
                    name="promotionEndDate"
                    type="date"
                    value={
                      formData.promotionEndDate
                        ? (() => {
                            try {
                              // Handle both Date objects and Firestore timestamps
                              const date =
                                typeof formData.promotionEndDate === "object" &&
                                formData.promotionEndDate !== null &&
                                "toDate" in formData.promotionEndDate &&
                                typeof formData.promotionEndDate.toDate ===
                                  "function"
                                  ? formData.promotionEndDate.toDate()
                                  : new Date(formData.promotionEndDate);

                              // Check if date is valid before calling toISOString
                              return !isNaN(date.getTime())
                                ? date.toISOString().split("T")[0]
                                : "";
                            } catch (error) {
                              console.error("Error formatting date:", error);
                              return "";
                            }
                          })()
                        : ""
                    }
                    onChange={(e) => {
                      try {
                        const date = e.target.value
                          ? new Date(e.target.value)
                          : undefined;

                        // Validate the date before setting it
                        if (date && isNaN(date.getTime())) {
                          console.error("Invalid date created");
                          return; // Don't update state with invalid date
                        }

                        setFormData((prev) => ({
                          ...prev,
                          promotionEndDate: date,
                        }));
                      } catch (error) {
                        console.error("Error setting date:", error);
                        // Don't update state on error
                      }
                    }}
                  />
                </div>
              </div>
            </div>
          </div>

          <Separator className="my-6" />

          {/* --- Section 4: Nutrition Information --- */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold border-b pb-2">
              Nutrition Information
            </h3>

            {/* Basic nutrition info */}
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
              <div className="space-y-2">
                <Label htmlFor="calories">Calories (kcal)</Label>
                <Input
                  id="calories"
                  name="calories"
                  type="number"
                  value={formData.calories ?? ""}
                  onChange={handleNumberInputChange}
                  placeholder="e.g., 550"
                  min="0"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="servingSize">Serving Size</Label>
                <Input
                  id="servingSize"
                  name="servingSize"
                  value={formData.servingSize || ""}
                  onChange={handleInputChange}
                  placeholder="e.g., 250g"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="servingsPerItem">Servings Per Item</Label>
                <Input
                  id="servingsPerItem"
                  name="servingsPerItem"
                  type="number"
                  value={formData.servingsPerItem ?? ""}
                  onChange={handleNumberInputChange}
                  placeholder="e.g., 1"
                  min="1"
                />
              </div>
            </div>

            {/* Macronutrients */}
            <h4 className="text-md font-medium mt-4">Macronutrients (g)</h4>
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
              <div className="space-y-2">
                <Label htmlFor="protein">Protein</Label>
                <Input
                  id="protein"
                  name="protein"
                  type="number"
                  value={formData.protein ?? ""}
                  onChange={handleNumberInputChange}
                  placeholder="e.g., 25"
                  min="0"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="carbs">Carbohydrates</Label>
                <Input
                  id="carbs"
                  name="carbs"
                  type="number"
                  value={formData.carbs ?? ""}
                  onChange={handleNumberInputChange}
                  placeholder="e.g., 30"
                  min="0"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="fat">Fat</Label>
                <Input
                  id="fat"
                  name="fat"
                  type="number"
                  value={formData.fat ?? ""}
                  onChange={handleNumberInputChange}
                  placeholder="e.g., 15"
                  min="0"
                />
              </div>
            </div>

            {/* Detailed nutrition */}
            <h4 className="text-md font-medium mt-4">Detailed Nutrition</h4>
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-4">
              <div className="space-y-2">
                <Label htmlFor="fiber">Fiber (g)</Label>
                <Input
                  id="fiber"
                  name="fiber"
                  type="number"
                  value={formData.fiber ?? ""}
                  onChange={handleNumberInputChange}
                  placeholder="e.g., 5"
                  min="0"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="sugar">Sugar (g)</Label>
                <Input
                  id="sugar"
                  name="sugar"
                  type="number"
                  value={formData.sugar ?? ""}
                  onChange={handleNumberInputChange}
                  placeholder="e.g., 10"
                  min="0"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="sodium">Sodium (mg)</Label>
                <Input
                  id="sodium"
                  name="sodium"
                  type="number"
                  value={formData.sodium ?? ""}
                  onChange={handleNumberInputChange}
                  placeholder="e.g., 500"
                  min="0"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="cholesterol">Cholesterol (mg)</Label>
                <Input
                  id="cholesterol"
                  name="cholesterol"
                  type="number"
                  value={formData.cholesterol ?? ""}
                  onChange={handleNumberInputChange}
                  placeholder="e.g., 50"
                  min="0"
                />
              </div>
            </div>

            {/* Health labels */}
            <div className="space-y-2 mt-4">
              <Label htmlFor="healthLabels">Health Labels</Label>
              <MultiSelect
                options={HEALTH_LABELS.map((label) => ({
                  label: label,
                  value: label,
                }))}
                selected={formData.healthLabels || []}
                onChange={handleMultiSelectChange("healthLabels")}
                placeholder="Select health labels"
                className="w-full"
              />
              <p className="text-xs text-muted-foreground">
                Select labels that describe the health benefits of this item.
              </p>
            </div>
          </div>

          <Separator className="my-6" />

          {/* --- Section 4: Dietary & Allergens --- */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold border-b pb-2">
              Dietary & Allergens
            </h3>
            <div className="space-y-2">
              <Label htmlFor="ingredients">Ingredients</Label>
              <div className="flex gap-2">
                <Input
                  id="ingredientInput"
                  placeholder="Type an ingredient and press Enter"
                  className="flex-1"
                  value={ingredientInput}
                  onChange={(e) => setIngredientInput(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === "Enter" || e.key === ",") {
                      e.preventDefault();
                      addIngredient();
                    }
                  }}
                />
                <Button
                  type="button"
                  onClick={addIngredient}
                  variant="secondary"
                >
                  Add
                </Button>
              </div>
              <div className="flex flex-wrap gap-2 mt-2">
                {formData.ingredients?.map((ingredient, index) => (
                  <Badge
                    key={index}
                    variant="secondary"
                    className="px-2 py-1 text-sm"
                  >
                    {ingredient}
                    <button
                      type="button"
                      className="ml-2 text-muted-foreground hover:text-foreground"
                      onClick={() => removeIngredient(index)}
                    >
                      ×
                    </button>
                  </Badge>
                ))}
              </div>
              <p className="text-xs text-muted-foreground">
                Type an ingredient and press Enter or click Add.
              </p>
            </div>
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="dietary">Dietary Options</Label>
                {/* Ensure MultiSelect pops content above others if needed */}
                <MultiSelect
                  options={DIETARY_OPTIONS.map((diet) => ({
                    label: diet,
                    value: diet,
                  }))}
                  selected={formData.dietary || []}
                  onChange={handleMultiSelectChange("dietary")}
                  placeholder="Select dietary tags"
                  className="w-full" // Ensure full width
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="allergens">Allergens</Label>
                <MultiSelect
                  options={ALLERGENS.map((allergen) => ({
                    label: allergen,
                    value: allergen,
                  }))}
                  selected={formData.allergens || []}
                  onChange={handleMultiSelectChange("allergens")}
                  placeholder="Select allergens"
                  className="w-full"
                />
              </div>
            </div>
          </div>

          <Separator className="my-6" />

          {/* --- Section 5: Tags --- */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold border-b pb-2">
              Tags & Status
            </h3>
            <div className="flex flex-wrap gap-x-6 gap-y-3 items-center">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="isSignatureDish"
                  checked={formData.isSignatureDish}
                  onCheckedChange={handleCheckboxChange("isSignatureDish")}
                />
                <Label htmlFor="isSignatureDish" className="font-normal">
                  Signature Dish
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="isSeasonalDish"
                  checked={formData.isSeasonalDish}
                  onCheckedChange={handleCheckboxChange("isSeasonalDish")}
                />
                <Label htmlFor="isSeasonalDish" className="font-normal">
                  Seasonal Dish
                </Label>
              </div>
            </div>
          </div>
        </form>

        <DialogFooter className="mt-6 pt-4 border-t">
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button onClick={handleFormSubmit} disabled={isLoading}>
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                {mode === "add" ? "Adding..." : "Saving..."}
              </>
            ) : mode === "add" ? (
              "Add Item"
            ) : (
              "Save Changes"
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
