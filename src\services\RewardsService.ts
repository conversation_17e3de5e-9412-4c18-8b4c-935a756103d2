import { firestore } from "@/config/firebase";
import {
  doc,
  collection,
  getDoc,
  getDocs,
  query,
  where,
  orderBy,
  limit,
  Timestamp,
  serverTimestamp,
  increment,
  writeBatch,
  addDoc,
  updateDoc,
  deleteDoc,
  setDoc,
} from "firebase/firestore";
import { Reward, RewardRedemption, RewardAnalytics } from "@/types/rewards";
import { PointTransaction } from "@/types/loyalty";
import { notificationService } from "./NotificationService";
import { v4 as uuidv4 } from "uuid";

/**
 * Service for managing loyalty rewards
 */
class RewardsService {
  /**
   * Generate a unique redemption code
   * @returns Redemption code
   */
  private generateRedemptionCode(): string {
    // Generate a code in format: XXXX-XXXX-XXXX
    const uuid = uuidv4().replace(/-/g, "").toUpperCase();
    const parts = [uuid.substr(0, 4), uuid.substr(4, 4), uuid.substr(8, 4)];
    return parts.join("-");
  }

  /**
   * Update reward analytics
   * @param rewardId Reward ID
   * @param userId User ID
   * @param pointsSpent Points spent
   */
  private async updateRewardAnalytics(
    rewardId: string,
    userId: string,
    pointsSpent: number
  ): Promise<void> {
    try {
      const analyticsRef = doc(firestore, "rewardAnalytics", rewardId);
      const analyticsDoc = await getDoc(analyticsRef);

      const now = new Date();
      const dayKey = now.toISOString().split("T")[0]; // YYYY-MM-DD
      const monthKey = dayKey.substring(0, 7); // YYYY-MM

      if (analyticsDoc.exists()) {
        const analytics = analyticsDoc.data() as RewardAnalytics;
        const redemptionsByDay = { ...analytics.redemptionsByDay };
        const redemptionsByMonth = { ...analytics.redemptionsByMonth };

        // Update day count
        redemptionsByDay[dayKey] = (redemptionsByDay[dayKey] || 0) + 1;

        // Update month count
        redemptionsByMonth[monthKey] = (redemptionsByMonth[monthKey] || 0) + 1;

        // Check if this is a new user
        const userRedemptionsQuery = query(
          collection(firestore, "redemptions"),
          where("rewardId", "==", rewardId),
          where("userId", "==", userId)
        );

        const userRedemptionsSnapshot = await getDocs(userRedemptionsQuery);
        const isNewUser = userRedemptionsSnapshot.empty;

        await updateDoc(analyticsRef, {
          totalRedemptions: increment(1),
          uniqueUsers: isNewUser ? increment(1) : increment(0),
          pointsSpent: increment(pointsSpent),
          lastRedemption: Timestamp.now(),
          redemptionsByDay,
          redemptionsByMonth,
        });
      }
    } catch (error) {
      console.error("Error updating reward analytics:", error);
    }
  }
  /**
   * Create a new reward
   * @param reward Reward data
   * @param userId User ID creating the reward
   * @returns Created reward ID
   */
  async createReward(
    reward: Omit<Reward, "id" | "createdAt" | "updatedAt" | "createdBy">,
    userId: string
  ): Promise<string> {
    try {
      const now = Timestamp.now();

      const rewardData: Omit<Reward, "id"> = {
        ...reward,
        createdAt: now,
        updatedAt: now,
        createdBy: userId,
      };

      let docRef;

      // Use subcollection for restaurant rewards, main collection for global rewards
      if (reward.restaurantId && !reward.isGlobal) {
        // Restaurant-specific reward - store in subcollection
        const rewardRef = collection(
          firestore,
          "restaurants",
          reward.restaurantId,
          "rewards"
        );
        docRef = await addDoc(rewardRef, rewardData);
      } else {
        // Global reward - store in main collection
        const rewardRef = collection(firestore, "rewards");
        docRef = await addDoc(rewardRef, rewardData);
      }

      // Update with ID
      await updateDoc(docRef, { id: docRef.id });

      // Create analytics document
      const analyticsRef = doc(firestore, "rewardAnalytics", docRef.id);
      await setDoc(analyticsRef, {
        rewardId: docRef.id,
        totalRedemptions: 0,
        uniqueUsers: 0,
        pointsSpent: 0,
        redemptionsByDay: {},
        redemptionsByMonth: {},
      });

      return docRef.id;
    } catch (error) {
      console.error("Error creating reward:", error);
      throw error;
    }
  }

  /**
   * Update an existing reward
   * @param rewardId Reward ID
   * @param data Updated reward data
   * @param restaurantId Restaurant ID (required for restaurant rewards)
   * @returns Success status
   */
  async updateReward(
    rewardId: string,
    data: Partial<Reward>,
    restaurantId?: string
  ): Promise<boolean> {
    try {
      let rewardRef;

      // First try to find the reward in restaurant subcollection if restaurantId is provided
      if (restaurantId) {
        rewardRef = doc(
          firestore,
          "restaurants",
          restaurantId,
          "rewards",
          rewardId
        );

        // Check if it exists in restaurant subcollection
        const rewardDoc = await getDoc(rewardRef);
        if (!rewardDoc.exists()) {
          // Fall back to global rewards collection
          rewardRef = doc(firestore, "rewards", rewardId);
        }
      } else {
        // Default to global rewards collection
        rewardRef = doc(firestore, "rewards", rewardId);
      }

      await updateDoc(rewardRef, {
        ...data,
        updatedAt: Timestamp.now(),
      });

      return true;
    } catch (error) {
      console.error("Error updating reward:", error);
      return false;
    }
  }

  /**
   * Delete a reward
   * @param rewardId Reward ID
   * @param restaurantId Restaurant ID (required for restaurant rewards)
   * @returns Success status
   */
  async deleteReward(
    rewardId: string,
    restaurantId?: string
  ): Promise<boolean> {
    try {
      let rewardRef;

      // First try to find the reward in restaurant subcollection if restaurantId is provided
      if (restaurantId) {
        rewardRef = doc(
          firestore,
          "restaurants",
          restaurantId,
          "rewards",
          rewardId
        );

        // Check if it exists in restaurant subcollection
        const rewardDoc = await getDoc(rewardRef);
        if (!rewardDoc.exists()) {
          // Fall back to global rewards collection
          rewardRef = doc(firestore, "rewards", rewardId);
        }
      } else {
        // Default to global rewards collection
        rewardRef = doc(firestore, "rewards", rewardId);
      }

      // Check if reward has been redeemed
      const redemptionsQuery = query(
        collection(firestore, "redemptions"),
        where("rewardId", "==", rewardId)
      );

      const redemptionsSnapshot = await getDocs(redemptionsQuery);

      if (!redemptionsSnapshot.empty) {
        // Don't delete, just mark as inactive
        await updateDoc(rewardRef, {
          isActive: false,
          updatedAt: Timestamp.now(),
        });
      } else {
        // No redemptions, safe to delete
        await deleteDoc(rewardRef);

        // Delete analytics
        const analyticsRef = doc(firestore, "rewardAnalytics", rewardId);
        await deleteDoc(analyticsRef);
      }

      return true;
    } catch (error) {
      console.error("Error deleting reward:", error);
      return false;
    }
  }

  /**
   * Get a reward by ID
   * @param rewardId Reward ID
   * @returns Reward data
   */
  async getReward(rewardId: string): Promise<Reward | null> {
    try {
      const rewardRef = doc(firestore, "rewards", rewardId);
      const rewardDoc = await getDoc(rewardRef);

      if (!rewardDoc.exists()) {
        return null;
      }

      return rewardDoc.data() as Reward;
    } catch (error) {
      console.error("Error getting reward:", error);
      return null;
    }
  }

  /**
   * Get all rewards for a restaurant
   * @param restaurantId Restaurant ID
   * @returns Array of rewards
   */
  async getRestaurantRewards(restaurantId: string): Promise<Reward[]> {
    try {
      // Get rewards from restaurant subcollection
      const rewardsQuery = query(
        collection(firestore, "restaurants", restaurantId, "rewards"),
        orderBy("createdAt", "desc")
      );

      const rewardsSnapshot = await getDocs(rewardsQuery);
      const rewards: Reward[] = [];

      rewardsSnapshot.forEach((doc) => {
        rewards.push(doc.data() as Reward);
      });

      return rewards;
    } catch (error) {
      console.error("Error getting restaurant rewards:", error);
      return [];
    }
  }

  /**
   * Get all global rewards
   * @returns Array of global rewards
   */
  async getGlobalRewards(): Promise<Reward[]> {
    try {
      const rewardsQuery = query(
        collection(firestore, "rewards"),
        where("isGlobal", "==", true),
        where("isActive", "==", true),
        orderBy("pointsCost", "asc")
      );

      const rewardsSnapshot = await getDocs(rewardsQuery);
      const rewards: Reward[] = [];

      rewardsSnapshot.forEach((doc) => {
        rewards.push(doc.data() as Reward);
      });

      return rewards;
    } catch (error) {
      console.error("Error getting global rewards:", error);
      return [];
    }
  }

  /**
   * Get featured rewards
   * @param limit Maximum number of rewards to return
   * @returns Array of featured rewards
   */
  async getFeaturedRewards(limitCount = 5): Promise<Reward[]> {
    try {
      const rewardsQuery = query(
        collection(firestore, "rewards"),
        where("isFeatured", "==", true),
        where("isActive", "==", true),
        orderBy("createdAt", "desc"),
        limit(limitCount)
      );

      const rewardsSnapshot = await getDocs(rewardsQuery);
      const rewards: Reward[] = [];

      rewardsSnapshot.forEach((doc) => {
        rewards.push(doc.data() as Reward);
      });

      return rewards;
    } catch (error) {
      console.error("Error getting featured rewards:", error);
      return [];
    }
  }

  /**
   * Get rewards available to a user
   * @param userId User ID
   * @returns Array of available rewards
   */
  async getAvailableRewardsForUser(userId: string): Promise<Reward[]> {
    try {
      // Get user's followed restaurants
      const followingRef = collection(
        firestore,
        "clients",
        userId,
        "following"
      );
      const followingSnapshot = await getDocs(followingRef);
      const followedRestaurantIds: string[] = [];

      followingSnapshot.forEach((doc) => {
        followedRestaurantIds.push(doc.id);
      });

      // Get rewards from followed restaurants (from subcollections)
      const rewards: Reward[] = [];

      if (followedRestaurantIds.length > 0) {
        // Fetch rewards from each restaurant's subcollection
        for (const restaurantId of followedRestaurantIds) {
          try {
            const restaurantRewardsQuery = query(
              collection(firestore, "restaurants", restaurantId, "rewards"),
              where("isActive", "==", true),
              orderBy("pointsCost", "asc")
            );

            const restaurantRewardsSnapshot = await getDocs(
              restaurantRewardsQuery
            );

            restaurantRewardsSnapshot.forEach((doc) => {
              rewards.push(doc.data() as Reward);
            });
          } catch (error) {
            console.error(
              `Error fetching rewards for restaurant ${restaurantId}:`,
              error
            );
            // Continue with other restaurants
          }
        }
      }

      // Get global rewards
      const globalRewards = await this.getGlobalRewards();

      // Combine and sort by point cost
      return [...rewards, ...globalRewards].sort(
        (a, b) => a.pointsCost - b.pointsCost
      );
    } catch (error) {
      console.error("Error getting available rewards for user:", error);
      return [];
    }
  }

  /**
   * Get user's redeemed rewards
   * @param userId User ID
   * @returns Array of redeemed rewards
   */
  async getUserRedeemedRewards(userId: string): Promise<RewardRedemption[]> {
    try {
      const redemptionsQuery = query(
        collection(firestore, "redemptions"),
        where("userId", "==", userId),
        orderBy("redeemedAt", "desc")
      );

      const redemptionsSnapshot = await getDocs(redemptionsQuery);
      const redemptions: RewardRedemption[] = [];

      redemptionsSnapshot.forEach((doc) => {
        redemptions.push(doc.data() as RewardRedemption);
      });

      return redemptions;
    } catch (error) {
      console.error("Error getting user redeemed rewards:", error);
      return [];
    }
  }

  /**
   * Redeem a reward for a user
   * @param userId User ID
   * @param rewardId Reward ID
   * @returns Redemption code or null if failed
   */
  async redeemReward(userId: string, rewardId: string): Promise<string | null> {
    try {
      // Get user's loyalty status
      const statusRef = doc(
        firestore,
        "clients",
        userId,
        "loyaltyStatus",
        "current"
      );
      const statusDoc = await getDoc(statusRef);

      if (!statusDoc.exists()) {
        console.error("User not in loyalty program:", userId);
        return null;
      }

      const status = statusDoc.data();
      const userPoints = status.totalPoints;

      // Get reward details - try to find in both global and restaurant subcollections
      let rewardRef = doc(firestore, "rewards", rewardId);
      let rewardDoc = await getDoc(rewardRef);

      // If not found in global collection, search in restaurant subcollections
      if (!rewardDoc.exists()) {
        // We need to search through restaurant subcollections
        // This is less efficient but necessary for the subcollection approach
        const restaurantsSnapshot = await getDocs(
          collection(firestore, "restaurants")
        );

        for (const restaurantDoc of restaurantsSnapshot.docs) {
          const restaurantRewardRef = doc(
            firestore,
            "restaurants",
            restaurantDoc.id,
            "rewards",
            rewardId
          );
          const restaurantRewardDoc = await getDoc(restaurantRewardRef);

          if (restaurantRewardDoc.exists()) {
            rewardRef = restaurantRewardRef;
            rewardDoc = restaurantRewardDoc;
            break;
          }
        }
      }

      if (!rewardDoc.exists()) {
        console.error("Reward not found:", rewardId);
        return null;
      }

      const reward = rewardDoc.data() as Reward;

      // Check if reward is active
      if (!reward.isActive) {
        console.error("Reward is not active:", rewardId);
        return null;
      }

      // Check if reward has expired
      if (reward.expiresAt && reward.expiresAt.toDate() < new Date()) {
        console.error("Reward has expired:", rewardId);
        return null;
      }

      // Check if user has enough points
      if (userPoints < reward.pointsCost) {
        console.error("User doesn't have enough points:", userId);
        return null;
      }

      // Check if reward has available quantity
      if (
        reward.availableQuantity !== undefined &&
        reward.availableQuantity <= 0
      ) {
        console.error("Reward is out of stock:", rewardId);
        return null;
      }

      // Generate redemption code
      const redemptionCode = this.generateRedemptionCode();

      // Set expiration date (30 days from now)
      const expiresAt = Timestamp.fromDate(
        new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
      );

      // Start a batch write
      const batch = writeBatch(firestore);

      // Create redemption record
      const redemptionRef = doc(collection(firestore, "redemptions"));
      const redemptionData: RewardRedemption = {
        id: redemptionRef.id,
        userId,
        rewardId,
        reward: { ...reward }, // Store denormalized reward data
        pointsSpent: reward.pointsCost,
        redeemedAt: Timestamp.now(),
        expiresAt,
        isUsed: false,
        code: redemptionCode,
        ...(reward.restaurantId && { restaurantId: reward.restaurantId }), // Only include if not undefined
      };

      batch.set(redemptionRef, redemptionData);

      // Create point transaction
      const transactionRef = doc(
        collection(firestore, "clients", userId, "pointTransactions")
      );
      const transactionData: PointTransaction = {
        id: transactionRef.id,
        userId,
        points: -reward.pointsCost, // Negative points for spending
        type: "reward",
        description: `Redeemed reward: ${reward.name}`,
        createdAt: Timestamp.now(),
        referenceId: redemptionRef.id,
        rewardId: rewardId,
      };

      batch.set(transactionRef, transactionData);

      // Update user's points
      batch.update(statusRef, {
        totalPoints: increment(-reward.pointsCost),
        lastUpdated: serverTimestamp(),
      });

      // Update client document
      const clientRef = doc(firestore, "clients", userId);
      batch.update(clientRef, {
        loyaltyPoints: increment(-reward.pointsCost),
      });

      // Update reward's available quantity if applicable
      if (reward.availableQuantity !== undefined) {
        batch.update(rewardRef, {
          availableQuantity: increment(-1),
        });
      }

      // Commit the batch
      await batch.commit();

      // Update analytics
      await this.updateRewardAnalytics(rewardId, userId, reward.pointsCost);

      // Send notification to user
      await notificationService.createDatabaseNotification(
        userId,
        "client",
        "reward_redeemed",
        "Reward Redeemed",
        `You have successfully redeemed ${reward.name} for ${reward.pointsCost} points. Your redemption code is ${redemptionCode}.`,
        {}
      );

      // Send notification to restaurant if applicable
      if (reward.restaurantId) {
        await notificationService.createDatabaseNotification(
          reward.restaurantId,
          "restaurant",
          "reward_redeemed",
          "Reward Redeemed",
          `A customer has redeemed your reward: ${reward.name}`,
          {}
        );
      }

      return redemptionCode;
    } catch (error) {
      console.error("Error redeeming reward:", error);
      return null;
    }
  }

  /**
   * Apply a reward to an order
   * @param userId User ID
   * @param redemptionCode Redemption code
   * @param orderTotal Order total amount
   * @param cartItems Optional cart items for item-specific discounts
   * @returns Applied discount amount or null if failed
   */
  async applyRewardToOrder(
    userId: string,
    redemptionCode: string,
    orderTotal: number,
    cartItems?: Array<{
      item: { itemId: string; name: string; price: number };
      quantity: number;
    }>
  ): Promise<{
    discountAmount: number;
    redemption: RewardRedemption;
    affectedItemId?: string;
  } | null> {
    try {
      // Find the redemption by code and user
      const redemptionsQuery = query(
        collection(firestore, "redemptions"),
        where("userId", "==", userId),
        where("code", "==", redemptionCode),
        where("isUsed", "==", false)
      );

      const redemptionsSnapshot = await getDocs(redemptionsQuery);

      if (redemptionsSnapshot.empty) {
        console.error("Redemption not found or already used:", redemptionCode);
        return null;
      }

      const redemptionDoc = redemptionsSnapshot.docs[0];
      const redemption = redemptionDoc.data() as RewardRedemption;

      // Check if redemption has expired
      if (redemption.expiresAt.toDate() < new Date()) {
        console.error("Redemption has expired:", redemptionCode);
        return null;
      }

      const reward = redemption.reward;
      if (!reward) {
        console.error("Reward data not found in redemption:", redemptionCode);
        return null;
      }

      // Check minimum order value if applicable
      if (reward.minimumOrderValue && orderTotal < reward.minimumOrderValue) {
        console.error(
          `Order total ${orderTotal} is below minimum ${reward.minimumOrderValue}`
        );
        return null;
      }

      // Calculate discount amount based on reward type
      let discountAmount = 0;
      let affectedItemId: string | undefined;

      if (reward.type === "discount") {
        // Check if this is an item-specific discount
        if (reward.menuItemId && cartItems) {
          // Find the specific item in cart
          const targetItem = cartItems.find(
            (cartItem) => cartItem.item.itemId === reward.menuItemId
          );

          if (targetItem) {
            affectedItemId = reward.menuItemId;
            // Apply discount to only ONE unit of the specific item
            const itemPrice = targetItem.item.price;

            if (reward.discountType === "percentage") {
              discountAmount = (itemPrice * (reward.discountValue || 0)) / 100;
            } else if (reward.discountType === "fixed") {
              discountAmount = Math.min(reward.discountValue || 0, itemPrice);
            }
          } else {
            console.error("Target item not found in cart:", reward.menuItemId);
            return null;
          }
        } else {
          // Apply discount to entire order (existing behavior)
          if (reward.discountType === "percentage") {
            discountAmount = (orderTotal * (reward.discountValue || 0)) / 100;
          } else if (reward.discountType === "fixed") {
            discountAmount = reward.discountValue || 0;
          }
        }
      } else if (reward.type === "freeDelivery") {
        // For free delivery, you might want to set a fixed delivery fee
        discountAmount = 5; // Assuming 5 AZN delivery fee
      } else if (reward.type === "freeItem" && reward.menuItemId && cartItems) {
        // For free item, find the item price
        const targetItem = cartItems.find(
          (cartItem) => cartItem.item.itemId === reward.menuItemId
        );

        if (targetItem) {
          affectedItemId = reward.menuItemId;
          discountAmount = targetItem.item.price; // Full price of one item
        } else {
          console.error("Free item not found in cart:", reward.menuItemId);
          return null;
        }
      }

      // Ensure discount doesn't exceed order total
      discountAmount = Math.min(discountAmount, orderTotal);

      return { discountAmount, redemption, affectedItemId };
    } catch (error) {
      console.error("Error applying reward to order:", error);
      return null;
    }
  }

  /**
   * Mark a reward as used after successful order
   * @param redemptionCode Redemption code
   * @param orderId Order ID
   * @returns Success status
   */
  async markRewardAsUsed(
    redemptionCode: string,
    orderId: string
  ): Promise<boolean> {
    try {
      // Find the redemption by code
      const redemptionsQuery = query(
        collection(firestore, "redemptions"),
        where("code", "==", redemptionCode)
      );

      const redemptionsSnapshot = await getDocs(redemptionsQuery);

      if (redemptionsSnapshot.empty) {
        console.error("Redemption not found:", redemptionCode);
        return false;
      }

      const redemptionDoc = redemptionsSnapshot.docs[0];

      // Update the redemption to mark as used
      await updateDoc(redemptionDoc.ref, {
        isUsed: true,
        usedAt: Timestamp.now(),
        orderId: orderId,
      });

      return true;
    } catch (error) {
      console.error("Error marking reward as used:", error);
      return false;
    }
  }

  /**
   * Validate a redemption code
   * @param userId User ID
   * @param redemptionCode Redemption code
   * @param orderTotal Order total amount
   * @param cartItems Optional cart items for item-specific discounts
   * @returns Validation result with discount info
   */
  async validateRedemptionCode(
    userId: string,
    redemptionCode: string,
    orderTotal: number,
    cartItems?: Array<{
      item: { itemId: string; name: string; price: number };
      quantity: number;
    }>
  ): Promise<{
    isValid: boolean;
    discountAmount?: number;
    errorMessage?: string;
    redemption?: RewardRedemption;
    affectedItemId?: string;
  }> {
    try {
      const result = await this.applyRewardToOrder(
        userId,
        redemptionCode,
        orderTotal,
        cartItems
      );

      if (!result) {
        return {
          isValid: false,
          errorMessage: "Invalid or expired redemption code",
        };
      }

      return {
        isValid: true,
        discountAmount: result.discountAmount,
        redemption: result.redemption,
        affectedItemId: result.affectedItemId,
      };
    } catch (error) {
      console.error("Error validating redemption code:", error);
      return {
        isValid: false,
        errorMessage: "Failed to validate redemption code",
      };
    }
  }

  /**
   * Get restaurant information for rewards display
   * @param restaurantId Restaurant ID
   * @returns Restaurant info or null
   */
  async getRestaurantInfo(restaurantId: string): Promise<{
    name: string;
    imageUrl?: string;
  } | null> {
    try {
      const restaurantRef = doc(firestore, "restaurants", restaurantId);
      const restaurantDoc = await getDoc(restaurantRef);

      if (!restaurantDoc.exists()) {
        console.warn(`Restaurant document not found for ID: ${restaurantId}`);
        return null;
      }

      const restaurantData = restaurantDoc.data();

      const restaurantName =
        restaurantData.restaurantName ||
        restaurantData.name ||
        "Unknown Restaurant";

      return {
        name: restaurantName,
        imageUrl: restaurantData.imageUrl,
      };
    } catch (error) {
      console.error("Error getting restaurant info:", error);
      return null;
    }
  }

  /**
   * Get rewards with restaurant information for marketplace display
   * @param userId User ID
   * @returns Array of rewards with restaurant info
   */
  async getAvailableRewardsWithRestaurantInfo(userId: string): Promise<
    Array<
      Reward & {
        restaurantInfo?: {
          name: string;
          imageUrl?: string;
        };
      }
    >
  > {
    try {
      const rewards = await this.getAvailableRewardsForUser(userId);
      const rewardsWithInfo = [];

      for (const reward of rewards) {
        const rewardWithInfo: Reward & {
          restaurantInfo?: {
            name: string;
            imageUrl?: string;
          };
        } = { ...reward };

        // Add restaurant info for restaurant-specific rewards
        if (reward.restaurantId) {
          const restaurantInfo = await this.getRestaurantInfo(
            reward.restaurantId
          );
          if (restaurantInfo) {
            rewardWithInfo.restaurantInfo = restaurantInfo;
          }
        }

        rewardsWithInfo.push(rewardWithInfo);
      }

      return rewardsWithInfo;
    } catch (error) {
      console.error("Error getting rewards with restaurant info:", error);
      return [];
    }
  }
}

// Export a singleton instance
export const rewardsService = new RewardsService();
