import React from "react";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { Tag, Gift, Percent, DollarSign } from "lucide-react";

interface CouponUsageDisplayProps {
  originalAmount?: number;
  discountAmount?: number;
  appliedCouponCode?: string;
  appliedRewardCode?: string;
  affectedItemId?: string;
  couponUsed?: boolean;
  totalPrice: number;
  className?: string;
  orderItems?: Array<{
    itemId?: string;
    name: string;
    price: number;
    quantity: number;
  }>;
}

export const CouponUsageDisplay: React.FC<CouponUsageDisplayProps> = ({
  originalAmount,
  discountAmount,
  appliedCouponCode,
  appliedRewardCode,
  affectedItemId,
  couponUsed,
  totalPrice,
  className = "",
  orderItems,
}) => {
  const hasDiscount = discountAmount && discountAmount > 0;
  const hasCoupon = appliedCouponCode || appliedRewardCode;

  if (!hasDiscount && !hasCoupon) {
    return null;
  }

  const formatCurrency = (amount: number) => {
    return `${amount.toFixed(2)} AZN`;
  };

  const getDiscountPercentage = () => {
    if (!originalAmount || !discountAmount) return 0;
    return Math.round((discountAmount / originalAmount) * 100);
  };

  const getAffectedItemName = () => {
    if (!affectedItemId || !orderItems) return null;
    const item = orderItems.find((item) => item.itemId === affectedItemId);
    return item?.name || "Unknown Item";
  };

  return (
    <Card className={`border-dashed border-primary/30 ${className}`}>
      <CardContent className="p-4">
        <div className="space-y-3">
          {/* Coupon/Reward Code Display */}
          {hasCoupon && (
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                {appliedRewardCode ? (
                  <Gift className="h-4 w-4 text-primary" />
                ) : (
                  <Tag className="h-4 w-4 text-primary" />
                )}
                <span className="text-sm font-medium">
                  {appliedRewardCode ? "Reward Applied:" : "Coupon Applied:"}
                </span>
              </div>
              <Badge variant="outline" className="font-mono">
                {appliedRewardCode || appliedCouponCode}
              </Badge>
            </div>
          )}

          {/* Usage Status */}
          {hasCoupon && (
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">Status:</span>
              <Badge
                variant={couponUsed ? "default" : "secondary"}
                className="text-xs"
              >
                {couponUsed ? "Used" : "Applied"}
              </Badge>
            </div>
          )}

          {/* Affected Item */}
          {affectedItemId && getAffectedItemName() && (
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">Applied to:</span>
              <Badge variant="outline" className="text-xs">
                {getAffectedItemName()} (1 item)
              </Badge>
            </div>
          )}

          {/* Discount Breakdown */}
          {hasDiscount && (
            <>
              <div className="border-t pt-3 space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">
                    Original Amount:
                  </span>
                  <span className="font-medium">
                    {formatCurrency(
                      originalAmount || totalPrice + (discountAmount || 0)
                    )}
                  </span>
                </div>

                <div className="flex items-center justify-between text-sm">
                  <div className="flex items-center gap-1">
                    <Percent className="h-3 w-3 text-green-600" />
                    <span className="text-green-600">
                      Discount ({getDiscountPercentage()}%):
                    </span>
                  </div>
                  <span className="font-medium text-green-600">
                    -{formatCurrency(discountAmount)}
                  </span>
                </div>

                <div className="flex items-center justify-between text-sm font-semibold border-t pt-2">
                  <div className="flex items-center gap-1">
                    <DollarSign className="h-3 w-3" />
                    <span>Final Total:</span>
                  </div>
                  <span>{formatCurrency(totalPrice)}</span>
                </div>
              </div>
            </>
          )}

          {/* Savings Highlight */}
          {hasDiscount && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-2">
              <div className="flex items-center justify-center gap-1 text-green-700">
                <Gift className="h-4 w-4" />
                <span className="text-sm font-medium">
                  You saved {formatCurrency(discountAmount || 0)}!
                </span>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
