import React, { useState, useEffect } from "react";
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Gift, TrendingUp, Users, Award } from "lucide-react";
import { useAuth } from "@/providers/AuthProvider";
import { firestore } from "@/config/firebase";
import {
  collection,
  query,
  where,
  getDocs,
  orderBy,
  limit,
} from "firebase/firestore";
import { format } from "date-fns";

interface RewardRedemption {
  id: string;
  rewardId: string;
  userId: string;
  pointsSpent: number;
  redeemedAt: { toDate: () => Date };
  code: string;
  isUsed: boolean;
  usedAt?: { toDate: () => Date };
  reward?: {
    name: string;
    type: string;
    restaurantId?: string;
  };
}

interface RewardAnalyticsProps {
  restaurantId: string;
}

export const RewardAnalytics: React.FC<RewardAnalyticsProps> = ({
  restaurantId,
}) => {
  const { user } = useAuth();

  const [loading, setLoading] = useState(true);
  const [analytics, setAnalytics] = useState({
    totalRedemptions: 0,
    totalPointsSpent: 0,
    uniqueUsers: 0,
    usageRate: 0,
    recentRedemptions: [] as RewardRedemption[],
  });

  useEffect(() => {
    const fetchRewardAnalytics = async () => {
      if (!user || !restaurantId) return;

      setLoading(true);
      try {
        // Fetch all redemptions for this restaurant's rewards
        const redemptionsQuery = query(
          collection(firestore, "rewardRedemptions"),
          where("restaurantId", "==", restaurantId),
          orderBy("redeemedAt", "desc"),
          limit(100)
        );

        const redemptionsSnapshot = await getDocs(redemptionsQuery);
        const redemptionsData = redemptionsSnapshot.docs.map((doc) => ({
          id: doc.id,
          ...doc.data(),
        })) as RewardRedemption[];

        // Calculate analytics
        const totalRedemptions = redemptionsData.length;
        const totalPointsSpent = redemptionsData.reduce(
          (sum, r) => sum + r.pointsSpent,
          0
        );
        const uniqueUsers = new Set(redemptionsData.map((r) => r.userId)).size;
        const usedRedemptions = redemptionsData.filter((r) => r.isUsed).length;
        const usageRate =
          totalRedemptions > 0 ? (usedRedemptions / totalRedemptions) * 100 : 0;
        const recentRedemptions = redemptionsData.slice(0, 5);

        setAnalytics({
          totalRedemptions,
          totalPointsSpent,
          uniqueUsers,
          usageRate,
          recentRedemptions,
        });
      } catch (error) {
        console.error("Error fetching reward analytics:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchRewardAnalytics();
  }, [user, restaurantId]);

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-4 w-4" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 w-16" />
              </CardContent>
            </Card>
          ))}
        </div>
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-32" />
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {[...Array(3)].map((_, i) => (
                <Skeleton key={i} className="h-16 w-full" />
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Key Metrics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Total Redemptions
            </CardTitle>
            <Gift className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {analytics.totalRedemptions}
            </div>
            <p className="text-xs text-muted-foreground">
              Rewards redeemed by customers
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Points Spent</CardTitle>
            <Award className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {analytics.totalPointsSpent.toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">
              Total loyalty points redeemed
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Unique Users</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics.uniqueUsers}</div>
            <p className="text-xs text-muted-foreground">
              Customers who redeemed rewards
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Usage Rate</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {analytics.usageRate.toFixed(1)}%
            </div>
            <p className="text-xs text-muted-foreground">
              Redeemed rewards actually used
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Recent Redemptions */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Reward Redemptions</CardTitle>
        </CardHeader>
        <CardContent>
          {analytics.recentRedemptions.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <Gift className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p>No reward redemptions yet</p>
              <p className="text-sm">
                Customers will see their redemptions here
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {analytics.recentRedemptions.map((redemption) => (
                <div
                  key={redemption.id}
                  className="flex items-center justify-between p-4 border rounded-lg"
                >
                  <div className="flex items-center space-x-3">
                    <div className="w-2 h-2 rounded-full bg-primary" />
                    <div>
                      <p className="font-medium">
                        {redemption.reward?.name || "Unknown Reward"}
                      </p>
                      <p className="text-sm text-muted-foreground">
                        Code: {redemption.code}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        {format(
                          redemption.redeemedAt.toDate(),
                          "MMM dd, yyyy 'at' HH:mm"
                        )}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-medium">
                      {redemption.pointsSpent} pts
                    </div>
                    <Badge
                      variant={redemption.isUsed ? "default" : "secondary"}
                      className="text-xs"
                    >
                      {redemption.isUsed ? "Used" : "Pending"}
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
