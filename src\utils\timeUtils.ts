export const to24HourFormat = (timeStr: string): string => {
  // If the time is already in 24-hour format (no AM/PM)
  if (!timeStr.includes(" ")) {
    return timeStr;
  }

  // Handle 12-hour format with AM/PM
  const [time, meridiem] = timeStr.toUpperCase().split(" ");
  const [hours, minutes] = time.split(":").map(Number);
  let formattedHours = hours;

  if (meridiem === "PM" && formattedHours !== 12) {
    formattedHours += 12;
  } else if (meridiem === "AM" && formattedHours === 12) {
    formattedHours = 0;
  }

  return `${formattedHours.toString().padStart(2, "0")}:${minutes
    .toString()
    .padStart(2, "0")}`;
};

export const to12HourFormat = (timeStr: string): string => {
  const [hours, minutes] = timeStr.split(":").map(Number);
  const meridiem = hours >= 12 ? "PM" : "AM";
  const displayHours = hours % 12 || 12;
  return `${displayHours}:${minutes.toString().padStart(2, "0")} ${meridiem}`;
};

export const calculateDuration = (
  openTime: string,
  closeTime: string
): number => {
  const [openHours, openMinutes] = openTime.split(":").map(Number);
  const closeMinutes = Number(closeTime.split(":")[1]);
  let closeHours = Number(closeTime.split(":")[0]);

  // If closing time is 00:00, treat it as 24:00
  if (closeHours === 0 && closeMinutes === 0) {
    closeHours = 24;
  }

  let duration = (closeHours - openHours) * 60 + (closeMinutes - openMinutes);
  if (duration < 0) {
    duration += 24 * 60; // Add 24 hours if closing time is on next day
  }

  return Math.round((duration / 60) * 100) / 100; // Return hours with 2 decimal places
};
