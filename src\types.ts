import { Timestamp } from "firebase/firestore";

// Base location interface
export interface Location {
  latitude: number;
  longitude: number;
}

// Base seating interface
export interface Seating {
  indoor: boolean;
  outdoor: boolean;
  totalCapacity: number;
}

// Working hours interface
export interface WorkingHours {
  day:
    | "monday"
    | "tuesday"
    | "wednesday"
    | "thursday"
    | "friday"
    | "saturday"
    | "sunday";
  isOpen: boolean;
  openTime: string;
  closeTime: string;
}

// Menu item interface
export interface MenuItem {
  itemId: string;
  restaurantId: string;
  name: string;
  description: string;
  price: number;
  category: string;
  imageUrl?: string;
  available: boolean;
  dietary: string[];
  spicyLevel?: "mild" | "medium" | "hot" | "extra hot";
  allergens?: string[];
  ingredients?: string[];

  // Basic nutrition information
  calories?: number;
  servingSize?: string;
  servingsPerItem?: number;

  // Macronutrients
  protein?: number; // in grams
  carbs?: number; // in grams
  fat?: number; // in grams

  // Detailed nutrition information
  fiber?: number; // in grams
  sugar?: number; // in grams
  sodium?: number; // in milligrams
  cholesterol?: number; // in milligrams

  // Additional nutrition information
  vitamins?: {
    vitaminA?: number;
    vitaminC?: number;
    vitaminD?: number;
    calcium?: number;
    iron?: number;
  };

  // Health labels
  healthLabels?: string[]; // e.g., "Low Fat", "High Protein", "Low Sodium"

  preparationTime?: string;
  isSignatureDish?: boolean;
  isSeasonalDish?: boolean;
}

// Base restaurant properties shared between Restaurant and RestaurantDetails
export interface BaseRestaurantProps {
  username: string;
  restaurantName: string;
  cuisines: string[];
  categories: string[];
  address: string;
  phone: string;
  description?: string;
  imageUrl?: string;
  location?: Location;
  isOpen: boolean;
  isActive: boolean;
  autoUpdateStatus?: boolean;
  workingHours?: WorkingHours[];
  seating: Seating;
  features?: string[];
  atmosphere?: string[];
  dietary?: string[];
  services?: string[];
  specialties?: string[];
  paymentMethods?: string[];
  certifications?: string[];
  languages?: string[];
  noiseLevel?: "quiet" | "moderate" | "loud";
  dressCode?: "casual" | "smart casual" | "formal";
  reservationPolicy?: "required" | "recommended" | "not required";
  parkingAvailable?: boolean;
}

// Restaurant interface for display and search purposes
export interface Restaurant extends BaseRestaurantProps {
  id: string;
  rating: number;
  distance?: number;
  matchScore?: number;
  menu?: MenuItem[];
}

// Restaurant details interface for management and profile
export interface RestaurantDetails extends BaseRestaurantProps {
  email: string;
  uid: string;
  createdAt: Date | Timestamp;
  isProfileComplete: boolean;
  workingHours: WorkingHours[]; // Required in details
}

// User preferences interface
export interface UserPreferences {
  cuisines: string[];
  categories: string[];
  priceRange: string;
  dietary: string[];
  atmosphere: string[];
  features: string[];
  services: string[];
  specialties: string[];
}

// Address interface
export interface Address {
  street: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  isDefault: boolean;
}

// Meal preferences interface
export interface MealPreferences {
  favoriteIngredients: string[];
  dislikedIngredients: string[];
  allergies: string[];
  dietaryRestrictions: string[];
  mealFrequency: number;
  preferredMealTimes: string[];
  preferredCuisines: string[];
  // Additional fields for recommendations
  preferredCategories: string[];
  preferredPriceRange: string;
  preferredAtmosphere: string[];
  preferredFeatures: string[];
  lastUpdated?: Date | Timestamp;
}

// Calorie calculator interface
export interface CalorieCalculator {
  age: number;
  gender: "male" | "female" | "other";
  weight: number; // in kg
  height: number; // in cm
  activityLevel: "sedentary" | "light" | "moderate" | "active" | "very active";
  goal: "lose" | "maintain" | "gain";
  dailyCalorieGoal: number;
  calculatedAt: Date | Timestamp;
}

// Meal interface
export interface Meal {
  id: string;
  name: string;
  description?: string;
  calories: number;
  protein?: number;
  carbs?: number;
  fat?: number;
  date: Date | Timestamp;
  mealType: "breakfast" | "lunch" | "dinner" | "snack";
  ingredients?: string[];
  restaurantId?: string;
  menuItemId?: string;
  imageUrl?: string;
}

// Meal log interface
export interface MealLog {
  meals: Meal[];
  date: Date | Timestamp;
  totalCalories: number;
  totalProtein?: number;
  totalCarbs?: number;
  totalFat?: number;
  notes?: string;
}

// Dietary goal interface
export interface DietaryGoal {
  id: string;
  type: "calorie" | "protein" | "carbs" | "fat" | "water" | "custom";
  target: number;
  unit: string;
  startDate: Date | Timestamp;
  endDate?: Date | Timestamp;
  progress: number;
  completed: boolean;
  notes?: string;
  dailyTarget?: number; // Daily target for the goal
  trackingEnabled: boolean; // Whether to track this goal
}

// Notification preferences interface
export interface NotificationPreferences {
  email: boolean;
  push: boolean;
  inApp: boolean;
  sound: boolean; // Added sound preference
  orderUpdates: boolean;
  promotions: boolean;
  newRestaurants: boolean;
  weeklyDigest: boolean;
}

// Client details interface
export interface ClientDetails {
  username: string;
  firstName: string;
  lastName: string;
  phone: string;
  email: string;
  uid: string;
  createdAt: Date | Timestamp;
  addresses?: Address[];
  mealPreferences?: MealPreferences;
  calorieCalculator?: CalorieCalculator;
  dietaryGoals?: DietaryGoal[];
  notificationPreferences?: NotificationPreferences;
  favoriteRestaurants?: string[]; // Array of restaurant IDs
  mealLogs?: MealLog[]; // Array of meal logs
  recentMeals?: Meal[]; // Array of recent meals for quick adding

  // Loyalty program fields
  loyaltyEnabled?: boolean; // Whether the user has opted into the loyalty program
  loyaltyJoinedAt?: Date | Timestamp; // When the user joined the loyalty program
  loyaltyTier?: "bronze" | "silver" | "gold" | "platinum"; // Current loyalty tier
  loyaltyPoints?: number; // Current available points
  loyaltyLifetimePoints?: number; // Total points earned over lifetime
  referralCode?: string; // User's unique referral code
  referralCount?: number; // Number of successful referrals
  earnedBadges?: string[]; // Array of earned badge IDs
  completedChallenges?: string[]; // Array of completed challenge IDs
}

// Geocoding result interface
export interface GeocodingResult {
  place_id: string;
  display_name: string;
  lat: string;
  lon: string;
}

// Restaurant options constant
export const RESTAURANT_OPTIONS = {
  cuisines: [
    "Turkish",
    "Azerbaijani",
    "Italian",
    "Japanese",
    "Chinese",
    "Indian",
    "Mexican",
    "French",
    "Mediterranean",
    "Middle Eastern",
    "Korean",
    "Thai",
    "Vietnamese",
    "Greek",
    "Spanish",
    "American",
    "BBQ",
    "Seafood",
    "Steakhouse",
    "Vegetarian",
    "Vegan",
    "Fusion",
    "Peruvian",
    "Brazilian",
    "Russian",
    "Nordic",
  ],
  categories: [
    "Fine Dining",
    "Casual Dining",
    "Fast Food",
    "Café",
    "Bistro",
    "Buffet",
    "Food Court",
    "Street Food",
    "Takeaway",
    "Delivery",
    "Family Style",
    "Pub",
    "Bar & Grill",
    "Food Truck",
    "Pop-up",
    "Tea House",
    "Ice Cream Shop",
    "Bakery",
    "Gastro Pub",
    "Pizza",
    "Sushi",
  ],
  priceRanges: [
    { value: "$", label: "Budget-friendly" },
    { value: "$$", label: "Moderate" },
    { value: "$$$", label: "Upscale" },
    { value: "$$$$", label: "Fine dining" },
  ],
  features: [
    "Wi-Fi",
    "Live Music",
    "Outdoor Seating",
    "Private Dining",
    "Waterfront",
    "Rooftop",
    "Sports TV",
    "Smoking Area",
    "Kids Area",
    "Wheelchair Accessible",
    "Pet Friendly",
    "Catering",
    "Drive-thru",
    "Karaoke",
    "Self-Service",
    "Live Cooking",
    "Open Kitchen",
  ],
  atmosphere: [
    "Romantic",
    "Cozy",
    "Casual",
    "Elegant",
    "Modern",
    "Traditional",
    "Rustic",
    "Trendy",
    "Family-friendly",
    "Business Casual",
    "Intimate",
    "Lively",
    "Quiet",
    "Industrial",
    "Bohemian",
    "High-End",
    "Hipster",
  ],
  dietary: [
    "Vegetarian",
    "Vegan",
    "Gluten-free",
    "Halal",
    "Kosher",
    "Dairy-free",
    "Nut-free",
    "Low-carb",
    "Organic",
    "Raw",
    "Paleo",
    "Keto",
    "Sugar-free",
    "Protein-rich",
    "Diabetic-friendly",
    "Egg-free",
    "Soy-free",
    "Shellfish-free",
    "Fish-free",
    "Wheat-free",
    "Corn-free",
    "Low-sodium",
    "Low-fat",
    "Low-calorie",
    "High-fiber",
    "FODMAP-friendly",
  ],

  // Common allergens for filtering
  allergens: [
    "Milk",
    "Eggs",
    "Fish",
    "Shellfish",
    "Tree Nuts",
    "Peanuts",
    "Wheat",
    "Soy",
    "Sesame",
    "Mustard",
    "Celery",
    "Lupin",
    "Molluscs",
    "Sulphites",
  ],

  // Health labels for menu items
  healthLabels: [
    "Low Fat",
    "Low Sodium",
    "Low Calorie",
    "Low Carb",
    "High Protein",
    "High Fiber",
    "Sugar Free",
    "No Added Sugar",
    "Whole Grain",
    "Heart Healthy",
    "Immunity Boosting",
    "Antioxidant Rich",
    "Superfood",
    "Locally Sourced",
    "Sustainable",
  ],
  services: [
    "Table Service",
    "Counter Service",
    "Delivery",
    "Takeout",
    "Catering",
    "Private Events",
    "Reservations",
    "Walk-ins Welcome",
    "Online Ordering",
    "Mobile Payment",
    "Gift Cards",
    "Loyalty Program",
    "Car-side Pickup",
    "Virtual Menu",
    "Cooking Classes",
    "Meal Subscription",
  ],
  specialties: [
    "Breakfast",
    "Brunch",
    "Lunch",
    "Dinner",
    "Late Night",
    "Desserts",
    "Coffee & Tea",
    "Wine & Beer",
    "Cocktails",
    "Happy Hour",
    "Seasonal Menu",
    "Chef's Specials",
    "Afternoon Tea",
    "Street Food Specials",
    "Signature Dish",
  ],
  paymentMethods: [
    "Cash",
    "Credit Card",
    "Debit Card",
    "Mobile Payment",
    "Digital Wallet",
    "Gift Card",
    "Bank Transfer",
    "Cryptocurrency",
    "Installments",
    "Direct Debit",
    "QR Code Payment",
  ],
  certifications: [
    "None",
    "Health & Safety",
    "Food Safety",
    "Quality Assurance",
    "Environmental",
    "Organic",
    "Sustainable",
    "Fair Trade",
    "ISO Certified",
    "Vegan Certified",
    "Halal Certified",
    "Kosher Certified",
    "Michelin Star",
    "Green Restaurant",
  ],
} as const;
