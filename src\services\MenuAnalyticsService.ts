import { firestore } from "@/config/firebase";
import {
  collection,
  addDoc,
  Timestamp,
  query,
  where,
  getDocs,
} from "firebase/firestore";

export interface MenuItemView {
  restaurantId: string;
  itemId: string;
  timestamp: Timestamp;
  source: string;
}

export interface MenuItemInteraction {
  restaurantId: string;
  itemId: string;
  action: string;
  timestamp: Timestamp;
  source: string;
}

export interface MenuItemAnalytics {
  itemId: string;
  itemName: string;
  viewCount: number;
  clickCount: number;
  addToCartCount: number;
  conversionRate: number; // addToCartCount / viewCount
}

class MenuAnalyticsService {
  /**
   * Track a menu item view
   * @param restaurantId Restaurant ID
   * @param itemId Menu item ID
   * @param source Source of the view (e.g., 'qr_code_menu', 'main_menu')
   */
  async trackMenuItemView(
    restaurantId: string,
    itemId: string,
    source: string
  ): Promise<void> {
    try {
      await addDoc(collection(firestore, "analytics", "menu_views", "items"), {
        restaurantId,
        itemId,
        timestamp: Timestamp.now(),
        source,
      });
    } catch (error) {
      console.error("Error tracking menu item view:", error);
    }
  }

  /**
   * Track a menu item interaction (click, add to cart, etc.)
   * @param restaurantId Restaurant ID
   * @param itemId Menu item ID
   * @param action Action performed (e.g., 'view_details', 'add_to_cart')
   * @param source Source of the interaction (e.g., 'qr_code_menu', 'main_menu')
   */
  async trackMenuItemInteraction(
    restaurantId: string,
    itemId: string,
    action: string,
    source: string
  ): Promise<void> {
    try {
      await addDoc(
        collection(firestore, "analytics", "menu_interactions", "items"),
        {
          restaurantId,
          itemId,
          action,
          timestamp: Timestamp.now(),
          source,
        }
      );
    } catch (error) {
      console.error("Error tracking menu item interaction:", error);
    }
  }

  /**
   * Get analytics for a specific menu item
   * @param restaurantId Restaurant ID
   * @param itemId Menu item ID
   */
  async getMenuItemAnalytics(
    restaurantId: string,
    itemId: string
  ): Promise<{ views: number; interactions: Record<string, number> }> {
    try {
      // Get view count
      const viewsQuery = query(
        collection(firestore, "analytics", "menu_views", "items"),
        where("restaurantId", "==", restaurantId),
        where("itemId", "==", itemId)
      );
      const viewsSnapshot = await getDocs(viewsQuery);
      const viewCount = viewsSnapshot.size;

      // Get interactions
      const interactionsQuery = query(
        collection(firestore, "analytics", "menu_interactions", "items"),
        where("restaurantId", "==", restaurantId),
        where("itemId", "==", itemId)
      );
      const interactionsSnapshot = await getDocs(interactionsQuery);

      // Count interactions by action type
      const interactionCounts: Record<string, number> = {};
      interactionsSnapshot.forEach((doc) => {
        const data = doc.data();
        const action = data.action;
        interactionCounts[action] = (interactionCounts[action] || 0) + 1;
      });

      return {
        views: viewCount,
        interactions: interactionCounts,
      };
    } catch (error) {
      console.error("Error getting menu item analytics:", error);
      return { views: 0, interactions: {} };
    }
  }

  /**
   * Get the most viewed menu items for a restaurant
   * @param restaurantId Restaurant ID
   * @param limit Maximum number of items to return
   */
  async getMostViewedItems(
    restaurantId: string,
    limitCount: number = 10
  ): Promise<Record<string, number>> {
    try {
      const viewsQuery = query(
        collection(firestore, "analytics", "menu_views", "items"),
        where("restaurantId", "==", restaurantId)
      );
      const viewsSnapshot = await getDocs(viewsQuery);

      // Count views by item ID
      const itemViews: Record<string, number> = {};
      viewsSnapshot.forEach((doc) => {
        const data = doc.data();
        const itemId = data.itemId;
        itemViews[itemId] = (itemViews[itemId] || 0) + 1;
      });

      // Sort items by view count and limit the results
      const sortedItems = Object.entries(itemViews)
        .sort(([, countA], [, countB]) => countB - countA)
        .slice(0, limitCount)
        .reduce((obj, [itemId, count]) => {
          obj[itemId] = count;
          return obj;
        }, {} as Record<string, number>);

      return sortedItems;
    } catch (error) {
      console.error("Error getting most viewed items:", error);
      return {};
    }
  }

  /**
   * Get the most added to cart menu items for a restaurant
   * @param restaurantId Restaurant ID
   * @param limit Maximum number of items to return
   */
  async getMostAddedToCartItems(
    restaurantId: string,
    limitCount: number = 10
  ): Promise<Record<string, number>> {
    try {
      const interactionsQuery = query(
        collection(firestore, "analytics", "menu_interactions", "items"),
        where("restaurantId", "==", restaurantId),
        where("action", "==", "add_to_cart")
      );
      const interactionsSnapshot = await getDocs(interactionsQuery);

      // Count add to cart actions by item ID
      const itemCounts: Record<string, number> = {};
      interactionsSnapshot.forEach((doc) => {
        const data = doc.data();
        const itemId = data.itemId;
        itemCounts[itemId] = (itemCounts[itemId] || 0) + 1;
      });

      // Sort items by count and limit the results
      const sortedItems = Object.entries(itemCounts)
        .sort(([, countA], [, countB]) => countB - countA)
        .slice(0, limitCount)
        .reduce((obj, [itemId, count]) => {
          obj[itemId] = count;
          return obj;
        }, {} as Record<string, number>);

      return sortedItems;
    } catch (error) {
      console.error("Error getting most added to cart items:", error);
      return {};
    }
  }

  /**
   * Get conversion rates (views to add to cart) for menu items
   * @param restaurantId Restaurant ID
   */
  async getItemConversionRates(
    restaurantId: string
  ): Promise<Record<string, number>> {
    try {
      // Get view counts
      const viewsQuery = query(
        collection(firestore, "analytics", "menu_views", "items"),
        where("restaurantId", "==", restaurantId)
      );
      const viewsSnapshot = await getDocs(viewsQuery);

      const itemViews: Record<string, number> = {};
      viewsSnapshot.forEach((doc) => {
        const data = doc.data();
        const itemId = data.itemId;
        itemViews[itemId] = (itemViews[itemId] || 0) + 1;
      });

      // Get add to cart counts
      const cartQuery = query(
        collection(firestore, "analytics", "menu_interactions", "items"),
        where("restaurantId", "==", restaurantId),
        where("action", "==", "add_to_cart")
      );
      const cartSnapshot = await getDocs(cartQuery);

      const itemCarts: Record<string, number> = {};
      cartSnapshot.forEach((doc) => {
        const data = doc.data();
        const itemId = data.itemId;
        itemCarts[itemId] = (itemCarts[itemId] || 0) + 1;
      });

      // Calculate conversion rates
      const conversionRates: Record<string, number> = {};
      Object.keys(itemViews).forEach((itemId) => {
        const views = itemViews[itemId] || 0;
        const carts = itemCarts[itemId] || 0;
        conversionRates[itemId] = views > 0 ? carts / views : 0;
      });

      return conversionRates;
    } catch (error) {
      console.error("Error getting item conversion rates:", error);
      return {};
    }
  }
}

export const menuAnalyticsService = new MenuAnalyticsService();
