import {
  Card,
  CardContent,
  CardDescription,
  <PERSON><PERSON><PERSON><PERSON>,
  CardTit<PERSON>,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";

interface Reservation {
  id: string;
  userId: string;
  customerName: string;
  customerPhone: string;
  restaurantId: string;
  tableId?: string;
  tableName?: string;
  date: string;
  arrivalTime: string;
  departureTime?: string;
  partySize: number;
  status: "pending" | "confirmed" | "cancelled" | "no-show";
  createdAt: Date;
  arrivedAt?: Date;
}

interface ReservationsPanelProps {
  reservations: Reservation[];
  onUpdateStatus: (
    reservationId: string,
    status: "confirmed" | "cancelled"
  ) => Promise<void>;
}

export const ReservationsPanel = ({
  reservations,
  onUpdateStatus,
}: ReservationsPanelProps) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Reservations</CardTitle>
        <CardDescription>Manage restaurant reservations</CardDescription>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Date</TableHead>
              <TableHead>Time</TableHead>
              <TableHead>Customer</TableHead>
              <TableHead>Table</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {reservations.map((reservation) => (
              <TableRow key={reservation.id}>
                <TableCell>
                  {new Date(reservation.date).toLocaleDateString()}
                </TableCell>
                <TableCell>{reservation.arrivalTime}</TableCell>
                <TableCell>
                  {reservation.customerName}
                  <br />
                  <span className="text-sm text-muted-foreground">
                    {reservation.customerPhone}
                  </span>
                </TableCell>
                <TableCell>{reservation.tableName}</TableCell>
                <TableCell>
                  <span
                    className={`inline-block px-2 py-1 rounded-full text-xs ${
                      reservation.status === "confirmed"
                        ? "bg-green-100 text-green-800"
                        : reservation.status === "cancelled"
                        ? "bg-red-100 text-red-800"
                        : "bg-yellow-100 text-yellow-800"
                    }`}
                  >
                    {reservation.status.charAt(0).toUpperCase() +
                      reservation.status.slice(1)}
                  </span>
                </TableCell>
                <TableCell>
                  {reservation.status === "pending" && (
                    <div className="space-x-2">
                      <Button
                        variant="default"
                        size="sm"
                        onClick={() =>
                          onUpdateStatus(reservation.id, "confirmed")
                        }
                        className="bg-green-500 hover:bg-green-600"
                      >
                        Confirm
                      </Button>
                      <Button
                        variant="destructive"
                        size="sm"
                        onClick={() =>
                          onUpdateStatus(reservation.id, "cancelled")
                        }
                      >
                        Cancel
                      </Button>
                    </div>
                  )}
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
};
