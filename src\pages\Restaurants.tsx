import { useState, useEffect, useMemo } from "react";
import { useSearchParams } from "react-router-dom";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useGeolocated } from "react-geolocated";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Search,
  Heart,
  MapPin,
  ChevronLeft,
  ChevronRight,
  SlidersHorizontal,
  X,
} from "lucide-react";
import { useFollowedRestaurants } from "@/hooks/useFollowedRestaurants";
import type { Restaurant } from "@/types/restaurant";
import { RestaurantCard } from "@/components/restaurant/RestaurantCard";
import { Skeleton } from "@/components/ui/skeleton";

import { AdvancedFilterPanel } from "@/components/filters/AdvancedFilterPanel";
import { FilterOptions } from "@/services/RestaurantFilterService";
import {
  useAllRestaurants,
  useFilteredRestaurants,
} from "@/lib/react-query/hooks/useRestaurants";

interface UserLocation {
  latitude: number;
  longitude: number;
}
type SortByType = "distance" | "rating" | "name" | "default";
const SORT_OPTIONS: { value: SortByType; label: string }[] = [
  { value: "default", label: "Recommended" },
  { value: "rating", label: "Rating (High to Low)" },
  { value: "name", label: "Name (A-Z)" },
  { value: "distance", label: "Distance (Near to Far)" },
] as const;
const CATEGORIES = [
  /* ... */ "All",
  "Fast Food",
  "Fine Dining",
  "Casual Dining",
  "Cafe",
  "Bistro",
  "Buffet",
  "Street Food",
  "Food Truck",
  "Pizzeria",
  "Steakhouse",
  "Seafood",
  "Bakery",
  "Dessert",
  "Vegan",
  "Vegetarian",
];
const CUISINES = [
  /* ... */ "All",
  "Azerbaijani",
  "Turkish",
  "Italian",
  "Chinese",
  "Japanese",
  "Korean",
  "Mexican",
  "Indian",
  "American",
  "French",
  "Mediterranean",
  "Middle Eastern",
  "Thai",
  "Vietnamese",
  "Greek",
  "Spanish",
];
const RESTAURANTS_PER_PAGE = 12;
const SEARCH_DEBOUNCE_MS = 300;

// Distance calculation is now handled in the RestaurantFilterService

// We're now using React Query hooks instead of this function

export function Restaurants() {
  const [searchParams, setSearchParams] = useSearchParams();

  const [restaurants, setRestaurants] = useState<Restaurant[]>([]);
  const [facets, setFacets] = useState<{
    categories: Record<string, number>;
    cuisines: Record<string, number>;
    dietary: Record<string, number>;
    allergens: Record<string, number>;
    healthLabels: Record<string, number>;
    features: Record<string, number>;
    priceRanges: Record<string, number>;
  }>({
    categories: {},
    cuisines: {},
    dietary: {},
    allergens: {},
    healthLabels: {},
    features: {},
    priceRanges: {},
  });
  const [isLoading, setIsLoading] = useState(true);
  const [userLocation, setUserLocation] = useState<UserLocation | null>(null);
  const [isFilterPanelOpen, setIsFilterPanelOpen] = useState(false);

  // Basic filters
  const [searchTerm, setSearchTerm] = useState(
    () => searchParams.get("search") || ""
  );
  const debouncedSearchTerm = useDebounce(searchTerm, SEARCH_DEBOUNCE_MS);
  const [selectedCategory, setSelectedCategory] = useState(
    () => searchParams.get("category") || "All"
  );
  const [selectedCuisine, setSelectedCuisine] = useState(
    () => searchParams.get("cuisine") || "All"
  );
  const [sortBy, setSortBy] = useState<SortByType>(
    () => (searchParams.get("sortBy") as SortByType) || "distance"
  );
  const [isOpenOnly, setIsOpenOnly] = useState(
    () => searchParams.get("isOpen") === "true"
  );
  const [showFollowingOnly, setShowFollowingOnly] = useState(
    () => searchParams.get("following") === "true"
  );

  // Advanced filters
  const [advancedFilters, setAdvancedFilters] = useState<FilterOptions>({
    priceRanges: searchParams.getAll("price") || [],
    dietary: searchParams.getAll("diet") || [],
    features: searchParams.getAll("feature") || [],
    deliveryTime: [
      parseInt(searchParams.get("minDelivery") || "10"),
      parseInt(searchParams.get("maxDelivery") || "60"),
    ],
    isOpenOnly: searchParams.get("isOpen") === "true",
    parkingAvailable: searchParams.get("parking") === "true",
  });

  const [currentPage, setCurrentPage] = useState(1);

  const { coords, isGeolocationAvailable, positionError } = useGeolocated({
    /* options */
  });
  const {
    followedIds,
    loading: followedLoading,
    isClient,
  } = useFollowedRestaurants();

  // Combine all filters into a single FilterOptions object
  const combinedFilters = useMemo((): FilterOptions => {
    const filters = {
      searchTerm: debouncedSearchTerm,
      categories: selectedCategory !== "All" ? [selectedCategory] : [],
      cuisines: selectedCuisine !== "All" ? [selectedCuisine] : [],
      sortBy: sortBy === "default" ? "rating" : sortBy,
      followedIds:
        showFollowingOnly && followedIds && followedIds.length > 0
          ? followedIds
          : undefined,
      userLocation: userLocation || undefined,
      page: currentPage,
      pageSize: RESTAURANTS_PER_PAGE,
      // Include advanced filters
      ...advancedFilters,
      // Override isOpenOnly if it's set in the basic filters
      isOpenOnly: isOpenOnly || advancedFilters.isOpenOnly,
    };

    return filters;
  }, [
    debouncedSearchTerm,
    selectedCategory,
    selectedCuisine,
    sortBy,
    showFollowingOnly,
    followedIds,
    userLocation,
    currentPage,
    advancedFilters,
    isOpenOnly,
  ]);

  // Fetch all restaurants once for caching
  useAllRestaurants();

  // Fetch filtered restaurants with React Query
  const {
    data: filteredData,
    isLoading: isFilteredLoading,
    isFetching: isFilteredFetching,
  } = useFilteredRestaurants(combinedFilters);

  // Update state when filtered data changes
  useEffect(() => {
    if (filteredData) {
      setRestaurants(filteredData.restaurants);
      setFacets(filteredData.facets);
    }
  }, [filteredData]);

  // Determine if we're loading
  useEffect(() => {
    setIsLoading(isFilteredLoading || isFilteredFetching);
  }, [isFilteredLoading, isFilteredFetching]);

  useEffect(() => {
    if (coords && !userLocation) {
      setUserLocation({
        latitude: coords.latitude,
        longitude: coords.longitude,
      });
    } else if ((!isGeolocationAvailable || positionError) && !userLocation) {
      // Use default location (Baku) if geolocation is unavailable
      setUserLocation({ latitude: 40.3893, longitude: 49.8037 });
    }
  }, [coords, isGeolocationAvailable, positionError, userLocation]);

  // Update URL parameters when filters change
  useEffect(() => {
    const params = new URLSearchParams();

    // Basic filters
    if (debouncedSearchTerm) params.set("search", debouncedSearchTerm);
    if (selectedCategory !== "All") params.set("category", selectedCategory);
    if (selectedCuisine !== "All") params.set("cuisine", selectedCuisine);
    if (sortBy !== "distance") params.set("sortBy", sortBy);
    if (isOpenOnly) params.set("isOpen", "true");
    if (showFollowingOnly) params.set("following", "true");
    if (currentPage > 1) params.set("page", currentPage.toString());

    // Advanced filters
    advancedFilters.priceRanges?.forEach((price) => {
      params.append("price", price);
    });

    advancedFilters.dietary?.forEach((diet) => {
      params.append("diet", diet);
    });

    advancedFilters.features?.forEach((feature) => {
      params.append("feature", feature);
    });

    if (
      advancedFilters.deliveryTime &&
      (advancedFilters.deliveryTime[0] !== 10 ||
        advancedFilters.deliveryTime[1] !== 60)
    ) {
      params.set("minDelivery", advancedFilters.deliveryTime[0].toString());
      params.set("maxDelivery", advancedFilters.deliveryTime[1].toString());
    }

    if (advancedFilters.parkingAvailable) {
      params.set("parking", "true");
    }

    if (
      decodeURIComponent(params.toString()) !==
      decodeURIComponent(searchParams.toString())
    ) {
      setSearchParams(params, { replace: true });
    }
  }, [
    debouncedSearchTerm,
    selectedCategory,
    selectedCuisine,
    sortBy,
    isOpenOnly,
    showFollowingOnly,
    currentPage,
    advancedFilters,
    setSearchParams,
    searchParams,
  ]);

  // Calculate total pages
  const totalPages = useMemo(() => {
    return Math.ceil(restaurants.length / RESTAURANTS_PER_PAGE);
  }, [restaurants.length]);

  // Calculate current items for pagination
  const currentItems = useMemo(() => {
    const indexOfLastItem = currentPage * RESTAURANTS_PER_PAGE;
    const indexOfFirstItem = indexOfLastItem - RESTAURANTS_PER_PAGE;
    return restaurants.slice(indexOfFirstItem, indexOfLastItem);
  }, [restaurants, currentPage]);

  useEffect(() => {
    setCurrentPage(1);
  }, [
    debouncedSearchTerm,
    selectedCategory,
    selectedCuisine,
    sortBy,
    isOpenOnly,
    showFollowingOnly,
  ]);

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
  };

  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= totalPages) {
      setCurrentPage(newPage);

      window.scrollTo({ top: 0, behavior: "smooth" });
    }
  };

  const clearFilters = () => {
    // Clear basic filters
    setSearchTerm("");
    setSelectedCategory("All");
    setSelectedCuisine("All");
    setSortBy("distance");
    setIsOpenOnly(false);
    setShowFollowingOnly(false);

    // Clear advanced filters
    setAdvancedFilters({
      priceRanges: [],
      dietary: [],
      features: [],
      deliveryTime: [10, 60],
      isOpenOnly: false,
      parkingAvailable: false,
    });

    setCurrentPage(1);
    setIsFilterPanelOpen(false);
  };

  // Handle advanced filter changes
  const handleAdvancedFilterChange = (newFilters: FilterOptions) => {
    setAdvancedFilters(newFilters);

    // Sync isOpenOnly between basic and advanced filters
    if (newFilters.isOpenOnly !== isOpenOnly) {
      setIsOpenOnly(newFilters.isOpenOnly || false);
    }
  };

  return (
    <section className="min-h-screen mx-auto px-4 py-6 md:py-8 lg:py-10 pb-20">
      {/* Konum Bekleme */}
      {!userLocation &&
        isGeolocationAvailable &&
        !positionError &&
        isLoading && (
          <div className="text-center text-sm text-muted-foreground mb-4">
            Detecting location...
          </div>
        )}

      {/* Search and Filters */}
      <div className="mb-6">
        <div className="flex flex-wrap items-center gap-4 mb-3">
          {/* Search Bar */}
          <div className="relative flex-1 min-w-[200px]">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground pointer-events-none" />
            <Input
              placeholder="Search restaurants..."
              value={searchTerm}
              onChange={handleSearchChange}
              className="pl-10 h-10 w-full"
              aria-label="Search restaurants by name"
            />
          </div>

          {/* Open Now Button */}
          <Button
            variant={isOpenOnly ? "default" : "outline"}
            className={`h-10 px-3 text-sm ${
              isOpenOnly
                ? "bg-primary text-primary-foreground hover:bg-primary/90"
                : ""
            }`}
            onClick={() => {
              setIsOpenOnly((prev) => !prev);
            }}
          >
            {isOpenOnly && (
              <span className="mr-1.5 h-2 w-2 rounded-full bg-primary-foreground"></span>
            )}
            Open Now
          </Button>

          {/* Following Button (Only for logged-in clients) */}
          {isClient && (
            <Button
              variant={showFollowingOnly ? "default" : "outline"}
              className={`h-10 px-3 text-sm ${
                showFollowingOnly
                  ? "bg-primary text-primary-foreground hover:bg-primary/90"
                  : ""
              }`}
              onClick={() => {
                setShowFollowingOnly((prev) => !prev);
              }}
              disabled={followedLoading}
            >
              <Heart
                className={`h-4 w-4 mr-1.5 ${
                  showFollowingOnly ? "fill-current" : ""
                }`}
              />
              Following
            </Button>
          )}

          {/* Filters Button */}
          <Button
            variant={isFilterPanelOpen ? "default" : "outline"}
            className="h-10 px-3 text-sm"
            onClick={() => setIsFilterPanelOpen(!isFilterPanelOpen)}
          >
            <SlidersHorizontal className="h-4 w-4 mr-1.5" />
            Filters
            {(selectedCategory !== "All" ||
              selectedCuisine !== "All" ||
              sortBy !== "distance" ||
              (advancedFilters.priceRanges?.length || 0) +
                (advancedFilters.dietary?.length || 0) +
                (advancedFilters.features?.length || 0) +
                (advancedFilters.parkingAvailable ? 1 : 0) >
                0) && (
              <span className="ml-1.5 h-2 w-2 rounded-full bg-primary-foreground"></span>
            )}
          </Button>
        </div>
      </div>

      {/* Absolute Positioned Filter Panel */}
      {isFilterPanelOpen && (
        <div className="relative z-50 mb-6">
          <div className="absolute top-0 right-0 left-0 bg-background border rounded-lg shadow-lg p-6 max-h-[80vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-6">
              <div>
                <h3 className="text-lg font-semibold">Filters</h3>
                <p className="text-sm text-muted-foreground">
                  Refine your restaurant search with these filters
                </p>
              </div>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setIsFilterPanelOpen(false)}
                className="h-8 w-8"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>

            {/* Basic Filters */}
            <div className="space-y-6 mb-8">
              <div className="space-y-4">
                <h4 className="text-sm font-medium">Basic Filters</h4>

                {/* Category Filter */}
                <div className="space-y-2">
                  <label className="text-sm text-muted-foreground">
                    Category
                  </label>
                  <Select
                    value={selectedCategory}
                    onValueChange={(v) => {
                      setSelectedCategory(v);
                    }}
                  >
                    <SelectTrigger className="h-10 w-full">
                      <SelectValue placeholder="Category" />
                    </SelectTrigger>
                    <SelectContent>
                      {CATEGORIES.map((c) => (
                        <SelectItem key={c} value={c}>
                          {c}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Cuisine Filter */}
                <div className="space-y-2">
                  <label className="text-sm text-muted-foreground">
                    Cuisine
                  </label>
                  <Select
                    value={selectedCuisine}
                    onValueChange={(v) => {
                      setSelectedCuisine(v);
                    }}
                  >
                    <SelectTrigger className="h-10 w-full">
                      <SelectValue placeholder="Cuisine" />
                    </SelectTrigger>
                    <SelectContent>
                      {CUISINES.map((c) => (
                        <SelectItem key={c} value={c}>
                          {c}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Sort By Filter */}
                <div className="space-y-2">
                  <label className="text-sm text-muted-foreground">
                    Sort By
                  </label>
                  <Select
                    value={sortBy}
                    onValueChange={(v) => {
                      setSortBy(v as SortByType);
                    }}
                  >
                    <SelectTrigger className="h-10 w-full">
                      <SelectValue placeholder="Sort by" />
                    </SelectTrigger>
                    <SelectContent>
                      {SORT_OPTIONS.map((o) => (
                        <SelectItem
                          key={o.value}
                          value={o.value}
                          disabled={o.value === "distance" && !userLocation}
                        >
                          {o.label}
                          {o.value === "distance" && !userLocation
                            ? " (Loc Needed)"
                            : ""}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>

            {/* Advanced Filters */}
            <AdvancedFilterPanel
              filters={advancedFilters}
              onChange={handleAdvancedFilterChange}
              facets={facets}
              onApply={() => setIsFilterPanelOpen(false)}
              onReset={clearFilters}
            />

            {/* No additional action buttons needed here, they're already in the AdvancedFilterPanel */}
          </div>
        </div>
      )}

      {/* Grid / Yükleme / Sonuç Yok */}
      {isLoading ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 md:gap-5">
          {Array.from({ length: 8 }).map((_, index) => (
            <Card key={index} className="overflow-hidden">
              <Skeleton className="aspect-video w-full" />
              <CardContent className="p-4 space-y-2">
                <Skeleton className="h-5 w-3/4" />
                <Skeleton className="h-4 w-1/2" />
                <Skeleton className="h-4 w-full" />
              </CardContent>
            </Card>
          ))}
        </div>
      ) : restaurants.length > 0 ? (
        <div className="grid sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-5">
          {currentItems.map((restaurant) => (
            <RestaurantCard key={restaurant.id} restaurant={restaurant} />
          ))}
        </div>
      ) : (
        <div className="col-span-full text-center py-16 px-6 border rounded-lg bg-card mt-6">
          <MapPin className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-xl font-semibold text-foreground mb-2">
            No Restaurants Found
          </h3>
          <p className="text-muted-foreground">
            Your search and filter combination didn't return any results.
          </p>
          <Button
            variant="outline"
            size="sm"
            className="mt-4"
            onClick={clearFilters}
          >
            Clear Filters
          </Button>
        </div>
      )}

      {/* Client-Side Pagination Controls */}
      {!isLoading && restaurants.length > 0 && (
        <div className="mt-8 flex justify-center items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePageChange(currentPage - 1)}
            disabled={currentPage === 1}
            aria-label="Previous page"
          >
            <ChevronLeft className="h-4 w-4 mr-1" /> Previous
          </Button>
          <span className="text-sm font-medium px-4 py-2 border rounded-md">
            Page {currentPage} of {totalPages || 1}
          </span>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePageChange(currentPage + 1)}
            disabled={
              currentPage === totalPages ||
              restaurants.length <= RESTAURANTS_PER_PAGE
            }
            aria-label="Next page"
          >
            Next <ChevronRight className="h-4 w-4 ml-1" />
          </Button>
        </div>
      )}
    </section>
  );
}

function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);
  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);
    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);
  return debouncedValue;
}
