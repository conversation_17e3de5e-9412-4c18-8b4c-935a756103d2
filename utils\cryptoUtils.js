/**
 * Crypto utilities
 */
const crypto = require('crypto');

/**
 * Generate a random string
 * @param {number} length - Length of the string
 * @returns {string} - Random string
 */
const generateRandomString = (length = 32) => {
  return crypto.randomBytes(Math.ceil(length / 2))
    .toString('hex')
    .slice(0, length);
};

/**
 * Generate a random OTP
 * @param {number} length - Length of the OTP
 * @returns {string} - Random OTP
 */
const generateOtp = (length = 6) => {
  const min = Math.pow(10, length - 1);
  const max = Math.pow(10, length) - 1;
  return Math.floor(min + Math.random() * (max - min + 1)).toString();
};

/**
 * Hash a string
 * @param {string} data - String to hash
 * @returns {string} - Hashed string
 */
const hashString = (data) => {
  return crypto.createHash('sha256').update(data).digest('hex');
};

module.exports = {
  generateRandomString,
  generateOtp,
  hashString,
};
