import { Order as DashboardOrder } from "@/types/dashboard";
import {
  Order as ServiceOrder,
  OrderItem as ServiceOrderItem,
} from "@/types/order";

/**
 * Converts a Dashboard Order to a Service Order
 */
export function convertToServiceOrder(
  dashboardOrder: DashboardOrder
): ServiceOrder {
  // Convert items
  const items: ServiceOrderItem[] = dashboardOrder.items.map((item) => ({
    id: item.itemId || `item-${Math.random().toString(36).substring(2, 9)}`,
    menuItemId:
      item.itemId || `item-${Math.random().toString(36).substring(2, 9)}`,
    name: item.name,
    price: item.price,
    quantity: item.quantity,
    notes: item.notes,
  }));

  // Create service order
  return {
    id: dashboardOrder.orderId,
    restaurantId: dashboardOrder.restaurantId,
    userId: dashboardOrder.userId,
    items: items,
    status: dashboardOrder.status,
    total: dashboardOrder.totalPrice,
    createdAt: dashboardOrder.createdAt || dashboardOrder.orderDate,
    updatedAt: dashboardOrder.updatedAt || dashboardOrder.orderDate,
    notes: dashboardOrder.notes,
    isScheduled: dashboardOrder.isScheduled,
    scheduledFor: dashboardOrder.scheduledFor,
    reminderSent: dashboardOrder.reminderSent,
  };
}
