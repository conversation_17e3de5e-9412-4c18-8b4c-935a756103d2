export interface Table {
  id: string;
  capacity: number;
  name: string;
}

export interface WorkingHours {
  day:
    | "monday"
    | "tuesday"
    | "wednesday"
    | "thursday"
    | "friday"
    | "saturday"
    | "sunday";
  isOpen: boolean;
  openTime: string;
  closeTime: string;
}

export interface Restaurant {
  id: string;
  restaurantName: string;
  username: string;
  uid: string;
  address: string;
  phone: string;
  category: string;
  cuisine: string;
  isOpen: boolean;
  imageUrl?: string;
  rating: number;
  workingHours: WorkingHours[];
  autoUpdateStatus: boolean;
}

export interface LayoutElement {
  id: string;
  name: string;
  type: string;
  x: number;
  y: number;
  width: number;
  height: number;
  rotation: number;
  borderRadius: number;
  capacity?: number;
  tableId?: string;
}

export interface Reservation {
  id: string;
  userId: string;
  customerName: string;
  customerPhone: string;
  restaurantId: string;
  tableId?: string;
  tableName?: string;
  date: string;
  arrivalTime: string;
  departureTime?: string;
  partySize: number;
  status:
    | "pending"
    | "confirmed"
    | "cancelled"
    | "no-show"
    | "active"
    | "completed";
  createdAt: Date;
  arrivedAt?: Date;
}
