import { CollapsibleCard } from "@/components/ui/collapsible-card";
import { Restaurant } from "@/types/restaurant";
import { Tag, Users, CreditCard } from "lucide-react";

interface RestaurantFeaturesProps {
  restaurant: Restaurant;
}

const TagList = ({
  title,
  items,
}: {
  title: string;
  items?: string[] | null;
}) => {
  if (!items || items.length === 0) return null;

  return (
    <div className="p-2 sm:p-3 bg-muted/50 rounded-lg overflow-hidden">
      <div className="font-semibold mb-1.5 sm:mb-2 text-xs sm:text-sm text-foreground/80">
        {title}
      </div>
      <div className="flex flex-wrap gap-1.5 sm:gap-2">
        {items.map((item, index) => (
          <div
            key={`${title}-${index}`}
            className="px-2 sm:px-3 py-0.5 sm:py-1 bg-primary/10 text-primary font-medium rounded-full text-xs truncate max-w-[95%] inline-block"
          >
            {item}
          </div>
        ))}
      </div>
    </div>
  );
};

const InfoItem = ({
  title,
  value,
}: {
  title: string;
  value?: string | null;
}) => {
  const displayValue = value || "Not specified";
  const isNotSpecified = !value;

  return (
    <div className="flex flex-col p-2 sm:p-3 bg-muted/50 rounded-lg">
      <div className="font-semibold mb-0.5 sm:mb-1 text-xs sm:text-sm text-foreground/80">
        {title}
      </div>
      <div
        className={`capitalize text-xs sm:text-sm break-words ${isNotSpecified ? "text-muted-foreground italic" : ""}`}
      >
        {displayValue}
      </div>
    </div>
  );
};

export const RestaurantFeatures = ({ restaurant }: RestaurantFeaturesProps) => {
  const hasFeaturesContent =
    (restaurant.features && restaurant.features.length > 0) ||
    (restaurant.atmosphere && restaurant.atmosphere.length > 0) ||
    restaurant.noiseLevel ||
    restaurant.dressCode;

  const hasServicesContent =
    (restaurant.services && restaurant.services.length > 0) ||
    (restaurant.specialties && restaurant.specialties.length > 0) ||
    restaurant.reservationPolicy;

  const hasPaymentContent =
    (restaurant.paymentMethods && restaurant.paymentMethods.length > 0) ||
    (restaurant.languages && restaurant.languages.length > 0);

  return (
    <div className="space-y-4 w-full">
      {hasFeaturesContent && (
        <CollapsibleCard
          title="Features & Amenities"
          icon={<Tag size={20} className="text-primary" />}
        >
          <div className="grid gap-4">
            <TagList title="Features" items={restaurant.features} />
            <TagList title="Atmosphere" items={restaurant.atmosphere} />
            {(restaurant.noiseLevel || restaurant.dressCode) && (
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <InfoItem title="Noise Level" value={restaurant.noiseLevel} />
                <InfoItem title="Dress Code" value={restaurant.dressCode} />
              </div>
            )}
          </div>
        </CollapsibleCard>
      )}

      {hasServicesContent && (
        <CollapsibleCard
          title="Services & Policies"
          icon={<Users size={20} className="text-primary" />}
        >
          <div className="grid gap-4">
            <TagList title="Services" items={restaurant.services} />
            <TagList title="Specialties" items={restaurant.specialties} />
            <InfoItem
              title="Reservation Policy"
              value={restaurant.reservationPolicy}
            />
          </div>
        </CollapsibleCard>
      )}

      {hasPaymentContent && (
        <CollapsibleCard
          title="Payment & Languages"
          icon={<CreditCard size={20} className="text-primary" />}
        >
          <div className="grid gap-4">
            <TagList
              title="Payment Methods"
              items={restaurant.paymentMethods}
            />
            <TagList title="Languages" items={restaurant.languages} />
          </div>
        </CollapsibleCard>
      )}
    </div>
  );
};
