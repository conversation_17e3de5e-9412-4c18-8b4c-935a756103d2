import { Link } from "react-router-dom";
import { CheckCircle } from "lucide-react";

const Success = () => {
  return (
    <div className="min-h-screen w-full flex items-center justify-center">
      <div className="text-center p-8 max-w-md">
        <div className="flex justify-center mb-6">
          <CheckCircle className="w-24 h-24 text-green-500" />
        </div>
        <h1 className="text-4xl font-bold text-green-700 mb-4">Success!</h1>
        <p className="text-gray-600 mb-8">
          Your transaction has been completed successfully. Thank you for using
          our service.
        </p>
        <Link
          to="/"
          className="inline-flex items-center px-6 py-3 text-base font-medium text-white bg-green-600 rounded-lg hover:bg-green-700 transition-colors duration-200"
        >
          Return to Home
        </Link>
      </div>
    </div>
  );
};

export default Success;
