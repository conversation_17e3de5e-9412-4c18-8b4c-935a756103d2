// PreferencesSurvey.tsx
import React, { useState, useEffect } from "react";
import {
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { firestore } from "@/config/firebase";
import { doc, updateDoc, deleteField, getDoc } from "firebase/firestore";
import { GoogleGenerativeAI } from "@google/generative-ai";
import { toast } from "sonner";
import { Loading } from "@/components/ui/loading";
import { useAuth } from "@/providers/AuthProvider";

const CUISINES = [
  "Azerbaijani",
  "Turkish",
  "Italian",
  "Chinese",
  "Japanese",
  "Korean",
  "Mexican",
  "Indian",
  "American",
  "French",
  "Mediterranean",
  "Middle Eastern",
  "Thai",
  "Vietnamese",
  "Greek",
  "Spanish",
  "Other",
];

const CATEGORIES = [
  "Fast Food",
  "Fine Dining",
  "Casual Dining",
  "Cafe",
  "Bistro",
  "Buffet",
  "Street Food",
  "Food Truck",
  "Pizzeria",
  "Steakhouse",
  "Seafood",
  "Bakery",
  "Dessert",
  "Vegan",
  "Vegetarian",
];

const PRICE_RANGES = ["₼10-20", "₼20-50", "₼50-100", "₼100+"];

const DIETARY_RESTRICTIONS = [
  "None",
  "Vegetarian",
  "Vegan",
  "Gluten-Free",
  "Halal",
  "Kosher",
  "Dairy-Free",
  "Nut-Free",
];

const ATMOSPHERE_PREFERENCES = [
  "Quiet and Intimate",
  "Lively and Energetic",
  "Family-Friendly",
  "Business-Casual",
  "Romantic",
  "Outdoor Seating",
];

const SPECIAL_FEATURES = [
  "Live Music",
  "Sports Screening",
  "Private Dining Rooms",
  "View/Scenery",
  "Kid's Play Area",
  "Wheelchair Accessible",
];

interface PreferencesSurveyProps {
  onClose: () => void;
}

interface PreferencesData {
  cuisines: string[];
  categories: string[];
  priceRange: string;
  dietary: string[];
  atmosphere: string[];
  features: string[];
}

interface StoredPreferences extends PreferencesData {
  summary: string;
  updatedAt: Date;
}

const PreferencesSurvey: React.FC<PreferencesSurveyProps> = ({ onClose }) => {
  const { user } = useAuth();
  const [step, setStep] = useState(1);
  const [cuisinePreferences, setCuisinePreferences] = useState<string[]>([]);
  const [categoryPreferences, setCategoryPreferences] = useState<string[]>([]);
  const [priceRangePreference, setPriceRangePreference] = useState<
    string | null
  >(null);
  const [dietaryRestrictions, setDietaryRestrictions] = useState<string[]>([]);
  const [atmospherePreferences, setAtmospherePreferences] = useState<string[]>(
    []
  );
  const [specialFeatures, setSpecialFeatures] = useState<string[]>([]);
  const [summary, setSummary] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [hasExistingPreferences, setHasExistingPreferences] = useState(false);

  useEffect(() => {
    const fetchPreferences = async () => {
      if (!user) {
        setIsLoading(false);
        return;
      }

      try {
        const userDocRef = doc(firestore, "clients", user.uid);
        const docSnap = await getDoc(userDocRef);

        if (docSnap.exists() && docSnap.data().preferences) {
          const preferences = docSnap.data().preferences as StoredPreferences;

          // Set all the preferences
          setCuisinePreferences(preferences.cuisines);
          setCategoryPreferences(preferences.categories);
          setPriceRangePreference(preferences.priceRange);
          setDietaryRestrictions(preferences.dietary);
          setAtmospherePreferences(preferences.atmosphere);
          setSpecialFeatures(preferences.features);
          setSummary(preferences.summary);

          setHasExistingPreferences(true);
          setStep(7); // Show summary step
        }
      } catch (error) {
        console.error("Error fetching preferences:", error);
        toast.error("Failed to load preferences");
      } finally {
        setIsLoading(false);
      }
    };

    fetchPreferences();
  }, [user]);

  const handleNext = () => {
    // Validate current step before proceeding
    if (
      (step === 1 && cuisinePreferences.length === 0) ||
      (step === 2 && categoryPreferences.length === 0) ||
      (step === 3 && !priceRangePreference) ||
      (step === 4 && dietaryRestrictions.length === 0) ||
      (step === 5 && atmospherePreferences.length === 0) ||
      (step === 6 && specialFeatures.length === 0)
    ) {
      toast.error("Please select at least one option");
      return;
    }
    setStep(step + 1);
  };

  const handlePrevious = () => {
    setStep(step - 1);
  };

  const handleSubmit = async () => {
    if (!user) {
      toast.error("You must be logged in to save preferences");
      return;
    }

    setIsLoading(true);

    async function generateSummary(
      preferences: PreferencesData
    ): Promise<string> {
      const apiKey = import.meta.env.VITE_APP_GEMINI_API_KEY;
      const genAI = new GoogleGenerativeAI(apiKey);

      const model = genAI.getGenerativeModel({
        model: "gemini-2.0-flash",
      });

      try {
        const prompt = `Create a detailed summary of restaurant preferences based on the following criteria:
        - Preferred Cuisines: ${preferences.cuisines.join(", ")}
        - Restaurant Categories: ${preferences.categories.join(", ")}
        - Price Range: ${preferences.priceRange}
        - Dietary Restrictions: ${preferences.dietary.join(", ")}
        - Preferred Atmospheres: ${preferences.atmosphere.join(", ")}
        - Special Features: ${preferences.features.join(", ")}

        Please provide a comprehensive yet concise summary that captures these preferences and suggests what kind of dining experience would be ideal for this person. Keep the response under 200 words.`;

        const result = await model.generateContent(prompt);
        const response = result.response;
        return response.text();
      } catch (error) {
        console.error("Gemini API error:", error);
        toast.error("Failed to generate summary");
        return "Unable to generate summary. Please try again.";
      }
    }

    if (
      cuisinePreferences.length > 0 &&
      categoryPreferences.length > 0 &&
      priceRangePreference &&
      dietaryRestrictions.length > 0 &&
      atmospherePreferences.length > 0 &&
      specialFeatures.length > 0
    ) {
      const preferences = {
        cuisines: cuisinePreferences,
        categories: categoryPreferences,
        priceRange: priceRangePreference,
        dietary: dietaryRestrictions,
        atmosphere: atmospherePreferences,
        features: specialFeatures,
      };

      const generatedSummary = await generateSummary(preferences);
      setSummary(generatedSummary);

      // Save to user's document in clients collection
      try {
        const userDocRef = doc(firestore, "clients", user.uid);
        await updateDoc(userDocRef, {
          preferences: {
            ...preferences,
            summary: generatedSummary,
            updatedAt: new Date(),
          },
        });
        toast.success("Preferences saved successfully!");
      } catch (error) {
        console.error("Firestore error:", error);
        toast.error("Failed to save preferences");
      }

      setStep(7); // Go to summary step
    } else {
      toast.error("Please complete all sections");
    }
    setIsLoading(false);
  };

  const handleDelete = async () => {
    if (!user) {
      toast.error("You must be logged in to delete preferences");
      return;
    }

    setIsLoading(true);
    try {
      const userDocRef = doc(firestore, "clients", user.uid);
      await updateDoc(userDocRef, {
        preferences: deleteField(),
      });

      // Reset all state
      setCuisinePreferences([]);
      setCategoryPreferences([]);
      setPriceRangePreference(null);
      setDietaryRestrictions([]);
      setAtmospherePreferences([]);
      setSpecialFeatures([]);
      setSummary(null);

      // Go back to first step
      setStep(1);

      toast.success("Preferences deleted successfully!");
    } catch (error) {
      console.error("Firestore error:", error);
      toast.error("Failed to delete preferences");
    }
    setIsLoading(false);
  };

  return (
    <DialogContent className="h-[80vh] overflow-y-auto">
      <DialogHeader>
        <DialogTitle>Preferences Survey</DialogTitle>
        <DialogDescription>
          {!hasExistingPreferences
            ? "Please answer the following questions to help us personalize your experience. You can select multiple options where applicable."
            : "Your dining preferences are shown below. You can delete them and create new ones."}
        </DialogDescription>
      </DialogHeader>

      {isLoading ? (
        <div className="flex justify-center items-center min-h-[200px]">
          <Loading type="default" />
        </div>
      ) : (
        <>
          {step === 1 && (
            <div>
              <p>
                What types of cuisine do you prefer? (Select all that apply)
              </p>
              <div className="grid grid-cols-2 gap-4">
                {CUISINES.map((cuisine) => (
                  <div className="flex items-center space-x-2" key={cuisine}>
                    <Checkbox
                      id={cuisine}
                      checked={cuisinePreferences.includes(cuisine)}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          setCuisinePreferences([
                            ...cuisinePreferences,
                            cuisine,
                          ]);
                        } else {
                          setCuisinePreferences(
                            cuisinePreferences.filter((c) => c !== cuisine)
                          );
                        }
                      }}
                    />
                    <Label htmlFor={cuisine}>{cuisine}</Label>
                  </div>
                ))}
              </div>
            </div>
          )}

          {step === 2 && (
            <div>
              <p>
                What categories of restaurants do you prefer? (Select all that
                apply)
              </p>
              <div className="grid grid-cols-2 gap-4">
                {CATEGORIES.map((category) => (
                  <div className="flex items-center space-x-2" key={category}>
                    <Checkbox
                      id={category}
                      checked={categoryPreferences.includes(category)}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          setCategoryPreferences([
                            ...categoryPreferences,
                            category,
                          ]);
                        } else {
                          setCategoryPreferences(
                            categoryPreferences.filter((c) => c !== category)
                          );
                        }
                      }}
                    />
                    <Label htmlFor={category}>{category}</Label>
                  </div>
                ))}
              </div>
            </div>
          )}

          {step === 3 && (
            <div>
              <p>What is your preferred price range?</p>
              <RadioGroup
                defaultValue={priceRangePreference || undefined}
                onValueChange={setPriceRangePreference}
              >
                {PRICE_RANGES.map((priceRange) => (
                  <div className="flex items-center space-x-2" key={priceRange}>
                    <RadioGroupItem value={priceRange} id={priceRange} />
                    <Label htmlFor={priceRange}>{priceRange}</Label>
                  </div>
                ))}
              </RadioGroup>
            </div>
          )}

          {step === 4 && (
            <div>
              <p>
                Do you have any dietary restrictions? (Select all that apply)
              </p>
              <div className="grid grid-cols-2 gap-4">
                {DIETARY_RESTRICTIONS.map((restriction) => (
                  <div
                    className="flex items-center space-x-2"
                    key={restriction}
                  >
                    <Checkbox
                      id={restriction}
                      checked={dietaryRestrictions.includes(restriction)}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          setDietaryRestrictions([
                            ...dietaryRestrictions,
                            restriction,
                          ]);
                        } else {
                          setDietaryRestrictions(
                            dietaryRestrictions.filter((r) => r !== restriction)
                          );
                        }
                      }}
                    />
                    <Label htmlFor={restriction}>{restriction}</Label>
                  </div>
                ))}
              </div>
            </div>
          )}

          {step === 5 && (
            <div>
              <p>
                What types of atmosphere do you prefer? (Select all that apply)
              </p>
              <div className="grid grid-cols-2 gap-4">
                {ATMOSPHERE_PREFERENCES.map((atmosphere) => (
                  <div className="flex items-center space-x-2" key={atmosphere}>
                    <Checkbox
                      id={atmosphere}
                      checked={atmospherePreferences.includes(atmosphere)}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          setAtmospherePreferences([
                            ...atmospherePreferences,
                            atmosphere,
                          ]);
                        } else {
                          setAtmospherePreferences(
                            atmospherePreferences.filter(
                              (a) => a !== atmosphere
                            )
                          );
                        }
                      }}
                    />
                    <Label htmlFor={atmosphere}>{atmosphere}</Label>
                  </div>
                ))}
              </div>
            </div>
          )}

          {step === 6 && (
            <div>
              <p>
                What special features are you interested in? (Select all that
                apply)
              </p>
              <div className="grid grid-cols-2 gap-4">
                {SPECIAL_FEATURES.map((feature) => (
                  <div className="flex items-center space-x-2" key={feature}>
                    <Checkbox
                      id={feature}
                      checked={specialFeatures.includes(feature)}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          setSpecialFeatures([...specialFeatures, feature]);
                        } else {
                          setSpecialFeatures(
                            specialFeatures.filter((f) => f !== feature)
                          );
                        }
                      }}
                    />
                    <Label htmlFor={feature}>{feature}</Label>
                  </div>
                ))}
              </div>
            </div>
          )}
        </>
      )}

      {!isLoading && step === 7 && (
        <div className="space-y-4">
          <div className="space-y-2">
            <h3 className="font-medium">Your Dining Preferences Summary:</h3>
            {summary && (
              <>
                <p className="text-sm text-gray-600">{summary}</p>
                <div className="pt-4 space-y-2">
                  <h4 className="text-sm font-medium">Selected Preferences:</h4>
                  <div className="text-sm text-gray-600 space-y-1">
                    <p>
                      <strong>Cuisines:</strong> {cuisinePreferences.join(", ")}
                    </p>
                    <p>
                      <strong>Categories:</strong>{" "}
                      {categoryPreferences.join(", ")}
                    </p>
                    <p>
                      <strong>Price Range:</strong> {priceRangePreference}
                    </p>
                    <p>
                      <strong>Dietary Restrictions:</strong>{" "}
                      {dietaryRestrictions.join(", ")}
                    </p>
                    <p>
                      <strong>Atmosphere:</strong>{" "}
                      {atmospherePreferences.join(", ")}
                    </p>
                    <p>
                      <strong>Special Features:</strong>{" "}
                      {specialFeatures.join(", ")}
                    </p>
                  </div>
                </div>
              </>
            )}
          </div>
          <div className="flex justify-end space-x-2">
            <Button
              type="button"
              variant="destructive"
              onClick={handleDelete}
              disabled={isLoading}
            >
              Delete & Start Over
            </Button>
            <Button type="button" onClick={onClose}>
              Close
            </Button>
          </div>
        </div>
      )}

      <DialogFooter>
        {!isLoading && step > 1 && step < 7 && (
          <Button type="button" variant="secondary" onClick={handlePrevious}>
            Previous
          </Button>
        )}

        {!isLoading && step < 6 && (
          <Button type="button" onClick={handleNext}>
            Next
          </Button>
        )}

        {!isLoading && step === 6 && (
          <Button type="button" onClick={handleSubmit} disabled={isLoading}>
            {isLoading ? "Generating..." : "Generate Summary"}
          </Button>
        )}
      </DialogFooter>
    </DialogContent>
  );
};

export default PreferencesSurvey;
