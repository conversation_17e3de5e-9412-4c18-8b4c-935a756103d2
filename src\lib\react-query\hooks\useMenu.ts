import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import {
  collection,
  query,
  getDocs,
  doc,
  updateDoc,
  addDoc,
  deleteDoc,
  onSnapshot,
  orderBy,
  FirestoreError,
} from "firebase/firestore";
import { firestore } from "@/config/firebase";
import { MenuItem } from "@/types/restaurant";
import { queryKeys, CACHE_TIME, STALE_TIME } from "../index";
import { useEffect, useState } from "react";
import { toast } from "sonner";

/**
 * Hook to fetch menu items for a restaurant with caching
 */
export function useMenuItems(restaurantId: string | undefined) {
  return useQuery({
    queryKey: queryKeys.restaurants.menu(restaurantId || ""),
    queryFn: async () => {
      if (!restaurantId) {
        throw new Error("Restaurant ID is required");
      }

      const menuRef = collection(
        doc(firestore, "restaurants", restaurantId),
        "menu"
      );
      const q = query(menuRef, orderBy("category"), orderBy("name"));
      const querySnapshot = await getDocs(q);

      const menuItems: MenuItem[] = [];
      querySnapshot.forEach((doc) => {
        menuItems.push({
          itemId: doc.id,
          ...doc.data(),
          restaurantId, // Ensure restaurantId is included
        } as MenuItem);
      });

      return menuItems;
    },
    enabled: !!restaurantId,
    staleTime: STALE_TIME,
    gcTime: CACHE_TIME,
  });
}

/**
 * Hook to fetch menu items with real-time updates
 */
export function useRealtimeMenu(restaurantId: string | undefined) {
  const [menu, setMenu] = useState<MenuItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<FirestoreError | null>(null);

  useEffect(() => {
    if (!restaurantId) {
      setIsLoading(false);
      setMenu([]);
      setError(null);
      return;
    }

    setIsLoading(true);
    setError(null);

    const menuRef = collection(
      doc(firestore, "restaurants", restaurantId),
      "menu"
    );
    const q = query(menuRef, orderBy("category"), orderBy("name"));

    const unsubscribe = onSnapshot(
      q,
      (snapshot) => {
        const menuItems = snapshot.docs.map((doc) => ({
          itemId: doc.id,
          ...doc.data(),
          restaurantId,
        })) as MenuItem[];

        setMenu(menuItems);
        setIsLoading(false);
      },
      (err: FirestoreError) => {
        console.error("Error fetching menu:", err);
        setError(err);
        toast.error("Failed to load menu items");
        setIsLoading(false);
      }
    );

    return () => unsubscribe();
  }, [restaurantId]);

  return { menu, isLoading, error };
}

/**
 * Hook to add a menu item with optimistic updates
 */
export function useAddMenuItem() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      restaurantId,
      item,
    }: {
      restaurantId: string;
      item: Omit<MenuItem, "itemId" | "restaurantId">;
    }) => {
      const menuRef = collection(
        doc(firestore, "restaurants", restaurantId),
        "menu"
      );
      const docRef = await addDoc(menuRef, {
        ...item,
        restaurantId,
        createdAt: new Date(),
      });

      return {
        itemId: docRef.id,
        ...item,
        restaurantId,
      } as MenuItem;
    },
    onMutate: async ({ restaurantId, item }) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({
        queryKey: queryKeys.restaurants.menu(restaurantId),
      });

      // Snapshot the previous value
      const previousMenu = queryClient.getQueryData<MenuItem[]>(
        queryKeys.restaurants.menu(restaurantId)
      );

      // Create a temporary ID for optimistic update
      const tempId = `temp_${Date.now()}`;

      // Optimistically update to the new value
      if (previousMenu) {
        queryClient.setQueryData(queryKeys.restaurants.menu(restaurantId), [
          ...previousMenu,
          {
            itemId: tempId,
            ...item,
            restaurantId,
          } as MenuItem,
        ]);
      }

      return { previousMenu, tempId };
    },
    onError: (_, { restaurantId }, context) => {
      // If the mutation fails, use the context returned from onMutate to roll back
      if (context?.previousMenu) {
        queryClient.setQueryData(
          queryKeys.restaurants.menu(restaurantId),
          context.previousMenu
        );
      }

      toast.error("Failed to add menu item");
    },
    onSuccess: () => {
      toast.success("Menu item added successfully");
    },
    onSettled: (_, __, { restaurantId }) => {
      // Always refetch after error or success to make sure our local data is correct
      queryClient.invalidateQueries({
        queryKey: queryKeys.restaurants.menu(restaurantId),
      });
    },
  });
}

/**
 * Hook to update a menu item with optimistic updates
 */
export function useUpdateMenuItem() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (item: MenuItem) => {
      const { itemId, restaurantId } = item;
      const menuItemRef = doc(
        collection(doc(firestore, "restaurants", restaurantId), "menu"),
        itemId
      );

      // Extract the update data without the itemId
      const { ...updateData } = item;

      await updateDoc(menuItemRef, {
        ...updateData,
        updatedAt: new Date(),
      });

      return item;
    },
    onMutate: async (item) => {
      const { itemId, restaurantId } = item;

      // Cancel any outgoing refetches
      await queryClient.cancelQueries({
        queryKey: queryKeys.restaurants.menu(restaurantId),
      });

      // Snapshot the previous value
      const previousMenu = queryClient.getQueryData<MenuItem[]>(
        queryKeys.restaurants.menu(restaurantId)
      );

      // Optimistically update to the new value
      if (previousMenu) {
        queryClient.setQueryData(
          queryKeys.restaurants.menu(restaurantId),
          previousMenu.map((menuItem) =>
            menuItem.itemId === itemId ? item : menuItem
          )
        );
      }

      return { previousMenu };
    },
    onError: (_, item, context) => {
      // If the mutation fails, use the context returned from onMutate to roll back
      if (context?.previousMenu) {
        queryClient.setQueryData(
          queryKeys.restaurants.menu(item.restaurantId),
          context.previousMenu
        );
      }

      toast.error("Failed to update menu item");
    },
    onSuccess: () => {
      toast.success("Menu item updated successfully");
    },
    onSettled: (_, __, item) => {
      // Always refetch after error or success to make sure our local data is correct
      queryClient.invalidateQueries({
        queryKey: queryKeys.restaurants.menu(item.restaurantId),
      });
    },
  });
}

/**
 * Hook to delete a menu item with optimistic updates
 */
export function useDeleteMenuItem() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      restaurantId,
      itemId,
    }: {
      restaurantId: string;
      itemId: string;
    }) => {
      const menuItemRef = doc(
        collection(doc(firestore, "restaurants", restaurantId), "menu"),
        itemId
      );

      await deleteDoc(menuItemRef);

      return { restaurantId, itemId };
    },
    onMutate: async ({ restaurantId, itemId }) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({
        queryKey: queryKeys.restaurants.menu(restaurantId),
      });

      // Snapshot the previous value
      const previousMenu = queryClient.getQueryData<MenuItem[]>(
        queryKeys.restaurants.menu(restaurantId)
      );

      // Optimistically update to the new value
      if (previousMenu) {
        queryClient.setQueryData(
          queryKeys.restaurants.menu(restaurantId),
          previousMenu.filter((menuItem) => menuItem.itemId !== itemId)
        );
      }

      return { previousMenu };
    },
    onError: (_, { restaurantId }, context) => {
      // If the mutation fails, use the context returned from onMutate to roll back
      if (context?.previousMenu) {
        queryClient.setQueryData(
          queryKeys.restaurants.menu(restaurantId),
          context.previousMenu
        );
      }

      toast.error("Failed to delete menu item");
    },
    onSuccess: () => {
      toast.success("Menu item deleted successfully");
    },
    onSettled: (_, __, { restaurantId }) => {
      // Always refetch after error or success to make sure our local data is correct
      queryClient.invalidateQueries({
        queryKey: queryKeys.restaurants.menu(restaurantId),
      });
    },
  });
}
