import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";
import { Checkbox } from "@/components/ui/checkbox";
import { ScrollArea } from "@/components/ui/scroll-area";
import { RESTAURANT_OPTIONS } from "@/types";

interface NutritionFilterProps {
  value: {
    calories?: [number, number];
    protein?: [number, number];
    carbs?: [number, number];
    fat?: [number, number];
    sodium?: [number, number];
  };
  healthLabels: string[];
  onChange: (value: NutritionFilterProps["value"]) => void;
  onHealthLabelsChange: (value: string[]) => void;
}

export function NutritionFilter({
  value,
  healthLabels,
  onChange,
  onHealthLabelsChange,
}: NutritionFilterProps) {
  // Default ranges for nutrition values
  const ranges = {
    calories: { min: 0, max: 2000, step: 50 },
    protein: { min: 0, max: 100, step: 5 },
    carbs: { min: 0, max: 200, step: 10 },
    fat: { min: 0, max: 100, step: 5 },
    sodium: { min: 0, max: 2000, step: 100 },
  };

  const handleSliderChange = (
    field: keyof typeof value,
    newValue: number[]
  ) => {
    onChange({
      ...value,
      [field]: [newValue[0], newValue[1]] as [number, number],
    });
  };

  const handleCheckboxChange = (
    checked: boolean | "indeterminate",
    option: string
  ) => {
    if (checked === "indeterminate") return;

    if (checked) {
      onHealthLabelsChange([...healthLabels, option]);
    } else {
      onHealthLabelsChange(healthLabels.filter((item) => item !== option));
    }
  };

  const clearFilter = (field: keyof typeof value) => {
    const newValue = { ...value };
    delete newValue[field];
    onChange(newValue);
  };

  return (
    <div className="space-y-6">
      {/* Calories Range */}
      <div className="space-y-2">
        <div className="flex justify-between items-center">
          <Label className="text-sm font-medium">Calories</Label>
          {value.calories && (
            <button
              onClick={() => clearFilter("calories")}
              className="text-xs text-muted-foreground hover:text-destructive"
            >
              Clear
            </button>
          )}
        </div>
        {value.calories ? (
          <>
            <Slider
              defaultValue={[value.calories[0], value.calories[1]]}
              min={ranges.calories.min}
              max={ranges.calories.max}
              step={ranges.calories.step}
              onValueChange={(newValue) =>
                handleSliderChange("calories", newValue)
              }
            />
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>{value.calories[0]} kcal</span>
              <span>{value.calories[1]} kcal</span>
            </div>
          </>
        ) : (
          <button
            onClick={() =>
              handleSliderChange("calories", [
                ranges.calories.min,
                ranges.calories.max,
              ])
            }
            className="text-xs text-primary hover:underline"
          >
            Set calorie range
          </button>
        )}
      </div>

      {/* Protein Range */}
      <div className="space-y-2">
        <div className="flex justify-between items-center">
          <Label className="text-sm font-medium">Protein</Label>
          {value.protein && (
            <button
              onClick={() => clearFilter("protein")}
              className="text-xs text-muted-foreground hover:text-destructive"
            >
              Clear
            </button>
          )}
        </div>
        {value.protein ? (
          <>
            <Slider
              defaultValue={[value.protein[0], value.protein[1]]}
              min={ranges.protein.min}
              max={ranges.protein.max}
              step={ranges.protein.step}
              onValueChange={(newValue) =>
                handleSliderChange("protein", newValue)
              }
            />
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>{value.protein[0]}g</span>
              <span>{value.protein[1]}g</span>
            </div>
          </>
        ) : (
          <button
            onClick={() =>
              handleSliderChange("protein", [
                ranges.protein.min,
                ranges.protein.max,
              ])
            }
            className="text-xs text-primary hover:underline"
          >
            Set protein range
          </button>
        )}
      </div>

      {/* Carbs Range */}
      <div className="space-y-2">
        <div className="flex justify-between items-center">
          <Label className="text-sm font-medium">Carbohydrates</Label>
          {value.carbs && (
            <button
              onClick={() => clearFilter("carbs")}
              className="text-xs text-muted-foreground hover:text-destructive"
            >
              Clear
            </button>
          )}
        </div>
        {value.carbs ? (
          <>
            <Slider
              defaultValue={[value.carbs[0], value.carbs[1]]}
              min={ranges.carbs.min}
              max={ranges.carbs.max}
              step={ranges.carbs.step}
              onValueChange={(newValue) =>
                handleSliderChange("carbs", newValue)
              }
            />
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>{value.carbs[0]}g</span>
              <span>{value.carbs[1]}g</span>
            </div>
          </>
        ) : (
          <button
            onClick={() =>
              handleSliderChange("carbs", [ranges.carbs.min, ranges.carbs.max])
            }
            className="text-xs text-primary hover:underline"
          >
            Set carbs range
          </button>
        )}
      </div>

      {/* Health Labels */}
      <div className="space-y-2">
        <Label className="text-sm font-medium">Health Labels</Label>
        <ScrollArea className="h-[150px] pr-4">
          <div className="space-y-2">
            {RESTAURANT_OPTIONS.healthLabels.map((option) => (
              <div key={option} className="flex items-center space-x-2">
                <Checkbox
                  id={`health-${option}`}
                  checked={healthLabels.includes(option)}
                  onCheckedChange={(checked) =>
                    handleCheckboxChange(checked, option)
                  }
                />
                <Label
                  htmlFor={`health-${option}`}
                  className="text-sm flex-1 cursor-pointer"
                >
                  {option}
                </Label>
              </div>
            ))}
          </div>
        </ScrollArea>
      </div>
    </div>
  );
}
