import React, { useState, useEffect, useCallback } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { gameService } from "@/services/GameService";
import { TriviaQuestion } from "@/types/games";
import { ArrowLeft, Trophy, Timer, CheckCircle, XCircle } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Progress } from "@/components/ui/progress";
import { motion } from "framer-motion";
import { Badge } from "@/components/ui/badge";

interface TriviaGameProps {
  onBack: () => void;
  onComplete: () => void;
  userId: string;
}

export const TriviaGame: React.FC<TriviaGameProps> = ({
  onBack,
  onComplete,
  userId,
}) => {
  const [questions, setQuestions] = useState<TriviaQuestion[]>([]);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [selectedAnswer, setSelectedAnswer] = useState<number | null>(null);
  const [isAnswerCorrect, setIsAnswerCorrect] = useState<boolean | null>(null);
  const [score, setScore] = useState(0);
  const [gameStarted, setGameStarted] = useState(false);
  const [gameCompleted, setGameCompleted] = useState(false);
  const [timeLeft, setTimeLeft] = useState(15); // 15 seconds per question
  const [timerRunning, setTimerRunning] = useState(false);
  const [result, setResult] = useState<{
    success: boolean;
    pointsEarned: number;
    message: string;
  } | null>(null);
  const [, setLoading] = useState(false);

  // Initialize game
  useEffect(() => {
    const triviaQuestions = gameService.getTriviaQuestions(5);
    setQuestions(triviaQuestions);
  }, []);

  // Handle game completion
  const handleGameComplete = useCallback(async () => {
    setGameCompleted(true);
    setLoading(true);

    try {
      const result = await gameService.submitTriviaResult(
        userId,
        score / 10, // Number of correct answers (assuming 10 points per correct answer)
        questions.length
      );
      setResult(result);
      if (result.success && result.pointsEarned > 0) {
        onComplete();
      }
    } catch (error) {
      console.error("Error submitting game result:", error);
      setResult({
        success: false,
        pointsEarned: 0,
        message: "An error occurred. Please try again.",
      });
    } finally {
      setLoading(false);
    }
  }, [userId, score, questions.length, onComplete]);

  // Move to next question
  const moveToNextQuestion = useCallback(() => {
    if (currentQuestionIndex < questions.length - 1) {
      setCurrentQuestionIndex((prevIndex) => prevIndex + 1);
      setSelectedAnswer(null);
      setIsAnswerCorrect(null);
      setTimeLeft(15);
      setTimerRunning(true);
    } else {
      // Game completed
      handleGameComplete();
    }
  }, [currentQuestionIndex, questions.length, handleGameComplete]);

  // Handle time up
  const handleTimeUp = useCallback(() => {
    if (selectedAnswer === null) {
      // Auto-select wrong answer if none selected
      setIsAnswerCorrect(false);
      setTimeout(() => {
        moveToNextQuestion();
      }, 1500);
    }
  }, [selectedAnswer, moveToNextQuestion]);

  // Timer effect
  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (timerRunning) {
      interval = setInterval(() => {
        setTimeLeft((prevTime) => {
          if (prevTime <= 1) {
            // Time's up, move to next question
            clearInterval(interval);
            handleTimeUp();
            return 0;
          }
          return prevTime - 1;
        });
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [timerRunning, handleTimeUp]);

  // Start game
  const handleStartGame = () => {
    setGameStarted(true);
    setTimerRunning(true);
  };

  // Handle answer selection
  const handleAnswerSelect = (answerIndex: number) => {
    if (selectedAnswer !== null) return; // Prevent multiple selections

    setSelectedAnswer(answerIndex);
    const currentQuestion = questions[currentQuestionIndex];
    const isCorrect = answerIndex === currentQuestion.correctAnswer;
    setIsAnswerCorrect(isCorrect);

    if (isCorrect) {
      setScore((prevScore) => prevScore + currentQuestion.points);
    }

    setTimerRunning(false);

    // Move to next question after delay
    setTimeout(() => {
      moveToNextQuestion();
    }, 1500);
  };

  // Current question
  const currentQuestion = questions[currentQuestionIndex];

  return (
    <Card className="w-full">
      <CardContent className="p-6">
        <div className="flex justify-between items-center mb-6">
          <Button
            variant="ghost"
            size="sm"
            onClick={onBack}
            className="flex items-center"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Games
          </Button>
          {gameStarted && !gameCompleted && (
            <div className="flex items-center">
              <Timer className="h-4 w-4 mr-2 text-muted-foreground" />
              <span className="text-lg font-medium">{timeLeft}s</span>
            </div>
          )}
        </div>

        <div className="text-center mb-6">
          <h2 className="text-2xl font-bold mb-2">Culinary Trivia</h2>
          <p className="text-muted-foreground">
            Test your food knowledge and earn points!
          </p>
        </div>

        {!gameStarted ? (
          <div className="text-center py-8">
            <p className="mb-6">
              Answer culinary trivia questions correctly to earn points. You
              have 15 seconds to answer each question.
            </p>
            <Button onClick={handleStartGame} size="lg">
              Start Game
            </Button>
          </div>
        ) : gameCompleted ? (
          <div className="text-center py-8">
            <div className="mb-6">
              <Trophy className="h-16 w-16 mx-auto text-primary mb-4" />
              <h3 className="text-xl font-bold mb-2">Quiz Completed!</h3>
              <p className="text-muted-foreground mb-2">
                Score: {score} points
              </p>
              <p className="text-muted-foreground mb-4">
                Correct answers: {score / 10} / {questions.length}
              </p>
              {result && (
                <Alert
                  className={`mt-4 ${
                    result.success ? "bg-primary/10" : "bg-destructive/10"
                  }`}
                >
                  <AlertTitle>
                    {result.success ? "Success!" : "Oops!"}
                  </AlertTitle>
                  <AlertDescription>{result.message}</AlertDescription>
                  {result.pointsEarned > 0 && (
                    <div className="mt-2 font-medium">
                      +{result.pointsEarned} points earned!
                    </div>
                  )}
                </Alert>
              )}
            </div>
            <Button onClick={onBack}>Return to Games</Button>
          </div>
        ) : (
          <>
            <div className="mb-4">
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm font-medium">
                  Question {currentQuestionIndex + 1} of {questions.length}
                </span>
                <span className="text-sm">Score: {score}</span>
              </div>
              <Progress
                value={(currentQuestionIndex / questions.length) * 100}
                className="h-2"
              />
            </div>

            <div className="mb-6">
              <div className="flex items-center mb-2">
                <Badge variant="outline" className="mr-2">
                  {currentQuestion?.category}
                </Badge>
                <Badge
                  variant="outline"
                  className={
                    currentQuestion?.difficulty === "easy"
                      ? "bg-green-100"
                      : currentQuestion?.difficulty === "medium"
                      ? "bg-yellow-100"
                      : "bg-red-100"
                  }
                >
                  {currentQuestion?.difficulty}
                </Badge>
              </div>
              <h3 className="text-lg font-medium mb-4">
                {currentQuestion?.question}
              </h3>

              <div className="space-y-3">
                {currentQuestion?.options.map((option, index) => (
                  <motion.div
                    key={index}
                    whileHover={{ scale: selectedAnswer === null ? 1.02 : 1 }}
                    whileTap={{ scale: selectedAnswer === null ? 0.98 : 1 }}
                  >
                    <Button
                      variant={
                        selectedAnswer === null
                          ? "outline"
                          : selectedAnswer === index
                          ? isAnswerCorrect
                            ? "default"
                            : "destructive"
                          : index === currentQuestion.correctAnswer &&
                            selectedAnswer !== null
                          ? "default"
                          : "outline"
                      }
                      className={`w-full justify-start text-left h-auto py-3 px-4 ${
                        selectedAnswer !== null
                          ? "cursor-default"
                          : "cursor-pointer"
                      }`}
                      onClick={() => handleAnswerSelect(index)}
                      disabled={selectedAnswer !== null}
                    >
                      <div className="flex items-center w-full">
                        <span className="flex-1">{option}</span>
                        {selectedAnswer === index && isAnswerCorrect && (
                          <CheckCircle className="h-5 w-5 ml-2 text-primary" />
                        )}
                        {selectedAnswer === index && !isAnswerCorrect && (
                          <XCircle className="h-5 w-5 ml-2 text-destructive" />
                        )}
                        {index === currentQuestion.correctAnswer &&
                          selectedAnswer !== null &&
                          selectedAnswer !== index && (
                            <CheckCircle className="h-5 w-5 ml-2 text-primary" />
                          )}
                      </div>
                    </Button>
                  </motion.div>
                ))}
              </div>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
};
