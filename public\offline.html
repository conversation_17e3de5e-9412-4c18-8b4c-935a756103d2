<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>You're Offline - SaleX</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      margin: 0;
      padding: 0;
      display: flex;
      flex-direction: column;
      min-height: 100vh;
      background-color: #f9fafb;
      color: #1f2937;
    }
    
    header {
      background-color: #ffffff;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      padding: 1rem;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .logo {
      font-size: 1.5rem;
      font-weight: bold;
      color: #4f46e5;
    }
    
    main {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 2rem;
      text-align: center;
    }
    
    .offline-icon {
      font-size: 4rem;
      margin-bottom: 1rem;
      color: #6b7280;
    }
    
    h1 {
      font-size: 1.5rem;
      margin-bottom: 1rem;
    }
    
    p {
      margin-bottom: 1.5rem;
      max-width: 500px;
      line-height: 1.5;
      color: #4b5563;
    }
    
    .button {
      background-color: #4f46e5;
      color: white;
      border: none;
      padding: 0.75rem 1.5rem;
      border-radius: 0.375rem;
      font-weight: 500;
      cursor: pointer;
      text-decoration: none;
      transition: background-color 0.2s;
    }
    
    .button:hover {
      background-color: #4338ca;
    }
    
    .cached-content {
      margin-top: 2rem;
      width: 100%;
      max-width: 600px;
    }
    
    .cached-content h2 {
      font-size: 1.25rem;
      margin-bottom: 1rem;
      border-bottom: 1px solid #e5e7eb;
      padding-bottom: 0.5rem;
    }
    
    .cached-list {
      list-style: none;
      padding: 0;
      margin: 0;
    }
    
    .cached-item {
      padding: 0.75rem;
      border-bottom: 1px solid #e5e7eb;
      display: flex;
      align-items: center;
    }
    
    .cached-item:last-child {
      border-bottom: none;
    }
    
    .cached-item-icon {
      margin-right: 0.75rem;
      color: #4f46e5;
    }
    
    footer {
      padding: 1rem;
      text-align: center;
      font-size: 0.875rem;
      color: #6b7280;
      background-color: #ffffff;
      border-top: 1px solid #e5e7eb;
    }
  </style>
</head>
<body>
  <header>
    <div class="logo">SaleX</div>
  </header>
  
  <main>
    <div class="offline-icon">📶</div>
    <h1>You're currently offline</h1>
    <p>
      It looks like you've lost your internet connection. Don't worry, you can still access some features and content that has been saved for offline use.
    </p>
    <button class="button" id="reload-button">Try Again</button>
    
    <div class="cached-content">
      <h2>Available Offline</h2>
      <ul class="cached-list" id="cached-content-list">
        <!-- This will be populated by JavaScript -->
        <li class="cached-item">
          <span class="cached-item-icon">📋</span>
          Loading cached content...
        </li>
      </ul>
    </div>
  </main>
  
  <footer>
    &copy; 2023 SaleX. All rights reserved.
  </footer>
  
  <script>
    // Check if we're back online periodically
    function checkOnlineStatus() {
      if (navigator.onLine) {
        document.getElementById('reload-button').textContent = 'You\'re back online! Reload';
        document.getElementById('reload-button').style.backgroundColor = '#10b981';
      } else {
        document.getElementById('reload-button').textContent = 'Try Again';
        document.getElementById('reload-button').style.backgroundColor = '#4f46e5';
      }
    }
    
    // Add event listener for the reload button
    document.getElementById('reload-button').addEventListener('click', () => {
      window.location.reload();
    });
    
    // Check online status initially and every 5 seconds
    checkOnlineStatus();
    setInterval(checkOnlineStatus, 5000);
    
    // Add online/offline event listeners
    window.addEventListener('online', checkOnlineStatus);
    window.addEventListener('offline', checkOnlineStatus);
    
    // Try to load cached content list
    async function loadCachedContent() {
      try {
        const cacheNames = await caches.keys();
        const cachedItems = [];
        
        for (const cacheName of cacheNames) {
          const cache = await caches.open(cacheName);
          const requests = await cache.keys();
          
          for (const request of requests) {
            // Skip service worker and non-HTML/JSON resources
            if (request.url.includes('sw.js') || 
                (!request.url.endsWith('.html') && 
                 !request.url.includes('/api/') && 
                 !request.url.endsWith('.json'))) {
              continue;
            }
            
            const url = new URL(request.url);
            const path = url.pathname;
            
            // Skip duplicates
            if (cachedItems.some(item => item.path === path)) {
              continue;
            }
            
            let title = path;
            if (path === '/' || path === '/index.html') {
              title = 'Home Page';
            } else if (path.includes('/restaurants/')) {
              title = 'Restaurant: ' + path.split('/').pop();
            } else if (path.includes('/api/')) {
              title = 'Data: ' + path.split('/').pop();
            }
            
            cachedItems.push({
              title,
              path,
              url: request.url
            });
          }
        }
        
        // Update the UI with cached items
        const listElement = document.getElementById('cached-content-list');
        
        if (cachedItems.length === 0) {
          listElement.innerHTML = `
            <li class="cached-item">
              <span class="cached-item-icon">ℹ️</span>
              No cached content available
            </li>
          `;
          return;
        }
        
        listElement.innerHTML = cachedItems.map(item => `
          <li class="cached-item">
            <span class="cached-item-icon">📄</span>
            <a href="${item.url}">${item.title}</a>
          </li>
        `).join('');
        
      } catch (error) {
        console.error('Error loading cached content:', error);
        document.getElementById('cached-content-list').innerHTML = `
          <li class="cached-item">
            <span class="cached-item-icon">❌</span>
            Error loading cached content
          </li>
        `;
      }
    }
    
    // Load cached content
    loadCachedContent();
  </script>
</body>
</html>
