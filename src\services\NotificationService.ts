import { doc, setDoc, serverTimestamp } from "firebase/firestore";
import { firestore } from "@/config/firebase";
import { v4 as uuidv4 } from "uuid";
import { apiRequest } from "./ApiAuthService";

// Import ApiError interface
interface ApiError extends Error {
  status?: number;
  data?: Record<string, unknown>;
  isNetworkError?: boolean;
  isTimeoutError?: boolean;
}

// Constants
const EMAIL_ENDPOINT = "/api/send-notification-email";
const REQUEST_TIMEOUT = 15000; // 15 seconds
const MAX_RETRIES = 3;

// Types
interface NotificationOptions {
  title: string;
  message: string;
  playSound?: boolean;
  url?: string;
  data?: Record<string, unknown>;
}

interface EmailOptions {
  to: string;
  subject: string;
  body: string;
  recipientName?: string;
  type?: string;
  data?: Record<string, unknown>;
}

interface EmailResponse {
  success: boolean;
  messageId?: string;
  error?: string;
}

export const notificationService = {
  isSoundEnabled: true,
  audioContext: null as AudioContext | null,
  audioBuffer: null as AudioBuffer | null,
  isAudioLoaded: false,

  // Initialize audio context and load sound
  initAudio: async () => {
    if (typeof window === "undefined") return;

    try {
      // Skip audio initialization to prevent AudioContext errors
      // We'll use the fallback Audio API instead
      notificationService.isAudioLoaded = false;
    } catch (error) {
      console.error("Error initializing audio:", error);
    }
  },

  // Play notification sound
  playSound: () => {
    if (!notificationService.isSoundEnabled) return;

    try {
      // Use only the Audio API to avoid AudioContext issues
      const audio = new Audio("/notification.mp3");
      audio.volume = 0.5; // Set volume to 50%

      // Play the sound with error handling
      audio.play().catch((error) => {
        console.error("Error playing sound:", error);
        // Silently fail - don't show any errors to the user
      });
    } catch (error) {
      console.error("Error playing notification sound:", error);
    }
  },

  requestNotificationPermission: async () => {
    if ("Notification" in window) {
      try {
        const permission = await Notification.requestPermission();

        // We'll skip audio initialization to prevent AudioContext errors
        // No need to call initAudio() here

        return permission;
      } catch (error) {
        console.error("Error requesting notification permission:", error);
        return "denied";
      }
    }
    return "denied";
  },

  toggleSound: (enabled: boolean | undefined) => {
    // Handle the case where enabled is undefined
    const soundEnabled = enabled === undefined ? true : enabled;
    notificationService.isSoundEnabled = soundEnabled;

    // Only try to access localStorage if it's available
    if (typeof window !== "undefined" && window.localStorage) {
      try {
        localStorage.setItem("notificationSound", String(soundEnabled));
      } catch (error) {
        console.warn(
          "Failed to save notification sound preference to localStorage:",
          error
        );
      }
    }
  },

  notify: ({
    title,
    message,
    playSound = false,
    url = "/",
    data = {},
  }: NotificationOptions) => {
    // Show browser notification if permission is granted
    if ("Notification" in window && Notification.permission === "granted") {
      // Check if service worker is available
      if ("serviceWorker" in navigator && navigator.serviceWorker.controller) {
        // Use service worker for notification if available
        navigator.serviceWorker.ready
          .then((registration) => {
            registration
              .showNotification(title, {
                body: message,
                icon: "/favicon.ico",
                badge: "/favicon.ico",
                // vibrate is not in NotificationOptions standard type, but browsers support it
                // @ts-expect-error - vibrate is a valid browser option
                vibrate: [100, 50, 100],
                data: { url, ...data },
                actions: [
                  {
                    action: "open",
                    title: "Open",
                  },
                ],
              })
              .catch((error) => {
                console.error(
                  "Error showing notification through service worker:",
                  error
                );
                // Fallback to regular notification
                new Notification(title, { body: message });
              });
          })
          .catch((error) => {
            console.error("Service worker not ready:", error);
            // Fallback to regular notification
            new Notification(title, { body: message });
          });
      } else {
        // Use regular notification if service worker is not available
        new Notification(title, { body: message });
      }

      // Play sound if requested
      if (playSound) {
        notificationService.playSound();
      }
    } else {
      console.warn("Notification permission not granted.");
      // Still play sound even if notification permission is not granted
      if (playSound && notificationService.isSoundEnabled) {
        notificationService.playSound();
      }
    }
  },

  // Create a notification in Firestore
  createDatabaseNotification: async (
    recipientId: string,
    recipientRole: "client" | "restaurant",
    type: string,
    title: string,
    message: string,
    data: { orderId?: string; reservationId?: string } = {}
  ) => {
    try {
      const notificationId = uuidv4();
      const recipientRef = doc(
        firestore,
        recipientRole === "restaurant" ? "restaurants" : "clients",
        recipientId,
        "notifications",
        notificationId
      );

      await setDoc(recipientRef, {
        id: notificationId,
        type,
        title,
        message,
        ...data,
        recipientId,
        read: false,
        createdAt: serverTimestamp(),
      });

      return notificationId;
    } catch (error) {
      console.error("Error creating database notification:", error);
      return null;
    }
  },

  /**
   * Send email notification with secure authentication and retry logic
   * @param options Email options
   * @returns Promise with success status
   */
  sendEmailNotification: async (options: EmailOptions): Promise<boolean> => {
    try {
      // Validate required fields
      if (!options.to || !options.subject) {
        console.error("Email notification missing required fields:", options);
        return false;
      }

      // Track start time for performance monitoring
      const startTime = performance.now();

      // Use the secure API request method with retry logic
      const response = await apiRequest<EmailResponse>(
        EMAIL_ENDPOINT,
        {
          method: "POST",
          body: JSON.stringify(options),
          timeout: REQUEST_TIMEOUT,
        },
        {
          maxRetries: MAX_RETRIES,
          retryDelay: 1000,
        }
      );

      // Log performance metrics
      const duration = Math.round(performance.now() - startTime);
      console.log(`Email notification sent in ${duration}ms to ${options.to}`);

      // Return success status
      return response.success;
    } catch (error) {
      const err = error as ApiError;
      // Detailed error logging
      if (err.isTimeoutError) {
        console.error(
          `Email notification timeout after ${REQUEST_TIMEOUT}ms:`,
          {
            recipient: options.to,
            subject: options.subject,
            type: options.type,
          }
        );
      } else if (err.isNetworkError) {
        console.error("Network error sending email notification:", {
          recipient: options.to,
          subject: options.subject,
          offline: !navigator.onLine,
        });
      } else if (err.status === 401 || err.status === 403) {
        console.error("Authentication error sending email notification:", {
          status: err.status,
          message: err.message,
          data: err.data,
        });
      } else {
        console.error("Error sending email notification:", {
          message: err.message,
          status: err.status,
          data: err.data,
          stack: err.stack,
        });
      }

      // Return failure
      return false;
    }
  },
};
