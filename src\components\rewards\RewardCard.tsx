import React from "react";
import { Card, CardContent, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Reward } from "@/types/rewards";
import { Gift, Tag, Clock, Store, Info } from "lucide-react";
import { format } from "date-fns";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface RewardCardProps {
  reward: Reward & {
    restaurantInfo?: {
      name: string;
      imageUrl?: string;
    };
  };
  userPoints?: number;
  onRedeem?: (rewardId: string) => void;
  isLoading?: boolean;
}

export const RewardCard: React.FC<RewardCardProps> = ({
  reward,
  userPoints = 0,
  onRedeem,
  isLoading = false,
}) => {
  const hasEnoughPoints = userPoints >= reward.pointsCost;
  const isExpired = reward.expiresAt && reward.expiresAt.toDate() < new Date();
  const isOutOfStock =
    reward.availableQuantity !== undefined && reward.availableQuantity <= 0;
  const canRedeem =
    hasEnoughPoints && !isExpired && !isOutOfStock && reward.isActive;

  const getRewardTypeIcon = () => {
    switch (reward.type) {
      case "discount":
        return <Tag className="h-4 w-4 mr-1" />;
      case "freeItem":
        return <Gift className="h-4 w-4 mr-1" />;
      case "freeDelivery":
        return <Store className="h-4 w-4 mr-1" />;
      default:
        return <Gift className="h-4 w-4 mr-1" />;
    }
  };

  const getRewardTypeLabel = () => {
    switch (reward.type) {
      case "discount":
        return `${reward.discountValue}% Discount`;
      case "freeItem":
        return "Free Item";
      case "freeDelivery":
        return "Free Delivery";
      case "vipAccess":
        return "VIP Access";
      default:
        return "Special Reward";
    }
  };

  const handleRedeem = () => {
    if (onRedeem && canRedeem) {
      onRedeem(reward.id);
    }
  };

  return (
    <Card className="overflow-hidden h-full flex flex-col hover:shadow-lg transition-shadow duration-200">
      {reward.imageUrl ? (
        <div className="h-40 overflow-hidden">
          <img
            src={reward.imageUrl}
            alt={reward.name}
            className="w-full h-full object-cover transition-transform duration-200 hover:scale-105"
          />
        </div>
      ) : (
        <div className="h-40 bg-gradient-to-br from-muted to-muted/50 flex items-center justify-center">
          <Gift className="h-16 w-16 text-muted-foreground opacity-40" />
        </div>
      )}

      <CardContent className="p-5 flex-grow">
        <div className="flex items-start justify-between mb-3">
          <h3 className="font-semibold text-base leading-tight">
            {reward.name}
          </h3>
          <Badge
            variant="secondary"
            className="ml-2 whitespace-nowrap font-medium"
          >
            {reward.pointsCost} pts
          </Badge>
        </div>

        <div className="flex items-center text-xs text-muted-foreground mb-3">
          {getRewardTypeIcon()}
          <span className="font-medium">{getRewardTypeLabel()}</span>
        </div>

        <p className="text-sm text-muted-foreground line-clamp-2 mb-3 leading-relaxed">
          {reward.description}
        </p>

        {reward.expiresAt && (
          <div className="flex items-center text-xs text-muted-foreground">
            <Clock className="h-3 w-3 mr-1" />
            <span>
              Expires {format(reward.expiresAt.toDate(), "MMM d, yyyy")}
            </span>
          </div>
        )}

        {reward.availableQuantity !== undefined && (
          <div className="text-xs text-muted-foreground mt-1">
            {reward.availableQuantity > 0 ? (
              <span>
                {reward.availableQuantity}{" "}
                {reward.availableQuantity === 1 ? "item" : "items"} left
              </span>
            ) : (
              <span className="text-destructive">Out of stock</span>
            )}
          </div>
        )}

        <div className="space-y-2 mt-3">
          {reward.restaurantInfo ? (
            <div className="flex items-center text-xs text-muted-foreground">
              <Store className="h-3 w-3 mr-1 text-primary" />
              <span className="font-medium">{reward.restaurantInfo.name}</span>
            </div>
          ) : reward.restaurantId ? (
            <div className="flex items-center text-xs text-muted-foreground">
              <Store className="h-3 w-3 mr-1 text-primary" />
              <span className="font-medium">Restaurant reward</span>
            </div>
          ) : null}

          {reward.isGlobal && (
            <div className="flex items-center text-xs text-muted-foreground">
              <Gift className="h-3 w-3 mr-1 text-primary" />
              <span className="font-medium">Platform reward</span>
            </div>
          )}
        </div>
      </CardContent>

      <CardFooter className="p-5 pt-0">
        <div className="w-full space-y-3">
          {reward.termsAndConditions && (
            <div>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div className="flex items-center text-xs text-muted-foreground cursor-help hover:text-foreground transition-colors">
                      <Info className="h-3 w-3 mr-1" />
                      <span>Terms & Conditions</span>
                    </div>
                  </TooltipTrigger>
                  <TooltipContent className="max-w-xs">
                    <p className="text-xs">{reward.termsAndConditions}</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          )}

          <Button
            onClick={handleRedeem}
            disabled={!canRedeem || isLoading}
            className="w-full font-medium"
            variant={canRedeem ? "default" : "outline"}
            size="default"
          >
            {isLoading
              ? "Processing..."
              : !hasEnoughPoints
              ? `Need ${reward.pointsCost - userPoints} more points`
              : isExpired
              ? "Expired"
              : isOutOfStock
              ? "Out of stock"
              : "Redeem Reward"}
          </Button>
        </div>
      </CardFooter>
    </Card>
  );
};
