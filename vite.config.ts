import path from "path";
import react from "@vitejs/plugin-react";
import { defineConfig } from "vite";

export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  build: {
    chunkSizeWarningLimit: 1000,
    rollupOptions: {
      output: {
        // Configure manual chunks to split the bundle
        manualChunks: {
          // Split React and related libraries
          "react-vendor": ["react", "react-dom", "react-router-dom"],
          // Split UI components
          "ui-components": [
            "@/components/ui/button",
            "@/components/ui/dialog",
            "@/components/ui/input",
            "@/components/ui/form",
            "@/components/ui/select",
            "@/components/ui/tabs",
            "@/components/ui/card",
          ],
          // Split Firebase
          "firebase-vendor": [
            "firebase/app",
            "firebase/auth",
            "firebase/firestore",
            "firebase/storage",
            "firebase/functions",
            "firebase/analytics",
          ],
          // Split date utilities
          "date-utils": ["date-fns", "date-fns/locale"],
          // Split chart libraries
          "chart-vendor": ["recharts"],
          // Split other large dependencies
          "other-vendor": ["lucide-react", "framer-motion", "uuid"],
        },
      },
    },
  },
});
