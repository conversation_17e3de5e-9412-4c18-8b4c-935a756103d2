"use client"; // Next.js App Router için gerekli

import * as React from "react";
import * as PopoverPrimitive from "@radix-ui/react-popover"; // Radix UI Popover temel bileşenleri

import { cn } from "@/lib/utils"; // yardımcı sınıf birleştirme fonksiyonu

/**
 * Tüm popover bileşenlerini içeren ana konteyner.
 * Popover'ın açık/kapalı durumunu yönetir.
 */
const Popover = PopoverPrimitive.Root;

/**
 * Popover'ı açıp kapatan tıklanabilir tetikleyici eleman.
 * Genellikle bir Button ile kullanılır (`asChild` prop'u ile).
 */
const PopoverTrigger = PopoverPrimitive.Trigger;

/**
 * Popover içinde açıkça bir kapatma düğmesi oluşturmak için kullanılır.
 * `asChild` prop'u ile Button gibi başka bir bileşenle birleştirilebilir.
 */
const PopoverClose = React.forwardRef<
  React.ElementRef<typeof PopoverPrimitive.Close>,
  React.ComponentPropsWithoutRef<typeof PopoverPrimitive.Close>
>(({ className, ...props }, ref) => (
  <PopoverPrimitive.Close
    ref={ref}
    className={cn(
      // İsteğe bağlı olarak temel stiller eklenebilir, örneğin:
      // "rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground"
      className
    )}
    {...props}
  />
));
PopoverClose.displayName = PopoverPrimitive.Close.displayName;

/**
 * Popover açıldığında görünen içerik alanı.
 * Portal kullanılarak DOM'da genellikle body'nin sonuna render edilir.
 */
const PopoverContent = React.forwardRef<
  React.ElementRef<typeof PopoverPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof PopoverPrimitive.Content>
>(({ className, align = "center", sideOffset = 4, ...props }, ref) => (
  <PopoverPrimitive.Portal>
    {" "}
    {/* İçeriği DOM'da farklı bir yere ışınlar */}
    <PopoverPrimitive.Content
      ref={ref}
      align={align} // İçeriğin tetikleyiciye göre hizalanması ('start', 'center', 'end')
      sideOffset={sideOffset} // İçerik ile tetikleyici arasındaki boşluk (piksel)
      className={cn(
        // Temel Stiller ve Animasyonlar
        "z-[100] w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none",
        // Açılma/Kapanma Animasyonları (shadcn/ui varsayılanları)
        "data-[state=open]:animate-in data-[state=closed]:animate-out",
        "data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",
        "data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95",
        // Konuma Göre Kayma Animasyonları
        "data-[side=bottom]:slide-in-from-top-2",
        "data-[side=left]:slide-in-from-right-2",
        "data-[side=right]:slide-in-from-left-2",
        "data-[side=top]:slide-in-from-bottom-2",
        className // Dışarıdan gelen sınıflarla birleştir
      )}
      {...props} // Diğer tüm PopoverPrimitive.Content prop'larını aktar
    />
  </PopoverPrimitive.Portal>
));
PopoverContent.displayName = PopoverPrimitive.Content.displayName; // React DevTools için isim

// Kullanılabilir bileşenleri dışa aktar
export { Popover, PopoverTrigger, PopoverContent, PopoverClose };
