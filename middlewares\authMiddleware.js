/**
 * Authentication middleware
 */
const { admin } = require("../config/firebase");
const authService = require("../services/authService");
const logger = require("../utils/logger");

/**
 * Verify Firebase ID token
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const verifyToken = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;

    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return res.status(401).json({ error: "Unauthorized: No token provided" });
    }

    const token = authHeader.split("Bearer ")[1];

    try {
      // First try to verify as a Firebase token
      try {
        const decodedToken = await admin.auth().verifyIdToken(token);
        req.user = decodedToken;

        // Log successful authentication
        logger.info(
          `User authenticated: ${decodedToken.uid.substring(0, 8)}...`
        );

        return next();
      } catch (firebaseError) {
        // If not a Firebase token, try as a JWT token
        try {
          const decodedJwt = authService.verifyToken(token);
          req.user = decodedJwt;

          // Log successful JWT authentication
          logger.info(
            `JWT authenticated: ${decodedJwt.clientId.substring(0, 8)}...`
          );

          return next();
        } catch (jwtError) {
          // Both verification methods failed
          logger.warn("Token verification failed for both Firebase and JWT", {
            firebaseError: firebaseError.message,
            jwtError: jwtError.message,
          });

          throw new Error("Invalid token");
        }
      }
    } catch (error) {
      logger.error("Error verifying token:", error);
      return res.status(401).json({ error: "Unauthorized: Invalid token" });
    }
  } catch (error) {
    logger.error("Error in auth middleware:", error);
    return res.status(500).json({ error: "Internal server error" });
  }
};

/**
 * Verify API key with enhanced security
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const verifyApiKey = async (req, res, next) => {
  try {
    // Get client IP for logging
    const clientIp =
      req.headers["x-forwarded-for"] ||
      req.connection.remoteAddress ||
      "unknown";

    // Get API key from header
    const apiKey = req.headers["x-api-key"];

    // Skip API key verification in development for allowed origins
    if (process.env.NODE_ENV !== "production") {
      const origin = req.headers.origin;
      const allowedOrigins = [
        "http://localhost:5173",
        "https://qonai.me",
        "https://salex-2025.firebaseapp.com",
      ];

      if (origin && allowedOrigins.includes(origin)) {
        logger.debug(`Development mode: Skipping API key check for ${origin}`);
        return next();
      }
    }

    // No API key provided
    if (!apiKey) {
      logger.warn(`API request without key from ${clientIp}`);
      return res.status(401).json({ error: "Unauthorized: API key required" });
    }

    // Legacy API key support (temporary)
    const legacyApiKeys = [
      process.env.API_KEY,
      process.env.LEGACY_API_KEY,
    ].filter(Boolean);

    if (legacyApiKeys.includes(apiKey)) {
      logger.info(`Legacy API key used from ${clientIp}`);
      return next();
    }

    // Validate API key using the auth service
    const apiKeyData = await authService.validateApiKey(apiKey);

    if (!apiKeyData) {
      logger.warn(
        `Invalid API key from ${clientIp}: ${apiKey.substring(0, 8)}...`
      );
      return res.status(401).json({ error: "Unauthorized: Invalid API key" });
    }

    // Add API key data to request for later use
    req.apiKey = {
      id: apiKeyData.id,
      clientId: apiKeyData.clientId,
      scope: apiKeyData.scope,
    };

    // Log successful API key authentication
    logger.info(
      `API key authenticated: ${apiKeyData.clientId} from ${clientIp}`
    );

    next();
  } catch (error) {
    logger.error("Error in API key middleware:", error);
    return res.status(500).json({ error: "Internal server error" });
  }
};

module.exports = {
  verifyToken,
  verifyApiKey,
};
