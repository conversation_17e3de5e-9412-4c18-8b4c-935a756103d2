import { useEffect, useState } from "react";
import { orderSchedulingService } from "@/services/OrderSchedulingService";

// This component doesn't render anything, it just runs in the background
export const ScheduledOrderProcessor: React.FC = () => {
  // We only need the setter function
  const [, setLastProcessed] = useState<Date | null>(null);

  useEffect(() => {
    // Process scheduled orders every minute
    const processScheduledOrders = async () => {
      try {
        // Process orders that are due
        const processedCount =
          await orderSchedulingService.processScheduledOrders();
        if (processedCount > 0) {
          console.log(`Processed ${processedCount} scheduled orders`);
        }

        // Send reminders for upcoming orders
        const reminderCount =
          await orderSchedulingService.sendScheduledOrderReminders();
        if (reminderCount > 0) {
          console.log(
            `Sent reminders for ${reminderCount} upcoming scheduled orders`
          );
        }

        setLastProcessed(new Date());
      } catch (error) {
        console.error("Error processing scheduled orders:", error);
      }
    };

    // Process immediately on component mount
    processScheduledOrders();

    // Then set up interval to process every minute
    const intervalId = setInterval(processScheduledOrders, 60 * 1000);

    // Clean up interval on unmount
    return () => clearInterval(intervalId);
  }, []);

  // This component doesn't render anything
  return null;
};
