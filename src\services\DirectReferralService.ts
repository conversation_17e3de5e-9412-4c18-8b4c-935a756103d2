/**
 * DirectReferralService.ts
 *
 * A simplified, direct approach to handling referrals
 */

import {
  doc,
  getDoc,
  updateDoc,
  collection,
  query,
  where,
  getDocs,
  limit,
  Timestamp,
  increment,
  serverTimestamp,
  writeBatch,
} from "firebase/firestore";
import { firestore } from "../config/firebase";
import { notificationService } from "./NotificationService";

// Points configuration
const REFERRAL_POINTS = 150;
const REFERRED_USER_POINTS = 75;

/**
 * Process a referral code for a newly registered user
 * @param userId The new user's ID
 * @param referralCode The referral code used during registration
 * @returns Success status
 */
export const processReferral = async (
  userId: string,
  referralCode: string
): Promise<boolean> => {
  console.log(`[DIRECT_REFERRAL] Starting direct referral processing`);
  console.log(
    `[DIRECT_REFERRAL] User ID: ${userId}, Referral Code: ${referralCode}`
  );

  try {
    // Step 1: Verify the new user exists
    const newUserRef = doc(firestore, "clients", userId);
    const newUserDoc = await getDoc(newUserRef);

    if (!newUserDoc.exists()) {
      console.error(`[DIRECT_REFERRAL] New user does not exist: ${userId}`);
      return false;
    }

    const newUserData = newUserDoc.data();
    const newUserEmail = newUserData.email || "";

    console.log(
      `[DIRECT_REFERRAL] New user verified: ${userId}, Email: ${newUserEmail}`
    );

    // Step 2: Find the referrer by referral code
    console.log(
      `[DIRECT_REFERRAL] Searching for referrer with code: ${referralCode}`
    );

    // Search in clients collection for the referral code
    const clientsQuery = query(
      collection(firestore, "clients"),
      where("referralCode", "==", referralCode),
      limit(1)
    );

    const clientsSnapshot = await getDocs(clientsQuery);

    if (clientsSnapshot.empty) {
      console.error(
        `[DIRECT_REFERRAL] No referrer found with code: ${referralCode}`
      );
      return false;
    }

    const referrerDoc = clientsSnapshot.docs[0];
    const referrerId = referrerDoc.id;
    const referrerData = referrerDoc.data();

    console.log(
      `[DIRECT_REFERRAL] Found referrer: ${referrerId}, Name: ${referrerData.firstName} ${referrerData.lastName}`
    );

    // Check if there's an existing referral record
    console.log(`[DIRECT_REFERRAL] Checking for existing referral records`);
    const existingReferralQuery = query(
      collection(firestore, "referrals"),
      where("referrerId", "==", referrerId),
      where("referredEmail", "==", newUserEmail),
      limit(1)
    );

    const existingReferrals = await getDocs(existingReferralQuery);
    let existingReferralId = null;

    if (!existingReferrals.empty) {
      const existingReferral = existingReferrals.docs[0];
      existingReferralId = existingReferral.id;
      console.log(
        `[DIRECT_REFERRAL] Found existing referral: ${existingReferralId}`
      );

      // Update the existing referral to completed status
      await updateDoc(doc(firestore, "referrals", existingReferralId), {
        referredUserId: userId,
        status: "completed",
        completedAt: Timestamp.now(),
        pointsAwarded: REFERRAL_POINTS,
      });

      console.log(
        `[DIRECT_REFERRAL] Updated existing referral to completed status`
      );

      // Also update in referrer's subcollection if it exists
      const referrerSubcollectionRef = doc(
        firestore,
        "clients",
        referrerId,
        "referrals",
        existingReferralId
      );

      const referrerSubDoc = await getDoc(referrerSubcollectionRef);
      if (referrerSubDoc.exists()) {
        await updateDoc(referrerSubcollectionRef, {
          referredUserId: userId,
          status: "completed",
          completedAt: Timestamp.now(),
          pointsAwarded: REFERRAL_POINTS,
        });
        console.log(
          `[DIRECT_REFERRAL] Updated referral in referrer's subcollection`
        );
      }
    }

    // Use a batch for atomic updates
    console.log(`[DIRECT_REFERRAL] Creating batch for loyalty updates`);
    const batch = writeBatch(firestore);

    // Step 3: Create or update loyalty status for both users
    // First for the new user
    const newUserStatusRef = doc(
      firestore,
      "clients",
      userId,
      "loyaltyStatus",
      "current"
    );
    const newUserStatusDoc = await getDoc(newUserStatusRef);

    // Generate a referral code for the new user if needed
    const newUserReferralCode = generateReferralCode(newUserEmail);

    if (!newUserStatusDoc.exists()) {
      console.log(`[DIRECT_REFERRAL] Creating loyalty status for new user`);

      // Create loyalty status for new user
      batch.set(newUserStatusRef, {
        userId,
        totalPoints: REFERRED_USER_POINTS,
        lifetimePoints: REFERRED_USER_POINTS,
        tier: "bronze",
        nextTierPoints: 1500,
        referralCode: newUserReferralCode,
        referralCount: 0,
        joinedAt: Timestamp.now(),
        lastUpdated: Timestamp.now(),
      });

      // Update client document
      batch.update(newUserRef, {
        loyaltyEnabled: true,
        loyaltyJoinedAt: serverTimestamp(),
        loyaltyTier: "bronze",
        loyaltyPoints: REFERRED_USER_POINTS,
        loyaltyLifetimePoints: REFERRED_USER_POINTS,
        referralCode: newUserReferralCode,
        referralCount: 0,
      });
    } else {
      console.log(
        `[DIRECT_REFERRAL] Updating existing loyalty status for new user`
      );

      // Update existing loyalty status
      batch.update(newUserStatusRef, {
        totalPoints: increment(REFERRED_USER_POINTS),
        lifetimePoints: increment(REFERRED_USER_POINTS),
        lastUpdated: Timestamp.now(),
      });

      // Update client document
      batch.update(newUserRef, {
        loyaltyPoints: increment(REFERRED_USER_POINTS),
        loyaltyLifetimePoints: increment(REFERRED_USER_POINTS),
      });
    }

    // Now for the referrer
    const referrerStatusRef = doc(
      firestore,
      "clients",
      referrerId,
      "loyaltyStatus",
      "current"
    );
    const referrerStatusDoc = await getDoc(referrerStatusRef);

    if (!referrerStatusDoc.exists()) {
      console.log(`[DIRECT_REFERRAL] Creating loyalty status for referrer`);

      // Create loyalty status for referrer
      batch.set(referrerStatusRef, {
        userId: referrerId,
        totalPoints: REFERRAL_POINTS,
        lifetimePoints: REFERRAL_POINTS,
        tier: "bronze",
        nextTierPoints: 1500,
        referralCode: referralCode,
        referralCount: 1,
        joinedAt: Timestamp.now(),
        lastUpdated: Timestamp.now(),
      });

      // Update client document
      batch.update(doc(firestore, "clients", referrerId), {
        loyaltyEnabled: true,
        loyaltyJoinedAt: serverTimestamp(),
        loyaltyTier: "bronze",
        loyaltyPoints: REFERRAL_POINTS,
        loyaltyLifetimePoints: REFERRAL_POINTS,
        referralCount: 1,
      });
    } else {
      console.log(
        `[DIRECT_REFERRAL] Updating existing loyalty status for referrer`
      );

      // Update existing loyalty status
      batch.update(referrerStatusRef, {
        totalPoints: increment(REFERRAL_POINTS),
        lifetimePoints: increment(REFERRAL_POINTS),
        referralCount: increment(1),
        lastUpdated: Timestamp.now(),
      });

      // Update client document
      batch.update(doc(firestore, "clients", referrerId), {
        loyaltyPoints: increment(REFERRAL_POINTS),
        loyaltyLifetimePoints: increment(REFERRAL_POINTS),
        referralCount: increment(1),
      });
    }

    // Step 4: Create referral record if one doesn't exist
    let referralId = existingReferralId;

    if (!referralId) {
      referralId = `${referrerId}_${userId}_${Date.now()}`;
      console.log(
        `[DIRECT_REFERRAL] Creating new referral record with ID: ${referralId}`
      );

      const referralData = {
        id: referralId,
        referrerId,
        referredUserId: userId,
        referralCode,
        referredEmail: newUserEmail,
        status: "completed",
        createdAt: Timestamp.now(),
        completedAt: Timestamp.now(),
        pointsAwarded: REFERRAL_POINTS,
      };

      // Add to global referrals collection
      batch.set(doc(firestore, "referrals", referralId), referralData);

      // Add to referrer's referrals subcollection
      batch.set(
        doc(firestore, "clients", referrerId, "referrals", referralId),
        referralData
      );
    }

    // Step 5: Create point transactions
    // For new user
    const newUserTransactionId = `referred_${userId}_${Date.now()}`;
    batch.set(
      doc(
        firestore,
        "clients",
        userId,
        "pointTransactions",
        newUserTransactionId
      ),
      {
        id: newUserTransactionId,
        userId,
        points: REFERRED_USER_POINTS,
        type: "bonus",
        description: "Bonus points for signing up with a referral",
        createdAt: Timestamp.now(),
        referenceId: referralId,
        expiresAt: Timestamp.fromDate(
          new Date(Date.now() + 90 * 24 * 60 * 60 * 1000)
        ), // 90 days
      }
    );

    // For referrer
    const referrerTransactionId = `referral_${referrerId}_${Date.now()}`;
    batch.set(
      doc(
        firestore,
        "clients",
        referrerId,
        "pointTransactions",
        referrerTransactionId
      ),
      {
        id: referrerTransactionId,
        userId: referrerId,
        points: REFERRAL_POINTS,
        type: "referral",
        description: "Points earned for successful referral",
        createdAt: Timestamp.now(),
        referenceId: referralId,
        expiresAt: Timestamp.fromDate(
          new Date(Date.now() + 90 * 24 * 60 * 60 * 1000)
        ), // 90 days
      }
    );

    // Commit all the batch operations
    console.log(`[DIRECT_REFERRAL] Committing batch operations`);
    await batch.commit();
    console.log(`[DIRECT_REFERRAL] Batch operations committed successfully`);

    // Step 6: Send notifications
    try {
      // To referrer
      const referrerEmail = referrerData.email || "";
      if (referrerEmail) {
        await notificationService.sendEmailNotification({
          to: referrerEmail,
          subject: "Referral Bonus!",
          body: `Your referral was successful! You've earned ${REFERRAL_POINTS} points.`,
          recipientName: `${referrerData.firstName || ""} ${
            referrerData.lastName || ""
          }`,
          type: "loyalty",
        });
      }

      // To new user
      if (newUserEmail) {
        await notificationService.sendEmailNotification({
          to: newUserEmail,
          subject: "Welcome Bonus!",
          body: `You've earned ${REFERRED_USER_POINTS} bonus points for signing up with a referral.`,
          recipientName: `${newUserData.firstName || ""} ${
            newUserData.lastName || ""
          }`,
          type: "loyalty",
        });
      }
    } catch (emailError) {
      console.error(
        `[DIRECT_REFERRAL] Error sending notification emails:`,
        emailError
      );
      // Continue even if email sending fails
    }

    console.log(`[DIRECT_REFERRAL] Referral processing completed successfully`);
    return true;
  } catch (error) {
    console.error(`[DIRECT_REFERRAL] Error processing referral:`, error);
    if (error instanceof Error) {
      console.error(`[DIRECT_REFERRAL] Error message: ${error.message}`);
      console.error(`[DIRECT_REFERRAL] Error stack: ${error.stack}`);
    }
    return false;
  }
};

/**
 * Verify a referral code and get referrer information
 * @param referralCode The referral code to verify
 * @returns Referrer information or null if not found
 */
export const verifyReferralCode = async (
  referralCode: string
): Promise<{
  referrerId: string;
  referrerName: string;
  referrerEmail: string;
} | null> => {
  try {
    console.log(`[VERIFY_REFERRAL] Verifying referral code: ${referralCode}`);

    if (!referralCode || referralCode.trim() === "") {
      return null;
    }

    // Search in clients collection for the referral code
    const clientsQuery = query(
      collection(firestore, "clients"),
      where("referralCode", "==", referralCode.trim()),
      limit(1)
    );

    const clientsSnapshot = await getDocs(clientsQuery);

    if (clientsSnapshot.empty) {
      console.log(
        `[VERIFY_REFERRAL] No referrer found with code: ${referralCode}`
      );
      return null;
    }

    const referrerDoc = clientsSnapshot.docs[0];
    const referrerId = referrerDoc.id;
    const referrerData = referrerDoc.data();

    console.log(
      `[VERIFY_REFERRAL] Found referrer: ${referrerId}, Name: ${referrerData.firstName} ${referrerData.lastName}`
    );

    return {
      referrerId,
      referrerName: `${referrerData.firstName || ""} ${
        referrerData.lastName || ""
      }`.trim(),
      referrerEmail: referrerData.email || "",
    };
  } catch (error) {
    console.error(`[VERIFY_REFERRAL] Error verifying referral code:`, error);
    return null;
  }
};

/**
 * Generate a unique referral code for a user
 * @param email User email
 * @returns Referral code
 */
const generateReferralCode = (email: string): string => {
  const prefix = email.split("@")[0].substring(0, 5).toUpperCase();
  const randomPart = Math.random().toString(36).substring(2, 6).toUpperCase();
  return `${prefix}${randomPart}`;
};
