/**
 * Newsletter API - Main Entry Point
 *
 * This file serves as the main entry point for the Newsletter API.
 * It sets up the Express server, middleware, and routes.
 */

// Import required modules
const express = require("express");
const cors = require("cors");
const helmet = require("helmet");
const morgan = require("morgan");
const rateLimit = require("express-rate-limit");
const dotenv = require("dotenv");

// Load environment variables
dotenv.config();

// Import routes
const newsletterRoutes = require("./routes/newsletterRoutes");
const notificationRoutes = require("./routes/notificationRoutes");

// Import middleware
const { errorHandler, notFound } = require("./middlewares/errorHandler");

// Create Express app
const app = express();
const PORT = process.env.PORT || 3000;

// Basic middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(helmet());
app.use(morgan("dev"));

// CORS configuration
app.use(
  cors({
    origin: [
      "http://localhost:5173",
      "https://qonai.me",
      "https://salex-2025.firebaseapp.com",
      // "https://newsletter-za17.onrender.com",
      "https://api.qonai.me",
    ],
    methods: ["GET", "POST", "OPTIONS"],
    credentials: true,
  })
);

// Rate limiting
const globalLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // 100 requests per IP
  message: "Too many requests from this IP, please try again later.",
});
app.use(globalLimiter);

// Subscribe rate limiter
const subscribeLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 5, // 5 subscription requests per IP
});

// Apply subscribe limiter to specific routes
app.use("/subscribe", subscribeLimiter);
app.use("/api/newsletter/subscribe", subscribeLimiter);

// Health check endpoint
app.get("/health", (req, res) => {
  res.status(200).json({
    status: "ok",
    service: "newsletter-api",
    version: "1.0.0",
  });
});

// Mount routes
// Legacy routes for backward compatibility
app.use("/", require("./routes"));

// API routes with /api prefix
app.use("/api", require("./routes"));

// Error handling middleware
app.use(notFound);
app.use(errorHandler);

// Start server
app.listen(PORT, () => {
  console.log(`Newsletter API running on port ${PORT}`);
  console.log(`Environment: ${process.env.NODE_ENV || "development"}`);
  console.log(`Health check: http://localhost:${PORT}/health`);
});

// Export app for testing
module.exports = app;
