/* Quill Editor Overrides */

/* Fix for findDOMNode warnings */
.quill-container {
  position: relative;
  width: 100%;
}

/* Improve editor appearance */
.ql-editor {
  min-height: 200px !important;
  font-size: 14px;
  line-height: 1.6;
}

/* Remove borders from container */
.ql-container {
  border: none !important;
}

/* Fix for toolbar appearance */
.ql-toolbar {
  border: none !important;
  background-color: #f9f9f9;
  border-bottom: 1px solid #e2e8f0 !important;
}

/* Fix for toolbar buttons */
.ql-toolbar button {
  height: 28px;
  width: 28px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

/* Fix for toolbar button hover */
.ql-toolbar button:hover {
  background-color: #f1f5f9;
}

/* Fix for active toolbar button */
.ql-toolbar button.ql-active {
  background-color: #e2e8f0;
}

/* Fix for toolbar dropdown */
.ql-toolbar .ql-picker {
  height: 28px;
}

/* Fix for toolbar dropdown hover */
.ql-toolbar .ql-picker:hover {
  color: #0f172a;
}

/* Fix for toolbar dropdown active */
.ql-toolbar .ql-picker.ql-expanded {
  color: #0f172a;
}

/* Fix for toolbar dropdown options */
.ql-toolbar .ql-picker-options {
  background-color: white;
  border: 1px solid #e2e8f0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Fix for toolbar dropdown option hover */
.ql-toolbar .ql-picker-item:hover {
  background-color: #f1f5f9;
}

/* Fix for toolbar dropdown option active */
.ql-toolbar .ql-picker-item.ql-selected {
  color: #0f172a;
}

/* Fix for editor content */
.ql-editor p {
  margin-bottom: 1em;
}

/* Fix for editor headings */
.ql-editor h1 {
  font-size: 2em;
  margin-bottom: 0.5em;
}

.ql-editor h2 {
  font-size: 1.5em;
  margin-bottom: 0.5em;
}

/* Fix for editor lists */
.ql-editor ul,
.ql-editor ol {
  padding-left: 1.5em;
  margin-bottom: 1em;
}

.ql-editor li {
  margin-bottom: 0.5em;
}

/* Fix for editor blockquote */
.ql-editor blockquote {
  border-left: 4px solid #e2e8f0;
  padding-left: 1em;
  margin-left: 0;
  margin-right: 0;
  margin-bottom: 1em;
}

/* Fix for editor code block */
.ql-editor pre {
  background-color: #f1f5f9;
  border-radius: 4px;
  padding: 1em;
  margin-bottom: 1em;
}

/* Fix for editor image */
.ql-editor img {
  max-width: 100%;
  height: auto;
  margin-bottom: 1em;
}

/* Fix for editor table */
.ql-editor table {
  border-collapse: collapse;
  width: 100%;
  margin-bottom: 1em;
}

.ql-editor table td,
.ql-editor table th {
  border: 1px solid #e2e8f0;
  padding: 0.5em;
}

/* Fix for editor link */
.ql-editor a {
  color: #2563eb;
  text-decoration: underline;
}

/* Fix for editor link hover */
.ql-editor a:hover {
  color: #1d4ed8;
}

/* Fix for editor placeholder */
.ql-editor.ql-blank::before {
  color: #94a3b8;
  font-style: italic;
}

/* Fix for editor focus */
.ql-container:focus-within {
  outline: none;
}

/* Fix for editor selection */
.ql-editor .ql-selected {
  background-color: #bfdbfe;
}

/* Fix for editor cursor */
.ql-editor .ql-cursor {
  border-left: 1px solid #0f172a;
}

/* Fix for editor tooltip */
.ql-tooltip {
  background-color: white;
  border: 1px solid #e2e8f0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 0.5em;
  border-radius: 4px;
}

/* Fix for editor tooltip input */
.ql-tooltip input {
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  padding: 0.25em 0.5em;
}

/* Fix for editor tooltip button */
.ql-tooltip button {
  background-color: #f1f5f9;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  padding: 0.25em 0.5em;
  margin-left: 0.5em;
}

/* Fix for editor tooltip button hover */
.ql-tooltip button:hover {
  background-color: #e2e8f0;
}

/* Fix for DOMNodeInserted warnings */
.ql-clipboard {
  position: absolute;
  left: -100000px;
  height: 1px;
  overflow-y: hidden;
  visibility: hidden;
}

/* Prevent mutation events */
.ql-editor * {
  transition: none !important;
}
