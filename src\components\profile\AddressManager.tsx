import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Address } from "@/types";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { toast } from "sonner";
import { PlusCircle, Trash2, Edit, Check, X } from "lucide-react";

interface AddressManagerProps {
  addresses: Address[];
  onSave: (addresses: Address[]) => void;
}

export const AddressManager: React.FC<AddressManagerProps> = ({
  addresses = [],
  onSave,
}) => {
  const [addressList, setAddressList] = useState<Address[]>(addresses);
  const [isAdding, setIsAdding] = useState(false);
  const [editingIndex, setEditingIndex] = useState<number | null>(null);

  const [newAddress, setNew<PERSON><PERSON><PERSON>] = useState<Address>({
    street: "",
    city: "",
    state: "",
    postalCode: "",
    country: "",
    isDefault: addressList.length === 0, // Make default if it's the first address
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;

    if (editingIndex !== null) {
      // Editing existing address
      const updatedAddresses = [...addressList];
      updatedAddresses[editingIndex] = {
        ...updatedAddresses[editingIndex],
        [name]: value,
      };
      setAddressList(updatedAddresses);
    } else {
      // Adding new address
      setNewAddress({
        ...newAddress,
        [name]: value,
      });
    }
  };

  const handleCheckboxChange = (checked: boolean) => {
    if (editingIndex !== null) {
      // If setting an address as default, unset all others
      if (checked) {
        const updatedAddresses = addressList.map((addr, idx) => ({
          ...addr,
          isDefault: idx === editingIndex,
        }));
        setAddressList(updatedAddresses);
      } else {
        // If unsetting default, just update this address
        const updatedAddresses = [...addressList];
        updatedAddresses[editingIndex] = {
          ...updatedAddresses[editingIndex],
          isDefault: checked,
        };
        setAddressList(updatedAddresses);
      }
    } else {
      // For new address
      if (checked) {
        // If setting new address as default, unset all others
        setAddressList(addressList.map(addr => ({
          ...addr,
          isDefault: false,
        })));
      }
      setNewAddress({
        ...newAddress,
        isDefault: checked,
      });
    }
  };

  const validateAddress = (address: Address): boolean => {
    if (!address.street.trim()) {
      toast.error("Street address is required");
      return false;
    }
    if (!address.city.trim()) {
      toast.error("City is required");
      return false;
    }
    if (!address.postalCode.trim()) {
      toast.error("Postal code is required");
      return false;
    }
    if (!address.country.trim()) {
      toast.error("Country is required");
      return false;
    }
    return true;
  };

  const handleAddAddress = () => {
    if (!validateAddress(newAddress)) return;

    // Ensure at least one address is default
    const addressesToSave = [...addressList, newAddress];
    if (!addressesToSave.some(addr => addr.isDefault)) {
      addressesToSave[0].isDefault = true;
    }

    setAddressList(addressesToSave);
    setNewAddress({
      street: "",
      city: "",
      state: "",
      postalCode: "",
      country: "",
      isDefault: false,
    });
    setIsAdding(false);
    toast.success("Address added successfully");
  };

  const handleUpdateAddress = () => {
    if (editingIndex === null) return;

    if (!validateAddress(addressList[editingIndex])) return;

    // Ensure at least one address is default
    if (!addressList.some(addr => addr.isDefault)) {
      addressList[0].isDefault = true;
    }

    setEditingIndex(null);
    toast.success("Address updated successfully");
  };

  const handleDeleteAddress = (index: number) => {
    const wasDefault = addressList[index].isDefault;
    const updatedAddresses = addressList.filter((_, idx) => idx !== index);

    // If we deleted the default address and there are other addresses, make the first one default
    if (wasDefault && updatedAddresses.length > 0) {
      updatedAddresses[0].isDefault = true;
    }

    setAddressList(updatedAddresses);
    toast.success("Address deleted successfully");
  };

  const handleSave = () => {
    onSave(addressList);
    toast.success("Addresses saved successfully");
  };

  const renderAddressForm = (address: Address, isNew: boolean = false) => (
    <div className="space-y-4 p-4 border rounded-md bg-muted/30">
      <div className="grid grid-cols-1 gap-4">
        <div className="space-y-2">
          <Label htmlFor={isNew ? "new-street" : `street-${editingIndex}`}>Street Address</Label>
          <Input
            id={isNew ? "new-street" : `street-${editingIndex}`}
            name="street"
            value={address.street}
            onChange={handleInputChange}
            placeholder="123 Main St"
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor={isNew ? "new-city" : `city-${editingIndex}`}>City</Label>
            <Input
              id={isNew ? "new-city" : `city-${editingIndex}`}
              name="city"
              value={address.city}
              onChange={handleInputChange}
              placeholder="City"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor={isNew ? "new-state" : `state-${editingIndex}`}>State/Province</Label>
            <Input
              id={isNew ? "new-state" : `state-${editingIndex}`}
              name="state"
              value={address.state}
              onChange={handleInputChange}
              placeholder="State"
            />
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor={isNew ? "new-postalCode" : `postalCode-${editingIndex}`}>Postal Code</Label>
            <Input
              id={isNew ? "new-postalCode" : `postalCode-${editingIndex}`}
              name="postalCode"
              value={address.postalCode}
              onChange={handleInputChange}
              placeholder="Postal Code"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor={isNew ? "new-country" : `country-${editingIndex}`}>Country</Label>
            <Input
              id={isNew ? "new-country" : `country-${editingIndex}`}
              name="country"
              value={address.country}
              onChange={handleInputChange}
              placeholder="Country"
            />
          </div>
        </div>

        <div className="flex items-center space-x-2 pt-2">
          <Checkbox
            id={isNew ? "new-isDefault" : `isDefault-${editingIndex}`}
            checked={address.isDefault}
            onCheckedChange={handleCheckboxChange}
          />
          <Label
            htmlFor={isNew ? "new-isDefault" : `isDefault-${editingIndex}`}
            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
          >
            Set as default address
          </Label>
        </div>
      </div>

      <div className="flex justify-end space-x-2 pt-2">
        <Button
          variant="outline"
          onClick={() => {
            if (isNew) {
              setIsAdding(false);
            } else {
              setEditingIndex(null);
            }
          }}
        >
          <X className="mr-2 h-4 w-4" />
          Cancel
        </Button>
        <Button
          onClick={isNew ? handleAddAddress : handleUpdateAddress}
        >
          <Check className="mr-2 h-4 w-4" />
          {isNew ? "Add Address" : "Update Address"}
        </Button>
      </div>
    </div>
  );

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="text-xl font-semibold">Manage Addresses</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* List of existing addresses */}
        {addressList.length > 0 ? (
          <div className="space-y-4">
            {addressList.map((address, index) => (
              <div key={index} className="p-4 border rounded-md relative">
                {editingIndex === index ? (
                  renderAddressForm(address)
                ) : (
                  <>
                    <div className="absolute top-2 right-2 flex space-x-2">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => setEditingIndex(index)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleDeleteAddress(index)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                    <div className="space-y-2 pr-16">
                      {address.isDefault && (
                        <Badge variant="outline" className="mb-2">Default</Badge>
                      )}
                      <p className="font-medium">{address.street}</p>
                      <p>{address.city}, {address.state} {address.postalCode}</p>
                      <p>{address.country}</p>
                    </div>
                  </>
                )}
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center p-6 border border-dashed rounded-md">
            <p className="text-muted-foreground">No addresses added yet</p>
          </div>
        )}

        {/* Add new address form */}
        {isAdding ? (
          renderAddressForm(newAddress, true)
        ) : (
          <Button
            variant="outline"
            className="w-full"
            onClick={() => setIsAdding(true)}
          >
            <PlusCircle className="mr-2 h-4 w-4" />
            Add New Address
          </Button>
        )}

        {/* Save button */}
        <Button onClick={handleSave} className="w-full mt-6">
          Save All Addresses
        </Button>
      </CardContent>
    </Card>
  );
};
