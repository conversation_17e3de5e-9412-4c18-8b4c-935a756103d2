// src/components/layout-editor/Toolbar.tsx
import React from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  PlusCircle,
  Save,
  Download,
  Upload,
  RotateCcw,
  ZoomIn,
  ZoomOut,
} from "lucide-react";
import type { ElementType } from "./types";

interface ToolbarProps {
  newElementType: ElementType;
  onNewElementTypeChange: (type: ElementType) => void;
  onAddElement: () => void;

  canUndo: boolean;
  onUndo: () => void;
  canRedo: boolean;
  onRedo: () => void;

  zoom: number;
  minZoom: number;
  maxZoom: number;
  onZoomIn: () => void;
  onZoomOut: () => void;

  gridSize: number;
  onGridSizeChange: (size: number) => void;
  snapToGrid: boolean;
  onSnapToggle: (snap: boolean) => void;

  saveStatus: "idle" | "saving" | "success" | "error";
  onSave?: () => void; // Optional save handler
  onLoad?: () => void; // Optional load handler
  onExport: () => void;
  onImport: (event: React.ChangeEvent<HTMLInputElement>) => void;

  // Constants passed down for UI constraints
  defaultGridSize: number;
}

export const Toolbar: React.FC<ToolbarProps> = ({
  newElementType,
  onNewElementTypeChange,
  onAddElement,
  canUndo,
  onUndo,
  canRedo,
  onRedo,
  zoom,
  minZoom,
  maxZoom,
  onZoomIn,
  onZoomOut,
  gridSize,
  onGridSizeChange,
  snapToGrid,
  onSnapToggle,
  saveStatus,
  onSave,
  onLoad,
  onExport,
  onImport,
  defaultGridSize,
}) => {
  return (
    <div className="flex gap-2 flex-wrap items-center">
      <TooltipProvider delayDuration={100}>
        {/* Add Element */}
        <Select value={newElementType} onValueChange={onNewElementTypeChange}>
          <SelectTrigger className="w-auto min-w-[150px] h-9">
            <SelectValue placeholder="Select Element" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="table-round">Round Table</SelectItem>
            <SelectItem value="table-rect">Rect Table</SelectItem>
            <SelectItem value="chair">Chair</SelectItem>
            <SelectItem value="window">Window</SelectItem>
            <SelectItem value="door">Door</SelectItem>
            <SelectItem value="stairs">Stairs</SelectItem>
            <SelectItem value="wall">Wall</SelectItem>
            <SelectItem value="private-room">Private Room</SelectItem>
            <SelectItem value="bar">Bar</SelectItem>
            <SelectItem value="kitchen">Kitchen</SelectItem>
          </SelectContent>
        </Select>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button onClick={onAddElement} size="icon" variant="outline">
              <PlusCircle className="w-4 h-4" />
            </Button>
          </TooltipTrigger>
          <TooltipContent>Add Selected Element</TooltipContent>
        </Tooltip>

        {/* Save/Load/IO */}
        {onSave && (
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                onClick={onSave}
                disabled={saveStatus === "saving"}
                size="icon"
                variant="outline"
              >
                <Save
                  className={`w-4 h-4 ${
                    saveStatus === "saving" ? "animate-spin" : ""
                  }`}
                />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              {saveStatus === "saving"
                ? "Saving..."
                : saveStatus === "success"
                ? "Saved!"
                : saveStatus === "error"
                ? "Save Error!"
                : "Save Layout"}
            </TooltipContent>
          </Tooltip>
        )}
        {onLoad && (
          <Tooltip>
            <TooltipTrigger asChild>
              <Button onClick={onLoad} size="icon" variant="outline">
                <Download className="w-4 h-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>Load Saved Layout</TooltipContent>
          </Tooltip>
        )}
        <Tooltip>
          <TooltipTrigger asChild>
            <Button onClick={onExport} size="icon" variant="outline">
              <Download className="w-4 h-4 text-blue-600" />
            </Button>
          </TooltipTrigger>
          <TooltipContent>Export Layout (JSON)</TooltipContent>
        </Tooltip>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              size="icon"
              variant="outline"
              className="relative overflow-hidden"
            >
              <Upload className="w-4 h-4 text-green-600" />
              <Input
                type="file"
                accept=".json"
                onChange={onImport}
                className="absolute inset-0 opacity-0 cursor-pointer w-full h-full"
                title="Import layout from JSON file"
              />
            </Button>
          </TooltipTrigger>
          <TooltipContent>Import Layout (JSON)</TooltipContent>
        </Tooltip>

        {/* History */}
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              onClick={onUndo}
              disabled={!canUndo}
              size="icon"
              variant="outline"
            >
              <RotateCcw className="w-4 h-4" />
            </Button>
          </TooltipTrigger>
          <TooltipContent>Undo (Ctrl+Z)</TooltipContent>
        </Tooltip>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              onClick={onRedo}
              disabled={!canRedo}
              size="icon"
              variant="outline"
            >
              <RotateCcw className="w-4 h-4 transform scale-x-[-1]" />
            </Button>
          </TooltipTrigger>
          <TooltipContent>Redo (Ctrl+Y)</TooltipContent>
        </Tooltip>

        {/* Zoom */}
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              onClick={onZoomIn}
              disabled={zoom >= maxZoom}
              size="icon"
              variant="outline"
            >
              <ZoomIn className="w-4 h-4" />
            </Button>
          </TooltipTrigger>
          <TooltipContent>Zoom In</TooltipContent>
        </Tooltip>
        <span className="text-xs font-semibold w-10 text-center">
          {(zoom * 100).toFixed(0)}%
        </span>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              onClick={onZoomOut}
              disabled={zoom <= minZoom}
              size="icon"
              variant="outline"
            >
              <ZoomOut className="w-4 h-4" />
            </Button>
          </TooltipTrigger>
          <TooltipContent>Zoom Out</TooltipContent>
        </Tooltip>

        {/* Grid */}
        <div className="flex items-center gap-2 border-l pl-2 ml-2">
          <Label htmlFor="gridSize" className="text-xs">
            Grid
          </Label>
          <Input
            id="gridSize"
            type="number"
            min="5"
            max="100"
            step="5"
            value={gridSize}
            onChange={(e) =>
              onGridSizeChange(
                Math.max(
                  5,
                  Math.min(100, parseInt(e.target.value) || defaultGridSize)
                )
              )
            }
            className="w-16 h-8 text-xs"
          />
          <Tooltip>
            <TooltipTrigger asChild>
              <Switch checked={snapToGrid} onCheckedChange={onSnapToggle} />
            </TooltipTrigger>
            <TooltipContent>
              {snapToGrid ? "Snap to Grid On" : "Snap to Grid Off"}
            </TooltipContent>
          </Tooltip>
        </div>
      </TooltipProvider>
    </div>
  );
};