import { Button } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  Di<PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Reservation, Table, LayoutElement, Restaurant } from "./types";
import TableLayout from "./TableLayout";
import { toast } from "sonner";

interface EditReservationDialogProps {
  editingReservation: Reservation | null;
  setEditingReservation: (value: Reservation | null) => void;
  editForm: {
    date: string;
    arrivalTime: string;
    departureTime: string;
    partySize: number;
    selectedTableId: string;
  };
  setEditForm: (
    value: React.SetStateAction<{
      date: string;
      arrivalTime: string;
      departureTime: string;
      partySize: number;
      selectedTableId: string;
    }>
  ) => void;
  handleUpdateReservation: () => void;
  layout: LayoutElement[];
  tables: Table[];
  reservations: Reservation[];
  restaurant: Restaurant;
  isValidDateTime: (
    date: string,
    time: string,
    departureTime?: string
  ) => boolean;
  isRestaurantOpen: (
    restaurant: Restaurant | null,
    date: string,
    time: string
  ) => boolean;
  checkTableAvailability: (
    restaurant: Restaurant | null,
    tables: Table[],
    reservations: Reservation[],
    newReservation: {
      date: string;
      arrivalTime: string;
      departureTime: string;
      partySize: number;
    },
    isRestaurantOpen: (
      restaurant: Restaurant | null,
      date: string,
      time: string
    ) => boolean
  ) => Table[];
}

const EditReservationDialog = ({
  editingReservation,
  setEditingReservation,
  editForm,
  setEditForm,
  handleUpdateReservation,
  layout,
  tables,
  reservations,
  restaurant,
  isValidDateTime,
  isRestaurantOpen,
  checkTableAvailability,
}: EditReservationDialogProps) => {
  return (
    <Dialog
      open={!!editingReservation}
      onOpenChange={() => setEditingReservation(null)}
    >
      <DialogContent hideCloseButton>
        <DialogHeader>
          <DialogTitle>Edit Reservation</DialogTitle>
          <DialogDescription>Update your reservation details</DialogDescription>
        </DialogHeader>
        <div className="space-y-4">
          <div>
            <Label htmlFor="date">Date</Label>
            <Input
              id="date"
              type="date"
              value={editForm.date}
              min={new Date().toLocaleDateString("en-CA", {
                timeZone: "Asia/Baku",
              })}
              onChange={(e) => {
                const now = new Date();
                const selected = new Date(e.target.value);
                selected.setHours(0, 0, 0, 0);
                now.setHours(0, 0, 0, 0);

                if (selected < now) {
                  toast.error("Cannot select past dates");
                  return;
                }
                setEditForm((prev) => ({ ...prev, date: e.target.value }));
              }}
            />
          </div>
          <div>
            <Label htmlFor="arrivalTime">Arrival Time</Label>
            <Input
              id="arrivalTime"
              type="time"
              value={editForm.arrivalTime}
              onChange={(e) => {
                if (!isValidDateTime(editForm.date, e.target.value)) {
                  const selectedDate = new Date(editForm.date);
                  const today = new Date();
                  const isToday =
                    selectedDate.toDateString() === today.toDateString();
                  if (isToday) {
                    toast.error(
                      "Please book at least 15 minutes in advance for today's reservations"
                    );
                  } else {
                    toast.error("Please select a valid future time");
                  }
                  return;
                }
                setEditForm((prev) => ({
                  ...prev,
                  arrivalTime: e.target.value,
                }));
              }}
            />
          </div>
          <div>
            <Label htmlFor="departureTime">Departure Time (Optional)</Label>
            <Input
              id="departureTime"
              type="time"
              value={editForm.departureTime}
              onChange={(e) => {
                if (
                  !isValidDateTime(
                    editForm.date,
                    editForm.arrivalTime,
                    e.target.value
                  )
                ) {
                  const selectedDate = new Date(editForm.date);
                  const today = new Date();
                  const isToday =
                    selectedDate.toDateString() === today.toDateString();
                  if (isToday) {
                    toast.error(
                      "Please book at least 15 minutes in advance for today's reservations"
                    );
                  } else {
                    toast.error("Please select a valid future date and time");
                  }
                  return;
                }
                setEditForm((prev) => ({
                  ...prev,
                  departureTime: e.target.value,
                }));
              }}
            />
          </div>
          <div>
            <Label htmlFor="partySize">Party Size</Label>
            <Input
              id="partySize"
              type="number"
              min="1"
              max="20"
              value={editForm.partySize}
              onChange={(e) =>
                setEditForm((prev) => ({
                  ...prev,
                  partySize: parseInt(e.target.value),
                }))
              }
            />
          </div>
          <div>
            <Label>Available Tables</Label>
            <TableLayout
              layout={layout}
              tables={tables}
              selectedTableId={editForm.selectedTableId}
              onTableSelect={(tableId) =>
                setEditForm((prev) => ({ ...prev, selectedTableId: tableId }))
              }
              isEditMode={true}
              reservations={reservations.filter(
                (r) => r.id !== editingReservation?.id
              )}
              restaurant={restaurant}
              editForm={editForm}
              isRestaurantOpen={isRestaurantOpen}
              checkTableAvailability={checkTableAvailability}
            />
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => setEditingReservation(null)}>
            Cancel
          </Button>
          <Button onClick={handleUpdateReservation}>Update Reservation</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default EditReservationDialog;
