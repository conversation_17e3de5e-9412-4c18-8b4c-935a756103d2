/**
 * Rate limiting middleware
 */
const rateLimit = require('express-rate-limit');
const logger = require('../utils/logger');

/**
 * Create a rate limiter with custom options
 * @param {Object} options - Rate limiter options
 * @returns {Function} - Express middleware
 */
const createRateLimiter = (options = {}) => {
  const defaultOptions = {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // Limit each IP to 100 requests per windowMs
    standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
    legacyHeaders: false, // Disable the `X-RateLimit-*` headers
    message: { error: 'Too many requests, please try again later' },
    skip: (req) => process.env.NODE_ENV === 'development', // Skip in development
    keyGenerator: (req) => {
      // Use API key as identifier if available, otherwise use IP
      return req.apiKey?.clientId || req.headers['x-forwarded-for'] || req.ip;
    },
    handler: (req, res, next, options) => {
      const clientIp = req.headers['x-forwarded-for'] || req.ip || 'unknown';
      const path = req.path;
      const method = req.method;
      
      logger.warn(`Rate limit exceeded for ${clientIp} on ${method} ${path}`, {
        ip: clientIp,
        path,
        method,
        apiKey: req.apiKey?.id,
        clientId: req.apiKey?.clientId,
      });
      
      res.status(options.statusCode).json(options.message);
    },
  };
  
  return rateLimit({
    ...defaultOptions,
    ...options,
  });
};

// General API rate limiter
const apiLimiter = createRateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // 100 requests per 15 minutes
});

// Stricter rate limiter for authentication endpoints
const authLimiter = createRateLimiter({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 10, // 10 requests per hour
});

// Rate limiter for email sending endpoints
const emailLimiter = createRateLimiter({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 20, // 20 emails per hour
  message: { error: 'Email sending rate limit exceeded. Please try again later.' },
});

// Rate limiter for newsletter subscription endpoints
const subscriptionLimiter = createRateLimiter({
  windowMs: 24 * 60 * 60 * 1000, // 24 hours
  max: 5, // 5 subscription attempts per 24 hours per IP
  message: { error: 'Too many subscription attempts. Please try again tomorrow.' },
});

module.exports = {
  apiLimiter,
  authLimiter,
  emailLimiter,
  subscriptionLimiter,
  createRateLimiter,
};
