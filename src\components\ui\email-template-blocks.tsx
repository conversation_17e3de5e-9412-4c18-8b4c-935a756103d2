import { Card, CardContent } from "@/components/ui/card";
import {
  <PERSON><PERSON><PERSON>,
  Toolt<PERSON>Content,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Image,
  Columns,
  Minus,
  Sparkles,
  LayoutTemplate,
  FileText,
  AlignCenter,
  Grid2X2,
  Footprints,
  Star,
  MessageSquareQuote,
  ArrowRightCircle,
} from "lucide-react";

// Define template block types
export type TemplateBlockType =
  | "header"
  | "text"
  | "image"
  | "button"
  | "spacer"
  | "divider"
  | "columns-2"
  | "columns-3"
  | "social"
  | "footer"
  | "hero"
  | "feature"
  | "testimonial"
  | "pricing";

// Define template block interface
export interface TemplateBlock {
  type: TemplateBlockType;
  name: string;
  icon: React.ReactNode;
  description: string;
  html: string;
}

// Define template blocks
// eslint-disable-next-line react-refresh/only-export-components
export const templateBlocks: TemplateBlock[] = [
  {
    type: "header",
    name: "Header",
    icon: <LayoutTemplate size={22} />,
    description: "Add a header section with logo and navigation",
    html: `
      <div style="padding: 20px; text-align: center; background-color: #f8f9fa;">
        <img src="https://via.placeholder.com/150x50" alt="Logo" style="max-width: 150px; height: auto;" />
        <div style="margin-top: 10px;">
          <a href="#" style="margin: 0 10px; color: #333333; text-decoration: none;">Home</a>
          <a href="#" style="margin: 0 10px; color: #333333; text-decoration: none;">Products</a>
          <a href="#" style="margin: 0 10px; color: #333333; text-decoration: none;">About</a>
          <a href="#" style="margin: 0 10px; color: #333333; text-decoration: none;">Contact</a>
        </div>
      </div>
    `,
  },
  {
    type: "hero",
    name: "Hero Section",
    icon: <Star size={22} />,
    description: "Add a hero section with image and call-to-action",
    html: `
      <div style="padding: 40px 20px; text-align: center; background-color: #f0f4f8;">
        <h1 style="color: #333333; margin-bottom: 20px; font-size: 28px;">Welcome to Our Newsletter</h1>
        <p style="color: #666666; margin-bottom: 30px; font-size: 16px; line-height: 1.6;">Stay updated with the latest news, trends, and exclusive offers.</p>
        <img src="https://via.placeholder.com/600x300" alt="Hero Image" style="max-width: 100%; height: auto; margin-bottom: 30px; border-radius: 8px;" />
        <a href="#" style="background-color: #ff6200; color: #ffffff; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold;">Get Started</a>
      </div>
    `,
  },
  {
    type: "text",
    name: "Text Block",
    icon: <FileText size={22} />,
    description: "Add a text paragraph",
    html: `
      <div style="padding: 20px;">
        <p style="color: #666666; line-height: 1.6; font-size: 16px;">
          Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam in dui mauris. Vivamus hendrerit arcu sed erat molestie vehicula. Sed auctor neque eu tellus rhoncus ut eleifend nibh porttitor. Ut in nulla enim.
        </p>
      </div>
    `,
  },
  {
    type: "image",
    name: "Image",
    icon: <Image size={22} />,
    description: "Add an image",
    html: `
      <div style="padding: 20px; text-align: center;">
        <img src="https://via.placeholder.com/600x300" alt="Image" style="max-width: 100%; height: auto; border-radius: 8px;" />
        <p style="color: #999999; font-size: 14px; margin-top: 10px;">Image caption goes here</p>
      </div>
    `,
  },
  {
    type: "button",
    name: "Button",
    icon: <ArrowRightCircle size={22} />,
    description: "Add a call-to-action button",
    html: `
      <div style="padding: 20px; text-align: center;">
        <a href="#" style="background-color: #ff6200; color: #ffffff; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold; display: inline-block;">Click Here</a>
      </div>
    `,
  },
  {
    type: "columns-2",
    name: "2 Columns",
    icon: <Columns size={22} />,
    description: "Add a two-column layout",
    html: `
      <div style="padding: 20px; display: table; width: 100%;">
        <div style="display: table-cell; width: 50%; padding-right: 10px; vertical-align: top;">
          <h3 style="color: #333333; margin-bottom: 10px;">Column 1</h3>
          <p style="color: #666666; line-height: 1.6;">Content for the first column goes here. You can add text, images, or other elements.</p>
        </div>
        <div style="display: table-cell; width: 50%; padding-left: 10px; vertical-align: top;">
          <h3 style="color: #333333; margin-bottom: 10px;">Column 2</h3>
          <p style="color: #666666; line-height: 1.6;">Content for the second column goes here. You can add text, images, or other elements.</p>
        </div>
      </div>
    `,
  },
  {
    type: "columns-3",
    name: "3 Columns",
    icon: <Grid2X2 size={22} />,
    description: "Add a three-column layout",
    html: `
      <div style="padding: 20px; display: table; width: 100%;">
        <div style="display: table-cell; width: 33.33%; padding-right: 10px; vertical-align: top;">
          <h3 style="color: #333333; margin-bottom: 10px;">Column 1</h3>
          <p style="color: #666666; line-height: 1.6;">Content for the first column goes here.</p>
        </div>
        <div style="display: table-cell; width: 33.33%; padding-left: 5px; padding-right: 5px; vertical-align: top;">
          <h3 style="color: #333333; margin-bottom: 10px;">Column 2</h3>
          <p style="color: #666666; line-height: 1.6;">Content for the second column goes here.</p>
        </div>
        <div style="display: table-cell; width: 33.33%; padding-left: 10px; vertical-align: top;">
          <h3 style="color: #333333; margin-bottom: 10px;">Column 3</h3>
          <p style="color: #666666; line-height: 1.6;">Content for the third column goes here.</p>
        </div>
      </div>
    `,
  },
  {
    type: "feature",
    name: "Feature Block",
    icon: <Sparkles size={22} />,
    description: "Add a feature highlight with icon and text",
    html: `
      <div style="padding: 20px; text-align: center; background-color: #ffffff; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
        <div style="width: 60px; height: 60px; background-color: #f0f4f8; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 15px;">
          <img src="https://via.placeholder.com/30" alt="Feature Icon" style="width: 30px; height: 30px;" />
        </div>
        <h3 style="color: #333333; margin-bottom: 10px; font-size: 18px;">Feature Title</h3>
        <p style="color: #666666; line-height: 1.6; font-size: 14px;">A brief description of this amazing feature and how it benefits your customers.</p>
      </div>
    `,
  },
  {
    type: "testimonial",
    name: "Testimonial",
    icon: <MessageSquareQuote size={22} />,
    description: "Add a customer testimonial",
    html: `
      <div style="padding: 20px; background-color: #f9f9f9; border-radius: 8px; margin: 20px 0;">
        <div style="font-style: italic; color: #666666; line-height: 1.6; font-size: 16px; margin-bottom: 15px;">
          "This product has completely transformed how we work. The team behind it is incredibly responsive and helpful. Highly recommended!"
        </div>
        <div style="display: flex; align-items: center;">
          <img src="https://via.placeholder.com/50" alt="Customer" style="width: 50px; height: 50px; border-radius: 50%; margin-right: 15px;" />
          <div>
            <div style="font-weight: bold; color: #333333;">Jane Smith</div>
            <div style="color: #999999; font-size: 14px;">CEO, Company Name</div>
          </div>
        </div>
      </div>
    `,
  },
  {
    type: "divider",
    name: "Divider",
    icon: <Minus size={22} />,
    description: "Add a horizontal divider",
    html: `
      <div style="padding: 10px 20px;">
        <hr style="border: none; border-top: 1px solid #eeeeee; margin: 0;" />
      </div>
    `,
  },
  {
    type: "spacer",
    name: "Spacer",
    icon: <AlignCenter size={22} />,
    description: "Add vertical space",
    html: `
      <div style="height: 40px;"></div>
    `,
  },
  {
    type: "footer",
    name: "Footer",
    icon: <Footprints size={22} />,
    description: "Add a footer with links and copyright",
    html: `
      <div style="padding: 30px 20px; background-color: #f8f9fa; text-align: center;">
        <div style="margin-bottom: 20px;">
          <a href="#" style="margin: 0 10px; color: #666666; text-decoration: none; font-size: 14px;">Privacy Policy</a>
          <a href="#" style="margin: 0 10px; color: #666666; text-decoration: none; font-size: 14px;">Terms of Service</a>
          <a href="#" style="margin: 0 10px; color: #666666; text-decoration: none; font-size: 14px;">Unsubscribe</a>
        </div>
        <div style="color: #999999; font-size: 12px;">
          © 2024 Your Company. All rights reserved.
        </div>
        <div style="color: #999999; font-size: 12px; margin-top: 10px;">
          <a href="{{unsubscribeLink}}" style="color: #999999;">Unsubscribe</a> from our newsletter.
        </div>
      </div>
    `,
  },
];

// Template Block Component
interface TemplateBlockItemProps {
  block: TemplateBlock;
  onSelect: (block: TemplateBlock) => void;
}

export function TemplateBlockItem({ block, onSelect }: TemplateBlockItemProps) {
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Card
            className="cursor-pointer hover:bg-blue-50 transition-all duration-200 border border-gray-200 hover:border-blue-300 hover:shadow-md group"
            onClick={() => onSelect(block)}
          >
            <CardContent className="p-3 flex flex-col items-center justify-center text-center">
              <div className="h-12 w-12 rounded-lg bg-blue-50 group-hover:bg-blue-100 flex items-center justify-center mb-2 text-blue-600 transition-colors duration-200">
                {block.icon}
              </div>
              <div className="text-xs font-medium text-gray-700 group-hover:text-blue-700 transition-colors duration-200">
                {block.name}
              </div>
            </CardContent>
          </Card>
        </TooltipTrigger>
        <TooltipContent side="right">
          <p className="text-sm">{block.description}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}

// Template Blocks Grid Component
interface TemplateBlocksGridProps {
  onSelectBlock: (block: TemplateBlock) => void;
}

export function TemplateBlocksGrid({ onSelectBlock }: TemplateBlocksGridProps) {
  // Group blocks by category for better organization
  const blockCategories = {
    layout: ["header", "footer", "columns-2", "columns-3", "spacer", "divider"],
    content: ["text", "image", "button", "hero", "feature", "testimonial"],
  };

  const layoutBlocks = templateBlocks.filter((block) =>
    blockCategories.layout.includes(block.type)
  );
  const contentBlocks = templateBlocks.filter((block) =>
    blockCategories.content.includes(block.type)
  );

  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <h4 className="text-sm font-semibold text-gray-700 uppercase tracking-wider">
          Layout Elements
        </h4>
        <div className="grid grid-cols-2 lg:grid-cols-3 gap-3">
          {layoutBlocks.map((block) => (
            <TemplateBlockItem
              key={block.type}
              block={block}
              onSelect={onSelectBlock}
            />
          ))}
        </div>
      </div>

      <div className="space-y-4">
        <h4 className="text-sm font-semibold text-gray-700 uppercase tracking-wider">
          Content Elements
        </h4>
        <div className="grid grid-cols-2 lg:grid-cols-3 gap-3">
          {contentBlocks.map((block) => (
            <TemplateBlockItem
              key={block.type}
              block={block}
              onSelect={onSelectBlock}
            />
          ))}
        </div>
      </div>
    </div>
  );
}
