import React, { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { gameService } from "@/services/GameService";
import { ArrowLeft, Calendar, CheckCircle } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Progress } from "@/components/ui/progress";
import { motion } from "framer-motion";

interface DailyCheckInProps {
  onBack: () => void;
  onComplete: () => void;
  userId: string;
  streak: number;
  lastCheckIn: Date | null;
}

export const DailyCheckIn: React.FC<DailyCheckInProps> = ({
  onBack,
  onComplete,
  userId,
  streak,
  lastCheckIn,
}) => {
  const [checking, setChecking] = useState(false);
  const [result, setResult] = useState<{
    success: boolean;
    pointsEarned: number;
    message: string;
    streakCount?: number;
  } | null>(null);

  // Check if already checked in today
  const isAlreadyCheckedIn = lastCheckIn
    ? new Date(lastCheckIn).toDateString() === new Date().toDateString()
    : false;

  // Handle check-in
  const handleCheckIn = async () => {
    setChecking(true);
    try {
      const result = await gameService.recordDailyCheckIn(userId);
      setResult(result);
      if (result.success && result.pointsEarned > 0) {
        onComplete();
      }
    } catch (error) {
      console.error("Error recording check-in:", error);
      setResult({
        success: false,
        pointsEarned: 0,
        message: "An error occurred. Please try again.",
      });
    } finally {
      setChecking(false);
    }
  };

  // Get streak bonus text
  const getStreakBonusText = () => {
    if (streak < 2) return "Start a streak for bonus points!";
    if (streak < 7)
      return `Current streak: ${streak} days (+${streak * 5} bonus points)`;
    return `Current streak: ${streak} days (Max bonus: +35 points)`;
  };

  // Get streak progress percentage
  const getStreakProgress = () => {
    return (Math.min(streak, 7) / 7) * 100;
  };

  // Get days of the week
  const getDaysOfWeek = () => {
    const days = ["S", "M", "T", "W", "T", "F", "S"];
    const today = new Date().getDay();

    return days.map((day, index) => {
      // Determine if this day is part of the streak
      const isPartOfStreak = index <= today && streak > today - index;
      // Determine if this is today
      const isToday = index === today;

      return { day, isPartOfStreak, isToday };
    });
  };

  return (
    <Card className="w-full">
      <CardContent className="p-6">
        <div className="flex justify-between items-center mb-6">
          <Button
            variant="ghost"
            size="sm"
            onClick={onBack}
            className="flex items-center"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Games
          </Button>
        </div>

        <div className="text-center mb-8">
          <h2 className="text-2xl font-bold mb-2">Daily Check-In</h2>
          <p className="text-muted-foreground">
            Check in daily to earn points and build your streak!
          </p>
        </div>

        <div className="mb-8">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm font-medium">Weekly Streak</span>
            <span className="text-sm">{Math.min(streak, 7)} / 7 days</span>
          </div>
          <Progress value={getStreakProgress()} className="h-2 mb-4" />

          <div className="grid grid-cols-7 gap-1 mb-4">
            {getDaysOfWeek().map((day, index) => (
              <div
                key={index}
                className={`aspect-square rounded-full flex items-center justify-center text-xs font-medium ${
                  day.isToday
                    ? "bg-primary text-primary-foreground"
                    : day.isPartOfStreak
                    ? "bg-primary/20 text-primary"
                    : "bg-muted text-muted-foreground"
                }`}
              >
                {day.day}
                {day.isPartOfStreak && (
                  <span className="absolute -bottom-1 -right-1">
                    <CheckCircle className="h-3 w-3 text-primary" />
                  </span>
                )}
              </div>
            ))}
          </div>

          <p className="text-sm text-muted-foreground text-center">
            {getStreakBonusText()}
          </p>
        </div>

        <div className="bg-muted p-4 rounded-lg mb-6">
          <div className="flex items-center mb-3">
            <Calendar className="h-5 w-5 mr-2 text-primary" />
            <h3 className="font-medium">Check-In Rewards</h3>
          </div>
          <ul className="space-y-2 text-sm">
            <li className="flex justify-between">
              <span>Daily Check-In</span>
              <span className="font-medium">25 points</span>
            </li>
            <li className="flex justify-between">
              <span>Streak Bonus (per day)</span>
              <span className="font-medium">+5 points</span>
            </li>
            <li className="flex justify-between">
              <span>Maximum Streak Bonus (7+ days)</span>
              <span className="font-medium">+35 points</span>
            </li>
            <li className="flex justify-between">
              <span>Maximum Daily Total</span>
              <span className="font-medium">60 points</span>
            </li>
          </ul>
        </div>

        {result ? (
          <div className="text-center py-4">
            <Alert
              className={`mb-6 ${
                result.success ? "bg-primary/10" : "bg-destructive/10"
              }`}
            >
              <AlertTitle>{result.success ? "Success!" : "Oops!"}</AlertTitle>
              <AlertDescription>{result.message}</AlertDescription>
              {result.pointsEarned > 0 && (
                <div className="mt-2 font-medium">
                  +{result.pointsEarned} points earned!
                </div>
              )}
              {result.streakCount && result.streakCount > 1 && (
                <div className="mt-1 text-sm">
                  Current streak: {result.streakCount} days
                </div>
              )}
            </Alert>
            <Button onClick={onBack}>Return to Games</Button>
          </div>
        ) : (
          <div className="text-center">
            <motion.div whileHover={{ scale: 1.03 }} whileTap={{ scale: 0.97 }}>
              <Button
                size="lg"
                className="w-full"
                onClick={handleCheckIn}
                disabled={checking || isAlreadyCheckedIn}
              >
                {checking
                  ? "Checking in..."
                  : isAlreadyCheckedIn
                  ? "Already Checked In Today"
                  : "Check In Now"}
              </Button>
            </motion.div>
            {isAlreadyCheckedIn && (
              <p className="text-sm text-muted-foreground mt-2">
                You've already checked in today. Come back tomorrow!
              </p>
            )}
            {lastCheckIn && (
              <p className="text-xs text-muted-foreground mt-4">
                Last check-in: {new Date(lastCheckIn).toLocaleDateString()} at{" "}
                {new Date(lastCheckIn).toLocaleTimeString()}
              </p>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};
