// src/components/layout-editor/TablesPanel.tsx
import React, { useState, useRef, useCallback, useEffect } from "react";
import { toast } from "sonner";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

// Import the new components
import { Toolbar } from "../layout-editor/Toolbar";
import { Canvas } from "../layout-editor/Canvas";
import { PropertiesPanel } from "../layout-editor/PropertiesPanel";

// Import types and utils
import type { Element, ElementType } from "../layout-editor/types";
import {
  getDefaultColor,
  getDefaultSize,
  getDefaultCapacity,
} from "../layout-editor/utils";

// --- Constants ---
const MAX_HISTORY_LENGTH = 30;
const DEFAULT_GRID_SIZE = 20;
const DEFAULT_MIN_ZOOM = 0.5;
const DEFAULT_MAX_ZOOM = 2.0;

// --- Props Interface ---
interface TablesPanelProps {
  initialElements?: Element[];
  onSave?: (elements: Element[]) => Promise<void>; // Expects parent to handle sanitization
  onLoad?: () => Promise<Element[]>;
  maxZoom?: number;
  minZoom?: number;
  gridSnapSize?: number;
  canvasWidth?: number; // Canvas width for responsive sizing
  canvasHeight?: number; // Canvas height for responsive sizing
}

// --- Main Component ---
export const TablesPanel: React.FC<TablesPanelProps> = ({
  initialElements = [],
  onSave,
  onLoad,
  maxZoom = DEFAULT_MAX_ZOOM,
  minZoom = DEFAULT_MIN_ZOOM,
  gridSnapSize = DEFAULT_GRID_SIZE,
  canvasWidth, // Add canvas width prop
  canvasHeight, // Add canvas height prop
}) => {
  // --- State ---
  const [elements, setElements] = useState<Element[]>(initialElements);
  const [selectedElements, setSelectedElements] = useState<string[]>([]);
  const [newElementType, setNewElementType] =
    useState<ElementType>("table-round");
  const [zoom, setZoom] = useState(1);
  const [gridSize, setGridSize] = useState(gridSnapSize);
  const [saveStatus, setSaveStatus] = useState<
    "idle" | "saving" | "success" | "error"
  >("idle");
  const [snapToGrid, setSnapToGrid] = useState(true);
  const [history, setHistory] = useState<Element[][]>([initialElements]);
  const [historyIndex, setHistoryIndex] = useState(0);

  // --- Refs ---
  const layoutRef = useRef<HTMLDivElement>(null);

  // --- State Update & History ---
  const updateElementsState = useCallback(
    (
      newElements: Element[] | ((prev: Element[]) => Element[]),
      updateHistory = true
    ) => {
      // Use functional update form of setElements
      setElements((prevElements) => {
        const updatedElements =
          typeof newElements === "function"
            ? newElements(prevElements)
            : newElements;

        if (updateHistory) {
          // Use functional update form for history/index to ensure atomicity
          // Capture the correct historyIndex *before* updating it
          const currentHistoryIndex = historyIndex;
          setHistory((prevHistory) => {
            const nextHistory = prevHistory.slice(0, currentHistoryIndex + 1);
            nextHistory.push(updatedElements);
            return nextHistory.length > MAX_HISTORY_LENGTH
              ? nextHistory.slice(-MAX_HISTORY_LENGTH)
              : nextHistory;
          });
          setHistoryIndex((prevIndex) =>
            Math.min(prevIndex + 1, MAX_HISTORY_LENGTH - 1)
          );
        }
        return updatedElements; // Return the updated state for setElements
      });
    },
    [historyIndex] // Depends only on historyIndex to correctly slice history
  );

  // --- Element Actions ---

  const addElement = useCallback(() => {
    const { width, height } = getDefaultSize(newElementType);
    const currentElementsLength = elements.length; // Get length before update
    const newElement: Element = {
      id: crypto.randomUUID(),
      type: newElementType,
      // Ensure new elements are placed exactly on grid points
      x: snapToGrid ? Math.floor(100 / gridSize) * gridSize : 100,
      y: snapToGrid ? Math.floor(100 / gridSize) * gridSize : 100,
      width,
      height,
      rotation: 0,
      zIndex: currentElementsLength, // Use length before adding
      opacity: 1,
      borderRadius: newElementType === "table-round" ? 9999 : 0,
      isLocked: false,
      color: getDefaultColor(newElementType),
      borderColor: "#000000",
      // Initialize optional fields explicitly to null or defaults
      name: undefined,
      capacity: getDefaultCapacity(newElementType),
      isGrouped: false,
      groupId: undefined,
    };

    updateElementsState((prevElements) => [...prevElements, newElement]);
    setSelectedElements([newElement.id]);
    toast.info(`Added ${newElementType}`);
  }, [
    newElementType,
    snapToGrid,
    gridSize,
    elements.length, // Need length for zIndex calculation
    updateElementsState,
  ]);

  const updateElement = useCallback(
    (id: string, updates: Partial<Element>, updateHistory = true) => {
      updateElementsState(
        (prevElements) =>
          prevElements.map((el) => (el.id === id ? { ...el, ...updates } : el)),
        updateHistory
      );
    },
    [updateElementsState] // Dependency is the state updater function itself
  );

  const deleteSelectedElements = useCallback(() => {
    if (selectedElements.length === 0) return;
    const elementCount = selectedElements.length;

    updateElementsState((prevElements) =>
      prevElements.filter((el) => !selectedElements.includes(el.id))
    );
    // Ref cleanup is handled by the useEffect hook

    setSelectedElements([]); // Deselect after deletion
    toast.success(
      `${elementCount} Element${elementCount > 1 ? "s" : ""} deleted`
    );
  }, [selectedElements, updateElementsState]);

  const duplicateSelectedElements = useCallback(() => {
    if (selectedElements.length === 0) return;

    const duplicates: Element[] = [];
    let currentZIndex =
      elements.reduce((maxZ, el) => Math.max(maxZ, el.zIndex), 0) + 1;

    selectedElements.forEach((id) => {
      const original = elements.find((el) => el.id === id);
      if (original) {
        duplicates.push({
          ...original,
          id: crypto.randomUUID(),
          // Ensure duplicated elements are placed exactly on grid points
          x: snapToGrid
            ? Math.floor((original.x + gridSize) / gridSize) * gridSize
            : original.x + gridSize,
          y: snapToGrid
            ? Math.floor((original.y + gridSize) / gridSize) * gridSize
            : original.y + gridSize,
          zIndex: currentZIndex++,
          isLocked: false, // Duplicates are unlocked
          isGrouped: false, // Duplicates are not grouped initially
          groupId: undefined, // Explicitly undefined
        });
      }
    });

    if (duplicates.length > 0) {
      updateElementsState((prevElements) => [...prevElements, ...duplicates]);
      setSelectedElements(duplicates.map((el) => el.id));
      toast.success(
        `${duplicates.length} Element${
          duplicates.length > 1 ? "s" : ""
        } duplicated`
      );
    }
  }, [selectedElements, elements, gridSize, snapToGrid, updateElementsState]);

  const groupSelectedElements = useCallback(() => {
    if (selectedElements.length < 2) {
      toast.warning("Select at least two elements to group.");
      return;
    }
    const groupId = crypto.randomUUID();
    updateElementsState((prevElements) =>
      prevElements.map((el) =>
        selectedElements.includes(el.id)
          ? { ...el, isGrouped: true, groupId }
          : el
      )
    );
    toast.success("Elements grouped successfully");
  }, [selectedElements, updateElementsState]);

  const ungroupSelectedElements = useCallback(() => {
    // Determine which of the *currently selected* elements are actually grouped
    const elementsToUngroup = selectedElements.filter(
      (id) => elements.find((el) => el.id === id)?.isGrouped // Need 'elements' to check 'isGrouped' status
    );

    if (elementsToUngroup.length === 0) {
      toast.info("No grouped elements selected to ungroup.");
      return;
    }

    updateElementsState((prevElements) =>
      prevElements.map((el) =>
        elementsToUngroup.includes(el.id)
          ? { ...el, isGrouped: false, groupId: undefined } // Set groupId to undefined
          : el
      )
    );
    toast.success("Selected groups ungrouped successfully");
  }, [selectedElements, elements, updateElementsState]); // Depends on elements to check isGrouped

  const alignElements = useCallback(
    (direction: "left" | "right" | "top" | "bottom") => {
      const selected = elements.filter((el) =>
        selectedElements.includes(el.id)
      );
      if (selected.length < 2) {
        toast.warning("Select at least two elements to align.");
        return;
      }

      let targetPos: number;
      switch (direction) {
        case "left":
          targetPos = Math.min(...selected.map((e) => e.x));
          break;
        case "right":
          targetPos = Math.max(...selected.map((e) => e.x + e.width));
          break;
        case "top":
          targetPos = Math.min(...selected.map((e) => e.y));
          break;
        case "bottom":
          targetPos = Math.max(...selected.map((e) => e.y + e.height));
          break;
        default:
          return;
      }

      const updates: Partial<Element>[] = selected.map((el) => {
        let newX, newY;

        switch (direction) {
          case "left":
            newX = targetPos;
            break;
          case "right":
            newX = targetPos - el.width;
            break;
          case "top":
            newY = targetPos;
            break;
          case "bottom":
            newY = targetPos - el.height;
            break;
          default:
            return {};
        }

        // Snap the new positions to grid if enabled
        if (snapToGrid) {
          if (newX !== undefined) {
            newX = Math.floor(newX / gridSize) * gridSize;
          }
          if (newY !== undefined) {
            newY = Math.floor(newY / gridSize) * gridSize;
          }
        }

        return {
          id: el.id,
          ...(newX !== undefined ? { x: newX } : {}),
          ...(newY !== undefined ? { y: newY } : {}),
        };
      });

      updateElementsState((prevElements) =>
        prevElements.map((el) => {
          const update = updates.find((u) => u.id === el.id);
          return update ? { ...el, ...update } : el;
        })
      );
      toast.info(`Aligned elements to ${direction}`);
    },
    [elements, selectedElements, updateElementsState, snapToGrid, gridSize]
  );

  // --- Save / Load / Import / Export ---
  const handleSave = useCallback(async () => {
    if (!onSave) {
      toast.error("Save function not provided.");
      return;
    }
    setSaveStatus("saving");
    try {
      // Pass the current elements state. The parent's onSave handles sanitization.
      await onSave(elements);
      setSaveStatus("success");
      toast.success("Layout saved successfully");
      setTimeout(() => setSaveStatus("idle"), 2000);
    } catch (error) {
      console.error("Save failed:", error);
      setSaveStatus("error");
      toast.error("Failed to save layout. Check console for details.");
      setTimeout(() => setSaveStatus("idle"), 3000);
    }
  }, [onSave, elements]); // Depends on current elements state

  const handleLoad = useCallback(async () => {
    if (!onLoad) {
      toast.error("Load function not provided.");
      return;
    }
    try {
      const loadedElements = await onLoad();
      if (Array.isArray(loadedElements)) {
        // Basic validation and setting defaults
        const validatedElements = loadedElements.map(
          (el: Partial<Element>) => ({
            id: el.id || crypto.randomUUID(),
            type: el.type || "table-rect",
            x: el.x ?? 0,
            y: el.y ?? 0,
            width: el.width ?? getDefaultSize(el.type || "table-rect").width,
            height: el.height ?? getDefaultSize(el.type || "table-rect").height,
            rotation: el.rotation ?? 0,
            zIndex: el.zIndex ?? 0,
            opacity: el.opacity ?? 1,
            borderRadius:
              el.borderRadius ?? (el.type === "table-round" ? 9999 : 0),
            isLocked: el.isLocked ?? false,
            name: el.name ?? undefined, // Use undefined instead of null
            capacity: el.capacity ?? undefined, // Use undefined instead of null
            color: el.color || getDefaultColor(el.type || "table-rect"),
            borderColor: el.borderColor || "#000000",
            isGrouped: el.isGrouped ?? false, // Default to false
            groupId: el.groupId ?? undefined, // Use undefined instead of null
          })
        );

        // Reset state and history directly
        setElements(validatedElements);
        setHistory([validatedElements]);
        setHistoryIndex(0);
        setSelectedElements([]);
        setZoom(1);
        toast.success("Layout loaded successfully");
      } else {
        throw new Error("Loaded data is not a valid element array.");
      }
    } catch (error) {
      console.error("Load failed:", error);
      toast.error("Failed to load layout. Check console for details.");
    }
  }, [onLoad]); // Dependencies are setters and onLoad

  const exportLayout = useCallback(() => {
    try {
      const dataStr = JSON.stringify(elements, null, 2);
      const blob = new Blob([dataStr], { type: "application/json" });
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
      link.download = `restaurant-layout-${timestamp}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      toast.success("Layout exported successfully");
    } catch (error) {
      console.error("Export failed:", error);
      toast.error("Failed to export layout.");
    }
  }, [elements]); // Depends on current elements state

  const importLayout = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const file = e.target.files?.[0];
      if (!file) return;

      const reader = new FileReader();
      reader.onload = (event) => {
        try {
          const importedData = JSON.parse(event.target?.result as string);
          if (!Array.isArray(importedData)) {
            throw new Error("Imported file is not a valid element array.");
          }
          // Validate and set defaults on import too
          const validatedElements = importedData.map(
            (el: Partial<Element>) => ({
              id: el.id || crypto.randomUUID(),
              type: el.type || "table-rect",
              x: el.x ?? 0,
              y: el.y ?? 0,
              width: el.width ?? getDefaultSize(el.type || "table-rect").width,
              height:
                el.height ?? getDefaultSize(el.type || "table-rect").height,
              rotation: el.rotation ?? 0,
              zIndex: el.zIndex ?? 0,
              opacity: el.opacity ?? 1,
              borderRadius:
                el.borderRadius ?? (el.type === "table-round" ? 9999 : 0),
              isLocked: el.isLocked ?? false,
              name: el.name ?? undefined, // Use undefined instead of null
              capacity: el.capacity ?? undefined, // Use undefined instead of null
              color: el.color || getDefaultColor(el.type || "table-rect"),
              borderColor: el.borderColor || "#000000",
              isGrouped: el.isGrouped ?? false,
              groupId: el.groupId ?? undefined, // Use undefined instead of null
            })
          );

          setElements(validatedElements);
          setHistory([validatedElements]);
          setHistoryIndex(0);
          setSelectedElements([]);
          setZoom(1);
          toast.success("Layout imported successfully");
        } catch (error) {
          console.error("Import failed:", error);
          toast.error(
            `Failed to import layout: ${
              error instanceof Error ? error.message : "Unknown error"
            }`
          );
        } finally {
          if (e.target) e.target.value = ""; // Reset file input
        }
      };
      reader.onerror = () => toast.error("Failed to read the file.");
      reader.readAsText(file);
    },
    [] // Only depends on setters
  );

  // --- History Actions ---
  const undo = useCallback(() => {
    if (historyIndex > 0) {
      const newIndex = historyIndex - 1;
      setHistoryIndex(newIndex);
      setElements(history[newIndex]); // Set elements directly from history
      setSelectedElements([]); // Clear selection on undo/redo
    }
  }, [history, historyIndex]); // Depends on history state and index

  const redo = useCallback(() => {
    if (historyIndex < history.length - 1) {
      const newIndex = historyIndex + 1;
      setHistoryIndex(newIndex);
      setElements(history[newIndex]); // Set elements directly from history
      setSelectedElements([]); // Clear selection on undo/redo
    }
  }, [history, historyIndex]); // Depends on history state and index

  // --- Zoom ---
  const handleZoomIn = useCallback(() => {
    setZoom((prevZoom) => Math.min(maxZoom, prevZoom + 0.1));
  }, [maxZoom]);

  const handleZoomOut = useCallback(() => {
    setZoom((prevZoom) => Math.max(minZoom, prevZoom - 0.1));
  }, [minZoom]);

  // --- Input Drag Handler ---
  const startInputDrag = useCallback(
    (
      e: React.MouseEvent<HTMLLabelElement>,
      elementId: string,
      property: keyof Element,
      initialValue: number,
      step: number = 1,
      minValue: number = -Infinity,
      maxValue: number = Infinity
    ) => {
      e.preventDefault();
      const startX = e.clientX;

      const handleMouseMove = (moveEvent: MouseEvent) => {
        const deltaX = moveEvent.clientX - startX;
        let newValue = initialValue + Math.round(deltaX * step);
        newValue = Math.max(minValue, Math.min(maxValue, newValue));

        // Update element *without* adding to history during drag
        updateElement(elementId, { [property]: newValue }, false);
      };

      const handleMouseUp = () => {
        document.removeEventListener("mousemove", handleMouseMove);
        document.removeEventListener("mouseup", handleMouseUp);

        // Add the final state to history using a functional update
        // This ensures we capture the absolute latest state after dragging
        updateElementsState((currentElements) => {
          const finalElement = currentElements.find(
            (el) => el.id === elementId
          );
          if (finalElement) {
            const finalValue = finalElement[property as keyof Element];
            const clampedFinalValue = Math.max(
              minValue,
              Math.min(
                maxValue,
                typeof finalValue === "number" ? finalValue : initialValue
              )
            );
            // Create new array only if value actually changed
            if (finalElement[property as keyof Element] !== clampedFinalValue) {
              return currentElements.map((el) =>
                el.id === elementId
                  ? { ...el, [property]: clampedFinalValue }
                  : el
              );
            }
          }
          return currentElements; // Return unchanged state if no change or element not found
        }, true); // Add to history on mouse up
      };

      document.addEventListener("mousemove", handleMouseMove);
      document.addEventListener("mouseup", handleMouseUp);
    },
    [updateElement, updateElementsState] // Depends on update functions
  );

  // --- Effects ---

  // Cleanup selected elements if they no longer exist
  useEffect(() => {
    const elementIds = new Set(elements.map((el) => el.id));
    setSelectedElements((currentSelected) =>
      currentSelected.filter((id) => elementIds.has(id))
    );
  }, [elements]); // Run only when elements array changes

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Ignore shortcuts if focus is inside specific input types
      const target = e.target as HTMLElement;
      if (
        target.tagName === "INPUT" ||
        target.tagName === "TEXTAREA" ||
        target.tagName === "SELECT"
      ) {
        // Allow Ctrl+Z/Y even in inputs
        if (
          !(
            e.ctrlKey &&
            (e.key.toLowerCase() === "z" || e.key.toLowerCase() === "y")
          )
        ) {
          return;
        }
      }

      const isCtrl = e.ctrlKey || e.metaKey; // Handle Cmd on macOS

      if (isCtrl) {
        switch (e.key.toLowerCase()) {
          case "z":
            e.preventDefault();
            undo();
            break;
          case "y":
            e.preventDefault();
            redo();
            break;
          case "d":
            e.preventDefault();
            duplicateSelectedElements();
            break;
          case "g":
            e.preventDefault();
            if (e.shiftKey) ungroupSelectedElements();
            else groupSelectedElements();
            break;
          case "a":
            e.preventDefault();
            setSelectedElements(elements.map((el) => el.id));
            break;
        }
      } else {
        switch (e.key) {
          case "Delete":
          case "Backspace":
            e.preventDefault();
            deleteSelectedElements();
            break;
          case "Escape":
            e.preventDefault();
            setSelectedElements([]);
            break;
          // Arrow keys could be used for moving selected elements - future enhancement
        }
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
    // Dependencies include all the shortcut action handlers and elements (for select all)
  }, [
    undo,
    redo,
    deleteSelectedElements,
    duplicateSelectedElements,
    groupSelectedElements,
    ungroupSelectedElements,
    elements, // Needed for Ctrl+A
  ]);

  // --- Canvas Click Handlers ---
  const handleElementClick = useCallback(
    (e: React.MouseEvent, elementId: string) => {
      e.stopPropagation(); // Prevent canvas click trigger

      const element = elements.find((el) => el.id === elementId);
      if (!element) return;

      const isCtrl = e.ctrlKey || e.metaKey;

      if (isCtrl) {
        // Toggle selection
        setSelectedElements((prev) =>
          prev.includes(elementId)
            ? prev.filter((id) => id !== elementId)
            : [...prev, elementId]
        );
      } else if (!selectedElements.includes(elementId)) {
        // If not Ctrl/Meta clicking, and element isn't already selected, select only this one
        setSelectedElements([elementId]);
      }
      // If single-clicking an element that IS already selected (and possibly others are too),
      // do nothing, allowing drag of multi-selection.
    },
    [selectedElements, elements] // Depends on selection state and elements array
  );

  const handleCanvasClick = useCallback(
    (e: React.MouseEvent<HTMLDivElement>) => {
      // Deselect only if clicking the direct background of the layoutRef
      if (e.target === layoutRef.current) {
        setSelectedElements([]);
      }
    },
    [] // No dependencies needed
  );

  // --- Rendering ---

  return (
    <div className="flex flex-col md:flex-row w-full h-screen max-h-screen gap-4 p-4 bg-gray-50 select-none overflow-hidden">
      {/* Toolbar & Canvas Column */}
      <div className="flex-1 flex flex-col min-w-0">
        <Card className="mb-4 flex-shrink-0 shadow-sm">
          <CardHeader className="pb-4">
            <CardTitle className="text-lg font-semibold mb-3">
              Restaurant Layout Editor
            </CardTitle>
            <Toolbar
              newElementType={newElementType}
              onNewElementTypeChange={setNewElementType}
              onAddElement={addElement}
              canUndo={historyIndex > 0}
              onUndo={undo}
              canRedo={historyIndex < history.length - 1}
              onRedo={redo}
              zoom={zoom}
              minZoom={minZoom}
              maxZoom={maxZoom}
              onZoomIn={handleZoomIn}
              onZoomOut={handleZoomOut}
              gridSize={gridSize}
              onGridSizeChange={setGridSize}
              snapToGrid={snapToGrid}
              onSnapToggle={setSnapToGrid}
              saveStatus={saveStatus}
              onSave={onSave ? handleSave : undefined} // Only pass handler if prop exists
              onLoad={onLoad ? handleLoad : undefined} // Only pass handler if prop exists
              onExport={exportLayout}
              onImport={importLayout}
              defaultGridSize={DEFAULT_GRID_SIZE}
            />
          </CardHeader>
        </Card>

        {/* Canvas Area Wrapper (hide scrollbars) */}
        <div className="flex-1 overflow-hidden border rounded-md bg-gray-200">
          <Canvas
            elements={elements}
            selectedElements={selectedElements}
            zoom={zoom}
            gridSize={gridSize}
            snapToGrid={snapToGrid}
            layoutRef={layoutRef}
            onElementClick={handleElementClick}
            onCanvasClick={handleCanvasClick}
            onUpdateElement={updateElement}
            // Apply canvas dimensions if provided
            style={{
              width: canvasWidth ? `${canvasWidth}px` : "100%",
              height: canvasHeight ? `${canvasHeight}px` : "100%",
            }}
          />
        </div>
      </div>

      {/* Properties Panel Column */}
      <Card className="w-full md:w-80 flex-shrink-0 overflow-y-auto h-full max-h-full shadow-sm">
        <CardHeader>
          <CardTitle className="text-base font-semibold">
            {selectedElements.length === 1
              ? "Element Properties"
              : selectedElements.length > 1
              ? "Multiple Selection"
              : "Properties"}
          </CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          <PropertiesPanel
            selectedElements={selectedElements}
            elements={elements}
            onUpdateElement={updateElement}
            onDeleteSelected={deleteSelectedElements}
            onDuplicateSelected={duplicateSelectedElements}
            onGroupSelected={groupSelectedElements}
            onUngroupSelected={ungroupSelectedElements}
            onAlign={alignElements}
            onStartInputDrag={startInputDrag}
            setSelectedElements={setSelectedElements}
          />
        </CardContent>
      </Card>
    </div>
  );
};
