import {
  doc,
  getDoc,
  onSnapshot,
  collection,
  query,
  where,
  orderBy,
  limit,
} from "firebase/firestore";
import { firestore } from "@/config/firebase";
import { notificationService } from "./NotificationService";
import { Order } from "@/types/dashboard";

interface RestaurantNotificationOptions {
  restaurantId: string;
  enableSound?: boolean;
  enableBrowserNotifications?: boolean;
  enableEmailNotifications?: boolean;
}

class RestaurantNotificationService {
  private activeListeners: Map<string, () => void> = new Map();
  private notificationQueue: Set<string> = new Set();
  private lastNotificationTime: number = 0;
  private readonly NOTIFICATION_COOLDOWN = 2000; // 2 seconds between notifications

  /**
   * Initialize restaurant notifications
   */
  async initializeRestaurantNotifications(
    options: RestaurantNotificationOptions
  ): Promise<void> {
    const {
      restaurantId,
      enableSound = true,
      enableBrowserNotifications = true,
    } = options;

    try {
      // Request notification permission if browser notifications are enabled
      if (enableBrowserNotifications) {
        const permission =
          await notificationService.requestNotificationPermission();
        if (permission !== "granted") {
          console.warn(
            "Notification permission not granted for restaurant:",
            restaurantId
          );
        }
      }

      // Set up real-time listener for new orders
      this.setupOrderListener(
        restaurantId,
        enableSound,
        enableBrowserNotifications
      );

      // Set up listener for order status changes (for customer notifications)
      this.setupOrderStatusListener(restaurantId);

      console.log("Restaurant notifications initialized for:", restaurantId);
    } catch (error) {
      console.error("Error initializing restaurant notifications:", error);
    }
  }

  /**
   * Set up real-time listener for new orders
   */
  private setupOrderListener(
    restaurantId: string,
    enableSound: boolean,
    enableBrowserNotifications: boolean
  ): void {
    const ordersRef = collection(
      firestore,
      "restaurants",
      restaurantId,
      "orders"
    );
    const ordersQuery = query(
      ordersRef,
      where("status", "==", "pending"),
      orderBy("orderDate", "desc"),
      limit(10)
    );

    const unsubscribe = onSnapshot(
      ordersQuery,
      (snapshot) => {
        snapshot.docChanges().forEach((change) => {
          if (change.type === "added") {
            const order = {
              orderId: change.doc.id,
              ...change.doc.data(),
            } as Order;
            this.handleNewOrder(order, enableSound, enableBrowserNotifications);
          }
        });
      },
      (error) => {
        console.error("Error listening to orders:", error);
      }
    );

    this.activeListeners.set(`orders_${restaurantId}`, unsubscribe);
  }

  /**
   * Set up listener for order status changes
   */
  private setupOrderStatusListener(restaurantId: string): void {
    const ordersRef = collection(
      firestore,
      "restaurants",
      restaurantId,
      "orders"
    );
    const ordersQuery = query(
      ordersRef,
      orderBy("updatedAt", "desc"),
      limit(20)
    );

    const unsubscribe = onSnapshot(
      ordersQuery,
      (snapshot) => {
        snapshot.docChanges().forEach((change) => {
          if (change.type === "modified") {
            const order = {
              orderId: change.doc.id,
              ...change.doc.data(),
            } as Order;
            this.handleOrderStatusChange(order);
          }
        });
      },
      (error) => {
        console.error("Error listening to order status changes:", error);
      }
    );

    this.activeListeners.set(`status_${restaurantId}`, unsubscribe);
  }

  /**
   * Handle new order notification
   */
  private async handleNewOrder(
    order: Order,
    enableSound: boolean,
    enableBrowserNotifications: boolean
  ): Promise<void> {
    // Prevent duplicate notifications
    if (this.notificationQueue.has(order.orderId)) {
      return;
    }

    // Rate limiting
    const now = Date.now();
    if (now - this.lastNotificationTime < this.NOTIFICATION_COOLDOWN) {
      setTimeout(() => {
        this.handleNewOrder(order, enableSound, enableBrowserNotifications);
      }, this.NOTIFICATION_COOLDOWN);
      return;
    }

    this.notificationQueue.add(order.orderId);
    this.lastNotificationTime = now;

    try {
      // Get customer details for better notification
      const customerDetails = await this.getCustomerDetails(order.userId);
      const customerName = customerDetails
        ? `${customerDetails.firstName} ${customerDetails.lastName}`
        : "Customer";

      const title = "New Order Received!";
      const message = `${customerName} placed an order for ${order.totalPrice.toFixed(
        2
      )} AZN`;

      // Show browser notification
      if (enableBrowserNotifications) {
        notificationService.notify({
          title,
          message,
          playSound: enableSound,
          url: "/dashboard?tab=orders",
          data: { orderId: order.orderId, type: "new_order" },
        });
      }

      // Create database notification
      await notificationService.createDatabaseNotification(
        order.restaurantId,
        "restaurant",
        "new_order",
        title,
        message,
        { orderId: order.orderId }
      );

      // Remove from queue after a delay
      setTimeout(() => {
        this.notificationQueue.delete(order.orderId);
      }, 5000);
    } catch (error) {
      console.error("Error handling new order notification:", error);
      this.notificationQueue.delete(order.orderId);
    }
  }

  /**
   * Handle order status change (for logging/analytics)
   */
  private handleOrderStatusChange(order: Order): void {
    console.log(`Order ${order.orderId} status changed to: ${order.status}`);

    // You can add additional logic here for status-specific notifications
    // For example, notify when an order is ready for pickup
    if (order.status === "ready") {
      this.notifyOrderReady(order);
    }
  }

  /**
   * Notify when order is ready
   */
  private async notifyOrderReady(order: Order): Promise<void> {
    try {
      const customerDetails = await this.getCustomerDetails(order.userId);
      const customerName = customerDetails
        ? `${customerDetails.firstName} ${customerDetails.lastName}`
        : "Customer";

      // This could trigger a notification to the customer
      console.log(`Order ready for ${customerName}: ${order.orderId}`);

      // You could also play a different sound or show a different notification
      // for order ready status
    } catch (error) {
      console.error("Error notifying order ready:", error);
    }
  }

  /**
   * Get customer details
   */
  private async getCustomerDetails(
    userId: string
  ): Promise<{ firstName?: string; lastName?: string } | null> {
    try {
      const customerDoc = await getDoc(doc(firestore, "clients", userId));
      return customerDoc.exists() ? customerDoc.data() : null;
    } catch (error) {
      console.error("Error getting customer details:", error);
      return null;
    }
  }

  /**
   * Send test notification
   */
  async sendTestNotification(restaurantId: string): Promise<void> {
    try {
      const title = "Test Notification";
      const message =
        "This is a test notification for your restaurant dashboard.";

      notificationService.notify({
        title,
        message,
        playSound: true,
        url: "/dashboard",
        data: { type: "test" },
      });

      await notificationService.createDatabaseNotification(
        restaurantId,
        "restaurant",
        "test",
        title,
        message
      );

      console.log("Test notification sent successfully");
    } catch (error) {
      console.error("Error sending test notification:", error);
    }
  }

  /**
   * Cleanup listeners
   */
  cleanup(restaurantId?: string): void {
    if (restaurantId) {
      // Cleanup specific restaurant listeners
      const orderListener = this.activeListeners.get(`orders_${restaurantId}`);
      const statusListener = this.activeListeners.get(`status_${restaurantId}`);

      if (orderListener) {
        orderListener();
        this.activeListeners.delete(`orders_${restaurantId}`);
      }

      if (statusListener) {
        statusListener();
        this.activeListeners.delete(`status_${restaurantId}`);
      }
    } else {
      // Cleanup all listeners
      this.activeListeners.forEach((unsubscribe) => unsubscribe());
      this.activeListeners.clear();
    }

    this.notificationQueue.clear();
  }

  /**
   * Check if notifications are properly set up
   */
  isNotificationSetup(): boolean {
    return "Notification" in window && Notification.permission === "granted";
  }

  /**
   * Get notification status
   */
  getNotificationStatus(): {
    permission: NotificationPermission;
    serviceWorkerReady: boolean;
    activeListeners: number;
  } {
    return {
      permission: "Notification" in window ? Notification.permission : "denied",
      serviceWorkerReady:
        "serviceWorker" in navigator && !!navigator.serviceWorker.controller,
      activeListeners: this.activeListeners.size,
    };
  }
}

export const restaurantNotificationService =
  new RestaurantNotificationService();
