import React, { useState, useEffect, useRef, forwardRef } from "react";
import ReactQuill from "react-quill";
// CSS imports are now in main.tsx

// Create a forwardRef wrapper for ReactQuill to avoid findDOMNode warnings
const QuillEditor = forwardRef<
  ReactQuill,
  React.ComponentProps<typeof ReactQuill>
>((props, ref) => {
  return <ReactQuill ref={ref} {...props} />;
});

// Add display name to the component
QuillEditor.displayName = "QuillEditor";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Code,
  Image,
  Link,
  Table,
  AlignLeft,
  AlignCenter,
  AlignRight,
  Palette,
  Columns,
  Undo,
  Redo,
  Eye,
  Heading1,
  Heading2,
  ListOrdered,
  List as ListChecks,
  Bold,
  Italic,
  Underline,
  Type,
} from "lucide-react";

// Define custom formats and modules for ReactQuill
const formats = [
  "header",
  "font",
  "size",
  "bold",
  "italic",
  "underline",
  "strike",
  "blockquote",
  "list",
  "bullet",
  "indent",
  "link",
  "image",
  "color",
  "background",
  "align",
  "code-block",
  "table",
];

// Custom button component for toolbar
const ToolbarButton = ({
  icon,
  tooltip,
  onClick,
  active = false,
}: {
  icon: React.ReactNode;
  tooltip: string;
  onClick: () => void;
  active?: boolean;
}) => {
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant={active ? "default" : "ghost"}
            size="icon"
            className="h-8 w-8"
            onClick={onClick}
          >
            {icon}
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          <p>{tooltip}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

// Image insertion dialog
const ImageDialog = ({
  onInsert,
}: {
  onInsert: (url: string, alt: string) => void;
}) => {
  const [open, setOpen] = useState(false);
  const [imageUrl, setImageUrl] = useState("");
  const [altText, setAltText] = useState("");

  const handleInsert = () => {
    if (imageUrl) {
      onInsert(imageUrl, altText);
      setImageUrl("");
      setAltText("");
      setOpen(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="ghost" size="icon" className="h-8 w-8">
          <Image className="h-4 w-4" />
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Insert Image</DialogTitle>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="imageUrl" className="text-right">
              Image URL
            </Label>
            <Input
              id="imageUrl"
              value={imageUrl}
              onChange={(e) => setImageUrl(e.target.value)}
              className="col-span-3"
              placeholder="https://example.com/image.jpg"
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="altText" className="text-right">
              Alt Text
            </Label>
            <Input
              id="altText"
              value={altText}
              onChange={(e) => setAltText(e.target.value)}
              className="col-span-3"
              placeholder="Image description"
            />
          </div>
        </div>
        <DialogFooter>
          <Button onClick={handleInsert}>Insert Image</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

// Link insertion dialog
const LinkDialog = ({
  onInsert,
}: {
  onInsert: (url: string, text: string) => void;
}) => {
  const [open, setOpen] = useState(false);
  const [linkUrl, setLinkUrl] = useState("");
  const [linkText, setLinkText] = useState("");

  const handleInsert = () => {
    if (linkUrl) {
      onInsert(linkUrl, linkText);
      setLinkUrl("");
      setLinkText("");
      setOpen(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="ghost" size="icon" className="h-8 w-8">
          <Link className="h-4 w-4" />
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Insert Link</DialogTitle>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="linkUrl" className="text-right">
              URL
            </Label>
            <Input
              id="linkUrl"
              value={linkUrl}
              onChange={(e) => setLinkUrl(e.target.value)}
              className="col-span-3"
              placeholder="https://example.com"
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="linkText" className="text-right">
              Text
            </Label>
            <Input
              id="linkText"
              value={linkText}
              onChange={(e) => setLinkText(e.target.value)}
              className="col-span-3"
              placeholder="Link text"
            />
          </div>
        </div>
        <DialogFooter>
          <Button onClick={handleInsert}>Insert Link</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

// Main HTML Editor component
interface HtmlEditorProps {
  value: string;
  onChange: (value: string) => void;
  className?: string;
  placeholder?: string;
}

export function HtmlEditor({
  value,
  onChange,
  className,
  placeholder = "Write your content here...",
}: HtmlEditorProps) {
  const [editorMode, setEditorMode] = useState<"visual" | "html">("visual");
  const [htmlValue, setHtmlValue] = useState(value);
  const quillRef = useRef<ReactQuill>(null);

  // Sync the HTML value with the editor value
  useEffect(() => {
    setHtmlValue(value);
  }, [value]);

  // Handle HTML mode changes
  const handleHtmlChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setHtmlValue(e.target.value);
  };

  // Apply HTML changes when switching back to visual mode
  const handleTabChange = (mode: string) => {
    if (mode === "visual" && editorMode === "html") {
      onChange(htmlValue);
    }
    setEditorMode(mode as "visual" | "html");
  };

  // Custom toolbar handlers with safety checks
  const handleInsertImage = (url: string, alt: string) => {
    try {
      const editor = quillRef.current?.getEditor();
      if (editor) {
        const range = editor.getSelection() || {
          index: editor.getLength(),
          length: 0,
        };
        editor.insertEmbed(range.index, "image", url);
        editor.formatText(range.index, 1, { alt });
        // Move cursor after the image
        editor.setSelection(range.index + 1, 0);
      }
    } catch (error) {
      console.warn("Error inserting image:", error);
      // Fallback: insert as HTML
      const imageHtml = `<img src="${url}" alt="${alt}" />`;
      setHtmlValue((prev) => prev + imageHtml);
      if (editorMode === "html") {
        onChange(htmlValue + imageHtml);
      }
    }
  };

  const handleInsertLink = (url: string, text: string) => {
    try {
      const editor = quillRef.current?.getEditor();
      if (editor) {
        const range = editor.getSelection() || {
          index: editor.getLength(),
          length: 0,
        };
        if (range.length > 0) {
          editor.formatText(range.index, range.length, "link", url);
        } else {
          editor.insertText(range.index, text, { link: url });
          // Move cursor after the link
          editor.setSelection(range.index + text.length, 0);
        }
      }
    } catch (error) {
      console.warn("Error inserting link:", error);
      // Fallback: insert as HTML
      const linkHtml = `<a href="${url}">${text}</a>`;
      setHtmlValue((prev) => prev + linkHtml);
      if (editorMode === "html") {
        onChange(htmlValue + linkHtml);
      }
    }
  };

  return (
    <div className={cn("border rounded-md", className)}>
      <Tabs
        defaultValue="visual"
        value={editorMode}
        onValueChange={handleTabChange}
      >
        <div className="flex items-center justify-between border-b px-3 py-2">
          <TabsList>
            <TabsTrigger value="visual" className="text-xs">
              Visual Editor
            </TabsTrigger>
            <TabsTrigger value="html" className="text-xs">
              HTML Code
            </TabsTrigger>
          </TabsList>

          {editorMode === "visual" && (
            <div className="flex items-center space-x-1">
              <ToolbarButton
                icon={<Eye className="h-4 w-4" />}
                tooltip="Preview"
                onClick={() => {
                  // Preview functionality can be implemented here
                  console.log("Preview");
                }}
              />
            </div>
          )}
        </div>

        <TabsContent value="visual" className="p-0">
          <div className="border-b bg-muted/50 p-1 flex flex-wrap gap-1">
            <ToolbarButton
              icon={<Bold className="h-4 w-4" />}
              tooltip="Bold"
              onClick={() => {
                const editor = quillRef.current?.getEditor();
                if (editor) {
                  const format = editor.getFormat();
                  editor.format("bold", !format.bold);
                }
              }}
            />
            <ToolbarButton
              icon={<Italic className="h-4 w-4" />}
              tooltip="Italic"
              onClick={() => {
                const editor = quillRef.current?.getEditor();
                if (editor) {
                  const format = editor.getFormat();
                  editor.format("italic", !format.italic);
                }
              }}
            />
            <ToolbarButton
              icon={<Underline className="h-4 w-4" />}
              tooltip="Underline"
              onClick={() => {
                const editor = quillRef.current?.getEditor();
                if (editor) {
                  const format = editor.getFormat();
                  editor.format("underline", !format.underline);
                }
              }}
            />
            <ToolbarButton
              icon={<Type className="h-4 w-4" />}
              tooltip="Font Size"
              onClick={() => {
                const editor = quillRef.current?.getEditor();
                if (editor) {
                  // Toggle between normal and large
                  const format = editor.getFormat();
                  editor.format(
                    "size",
                    format.size === "large" ? false : "large"
                  );
                }
              }}
            />
            <ToolbarButton
              icon={<Heading1 className="h-4 w-4" />}
              tooltip="Heading 1"
              onClick={() => {
                const editor = quillRef.current?.getEditor();
                if (editor) {
                  const format = editor.getFormat();
                  editor.format("header", format.header === 1 ? false : 1);
                }
              }}
            />
            <ToolbarButton
              icon={<Heading2 className="h-4 w-4" />}
              tooltip="Heading 2"
              onClick={() => {
                const editor = quillRef.current?.getEditor();
                if (editor) {
                  const format = editor.getFormat();
                  editor.format("header", format.header === 2 ? false : 2);
                }
              }}
            />
            <ToolbarButton
              icon={<ListOrdered className="h-4 w-4" />}
              tooltip="Ordered List"
              onClick={() => {
                const editor = quillRef.current?.getEditor();
                if (editor) {
                  const format = editor.getFormat();
                  editor.format(
                    "list",
                    format.list === "ordered" ? false : "ordered"
                  );
                }
              }}
            />
            <ToolbarButton
              icon={<ListChecks className="h-4 w-4" />}
              tooltip="Bullet List"
              onClick={() => {
                const editor = quillRef.current?.getEditor();
                if (editor) {
                  const format = editor.getFormat();
                  editor.format(
                    "list",
                    format.list === "bullet" ? false : "bullet"
                  );
                }
              }}
            />
            <ToolbarButton
              icon={<AlignLeft className="h-4 w-4" />}
              tooltip="Align Left"
              onClick={() => {
                const editor = quillRef.current?.getEditor();
                if (editor) {
                  editor.format("align", "");
                }
              }}
            />
            <ToolbarButton
              icon={<AlignCenter className="h-4 w-4" />}
              tooltip="Align Center"
              onClick={() => {
                const editor = quillRef.current?.getEditor();
                if (editor) {
                  const format = editor.getFormat();
                  editor.format(
                    "align",
                    format.align === "center" ? false : "center"
                  );
                }
              }}
            />
            <ToolbarButton
              icon={<AlignRight className="h-4 w-4" />}
              tooltip="Align Right"
              onClick={() => {
                const editor = quillRef.current?.getEditor();
                if (editor) {
                  const format = editor.getFormat();
                  editor.format(
                    "align",
                    format.align === "right" ? false : "right"
                  );
                }
              }}
            />
            <ToolbarButton
              icon={<Palette className="h-4 w-4" />}
              tooltip="Text Color"
              onClick={() => {
                // Color picker functionality
                console.log("Color picker");
              }}
            />
            <ImageDialog onInsert={handleInsertImage} />
            <LinkDialog onInsert={handleInsertLink} />
            <ToolbarButton
              icon={<Table className="h-4 w-4" />}
              tooltip="Insert Table"
              onClick={() => {
                // Table insertion functionality
                console.log("Insert table");
              }}
            />
            <ToolbarButton
              icon={<Code className="h-4 w-4" />}
              tooltip="Code Block"
              onClick={() => {
                const editor = quillRef.current?.getEditor();
                if (editor) {
                  const format = editor.getFormat();
                  editor.format("code-block", !format["code-block"]);
                }
              }}
            />
            <ToolbarButton
              icon={<Columns className="h-4 w-4" />}
              tooltip="Insert Columns"
              onClick={() => {
                // Columns functionality
                console.log("Insert columns");
              }}
            />
            <ToolbarButton
              icon={<Undo className="h-4 w-4" />}
              tooltip="Undo"
              onClick={() => {
                const editor = quillRef.current?.getEditor();
                if (editor) {
                  const history = editor.getModule("history");
                  if (history && typeof history.undo === "function") {
                    history.undo();
                  }
                }
              }}
            />
            <ToolbarButton
              icon={<Redo className="h-4 w-4" />}
              tooltip="Redo"
              onClick={() => {
                const editor = quillRef.current?.getEditor();
                if (editor) {
                  const history = editor.getModule("history");
                  if (history && typeof history.redo === "function") {
                    history.redo();
                  }
                }
              }}
            />
          </div>
          <div className="quill-container">
            <QuillEditor
              ref={quillRef}
              theme="snow"
              value={value}
              onChange={onChange}
              formats={formats}
              placeholder={placeholder}
              modules={{
                toolbar: false, // We're using our custom toolbar
                history: {
                  delay: 1000,
                  maxStack: 100,
                  userOnly: true,
                },
                clipboard: {
                  matchVisual: false, // Helps with some clipboard issues
                  matchers: [], // Disable custom matchers to avoid DOMNodeInserted events
                },
                keyboard: {
                  bindings: {}, // Minimal keyboard bindings to avoid some issues
                },
              }}
              className="border-0 [&_.ql-editor]:min-h-[200px] [&_.ql-container]:border-0"
            />
          </div>
        </TabsContent>

        <TabsContent value="html" className="p-0">
          <Textarea
            value={htmlValue}
            onChange={handleHtmlChange}
            className="min-h-[300px] font-mono text-sm p-4 rounded-none border-0 focus-visible:ring-0"
            placeholder="<div>Your HTML code here...</div>"
          />
        </TabsContent>
      </Tabs>
    </div>
  );
}
