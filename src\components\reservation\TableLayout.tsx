import { Table, LayoutElement, Restaurant, Reservation } from "./types";

interface TableLayoutProps {
  layout: LayoutElement[];
  tables: Table[];
  selectedTableId: string;
  onTableSelect: (tableId: string) => void;
  isEditMode: boolean;
  reservations?: Reservation[];
  restaurant?: Restaurant;
  editForm?: {
    date: string;
    arrivalTime: string;
    departureTime: string;
    partySize: number;
  };
  isRestaurantOpen?: (
    restaurant: Restaurant | null,
    date: string,
    time: string
  ) => boolean;
  checkTableAvailability?: (
    restaurant: Restaurant | null,
    tables: Table[],
    reservations: Reservation[],
    newReservation: {
      date: string;
      arrivalTime: string;
      departureTime: string;
      partySize: number;
    },
    isRestaurantOpen: (
      restaurant: Restaurant | null,
      date: string,
      time: string
    ) => boolean
  ) => Table[];
}

const TableLayout = ({
  layout,
  tables,
  selectedTableId,
  onTableSelect,
  isEditMode,
  reservations,
  restaurant,
  editForm,
  isRestaurantOpen,
  checkTableAvailability,
}: TableLayoutProps) => {
  return (
    <div className="mt-2 relative w-full h-[400px] border">
      {layout.map((element) => {
        const table = tables.find((t) => t.name === element.name);
        if (!table || !element.type.includes("table")) return null;

        let isAvailable = true;
        if (
          isEditMode &&
          reservations &&
          restaurant &&
          editForm &&
          isRestaurantOpen &&
          checkTableAvailability
        ) {
          isAvailable =
            checkTableAvailability(
              restaurant,
              [table],
              reservations,
              editForm,
              isRestaurantOpen
            ).length > 0;
        } else {
          isAvailable = tables.some((t) => t.id === table.id);
        }

        const isSelected = selectedTableId === table.id;

        return (
          <div
            key={element.id}
            style={{
              position: "absolute",
              left: `${element.x}px`,
              top: `${element.y}px`,
              width: `${element.width}px`,
              height: `${element.height}px`,
              transform: `rotate(${element.rotation}deg)`,
              backgroundColor: isAvailable
                ? isSelected
                  ? "#4CAF50"
                  : "#8BC34A"
                : "#EF5350",
              border: "1px solid #000",
              cursor: isAvailable ? "pointer" : "not-allowed",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              color: "#fff",
              fontSize: "12px",
              textAlign: "center",
              borderRadius:
                element.type === "table-round"
                  ? "50%"
                  : `${element.borderRadius}px`,
            }}
            onClick={() => isAvailable && onTableSelect(table.id)}
          >
            {element.name} ({element.capacity})
            <br />
            {isAvailable ? "Available" : "Reserved"}
          </div>
        );
      })}
    </div>
  );
};

export default TableLayout;
