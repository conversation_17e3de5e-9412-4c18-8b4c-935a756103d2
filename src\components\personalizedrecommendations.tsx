import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { useAuth } from "@/providers/AuthProvider";
import { Recommendation } from "@/utils/recommendationUtils";
import { recommendationService } from "@/services/RecommendationService";
import { Restaurant } from "@/types/restaurant";
import { MenuItem } from "@/types";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Link } from "react-router-dom";
import { Loading } from "@/components/ui/loading";
import { Button } from "@/components/ui/button";
import { ChevronRight, Star } from "lucide-react";

const sectionVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 },
};

const itemVariants = {
  hidden: { opacity: 0, scale: 0.9 },
  visible: {
    opacity: 1,
    scale: 1,
    transition: { duration: 0.4 }
  },
  hover: {
    scale: 1.03,
    boxShadow: "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",
    transition: { duration: 0.2 }
  }
};

export const PersonalizedRecommendations: React.FC = () => {
  const { user, userRole } = useAuth();
  const [recommendations, setRecommendations] = useState<Recommendation[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchRecommendations = async () => {
      if (!user || userRole !== "client") {
        setLoading(false);
        return;
      }

      setLoading(true);
      setError(null);

      try {
        const recs = await recommendationService.getPersonalizedRecommendations(
          user.uid,
          6 // Limit the number of recommendations shown
        );

        setRecommendations(recs);
      } catch (err) {
        console.error("Error fetching recommendations:", err);
        setError("Could not load recommendations. Please try again later.");
      } finally {
        setLoading(false);
      }
    };

    fetchRecommendations();
  }, [user, userRole]);

  if (!user || userRole !== "client") {
    // Don't show the section if the user is not logged in as a client
    return null;
  }

  if (loading) {
    return (
      <section className="py-16 bg-muted/30">
        <div className="mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-8">For You</h2>
          <div className="flex justify-center">
            <Loading />
          </div>
        </div>
      </section>
    );
  }

  if (error) {
    return (
      <section className="py-16 bg-muted/30">
        <div className="mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold text-center mb-8">For You</h2>
          <p className="text-destructive">{error}</p>
        </div>
      </section>
    );
  }

  if (recommendations.length === 0) {
    return (
      <section className="py-16 bg-muted/30">
        <div className="mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold text-center mb-8">For You</h2>
          <p className="text-muted-foreground mb-6">
            Complete your profile preferences and place orders to get personalized recommendations.
          </p>
          <Link to="/profile">
            <Button>
              Update Preferences
            </Button>
          </Link>
        </div>
      </section>
    );
  }

  return (
    <motion.section
      variants={sectionVariants}
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, amount: 0.2 }}
      className="py-16 bg-muted/30"
    >
      <div className="mx-auto px-4">
        <div className="flex justify-between items-center mb-10">
          <h2 className="text-3xl font-bold">For You</h2>
          <Link to="/restaurants" className="text-primary flex items-center hover:underline">
            View All <ChevronRight className="h-4 w-4 ml-1" />
          </Link>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
          {recommendations.map((rec) => (
            <motion.div
              key={`${rec.type}-${rec.id}`}
              variants={itemVariants}
              whileHover="hover"
            >
              {rec.type === "restaurant" && (
                <RestaurantRecommendation recommendation={rec} />
              )}
              {rec.type === "menuItem" && (
                <MenuItemRecommendation recommendation={rec} />
              )}
            </motion.div>
          ))}
        </div>
      </div>
    </motion.section>
  );
};

interface RecommendationProps {
  recommendation: Recommendation;
}

const RestaurantRecommendation: React.FC<RecommendationProps> = ({ recommendation }) => {
  const restaurant = recommendation.details as Restaurant;

  return (
    <Card className="h-full overflow-hidden hover:shadow-lg transition-shadow duration-300">
      <Link to={`/restaurants/${restaurant.username || restaurant.id}`} className="block h-full">
        <div className="relative h-48 overflow-hidden">
          <img
            src={restaurant.imageUrl || "/placeholder-restaurant.jpg"}
            alt={restaurant.restaurantName || restaurant.name || "Restaurant"}
            className="w-full h-full object-cover transition-transform duration-500 hover:scale-105"
          />
          {restaurant.rating > 0 && (
            <div className="absolute top-2 right-2 bg-primary text-white px-2 py-1 rounded-md flex items-center text-sm font-medium">
              <Star className="h-3 w-3 mr-1 fill-current" />
              {restaurant.rating.toFixed(1)}
            </div>
          )}
        </div>
        <CardHeader className="pb-2">
          <CardTitle className="text-lg truncate">
            {restaurant.restaurantName || restaurant.name}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground truncate mb-2">
            {restaurant.cuisines?.join(", ") || "Variety of cuisines"}
          </p>
          {recommendation.reason && (
            <p className="text-xs text-primary italic">
              {recommendation.reason}
            </p>
          )}
        </CardContent>
      </Link>
    </Card>
  );
};

const MenuItemRecommendation: React.FC<RecommendationProps> = ({ recommendation }) => {
  const menuItem = recommendation.details as MenuItem & { restaurantUsername?: string, restaurantName?: string };

  return (
    <Card className="h-full overflow-hidden hover:shadow-lg transition-shadow duration-300">
      <Link to={`/restaurants/${menuItem.restaurantUsername || menuItem.restaurantId}`} className="block h-full">
        <div className="relative h-48 overflow-hidden">
          <img
            src={menuItem.imageUrl || "/placeholder-food.jpg"}
            alt={menuItem.name}
            className="w-full h-full object-cover transition-transform duration-500 hover:scale-105"
          />
          <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-3">
            <p className="text-white font-medium">{menuItem.price.toFixed(2)} ₼</p>
          </div>
        </div>
        <CardHeader className="pb-2">
          <CardTitle className="text-lg truncate">
            {menuItem.name}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground truncate mb-1">
            {menuItem.restaurantName || "Restaurant"}
          </p>
          <p className="text-xs text-muted-foreground line-clamp-2 mb-2">
            {menuItem.description}
          </p>
          {recommendation.reason && (
            <p className="text-xs text-primary italic">
              {recommendation.reason}
            </p>
          )}
        </CardContent>
      </Link>
    </Card>
  );
};