// src/components/layout-editor/utils.ts
import type { ElementType } from "./types";

export const getDefaultColor = (type: ElementType): string => {
  switch (type) {
    case "table-round":
      return "#d1d5db";
    case "table-rect":
      return "#d1d5db";
    case "chair":
      return "#a16207";
    case "window":
      return "#bfdbfe";
    case "door":
      return "#9a3412";
    case "stairs":
      return "#6b7280";
    case "wall":
      return "#1f2937";
    case "private-room":
      return "#ddd6fe";
    case "bar":
      return "#fecaca";
    case "kitchen":
      return "#bbf7d0";
    default:
      return "#ffffff";
  }
};

// Ensure all default sizes are multiples of 20 (standard grid size)
// This helps with precise grid alignment
export const getDefaultSize = (
  type: ElementType
): { width: number; height: number } => {
  switch (type) {
    case "table-round":
      return { width: 60, height: 60 };
    case "table-rect":
      return { width: 80, height: 60 };
    case "chair":
      return { width: 40, height: 40 };
    case "window":
      return { width: 80, height: 20 };
    case "door":
      return { width: 60, height: 20 };
    case "stairs":
      return { width: 60, height: 100 };
    case "wall":
      return { width: 100, height: 20 };
    case "private-room":
      return { width: 200, height: 160 };
    case "bar":
      return { width: 160, height: 60 };
    case "kitchen":
      return { width: 120, height: 100 };
    default:
      return { width: 40, height: 40 };
  }
};

// Get default capacity for element types
export const getDefaultCapacity = (type: ElementType): number | undefined => {
  switch (type) {
    case "table-round":
      return 4;
    case "table-rect":
      return 6;
    case "private-room":
      return 8;
    default:
      return undefined;
  }
};
