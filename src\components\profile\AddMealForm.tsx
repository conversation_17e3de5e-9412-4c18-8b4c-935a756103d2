import React, { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Meal, DietaryGoal } from "@/types";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "sonner";
import { Check, X, Plus, ArrowLeft } from "lucide-react";
import { Timestamp } from "firebase/firestore";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { SimpleDatePicker } from "@/components/ui/simple-date-picker";
import { format } from "date-fns";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

interface AddMealFormProps {
  onSubmit: (meal: Omit<Meal, 'id'>) => void;
  onCancel: () => void;
  recentMeals?: Meal[];
  dietaryGoals?: DietaryGoal[];
}

export const AddMealForm: React.FC<AddMealFormProps> = ({
  onSubmit,
  onCancel,
  recentMeals = []
}) => {
  const [activeTab, setActiveTab] = useState("new");
  const [formData, setFormData] = useState<Omit<Meal, 'id'>>({
    name: "",
    calories: 0,
    protein: 0,
    carbs: 0,
    fat: 0,
    date: Timestamp.now(),
    mealType: "breakfast",
    ingredients: []
  });
  const [newIngredient, setNewIngredient] = useState("");

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: name === "calories" || name === "protein" || name === "carbs" || name === "fat"
        ? parseFloat(value) || 0
        : value,
    }));
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleDateChange = (date: Date | undefined) => {
    if (!date) return;
    setFormData((prev) => ({
      ...prev,
      date: Timestamp.fromDate(date),
    }));
  };

  const handleAddIngredient = () => {
    if (!newIngredient.trim()) return;

    setFormData((prev) => ({
      ...prev,
      ingredients: [...(prev.ingredients || []), newIngredient.trim()],
    }));
    setNewIngredient("");
  };

  const handleRemoveIngredient = (index: number) => {
    setFormData((prev) => ({
      ...prev,
      ingredients: (prev.ingredients || []).filter((_, i) => i !== index),
    }));
  };

  const handleSubmit = () => {
    if (!formData.name.trim()) {
      toast.error("Please enter a meal name");
      return;
    }

    if (formData.calories <= 0) {
      toast.error("Please enter a valid calorie amount");
      return;
    }

    onSubmit(formData);
  };

  const handleSelectRecentMeal = (meal: Meal) => {
    // Create a copy of the meal without the id
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { id, ...mealData } = meal;

    // Update the date to now
    setFormData({
      ...mealData,
      date: Timestamp.now()
    });

    // Switch to the edit tab
    setActiveTab("new");
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center mb-4">
        <Button variant="ghost" onClick={onCancel} className="mr-2">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back
        </Button>
        <h3 className="text-lg font-medium">Add Meal</h3>
      </div>

      <Tabs defaultValue={activeTab} value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid grid-cols-2 mb-4">
          <TabsTrigger value="new">New Meal</TabsTrigger>
          <TabsTrigger value="recent">Recent Meals</TabsTrigger>
        </TabsList>

        <TabsContent value="new" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">Meal Name</Label>
              <Input
                id="name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                placeholder="e.g., Chicken Salad"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="mealType">Meal Type</Label>
              <Select
                value={formData.mealType}
                onValueChange={(value) => handleSelectChange("mealType", value)}
              >
                <SelectTrigger id="mealType">
                  <SelectValue placeholder="Select meal type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="breakfast">Breakfast</SelectItem>
                  <SelectItem value="lunch">Lunch</SelectItem>
                  <SelectItem value="dinner">Dinner</SelectItem>
                  <SelectItem value="snack">Snack</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="space-y-2">
              <Label htmlFor="calories">Calories</Label>
              <Input
                id="calories"
                name="calories"
                type="number"
                value={formData.calories}
                onChange={handleInputChange}
                min="0"
                step="1"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="protein">Protein (g)</Label>
              <Input
                id="protein"
                name="protein"
                type="number"
                value={formData.protein}
                onChange={handleInputChange}
                min="0"
                step="0.1"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="carbs">Carbs (g)</Label>
              <Input
                id="carbs"
                name="carbs"
                type="number"
                value={formData.carbs}
                onChange={handleInputChange}
                min="0"
                step="0.1"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="fat">Fat (g)</Label>
              <Input
                id="fat"
                name="fat"
                type="number"
                value={formData.fat}
                onChange={handleInputChange}
                min="0"
                step="0.1"
              />
            </div>
          </div>

          <div className="space-y-4 relative">
            <h3 className="text-base font-medium">Date & Time</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="date">Date</Label>
                <SimpleDatePicker
                  value={formData.date instanceof Timestamp
                    ? formData.date.toDate()
                    : formData.date as Date}
                  onChange={handleDateChange}
                  className="w-full"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="time">Time</Label>
                <Input
                  type="time"
                  value={formData.date instanceof Timestamp
                    ? format(formData.date.toDate(), "HH:mm")
                    : format(formData.date as Date, "HH:mm")}
                  onChange={(e) => {
                    const [hours, minutes] = e.target.value.split(':').map(Number);
                    const date = formData.date instanceof Timestamp
                      ? formData.date.toDate()
                      : formData.date as Date;
                    date.setHours(hours, minutes);
                    setFormData((prev) => ({
                      ...prev,
                      date: Timestamp.fromDate(date),
                    }));
                  }}
                  className="w-full"
                />
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <Label>Ingredients</Label>
            <div className="flex flex-wrap gap-2 mb-2">
              {(formData.ingredients || []).map((ingredient, index) => (
                <div key={index} className="flex items-center bg-muted px-2 py-1 rounded-md">
                  <span className="text-sm">{ingredient}</span>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-6 w-6 ml-1"
                    onClick={() => handleRemoveIngredient(index)}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </div>
              ))}
            </div>
            <div className="flex gap-2">
              <Input
                value={newIngredient}
                onChange={(e) => setNewIngredient(e.target.value)}
                placeholder="Add ingredient"
                className="flex-1"
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault();
                    handleAddIngredient();
                  }
                }}
              />
              <Button
                type="button"
                variant="outline"
                onClick={handleAddIngredient}
              >
                <Plus className="h-4 w-4" />
              </Button>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description (Optional)</Label>
            <Textarea
              id="description"
              name="description"
              value={formData.description || ""}
              onChange={handleInputChange}
              placeholder="Add any notes about this meal..."
              rows={3}
            />
          </div>

          <div className="flex justify-end space-x-2 pt-4">
            <Button
              variant="outline"
              onClick={onCancel}
            >
              <X className="mr-2 h-4 w-4" />
              Cancel
            </Button>
            <Button
              onClick={handleSubmit}
            >
              <Check className="mr-2 h-4 w-4" />
              Add Meal
            </Button>
          </div>
        </TabsContent>

        <TabsContent value="recent">
          {recentMeals.length > 0 ? (
            <div className="space-y-4">
              <p className="text-sm text-muted-foreground">
                Select a recent meal to quickly add it again
              </p>

              <div className="grid grid-cols-1 gap-4">
                {recentMeals.map((meal) => (
                  <Card key={meal.id} className="cursor-pointer hover:bg-muted/50 transition-colors" onClick={() => handleSelectRecentMeal(meal)}>
                    <CardContent className="p-4">
                      <div className="flex justify-between items-start">
                        <div>
                          <h4 className="font-medium">{meal.name}</h4>
                          <p className="text-sm text-muted-foreground capitalize">{meal.mealType}</p>
                        </div>
                        <div className="text-right">
                          <p className="font-medium">{meal.calories} kcal</p>
                          <p className="text-xs text-muted-foreground">
                            {meal.date instanceof Timestamp
                              ? format(meal.date.toDate(), "PPP")
                              : format(meal.date as Date, "PPP")}
                          </p>
                        </div>
                      </div>

                      {(meal.protein || meal.carbs || meal.fat) && (
                        <div className="grid grid-cols-3 gap-2 mt-2 text-sm">
                          {meal.protein !== undefined && (
                            <div>
                              <span className="text-muted-foreground">Protein:</span> {meal.protein}g
                            </div>
                          )}
                          {meal.carbs !== undefined && (
                            <div>
                              <span className="text-muted-foreground">Carbs:</span> {meal.carbs}g
                            </div>
                          )}
                          {meal.fat !== undefined && (
                            <div>
                              <span className="text-muted-foreground">Fat:</span> {meal.fat}g
                            </div>
                          )}
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          ) : (
            <div className="text-center p-6 border border-dashed rounded-md">
              <p className="text-muted-foreground">No recent meals found</p>
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
};
