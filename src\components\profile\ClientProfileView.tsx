import { ClientDetails } from "@/types";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { FollowedRestaurants } from "./FollowedRestaurants";
import { MealTracker } from "./MealTracker";
import { Button } from "@/components/ui/button";
import { useState } from "react";
import { ChevronDown, ChevronUp, MapPin, Target, Bell, Utensils } from "lucide-react";
import { Progress } from "@/components/ui/progress";
// Timestamp is used indirectly in formatCreatedAtDate

interface ClientProfileViewProps {
  userData: ClientDetails;
}

export const ClientProfileView = ({ userData }: ClientProfileViewProps) => {
  const [showCalorieCalculator, setShowCalorieCalculator] = useState(false);
  const [showMealPreferences, setShowMealPreferences] = useState(false);
  const [showAddresses, setShowAddresses] = useState(false);
  const [showDietaryGoals, setShowDietaryGoals] = useState(false);
  const [showNotifications, setShowNotifications] = useState(false);
  const [showMealTracker, setShowMealTracker] = useState(false);

  // Helper function to safely format the createdAt date
  const formatCreatedAtDate = (createdAt: Date | { toDate: () => Date } | { seconds: number; nanoseconds: number } | string | number | unknown) => {
    try {
      // Handle different date formats
      let date;

      if (createdAt instanceof Date) {
        date = createdAt;
      } else if (createdAt && typeof createdAt === 'object') {
        // Check for Firestore Timestamp with toDate method
        const timestampWithMethod = createdAt as { toDate?: () => Date };
        if (typeof timestampWithMethod.toDate === 'function') {
          date = timestampWithMethod.toDate();
        } else {
          // Check for Firestore Timestamp-like object
          const timestamp = createdAt as { seconds?: number; nanoseconds?: number };
          if (timestamp.seconds && timestamp.nanoseconds) {
            date = new Date(timestamp.seconds * 1000);
          } else {
            // Try to convert other objects to date
            try {
              date = new Date(createdAt as unknown as string | number);
            } catch {
              console.warn('Invalid date format for createdAt:', createdAt);
              return 'Unknown';
            }
          }
        }
      } else if (createdAt && typeof createdAt === 'string') {
        // ISO string
        date = new Date(createdAt);
      } else if (createdAt && typeof createdAt === 'number') {
        // Unix timestamp
        date = new Date(createdAt);
      } else {
        // Fallback to current date if no valid date is provided
        console.warn('Invalid date format for createdAt:', createdAt);
        return 'Unknown';
      }

      // Format the date
      return date.toLocaleDateString("en-US", {
        year: "numeric",
        month: "long",
        day: "numeric",
      });
    } catch (error) {
      console.error('Error formatting date:', error);
      return 'Unknown';
    }
  };

  // Helper function to format calorie calculator date
  const formatCalorieDate = (calculatedAt: Date | { toDate: () => Date } | { seconds: number; nanoseconds: number } | string | number | unknown) => {
    if (!calculatedAt) return null;
    return formatCreatedAtDate(calculatedAt);
  };
  return (
    <div className="space-y-8">
      {/* Basic Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        {/* Personal Information */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">Personal Information</h3>
          <div className="space-y-3">
            <div>
              <span className="text-sm text-muted-foreground">Full Name</span>
              <p className="font-medium">
                {userData.firstName} {userData.lastName}
              </p>
            </div>
            <div>
              <span className="text-sm text-muted-foreground">Username</span>
              <p className="font-medium">{userData.username}</p>
            </div>
            <div>
              <span className="text-sm text-muted-foreground">Email</span>
              <p className="font-medium">{userData.email}</p>
            </div>
            <div>
              <span className="text-sm text-muted-foreground">Phone</span>
              <p className="font-medium">{userData.phone}</p>
            </div>
          </div>
        </Card>

        {/* Account Information */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">Account Information</h3>
          <div className="space-y-3">
            <div>
              <span className="text-sm text-muted-foreground">
                Member Since
              </span>
              <p className="font-medium">
                {formatCreatedAtDate(userData.createdAt)}
              </p>
            </div>
            <div>
              <span className="text-sm text-muted-foreground">
                Account Type
              </span>
              <div className="mt-1">
                <Badge variant="secondary">Client</Badge>
              </div>
            </div>
            <div>
              <span className="text-sm text-muted-foreground">Account ID</span>
              <p className="font-medium text-sm font-mono">{userData.uid}</p>
            </div>
          </div>
        </Card>
      </div>

      {/* Calorie Calculator Section */}
      <Card>
        <div
          className="p-6 flex justify-between items-center cursor-pointer"
          onClick={() => setShowCalorieCalculator(!showCalorieCalculator)}
        >
          <h3 className="text-lg font-semibold">Daily Calorie Calculator</h3>
          <Button variant="ghost" size="icon">
            {showCalorieCalculator ? <ChevronUp /> : <ChevronDown />}
          </Button>
        </div>

        {showCalorieCalculator && (
          <CardContent className="pt-0 pb-6">
            {userData.calorieCalculator ? (
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 bg-muted rounded-lg">
                  <div>
                    <h4 className="font-medium mb-2">Your Daily Calorie Goal</h4>
                    <p className="text-3xl font-bold text-primary">
                      {userData.calorieCalculator.dailyCalorieGoal} calories
                    </p>
                    <p className="text-sm text-muted-foreground mt-1">
                      Goal: {userData.calorieCalculator.goal === "lose"
                        ? "Weight Loss"
                        : userData.calorieCalculator.goal === "gain"
                          ? "Weight Gain"
                          : "Weight Maintenance"}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      Calculated on: {formatCalorieDate(userData.calorieCalculator.calculatedAt)}
                    </p>
                  </div>
                  <div className="space-y-2">
                    <div className="grid grid-cols-2 gap-2">
                      <div>
                        <span className="text-sm text-muted-foreground">Age</span>
                        <p>{userData.calorieCalculator.age} years</p>
                      </div>
                      <div>
                        <span className="text-sm text-muted-foreground">Gender</span>
                        <p className="capitalize">{userData.calorieCalculator.gender}</p>
                      </div>
                      <div>
                        <span className="text-sm text-muted-foreground">Weight</span>
                        <p>{userData.calorieCalculator.weight} kg</p>
                      </div>
                      <div>
                        <span className="text-sm text-muted-foreground">Height</span>
                        <p>{userData.calorieCalculator.height} cm</p>
                      </div>
                    </div>
                    <div>
                      <span className="text-sm text-muted-foreground">Activity Level</span>
                      <p className="capitalize">
                        {userData.calorieCalculator.activityLevel.replace('-', ' ')}
                      </p>
                    </div>
                  </div>
                </div>
                <p className="text-sm text-muted-foreground">
                  This is your estimated daily calorie goal based on your personal information and goals.
                  You can recalculate at any time if your information or goals change.
                </p>
              </div>
            ) : (
              <p className="text-muted-foreground">
                You haven't calculated your daily calorie needs yet. Use the calorie calculator in edit mode to determine your personalized calorie goal.
              </p>
            )}
          </CardContent>
        )}
      </Card>

      {/* Meal Preferences Section */}
      <Card>
        <div
          className="p-6 flex justify-between items-center cursor-pointer"
          onClick={() => setShowMealPreferences(!showMealPreferences)}
        >
          <h3 className="text-lg font-semibold">Meal Preferences</h3>
          <Button variant="ghost" size="icon">
            {showMealPreferences ? <ChevronUp /> : <ChevronDown />}
          </Button>
        </div>

        {showMealPreferences && (
          <CardContent className="pt-0 pb-6">
            {userData.mealPreferences ? (
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-3">
                    <div>
                      <h4 className="font-medium mb-2">Meal Frequency</h4>
                      <p>{userData.mealPreferences.mealFrequency} meals per day</p>
                    </div>

                    {userData.mealPreferences.preferredMealTimes.length > 0 && (
                      <div>
                        <h4 className="font-medium mb-2">Preferred Meal Times</h4>
                        <div className="flex flex-wrap gap-2">
                          {userData.mealPreferences.preferredMealTimes.map((time) => (
                            <Badge key={time} variant="outline">{time}</Badge>
                          ))}
                        </div>
                      </div>
                    )}

                    {userData.mealPreferences.preferredCuisines.length > 0 && (
                      <div>
                        <h4 className="font-medium mb-2">Preferred Cuisines</h4>
                        <div className="flex flex-wrap gap-2">
                          {userData.mealPreferences.preferredCuisines.map((cuisine) => (
                            <Badge key={cuisine} variant="outline">{cuisine}</Badge>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>

                  <div className="space-y-3">
                    {userData.mealPreferences.dietaryRestrictions.length > 0 && (
                      <div>
                        <h4 className="font-medium mb-2">Dietary Restrictions</h4>
                        <div className="flex flex-wrap gap-2">
                          {userData.mealPreferences.dietaryRestrictions.map((restriction) => (
                            <Badge key={restriction} variant="outline">{restriction}</Badge>
                          ))}
                        </div>
                      </div>
                    )}

                    {userData.mealPreferences.allergies.length > 0 && (
                      <div>
                        <h4 className="font-medium mb-2">Food Allergies</h4>
                        <div className="flex flex-wrap gap-2">
                          {userData.mealPreferences.allergies.map((allergy) => (
                            <Badge key={allergy} variant="destructive">{allergy}</Badge>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 pt-4">
                  {userData.mealPreferences.favoriteIngredients.length > 0 && (
                    <div>
                      <h4 className="font-medium mb-2">Favorite Ingredients</h4>
                      <div className="flex flex-wrap gap-2">
                        {userData.mealPreferences.favoriteIngredients.map((ingredient) => (
                          <Badge key={ingredient} variant="secondary">{ingredient}</Badge>
                        ))}
                      </div>
                    </div>
                  )}

                  {userData.mealPreferences.dislikedIngredients.length > 0 && (
                    <div>
                      <h4 className="font-medium mb-2">Disliked Ingredients</h4>
                      <div className="flex flex-wrap gap-2">
                        {userData.mealPreferences.dislikedIngredients.map((ingredient) => (
                          <Badge key={ingredient} variant="outline">{ingredient}</Badge>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            ) : (
              <p className="text-muted-foreground">
                You haven't set your meal preferences yet. Edit your profile to add your meal preferences.
              </p>
            )}
          </CardContent>
        )}
      </Card>

      {/* Addresses Section */}
      <Card>
        <div
          className="p-6 flex justify-between items-center cursor-pointer"
          onClick={() => setShowAddresses(!showAddresses)}
        >
          <div className="flex items-center">
            <MapPin className="h-5 w-5 text-primary mr-2" />
            <h3 className="text-lg font-semibold">Addresses</h3>
          </div>
          <Button variant="ghost" size="icon">
            {showAddresses ? <ChevronUp /> : <ChevronDown />}
          </Button>
        </div>

        {showAddresses && (
          <CardContent className="pt-0 pb-6">
            {userData.addresses && userData.addresses.length > 0 ? (
              <div className="space-y-4">
                {userData.addresses.map((address, index) => (
                  <div key={index} className="p-4 border rounded-md relative">
                    <div className="space-y-1">
                      {address.isDefault && (
                        <Badge variant="outline" className="mb-2">Default</Badge>
                      )}
                      <p className="font-medium">{address.street}</p>
                      <p>{address.city}, {address.state} {address.postalCode}</p>
                      <p>{address.country}</p>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-muted-foreground">
                You haven't added any addresses yet. Edit your profile to add addresses.
              </p>
            )}
          </CardContent>
        )}
      </Card>

      {/* Dietary Goals Section */}
      <Card>
        <div
          className="p-6 flex justify-between items-center cursor-pointer"
          onClick={() => setShowDietaryGoals(!showDietaryGoals)}
        >
          <div className="flex items-center">
            <Target className="h-5 w-5 text-primary mr-2" />
            <h3 className="text-lg font-semibold">Dietary Goals</h3>
          </div>
          <Button variant="ghost" size="icon">
            {showDietaryGoals ? <ChevronUp /> : <ChevronDown />}
          </Button>
        </div>

        {showDietaryGoals && (
          <CardContent className="pt-0 pb-6">
            {userData.dietaryGoals && userData.dietaryGoals.length > 0 ? (
              <div className="space-y-4">
                {userData.dietaryGoals.map((goal, index) => {
                  const progressPercentage = goal.target > 0
                    ? Math.min(Math.max((goal.progress / goal.target) * 100, 0), 100)
                    : 0;

                  return (
                    <div key={index} className="p-4 border rounded-md">
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <h4 className="font-medium">
                              {goal.type === "custom" ? "Custom Goal" : `${goal.type.charAt(0).toUpperCase() + goal.type.slice(1)} Goal`}
                            </h4>
                            {goal.completed && (
                              <Badge variant="success" className="ml-2">Completed</Badge>
                            )}
                          </div>
                        </div>

                        <p className="font-medium">
                          Target: {goal.target} {goal.unit}
                        </p>

                        <div className="space-y-1">
                          <div className="flex justify-between text-sm">
                            <span>Progress: {goal.progress} {goal.unit}</span>
                            <span>{Math.round(progressPercentage)}%</span>
                          </div>
                          <Progress value={progressPercentage} className="h-2" />
                        </div>

                        {goal.notes && (
                          <p className="text-sm text-muted-foreground mt-2">{goal.notes}</p>
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>
            ) : (
              <p className="text-muted-foreground">
                You haven't set any dietary goals yet. Edit your profile to add goals.
              </p>
            )}
          </CardContent>
        )}
      </Card>

      {/* Notification Preferences Section */}
      <Card>
        <div
          className="p-6 flex justify-between items-center cursor-pointer"
          onClick={() => setShowNotifications(!showNotifications)}
        >
          <div className="flex items-center">
            <Bell className="h-5 w-5 text-primary mr-2" />
            <h3 className="text-lg font-semibold">Notification Preferences</h3>
          </div>
          <Button variant="ghost" size="icon">
            {showNotifications ? <ChevronUp /> : <ChevronDown />}
          </Button>
        </div>

        {showNotifications && (
          <CardContent className="pt-0 pb-6">
            {userData.notificationPreferences ? (
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-3">
                    <h4 className="font-medium">Notification Channels</h4>
                    <ul className="space-y-2">
                      <li className="flex items-center justify-between">
                        <span>Email Notifications</span>
                        <Badge variant={userData.notificationPreferences.email ? "success" : "outline"}>
                          {userData.notificationPreferences.email ? "Enabled" : "Disabled"}
                        </Badge>
                      </li>
                      <li className="flex items-center justify-between">
                        <span>Push Notifications</span>
                        <Badge variant={userData.notificationPreferences.push ? "success" : "outline"}>
                          {userData.notificationPreferences.push ? "Enabled" : "Disabled"}
                        </Badge>
                      </li>
                      <li className="flex items-center justify-between">
                        <span>In-App Notifications</span>
                        <Badge variant={userData.notificationPreferences.inApp ? "success" : "outline"}>
                          {userData.notificationPreferences.inApp ? "Enabled" : "Disabled"}
                        </Badge>
                      </li>
                    </ul>
                  </div>

                  <div className="space-y-3">
                    <h4 className="font-medium">Notification Types</h4>
                    <ul className="space-y-2">
                      <li className="flex items-center justify-between">
                        <span>Order Updates</span>
                        <Badge variant={userData.notificationPreferences.orderUpdates ? "success" : "outline"}>
                          {userData.notificationPreferences.orderUpdates ? "Enabled" : "Disabled"}
                        </Badge>
                      </li>
                      <li className="flex items-center justify-between">
                        <span>Promotions and Offers</span>
                        <Badge variant={userData.notificationPreferences.promotions ? "success" : "outline"}>
                          {userData.notificationPreferences.promotions ? "Enabled" : "Disabled"}
                        </Badge>
                      </li>
                      <li className="flex items-center justify-between">
                        <span>New Restaurant Alerts</span>
                        <Badge variant={userData.notificationPreferences.newRestaurants ? "success" : "outline"}>
                          {userData.notificationPreferences.newRestaurants ? "Enabled" : "Disabled"}
                        </Badge>
                      </li>
                      <li className="flex items-center justify-between">
                        <span>Weekly Digest</span>
                        <Badge variant={userData.notificationPreferences.weeklyDigest ? "success" : "outline"}>
                          {userData.notificationPreferences.weeklyDigest ? "Enabled" : "Disabled"}
                        </Badge>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            ) : (
              <p className="text-muted-foreground">
                You haven't set your notification preferences yet. Edit your profile to customize notifications.
              </p>
            )}
          </CardContent>
        )}
      </Card>

      {/* Meal Tracker Section */}
      <Card>
        <div
          className="p-6 flex justify-between items-center cursor-pointer"
          onClick={() => setShowMealTracker(!showMealTracker)}
        >
          <div className="flex items-center">
            <Utensils className="h-5 w-5 text-primary mr-2" />
            <h3 className="text-lg font-semibold">Meal Tracker</h3>
          </div>
          <Button variant="ghost" size="icon">
            {showMealTracker ? <ChevronUp /> : <ChevronDown />}
          </Button>
        </div>

        {showMealTracker && (
          <CardContent className="pt-0 pb-6">
            <MealTracker
              userId={userData.uid}
              dietaryGoals={userData.dietaryGoals}
            />
          </CardContent>
        )}
      </Card>

      {/* Followed Restaurants */}
      <FollowedRestaurants userId={userData.uid} />
    </div>
  );
};
