import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { useAuth } from "@/providers/AuthProvider";
import { firestore } from "@/config/firebase";
import {
  doc,
  getDoc,
} from "firebase/firestore";
import { useToggleFollow } from "@/lib/react-query/hooks/useFollowers";
import { toast } from "sonner";

import { Heart } from "lucide-react";

interface FollowButtonProps {
  restaurantId: string;
  restaurantName: string;
  /** Optional: Provide if readily available to avoid an extra DB read. */
  restaurantUsername?: string;
  variant?: "default" | "outline" | "secondary" | "ghost";
  size?: "default" | "sm" | "lg" | "icon";
  className?: string;
}

export const FollowButton = ({
  restaurantId,
  restaurantName,
  restaurantUsername,
  variant = "secondary",
  size = "default",
  className = "",
}: FollowButtonProps) => {
  const { user, userRole } = useAuth();
  const [isFollowing, setIsFollowing] = useState(false);
  const [isChecking, setIsChecking] = useState(true);

  // Use the React Query mutation hook
  const toggleFollow = useToggleFollow();

  useEffect(() => {
    setIsFollowing(false);
    setIsChecking(true);

    if (!user || userRole !== "client" || !restaurantId) {
      setIsChecking(false);
      return;
    }

    const checkFollowStatus = async () => {
      try {
        const followingRef = doc(
          firestore,
          "clients",
          user.uid,
          "following",
          restaurantId
        );
        const followingDoc = await getDoc(followingRef);
        setIsFollowing(followingDoc.exists());
      } catch (error) {
        console.error("Error checking follow status:", error);
      } finally {
        setIsChecking(false);
      }
    };

    checkFollowStatus();
  }, [user, userRole, restaurantId]);

  const handleFollowToggle = async () => {
    if (!user || userRole !== "client" || !restaurantId) {
      return;
    }

    // Immediately update UI state for better UX
    const newFollowingState = !isFollowing;
    setIsFollowing(newFollowingState);

    try {
      // Get username for the mutation
      const userDoc = await getDoc(doc(firestore, "clients", user.uid));
      const username =
        userDoc.data()?.username || user.email?.split("@")[0] || "Anonymous";

      // Get restaurant username if not provided
      let finalRestaurantUsername = restaurantUsername;
      if (!finalRestaurantUsername) {
        try {
          const restaurantDoc = await getDoc(
            doc(firestore, "restaurants", restaurantId)
          );
          finalRestaurantUsername = restaurantDoc.data()?.username || "";
        } catch (readError) {
          console.warn("Could not fetch restaurant username:", readError);
          finalRestaurantUsername = "";
        }
      }

      // Execute the mutation in the background
      toggleFollow.mutate(
        {
          clientId: user.uid,
          restaurantId,
          restaurantName,
          restaurantUsername: finalRestaurantUsername || "",
          username,
          userAvatarUrl: user.photoURL || null,
        },
        {
          // If there's an error, revert the UI state
          onError: () => {
            setIsFollowing(!newFollowingState);
            toast.error(
              `Failed to ${
                newFollowingState ? "follow" : "unfollow"
              } ${restaurantName}`
            );
          },
        }
      );
    } catch (error) {
      // Revert UI state if there's an error
      setIsFollowing(!newFollowingState);
      console.error("Error in follow toggle:", error);
      toast.error(
        `Failed to ${
          newFollowingState ? "follow" : "unfollow"
        } ${restaurantName}`
      );
    }
  };

  if (userRole === "restaurant" || !user) {
    return null;
  }

  if (isChecking) {
    return <Skeleton className={`h-9 w-24 rounded-md ${className}`} />;
  }

  const currentVariant = isFollowing ? "default" : variant;
  const buttonText = isFollowing ? "Following" : "Follow";
  const hoverText = isFollowing
    ? `Unfollow ${restaurantName}`
    : `Follow ${restaurantName}`;

  return (
    <Button
      variant={currentVariant}
      size={size}
      onClick={handleFollowToggle}
      className={`transition-colors duration-200 ease-in-out ${className} ${
        isFollowing
          ? "bg-primary text-primary-foreground hover:bg-primary/80"
          : ""
      }`}
      title={hoverText}
      aria-pressed={isFollowing}
      aria-live="polite"
    >
      <Heart
        className={`mr-2 h-4 w-4 transition-all ${
          isFollowing ? "fill-current" : ""
        }`}
      />
      {buttonText}
    </Button>
  );
};
