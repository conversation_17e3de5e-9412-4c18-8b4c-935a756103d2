import { useState, useEffect } from 'react';
import { differenceInSeconds, format } from 'date-fns';
import { Clock } from 'lucide-react';

interface ScheduledOrderCountdownProps {
  scheduledTime: Date;
  onReady?: () => void;
}

export function ScheduledOrderCountdown({ 
  scheduledTime, 
  onReady 
}: ScheduledOrderCountdownProps) {
  const [timeRemaining, setTimeRemaining] = useState<number>(
    differenceInSeconds(scheduledTime, new Date())
  );
  const [isReady, setIsReady] = useState<boolean>(timeRemaining <= 600); // 10 minutes = 600 seconds
  
  useEffect(() => {
    // Update time remaining every second
    const interval = setInterval(() => {
      const newTimeRemaining = differenceInSeconds(scheduledTime, new Date());
      setTimeRemaining(newTimeRemaining);
      
      // Check if we've reached the 10-minute mark
      if (newTimeRemaining <= 600 && !isReady) {
        setIsReady(true);
        if (onReady) onReady();
      }
      
      // Clear interval if time is up
      if (newTimeRemaining <= 0) {
        clearInterval(interval);
      }
    }, 1000);
    
    return () => clearInterval(interval);
  }, [scheduledTime, isReady, onReady]);
  
  // Format the time remaining
  const formatTimeRemaining = () => {
    if (timeRemaining <= 0) {
      return "Ready to process";
    }
    
    const hours = Math.floor(timeRemaining / 3600);
    const minutes = Math.floor((timeRemaining % 3600) / 60);
    const seconds = timeRemaining % 60;
    
    if (hours > 0) {
      return `${hours}h ${minutes}m ${seconds}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds}s`;
    } else {
      return `${seconds}s`;
    }
  };
  
  return (
    <div className={`flex items-center gap-1.5 text-sm ${isReady ? 'text-green-600' : 'text-muted-foreground'}`}>
      <Clock className="h-3.5 w-3.5" />
      <span>
        {timeRemaining <= 0 
          ? "Ready to process" 
          : isReady 
            ? `Ready in ${formatTimeRemaining()}` 
            : `Scheduled for ${format(scheduledTime, 'p')}`}
      </span>
    </div>
  );
}
