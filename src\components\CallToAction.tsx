import { But<PERSON> } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";

export function CallToAction() {
  const navigate = useNavigate();

  return (
    <section className="py-20 bg-white">
      <div className="mx-auto px-4 text-center">
        <h2 className="text-4xl font-bold mb-6">
          Ready to Start Your Culinary Journey?
        </h2>
        <p className="text-xl text-gray-600 mb-8">
          Join thousands of food lovers who have discovered their perfect dining
          spots with Qonai
        </p>
        <Button
          className="px-8 py-4 bg-orange-500 text-white rounded-lg hover:bg-orange-600"
          onClick={() => navigate("/auth")}
        >
          Get Started Now
        </Button>
      </div>
    </section>
  );
}
