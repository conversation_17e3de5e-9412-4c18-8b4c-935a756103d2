import React, { useState, useEffect, useCallback } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Bell,
  Volume2,
  Mail,
  Smartphone,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Settings,
} from "lucide-react";
import { restaurantNotificationService } from "@/services/RestaurantNotificationService";
import { notificationService } from "@/services/NotificationService";
import { toast } from "sonner";

interface NotificationSettingsProps {
  restaurantId: string;
}

interface NotificationPreferences {
  enableBrowserNotifications: boolean;
  enableSound: boolean;
  enableEmailNotifications: boolean;
  enableOrderAlerts: boolean;
  enableStatusUpdates: boolean;
}

export const NotificationSettings: React.FC<NotificationSettingsProps> = ({
  restaurantId,
}) => {
  const [preferences, setPreferences] = useState<NotificationPreferences>({
    enableBrowserNotifications: true,
    enableSound: true,
    enableEmailNotifications: true,
    enableOrderAlerts: true,
    enableStatusUpdates: true,
  });

  const [notificationStatus, setNotificationStatus] = useState({
    permission: "default" as NotificationPermission,
    serviceWorkerReady: false,
    activeListeners: 0,
  });

  const [isInitializing, setIsInitializing] = useState(false);
  const [isTestingNotification, setIsTestingNotification] = useState(false);

  const initializeNotifications = useCallback(async () => {
    setIsInitializing(true);
    try {
      await restaurantNotificationService.initializeRestaurantNotifications({
        restaurantId,
        enableSound: preferences.enableSound,
        enableBrowserNotifications: preferences.enableBrowserNotifications,
        enableEmailNotifications: preferences.enableEmailNotifications,
      });

      localStorage.setItem(`notification_auto_init_${restaurantId}`, "true");
      updateNotificationStatus();
      toast.success("Notifications initialized successfully!");
    } catch (error) {
      console.error("Error initializing notifications:", error);
      toast.error("Failed to initialize notifications");
    } finally {
      setIsInitializing(false);
    }
  }, [
    restaurantId,
    preferences.enableSound,
    preferences.enableBrowserNotifications,
    preferences.enableEmailNotifications,
  ]);

  const updateNotificationStatus = () => {
    const status = restaurantNotificationService.getNotificationStatus();
    setNotificationStatus(status);
  };

  useEffect(() => {
    // Load saved preferences from localStorage
    const savedPreferences = localStorage.getItem(
      `notification_prefs_${restaurantId}`
    );
    if (savedPreferences) {
      setPreferences(JSON.parse(savedPreferences));
    }

    // Get current notification status
    updateNotificationStatus();

    // Auto-initialize if preferences are enabled
    const autoInit = localStorage.getItem(
      `notification_auto_init_${restaurantId}`
    );
    if (autoInit === "true") {
      initializeNotifications();
    }
  }, [restaurantId, initializeNotifications]);

  const savePreferences = (newPreferences: NotificationPreferences) => {
    setPreferences(newPreferences);
    localStorage.setItem(
      `notification_prefs_${restaurantId}`,
      JSON.stringify(newPreferences)
    );
  };

  const handlePreferenceChange = (
    key: keyof NotificationPreferences,
    value: boolean
  ) => {
    const newPreferences = { ...preferences, [key]: value };
    savePreferences(newPreferences);

    // If disabling browser notifications, cleanup
    if (key === "enableBrowserNotifications" && !value) {
      restaurantNotificationService.cleanup(restaurantId);
      updateNotificationStatus();
    }

    // If enabling notifications, reinitialize
    if (key === "enableBrowserNotifications" && value) {
      initializeNotifications();
    }
  };

  const requestNotificationPermission = async () => {
    try {
      const permission =
        await notificationService.requestNotificationPermission();
      updateNotificationStatus();

      if (permission === "granted") {
        toast.success("Notification permission granted!");
        if (preferences.enableBrowserNotifications) {
          initializeNotifications();
        }
      } else {
        toast.error(
          "Notification permission denied. Please enable in browser settings."
        );
      }
    } catch (error) {
      console.error("Error requesting notification permission:", error);
      toast.error("Failed to request notification permission");
    }
  };

  const sendTestNotification = async () => {
    setIsTestingNotification(true);
    try {
      await restaurantNotificationService.sendTestNotification(restaurantId);
      toast.success("Test notification sent!");
    } catch (error) {
      console.error("Error sending test notification:", error);
      toast.error("Failed to send test notification");
    } finally {
      setIsTestingNotification(false);
    }
  };

  const getPermissionBadge = () => {
    switch (notificationStatus.permission) {
      case "granted":
        return (
          <Badge variant="default" className="bg-green-100 text-green-800">
            <CheckCircle className="h-3 w-3 mr-1" />
            Granted
          </Badge>
        );
      case "denied":
        return (
          <Badge variant="destructive">
            <XCircle className="h-3 w-3 mr-1" />
            Denied
          </Badge>
        );
      default:
        return (
          <Badge variant="secondary">
            <AlertTriangle className="h-3 w-3 mr-1" />
            Not Set
          </Badge>
        );
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bell className="h-5 w-5" />
            Notification Settings
          </CardTitle>
          <CardDescription>
            Configure how you want to receive notifications about new orders and
            updates.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Notification Status */}
          <div className="space-y-3">
            <h4 className="font-medium">Notification Status</h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center gap-2">
                  <Bell className="h-4 w-4" />
                  <span className="text-sm">Permission</span>
                </div>
                {getPermissionBadge()}
              </div>
              <div className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center gap-2">
                  <Settings className="h-4 w-4" />
                  <span className="text-sm">Service Worker</span>
                </div>
                <Badge
                  variant={
                    notificationStatus.serviceWorkerReady
                      ? "default"
                      : "secondary"
                  }
                >
                  {notificationStatus.serviceWorkerReady
                    ? "Ready"
                    : "Not Ready"}
                </Badge>
              </div>
              <div className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center gap-2">
                  <Smartphone className="h-4 w-4" />
                  <span className="text-sm">Active Listeners</span>
                </div>
                <Badge variant="outline">
                  {notificationStatus.activeListeners}
                </Badge>
              </div>
            </div>
          </div>

          {/* Permission Alert */}
          {notificationStatus.permission !== "granted" && (
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                Browser notifications are not enabled.
                <Button
                  variant="link"
                  className="p-0 h-auto ml-1"
                  onClick={requestNotificationPermission}
                >
                  Click here to enable them.
                </Button>
              </AlertDescription>
            </Alert>
          )}

          {/* Notification Preferences */}
          <div className="space-y-4">
            <h4 className="font-medium">Notification Preferences</h4>

            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Smartphone className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <Label htmlFor="browser-notifications">
                      Browser Notifications
                    </Label>
                    <p className="text-sm text-muted-foreground">
                      Show desktop notifications for new orders
                    </p>
                  </div>
                </div>
                <Switch
                  id="browser-notifications"
                  checked={preferences.enableBrowserNotifications}
                  onCheckedChange={(checked) =>
                    handlePreferenceChange(
                      "enableBrowserNotifications",
                      checked
                    )
                  }
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Volume2 className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <Label htmlFor="sound-notifications">Sound Alerts</Label>
                    <p className="text-sm text-muted-foreground">
                      Play sound when receiving notifications
                    </p>
                  </div>
                </div>
                <Switch
                  id="sound-notifications"
                  checked={preferences.enableSound}
                  onCheckedChange={(checked) =>
                    handlePreferenceChange("enableSound", checked)
                  }
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Mail className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <Label htmlFor="email-notifications">
                      Email Notifications
                    </Label>
                    <p className="text-sm text-muted-foreground">
                      Receive email alerts for important updates
                    </p>
                  </div>
                </div>
                <Switch
                  id="email-notifications"
                  checked={preferences.enableEmailNotifications}
                  onCheckedChange={(checked) =>
                    handlePreferenceChange("enableEmailNotifications", checked)
                  }
                />
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3 pt-4 border-t">
            <Button
              onClick={initializeNotifications}
              disabled={
                isInitializing || notificationStatus.permission !== "granted"
              }
              className="flex items-center gap-2"
            >
              <Bell className="h-4 w-4" />
              {isInitializing ? "Initializing..." : "Initialize Notifications"}
            </Button>

            <Button
              variant="outline"
              onClick={sendTestNotification}
              disabled={
                isTestingNotification || !preferences.enableBrowserNotifications
              }
              className="flex items-center gap-2"
            >
              <Volume2 className="h-4 w-4" />
              {isTestingNotification ? "Sending..." : "Test Notification"}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
