import { useState, useEffect } from "react";
import { useParams } from "react-router-dom";
import { doc, getDoc } from "firebase/firestore";
import { firestore } from "@/config/firebase";
import { useAuth } from "@/providers/AuthProvider";
import { Loading } from "@/components/ui/loading";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Printer,
  ShoppingBag,
  Calendar,
  Clock,
  AlertCircle,
  Copy,
  Check,
} from "lucide-react";
import { format, isPast } from "date-fns";
import { tr } from "date-fns/locale";
import { Order } from "@/types/dashboard";
import { toast } from "sonner";

export const OrderPage = () => {
  const { orderId } = useParams();
  const { user, userRole } = useAuth();
  const [order, setOrder] = useState<Order | null>(null);
  const [loading, setLoading] = useState(true);
  const [copiedOrderId, setCopiedOrderId] = useState<boolean>(false);

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text).then(() => {
      setCopiedOrderId(true);
      setTimeout(() => setCopiedOrderId(false), 2000);
    });
  };

  useEffect(() => {
    const fetchOrder = async () => {
      if (!user || !orderId) return;

      try {
        let orderDoc;
        if (userRole === "restaurant") {
          // Restoran kendi siparişlerini kendi koleksiyonundan alır
          const orderRef = doc(
            firestore,
            "restaurants",
            user.uid,
            "orders",
            orderId
          );
          orderDoc = await getDoc(orderRef);
        } else {
          // Müşteri siparişi restoranın koleksiyonundan alır
          const clientRef = doc(firestore, "clients", user.uid);
          const clientOrderRef = doc(clientRef, "orders", orderId);
          const clientOrderDoc = await getDoc(clientOrderRef);

          if (clientOrderDoc.exists()) {
            const clientOrderData = clientOrderDoc.data();
            // Restoranın orders koleksiyonundan asıl siparişi al
            const restaurantOrderRef = doc(
              firestore,
              "restaurants",
              clientOrderData.restaurantId,
              "orders",
              orderId
            );
            orderDoc = await getDoc(restaurantOrderRef);
          }
        }

        if (!orderDoc || !orderDoc.exists()) {
          toast.error("Order not found");
          return;
        }

        setOrder({ orderId: orderDoc.id, ...orderDoc.data() } as Order);
      } catch (error) {
        console.error("Error fetching order:", error);
        toast.error("Error loading order");
      } finally {
        setLoading(false);
      }
    };

    fetchOrder();
  }, [user, userRole, orderId]);

  const getStatusColor = (status: Order["status"]) => {
    switch (status) {
      case "pending":
        return "bg-yellow-100 text-yellow-800";
      case "preparing":
        return "bg-blue-100 text-blue-800";
      case "ready":
        return "bg-green-100 text-green-800";
      case "completed":
        return "bg-green-100 text-green-800";
      case "cancelled":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const handlePrintReceipt = () => {
    if (!order) return;

    const receiptUrl = `/receipts/${order.restaurantId}/${order.orderId}`;
    window.open(receiptUrl, "_blank");
  };

  if (loading) return <Loading />;
  if (!order) return <div>Order not found</div>;

  return (
    <div className="mx-auto p-4">
      <div className="max-w-3xl mx-auto bg-white p-8 rounded-lg shadow">
        <div className="flex justify-between items-start mb-8">
          <div>
            <h1 className="text-3xl font-bold mb-2">Order Details</h1>
            <div className="flex items-center gap-2">
              <p className="text-gray-500">
                Order #{order.orderId.substring(0, 8)}
              </p>
              <Button
                variant="ghost"
                size="sm"
                className="h-7 px-2"
                onClick={() => copyToClipboard(order.orderId)}
              >
                {copiedOrderId ? (
                  <Check className="h-3.5 w-3.5 text-green-500" />
                ) : (
                  <Copy className="h-3.5 w-3.5 text-gray-500" />
                )}
                <span className="sr-only">Copy full order ID</span>
              </Button>
            </div>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={handlePrintReceipt}
            className="flex items-center gap-2"
          >
            <Printer className="h-4 w-4" />
            Print Receipt
          </Button>
        </div>

        <div className="space-y-6">
          {/* Restaurant Info */}
          <div className="flex items-center gap-4 pb-6 border-b">
            <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center">
              <ShoppingBag className="h-6 w-6 text-primary" />
            </div>
            <div>
              <h2 className="font-semibold text-lg">{order.restaurantName}</h2>
              <p className="text-gray-500">
                {format(order.orderDate.toDate(), "PPPp", { locale: tr })}
              </p>
            </div>
          </div>

          {/* Order Status */}
          <div>
            <h3 className="font-semibold mb-2">Status</h3>
            <Badge className={getStatusColor(order.status)}>
              {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
            </Badge>
          </div>

          {/* Scheduled Order Info */}
          {order.isScheduled && (
            <div className="pt-4 pb-4 border-t border-b">
              <h3 className="font-semibold mb-3 flex items-center gap-2">
                <Calendar className="h-5 w-5 text-primary" />
                Scheduled Order
              </h3>

              <div className="space-y-2">
                <div className="flex items-start gap-2">
                  <Clock className="h-4 w-4 text-muted-foreground mt-0.5" />
                  <div>
                    <p className="font-medium">
                      {order.scheduledFor
                        ? format(order.scheduledFor.toDate(), "PPP 'at' p", {
                            locale: tr,
                          })
                        : "Not specified"}
                    </p>
                    {order.scheduledFor && (
                      <p className="text-sm text-muted-foreground">
                        {isPast(order.scheduledFor.toDate())
                          ? "This order has been processed"
                          : "This order will be processed at the scheduled time"}
                      </p>
                    )}
                  </div>
                </div>

                {order.status === "pending" &&
                  order.scheduledFor &&
                  !isPast(order.scheduledFor.toDate()) && (
                    <div className="rounded-md bg-blue-50 p-3 border border-blue-200 mt-2">
                      <div className="flex items-center">
                        <AlertCircle className="h-4 w-4 text-blue-500 mr-2 flex-shrink-0" />
                        <span className="text-blue-800 text-sm">
                          You'll receive a notification 30 minutes before your
                          order is processed.
                        </span>
                      </div>
                    </div>
                  )}
              </div>
            </div>
          )}

          {/* Order Items */}
          <div>
            <h3 className="font-semibold mb-4">Order Items</h3>
            <div className="space-y-3">
              {order.items.map((item, index) => (
                <div
                  key={index}
                  className="flex justify-between items-center py-2 border-b last:border-0"
                >
                  <div>
                    <p className="font-medium">
                      {item.name} x {item.quantity}
                    </p>
                    {item.notes && (
                      <p className="text-sm text-gray-500">
                        Note: {item.notes}
                      </p>
                    )}
                  </div>
                  <span className="font-medium">
                    {(item.price * item.quantity).toFixed(2)} AZN
                  </span>
                </div>
              ))}
            </div>
          </div>

          {/* Total */}
          <div className="flex justify-between items-center pt-4 border-t text-lg font-bold">
            <span>Total</span>
            <span>
              {typeof order.totalPrice === "number"
                ? order.totalPrice.toFixed(2)
                : order.totalPrice}{" "}
              AZN
            </span>
          </div>

          {/* Notes */}
          {order.notes && (
            <div className="pt-6 border-t">
              <h3 className="font-semibold mb-2">Order Notes</h3>
              <p className="text-gray-600">{order.notes}</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
