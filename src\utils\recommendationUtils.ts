import { Restaurant } from "@/types/restaurant";
import { Order } from "@/types/order";
import { MenuItem } from "@/types";
import { MealPreferences } from "@/types";
import { firestore } from "@/config/firebase";
import { doc, getDoc } from "firebase/firestore";

// Define the recommendation types
export interface Recommendation {
  id: string;
  type: "restaurant" | "menuItem";
  details: Restaurant | MenuItem;
  score: number;
  reason: string;
}

/**
 * Fetches user preferences from Firestore
 * @param userId The user ID
 * @returns The user's meal preferences or null if not found
 */
export const fetchUserPreferences = async (userId: string): Promise<MealPreferences | null> => {
  try {
    const userDoc = await getDoc(doc(firestore, "clients", userId));
    if (!userDoc.exists()) return null;

    const userData = userDoc.data();
    return userData.mealPreferences || null;
  } catch {
    // Handle error fetching user preferences
    return null;
  }
};

/**
 * Generates restaurant recommendations based on user order history
 * @param userId The user ID
 * @param orders The user's order history
 * @param allRestaurants All available restaurants
 * @param limit Maximum number of recommendations to return
 * @returns Array of restaurant recommendations
 */
export const getRestaurantRecommendations = (
  _userId: string,
  orders: Order[],
  allRestaurants: Restaurant[],
  limit: number = 3
): Recommendation[] => {
  // If no orders, return empty array
  if (!orders.length) return [];

  // Create a map of restaurant IDs to frequency of orders
  const restaurantFrequency: Record<string, number> = {};

  // Count how many times the user has ordered from each restaurant
  orders.forEach(order => {
    if (order.restaurantId) {
      restaurantFrequency[order.restaurantId] = (restaurantFrequency[order.restaurantId] || 0) + 1;
    }
  });

  // Create a map of restaurant IDs to their details
  const restaurantMap = new Map<string, Restaurant>();
  allRestaurants.forEach(restaurant => {
    restaurantMap.set(restaurant.id, restaurant);
  });

  // Generate recommendations based on order frequency
  const recommendations: Recommendation[] = [];

  // First, add restaurants the user has ordered from before
  Object.entries(restaurantFrequency)
    .sort((a, b) => b[1] - a[1]) // Sort by frequency (descending)
    .slice(0, Math.ceil(limit / 2)) // Take half of the limit
    .forEach(([restaurantId, frequency]) => {
      const restaurant = restaurantMap.get(restaurantId);
      if (restaurant) {
        recommendations.push({
          id: restaurantId,
          type: "restaurant",
          details: restaurant,
          score: frequency,
          reason: `You've ordered from here ${frequency} ${frequency === 1 ? 'time' : 'times'}`
        });
      }
    });

  return recommendations;
};

/**
 * Generates menu item recommendations based on user order history
 * @param userId The user ID
 * @param orders The user's order history
 * @param allRestaurants All available restaurants with their menus
 * @param limit Maximum number of recommendations to return
 * @returns Array of menu item recommendations
 */
export const getMenuItemRecommendations = (
  _userId: string,
  orders: Order[],
  allRestaurants: Restaurant[],
  limit: number = 3
): Recommendation[] => {
  // If no orders, return empty array
  if (!orders.length) return [];

  // Create a map of menu item IDs to frequency of orders
  const menuItemFrequency: Record<string, number> = {};

  // Count how many times the user has ordered each menu item
  orders.forEach(order => {
    order.items.forEach(item => {
      if (item.menuItemId) {
        menuItemFrequency[item.menuItemId] = (menuItemFrequency[item.menuItemId] || 0) + item.quantity;
      }
    });
  });

  // Create a map of menu item IDs to their details
  const menuItemMap = new Map<string, MenuItem>();

  // Populate the menu item map from all restaurants
  allRestaurants.forEach(restaurant => {
    if (restaurant.menu) {
      restaurant.menu.forEach(item => {
        // Make sure the item has all required fields
        const menuItem: MenuItem = {
          ...item,
          itemId: item.itemId || item.id || '',
          restaurantId: restaurant.id,
          // Add restaurant name for display purposes
          dietary: item.dietary || []
        };
        menuItemMap.set(menuItem.itemId, menuItem);
      });
    }
  });

  // Generate recommendations based on order frequency
  const recommendations: Recommendation[] = [];

  // Add menu items the user has ordered before
  Object.entries(menuItemFrequency)
    .sort((a, b) => b[1] - a[1]) // Sort by frequency (descending)
    .slice(0, limit)
    .forEach(([menuItemId, frequency]) => {
      const menuItem = menuItemMap.get(menuItemId);
      if (menuItem) {
        recommendations.push({
          id: menuItemId,
          type: "menuItem",
          details: menuItem,
          score: frequency,
          reason: `You've ordered this ${frequency} ${frequency === 1 ? 'time' : 'times'}`
        });
      }
    });

  return recommendations;
};

/**
 * Generates preference-based restaurant recommendations
 * @param userPreferences The user's meal preferences
 * @param allRestaurants All available restaurants
 * @param limit Maximum number of recommendations to return
 * @returns Array of restaurant recommendations
 */
export const getPreferenceBasedRecommendations = (
  userPreferences: MealPreferences | null,
  allRestaurants: Restaurant[],
  limit: number = 3
): Recommendation[] => {
  // If no preferences, return empty array
  if (!userPreferences) return [];

  // Calculate a match score for each restaurant based on user preferences
  const scoredRestaurants = allRestaurants.map(restaurant => {
    let score = 0;
    const matchReasons: string[] = [];

    // Match cuisines
    if (userPreferences.preferredCuisines && userPreferences.preferredCuisines.length > 0) {
      const matchedCuisines = restaurant.cuisines?.filter(cuisine =>
        userPreferences.preferredCuisines.includes(cuisine)
      ) || [];

      if (matchedCuisines.length > 0) {
        score += matchedCuisines.length * 2;
        matchReasons.push(`Matches your preferred cuisines: ${matchedCuisines.join(', ')}`);
      }
    }

    // Match categories
    if (userPreferences.preferredCategories && userPreferences.preferredCategories.length > 0) {
      const matchedCategories = restaurant.categories?.filter(category =>
        userPreferences.preferredCategories.includes(category)
      ) || [];

      if (matchedCategories.length > 0) {
        score += matchedCategories.length * 1.5;
        matchReasons.push(`Matches your preferred categories: ${matchedCategories.join(', ')}`);
      }
    }

    // Match price range
    if (userPreferences.preferredPriceRange && restaurant.priceRange) {
      if (userPreferences.preferredPriceRange === restaurant.priceRange) {
        score += 1;
        matchReasons.push(`Matches your preferred price range: ${restaurant.priceRange}`);
      }
    }

    // Match atmosphere
    if (userPreferences.preferredAtmosphere && userPreferences.preferredAtmosphere.length > 0 && restaurant.atmosphere) {
      const matchedAtmosphere = userPreferences.preferredAtmosphere.filter(atm =>
        restaurant.atmosphere?.includes(atm)
      );

      if (matchedAtmosphere.length > 0) {
        score += matchedAtmosphere.length;
        matchReasons.push(`Matches your preferred atmosphere: ${matchedAtmosphere.join(', ')}`);
      }
    }

    // Match features
    if (userPreferences.preferredFeatures && userPreferences.preferredFeatures.length > 0 && restaurant.features) {
      const matchedFeatures = userPreferences.preferredFeatures.filter(feature =>
        restaurant.features?.includes(feature)
      );

      if (matchedFeatures.length > 0) {
        score += matchedFeatures.length;
        matchReasons.push(`Has features you like: ${matchedFeatures.join(', ')}`);
      }
    }

    return {
      restaurant,
      score,
      reason: matchReasons.length > 0 ? matchReasons[0] : "Recommended for you"
    };
  });

  // Sort by score (descending) and take the top results
  const topRestaurants = scoredRestaurants
    .filter(item => item.score > 0) // Only include restaurants with a positive score
    .sort((a, b) => b.score - a.score)
    .slice(0, limit);

  // Convert to recommendation format
  return topRestaurants.map(item => ({
    id: item.restaurant.id,
    type: "restaurant" as const,
    details: item.restaurant,
    score: item.score,
    reason: item.reason
  }));
};

/**
 * Generates collaborative filtering recommendations
 * @param userId The user ID
 * @param orders The user's order history
 * @param allRestaurants All available restaurants
 * @param limit Maximum number of recommendations to return
 * @returns Array of restaurant recommendations
 */
export const getCollaborativeFilteringRecommendations = async (
  _userId: string,
  orders: Order[],
  allRestaurants: Restaurant[],
  limit: number = 3
): Promise<Recommendation[]> => {
  // This is a simplified collaborative filtering implementation
  // In a real-world scenario, this would be more sophisticated

  // If no orders, return empty array
  if (!orders.length) return [];

  // Get the restaurants the user has ordered from
  const userRestaurantIds = new Set(orders.map(order => order.restaurantId));

  // Get restaurants with similar categories/cuisines to what the user has ordered from
  const userRestaurants = allRestaurants.filter(restaurant => userRestaurantIds.has(restaurant.id));

  // Extract all categories and cuisines from user's restaurants
  const userCategories = new Set<string>();
  const userCuisines = new Set<string>();

  userRestaurants.forEach(restaurant => {
    restaurant.categories?.forEach(category => userCategories.add(category));
    restaurant.cuisines?.forEach(cuisine => userCuisines.add(cuisine));
  });

  // Find similar restaurants that the user hasn't ordered from yet
  const similarRestaurants = allRestaurants
    .filter(restaurant => !userRestaurantIds.has(restaurant.id)) // Exclude restaurants the user has already ordered from
    .map(restaurant => {
      let score = 0;
      const matchReasons: string[] = [];

      // Calculate similarity based on categories
      const matchedCategories = restaurant.categories?.filter(category => userCategories.has(category)) || [];
      score += matchedCategories.length * 1.5;

      if (matchedCategories.length > 0) {
        matchReasons.push(`Similar to restaurants you've tried: ${matchedCategories.join(', ')}`);
      }

      // Calculate similarity based on cuisines
      const matchedCuisines = restaurant.cuisines?.filter(cuisine => userCuisines.has(cuisine)) || [];
      score += matchedCuisines.length * 2;

      if (matchedCuisines.length > 0 && matchReasons.length === 0) {
        matchReasons.push(`Similar cuisine to what you've enjoyed: ${matchedCuisines.join(', ')}`);
      }

      return {
        restaurant,
        score,
        reason: matchReasons.length > 0 ? matchReasons[0] : "Others with similar taste enjoyed this"
      };
    })
    .filter(item => item.score > 0) // Only include restaurants with a positive score
    .sort((a, b) => b.score - a.score)
    .slice(0, limit);

  // Convert to recommendation format
  return similarRestaurants.map(item => ({
    id: item.restaurant.id,
    type: "restaurant" as const,
    details: item.restaurant,
    score: item.score,
    reason: item.reason
  }));
};

/**
 * Combines different recommendation approaches into a single list
 * @param userId The user ID
 * @param orders The user's order history
 * @param allRestaurants All available restaurants
 * @param userPreferences The user's meal preferences
 * @param limit Maximum number of recommendations to return
 * @returns Array of combined recommendations
 */
export const getCombinedRecommendations = async (
  userId: string,
  orders: Order[],
  allRestaurants: Restaurant[],
  userPreferences: MealPreferences | null,
  limit: number = 6
): Promise<Recommendation[]> => {
  // If userPreferences is null, try to fetch it
  const preferences = userPreferences || await fetchUserPreferences(userId);

  // Get recommendations from different approaches
  const historyBasedRecs = getRestaurantRecommendations(userId, orders, allRestaurants, Math.ceil(limit / 3));
  const preferenceBasedRecs = getPreferenceBasedRecommendations(preferences, allRestaurants, Math.ceil(limit / 3));
  const collaborativeRecs = await getCollaborativeFilteringRecommendations(userId, orders, allRestaurants, Math.ceil(limit / 3));

  // Combine all recommendations
  const allRecs = [...historyBasedRecs, ...preferenceBasedRecs, ...collaborativeRecs];

  // Remove duplicates (prefer the one with higher score)
  const uniqueRecs = allRecs.reduce((acc, current) => {
    const existingRec = acc.find(item => item.id === current.id);
    if (!existingRec) {
      acc.push(current);
    } else if (current.score > existingRec.score) {
      // Replace with higher scored recommendation
      acc[acc.indexOf(existingRec)] = current;
    }
    return acc;
  }, [] as Recommendation[]);

  // Sort by score (descending) and take the top results
  return uniqueRecs
    .sort((a, b) => b.score - a.score)
    .slice(0, limit);
};
