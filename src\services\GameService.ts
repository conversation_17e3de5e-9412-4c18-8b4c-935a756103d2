import { firestore } from "@/config/firebase";
import { collection, addDoc, serverTimestamp } from "firebase/firestore";
import {
  MiniGame,
  FoodMatchCard,
  TriviaQuestion,
  GameResult,
} from "@/types/games";
import { loyaltyService } from "./LoyaltyService";

// Define the mini-games
const MINI_GAMES: MiniGame[] = [
  {
    id: "foodMatch",
    name: "Food Match Challenge",
    description:
      "Match pairs of food items to earn points. Complete the game to earn rewards!",
    iconName: "utensils",
    pointsPerWin: 50,
    maxDailyPlays: 3,
    isActive: true,
  },
  {
    id: "culinaryTrivia",
    name: "Culinary Trivia",
    description:
      "Test your food knowledge with trivia questions about cuisines, ingredients, and cooking techniques.",
    iconName: "brain",
    pointsPerWin: 10, // Per correct answer
    maxDailyPlays: 5,
    isActive: true,
  },
  {
    id: "dailyCheckIn",
    name: "Daily Check-in",
    description:
      "Check in daily to earn points. Build a streak for bonus points!",
    iconName: "calendar-check",
    pointsPerWin: 25, // Base points
    maxDailyPlays: 1,
    isActive: true,
  },
  {
    id: "wordScramble",
    name: "Word Scramble",
    description:
      "Unscramble letters to find food-related words. The faster you solve, the more points you earn!",
    iconName: "text",
    pointsPerWin: 15, // Per word solved
    maxDailyPlays: 3,
    isActive: true,
  },
  {
    id: "recipeChallenge",
    name: "Recipe Challenge",
    description:
      "Guess what dish can be made with the given ingredients. Test your culinary creativity!",
    iconName: "chef-hat",
    pointsPerWin: 30,
    maxDailyPlays: 2,
    isActive: true,
  },
];

// Food match card data with emojis
const FOOD_MATCH_CARDS: FoodMatchCard[] = [
  { id: "burger", name: "Burger", emoji: "🍔" },
  { id: "pizza", name: "Pizza", emoji: "🍕" },
  { id: "sushi", name: "Sushi", emoji: "🍣" },
  { id: "pasta", name: "Pasta", emoji: "🍝" },
  { id: "salad", name: "Salad", emoji: "🥗" },
  { id: "taco", name: "Taco", emoji: "🌮" },
  { id: "steak", name: "Steak", emoji: "🥩" },
  { id: "icecream", name: "Ice Cream", emoji: "🍦" },
  { id: "cake", name: "Cake", emoji: "🍰" },
  { id: "coffee", name: "Coffee", emoji: "☕" },
  { id: "sandwich", name: "Sandwich", emoji: "🥪" },
  { id: "donut", name: "Donut", emoji: "🍩" },
  { id: "apple", name: "Apple", emoji: "🍎" },
  { id: "bread", name: "Bread", emoji: "🍞" },
  { id: "chicken", name: "Chicken", emoji: "🍗" },
  { id: "chocolate", name: "Chocolate", emoji: "🍫" },
  { id: "pancakes", name: "Pancakes", emoji: "🥞" },
  { id: "cupcake", name: "Cupcake", emoji: "🧁" },
  { id: "croissant", name: "Croissant", emoji: "🥐" },
  { id: "avocado", name: "Avocado", emoji: "🥑" },
  { id: "bacon", name: "Bacon", emoji: "🥓" },
  { id: "hotdog", name: "Hot Dog", emoji: "🌭" },
  { id: "popcorn", name: "Popcorn", emoji: "🍿" },
  { id: "pretzel", name: "Pretzel", emoji: "🥨" },
];

// Word Scramble game words
const WORD_SCRAMBLE_WORDS = [
  { word: "PIZZA", hint: "Italian dish with cheese and toppings" },
  { word: "BURGER", hint: "Sandwich with a patty" },
  { word: "PASTA", hint: "Italian noodle dish" },
  { word: "SUSHI", hint: "Japanese dish with rice and fish" },
  { word: "SALAD", hint: "Dish with mixed vegetables" },
  { word: "STEAK", hint: "Cut of meat" },
  { word: "BREAD", hint: "Baked staple food" },
  { word: "CHEESE", hint: "Dairy product" },
  { word: "CHICKEN", hint: "Popular poultry" },
  { word: "DESSERT", hint: "Sweet course after a meal" },
  { word: "BREAKFAST", hint: "First meal of the day" },
  { word: "DINNER", hint: "Evening meal" },
  { word: "LUNCH", hint: "Midday meal" },
  { word: "APPETIZER", hint: "Starter before main course" },
  { word: "CUISINE", hint: "Style of cooking" },
  { word: "RECIPE", hint: "Instructions for preparing food" },
  { word: "RESTAURANT", hint: "Place to eat out" },
  { word: "MENU", hint: "List of dishes available" },
  { word: "CHEF", hint: "Professional cook" },
  { word: "GRILL", hint: "Cooking method with direct heat" },
];

// Recipe Challenge data
const RECIPE_CHALLENGE_DATA = [
  {
    ingredients: ["Flour", "Eggs", "Milk", "Butter"],
    dish: "Pancakes",
    hint: "Popular breakfast item, flat and round",
  },
  {
    ingredients: ["Rice", "Vinegar", "Fish", "Seaweed", "Wasabi"],
    dish: "Sushi",
    hint: "Japanese dish often served with soy sauce",
  },
  {
    ingredients: ["Flour", "Yeast", "Tomato Sauce", "Cheese", "Toppings"],
    dish: "Pizza",
    hint: "Italian dish baked in an oven",
  },
  {
    ingredients: ["Ground Beef", "Buns", "Lettuce", "Tomato", "Cheese"],
    dish: "Burger",
    hint: "Popular fast food sandwich",
  },
  {
    ingredients: ["Pasta", "Tomato Sauce", "Ground Beef", "Onions", "Herbs"],
    dish: "Spaghetti Bolognese",
    hint: "Italian pasta dish with meat sauce",
  },
  {
    ingredients: ["Chicken", "Rice", "Saffron", "Vegetables", "Broth"],
    dish: "Paella",
    hint: "Spanish rice dish",
  },
  {
    ingredients: ["Flour", "Butter", "Sugar", "Eggs", "Chocolate Chips"],
    dish: "Chocolate Chip Cookies",
    hint: "Popular sweet baked treat",
  },
  {
    ingredients: ["Rice", "Vegetables", "Egg", "Soy Sauce", "Oil"],
    dish: "Fried Rice",
    hint: "Asian stir-fried dish",
  },
  {
    ingredients: ["Potatoes", "Oil", "Salt"],
    dish: "French Fries",
    hint: "Popular side dish, crispy and golden",
  },
  {
    ingredients: ["Bread", "Cheese", "Butter"],
    dish: "Grilled Cheese Sandwich",
    hint: "Simple hot sandwich",
  },
];

// Trivia questions
const TRIVIA_QUESTIONS: TriviaQuestion[] = [
  {
    id: "q1",
    question: "Which cuisine is known for dishes like Paella and Gazpacho?",
    options: ["Italian", "Spanish", "Greek", "French"],
    correctAnswer: 1,
    difficulty: "easy",
    category: "cuisines",
    points: 10,
  },
  {
    id: "q2",
    question: "What is the main ingredient in traditional guacamole?",
    options: ["Tomato", "Avocado", "Onion", "Lime"],
    correctAnswer: 1,
    difficulty: "easy",
    category: "ingredients",
    points: 10,
  },
  {
    id: "q3",
    question: "Which cooking method involves submerging food in hot oil?",
    options: ["Braising", "Poaching", "Frying", "Steaming"],
    correctAnswer: 2,
    difficulty: "easy",
    category: "cooking",
    points: 10,
  },
  {
    id: "q4",
    question: "What is the national dish of Japan?",
    options: ["Sushi", "Ramen", "Tempura", "Curry Rice"],
    correctAnswer: 0,
    difficulty: "medium",
    category: "cuisines",
    points: 15,
  },
  {
    id: "q5",
    question: "Which spice is known as 'red gold' and is used in paella?",
    options: ["Paprika", "Turmeric", "Saffron", "Cayenne"],
    correctAnswer: 2,
    difficulty: "medium",
    category: "ingredients",
    points: 15,
  },
  {
    id: "q6",
    question: "What is the process of using salt to preserve food called?",
    options: ["Pickling", "Curing", "Fermenting", "Smoking"],
    correctAnswer: 1,
    difficulty: "medium",
    category: "cooking",
    points: 15,
  },
  {
    id: "q7",
    question: "Which country is known for inventing the croissant?",
    options: ["France", "Austria", "Italy", "Switzerland"],
    correctAnswer: 1,
    difficulty: "hard",
    category: "cuisines",
    points: 20,
  },
  {
    id: "q8",
    question: "What is the main ingredient in the Italian dessert Tiramisu?",
    options: ["Ladyfingers", "Ricotta cheese", "Puff pastry", "Almond flour"],
    correctAnswer: 0,
    difficulty: "medium",
    category: "ingredients",
    points: 15,
  },
  {
    id: "q9",
    question:
      "Which cooking technique involves cooking food slowly in a covered pot?",
    options: ["Sautéing", "Braising", "Blanching", "Broiling"],
    correctAnswer: 1,
    difficulty: "medium",
    category: "cooking",
    points: 15,
  },
  {
    id: "q10",
    question: "What is the main ingredient in traditional hummus?",
    options: ["Lentils", "Black beans", "Chickpeas", "Fava beans"],
    correctAnswer: 2,
    difficulty: "easy",
    category: "ingredients",
    points: 10,
  },
  {
    id: "q11",
    question: "Which country is credited with inventing pizza?",
    options: ["Greece", "Italy", "France", "Spain"],
    correctAnswer: 1,
    difficulty: "easy",
    category: "cuisines",
    points: 10,
  },
  {
    id: "q12",
    question:
      "What is the process of cooking food directly under high heat called?",
    options: ["Broiling", "Braising", "Poaching", "Simmering"],
    correctAnswer: 0,
    difficulty: "medium",
    category: "cooking",
    points: 15,
  },
  {
    id: "q13",
    question: "Which cuisine features dishes like Pad Thai and Tom Yum soup?",
    options: ["Vietnamese", "Chinese", "Thai", "Malaysian"],
    correctAnswer: 2,
    difficulty: "easy",
    category: "cuisines",
    points: 10,
  },
  {
    id: "q14",
    question: "What is the main ingredient in traditional pesto sauce?",
    options: ["Spinach", "Basil", "Arugula", "Parsley"],
    correctAnswer: 1,
    difficulty: "easy",
    category: "ingredients",
    points: 10,
  },
  {
    id: "q15",
    question:
      "Which cooking method involves cooking food in hot water that is not boiling?",
    options: ["Blanching", "Poaching", "Steaming", "Simmering"],
    correctAnswer: 1,
    difficulty: "medium",
    category: "cooking",
    points: 15,
  },
];

/**
 * Service for managing mini-games
 */
class GameService {
  /**
   * Get all available mini-games
   * @returns Array of mini-games
   */
  getGames(): MiniGame[] {
    return MINI_GAMES.filter((game) => game.isActive);
  }

  /**
   * Get a specific mini-game by ID
   * @param gameId Game ID
   * @returns Mini-game or null if not found
   */
  getGame(gameId: string): MiniGame | null {
    return MINI_GAMES.find((game) => game.id === gameId) || null;
  }

  /**
   * Get food match cards for the game
   * @param count Number of pairs to include (default: 6)
   * @returns Array of food match cards
   */
  getFoodMatchCards(count: number = 6): FoodMatchCard[] {
    // Shuffle the cards and take the requested number
    const shuffled = [...FOOD_MATCH_CARDS].sort(() => 0.5 - Math.random());
    const selected = shuffled.slice(0, count);

    // Create pairs by duplicating the cards
    const pairs = [...selected, ...selected].map((card, index) => ({
      ...card,
      id: `${card.id}-${index}`,
      pairId: card.id,
    }));

    // Shuffle the pairs
    return pairs.sort(() => 0.5 - Math.random());
  }

  /**
   * Get trivia questions for the game
   * @param count Number of questions to include (default: 5)
   * @param difficulty Optional difficulty filter
   * @param category Optional category filter
   * @returns Array of trivia questions
   */
  getTriviaQuestions(
    count: number = 5,
    difficulty?: "easy" | "medium" | "hard",
    category?:
      | "cuisines"
      | "ingredients"
      | "cooking"
      | "restaurants"
      | "nutrition"
  ): TriviaQuestion[] {
    let questions = [...TRIVIA_QUESTIONS];

    // Apply filters if provided
    if (difficulty) {
      questions = questions.filter((q) => q.difficulty === difficulty);
    }

    if (category) {
      questions = questions.filter((q) => q.category === category);
    }

    // Shuffle and take the requested number
    const shuffled = questions.sort(() => 0.5 - Math.random());
    return shuffled.slice(0, Math.min(count, shuffled.length));
  }

  /**
   * Get word scramble words for the game
   * @param count Number of words to include (default: 5)
   * @returns Array of word scramble words
   */
  getWordScrambleWords(
    count: number = 5
  ): { word: string; hint: string; scrambled: string }[] {
    // Shuffle the words and take the requested number
    const shuffled = [...WORD_SCRAMBLE_WORDS].sort(() => 0.5 - Math.random());
    const selected = shuffled.slice(0, Math.min(count, shuffled.length));

    // Scramble the words
    return selected.map((item) => {
      const word = item.word;
      // Convert to array, shuffle, and join back to string
      const scrambled = word
        .split("")
        .sort(() => 0.5 - Math.random())
        .join("");

      return {
        ...item,
        scrambled,
      };
    });
  }

  /**
   * Get recipe challenge data for the game
   * @param count Number of recipes to include (default: 3)
   * @returns Array of recipe challenges
   */
  getRecipeChallenges(count: number = 3) {
    // Shuffle the recipes and take the requested number
    const shuffled = [...RECIPE_CHALLENGE_DATA].sort(() => 0.5 - Math.random());
    return shuffled.slice(0, Math.min(count, shuffled.length));
  }

  /**
   * Submit food match game result
   * @param userId User ID
   * @param score Score (number of pairs matched)
   * @param timeSeconds Time taken in seconds
   * @returns Game result with points earned
   */
  async submitFoodMatchResult(
    userId: string,
    score: number,
    timeSeconds: number
  ): Promise<GameResult> {
    try {
      const game = this.getGame("foodMatch");
      if (!game) {
        return {
          success: false,
          pointsEarned: 0,
          message: "Game not found",
        };
      }

      // Check if user has reached daily play limit for this game
      const stats = await this.getUserGameStats(userId);
      if (stats.playCount && stats.playCount[game.id] >= game.maxDailyPlays) {
        return {
          success: false,
          pointsEarned: 0,
          message:
            "You've reached the daily play limit for this game. Come back tomorrow!",
        };
      }

      // Calculate points based on score and time
      // Only award points if all pairs were matched
      const totalPairs = score;
      const isCompleted = totalPairs >= 8; // Minimum 8 pairs to complete

      if (!isCompleted) {
        return {
          success: true,
          pointsEarned: 0,
          message: "Game not completed. Match all pairs to earn points!",
        };
      }

      // Award points
      const pointsToAward = game.pointsPerWin;

      // Record the result in Firestore
      const gamePlayRef = collection(firestore, "clients", userId, "gamePlays");
      await addDoc(gamePlayRef, {
        userId,
        gameId: game.id,
        score,
        timeSeconds,
        completed: isCompleted,
        createdAt: serverTimestamp(),
      });

      // Award loyalty points
      const result = await loyaltyService.awardGamePoints(
        userId,
        game.id,
        pointsToAward,
        { score, details: `Matched ${score} pairs in ${timeSeconds} seconds` }
      );

      if (result.success) {
        return {
          success: true,
          pointsEarned: result.pointsAwarded,
          message: result.dailyLimitReached
            ? "Daily limit reached, but you still earned some points!"
            : `Great job! You earned ${result.pointsAwarded} points!`,
          dailyLimit: result.dailyLimitReached,
        };
      } else {
        return {
          success: false,
          pointsEarned: 0,
          message: "Failed to award points. Please try again.",
        };
      }
    } catch (error) {
      console.error("Error submitting food match result:", error);
      return {
        success: false,
        pointsEarned: 0,
        message: "An error occurred. Please try again.",
      };
    }
  }

  /**
   * Submit trivia game result
   * @param userId User ID
   * @param correctAnswers Number of correct answers
   * @param totalQuestions Total number of questions
   * @returns Game result with points earned
   */
  async submitTriviaResult(
    userId: string,
    correctAnswers: number,
    totalQuestions: number
  ): Promise<GameResult> {
    try {
      const game = this.getGame("culinaryTrivia");
      if (!game) {
        return {
          success: false,
          pointsEarned: 0,
          message: "Game not found",
        };
      }

      // Check if user has reached daily play limit for this game
      const stats = await this.getUserGameStats(userId);
      if (stats.playCount && stats.playCount[game.id] >= game.maxDailyPlays) {
        return {
          success: false,
          pointsEarned: 0,
          message:
            "You've reached the daily play limit for this game. Come back tomorrow!",
        };
      }

      // Calculate points based on correct answers
      const pointsPerQuestion = game.pointsPerWin;
      const pointsToAward = correctAnswers * pointsPerQuestion;

      if (pointsToAward <= 0) {
        return {
          success: true,
          pointsEarned: 0,
          message: "Try again! Answer questions correctly to earn points.",
        };
      }

      // Record the result in Firestore
      const gamePlayRef = collection(firestore, "clients", userId, "gamePlays");
      await addDoc(gamePlayRef, {
        userId,
        gameId: game.id,
        score: correctAnswers,
        totalQuestions,
        completed: true,
        createdAt: serverTimestamp(),
      });

      // Award loyalty points
      const result = await loyaltyService.awardGamePoints(
        userId,
        game.id,
        pointsToAward,
        {
          score: correctAnswers,
          details: `Answered ${correctAnswers}/${totalQuestions} questions correctly`,
        }
      );

      if (result.success) {
        return {
          success: true,
          pointsEarned: result.pointsAwarded,
          message: result.dailyLimitReached
            ? "Daily limit reached, but you still earned some points!"
            : `Great job! You earned ${result.pointsAwarded} points!`,
          dailyLimit: result.dailyLimitReached,
        };
      } else {
        return {
          success: false,
          pointsEarned: 0,
          message: "Failed to award points. Please try again.",
        };
      }
    } catch (error) {
      console.error("Error submitting trivia result:", error);
      return {
        success: false,
        pointsEarned: 0,
        message: "An error occurred. Please try again.",
      };
    }
  }

  /**
   * Record daily check-in
   * @param userId User ID
   * @returns Check-in result with points earned and streak
   */
  async recordDailyCheckIn(userId: string): Promise<GameResult> {
    try {
      // Use the loyalty service to record the check-in
      const result = await loyaltyService.recordDailyCheckIn(userId);

      if (result.success) {
        return {
          success: true,
          pointsEarned: result.pointsAwarded,
          message:
            result.pointsAwarded > 0
              ? `Check-in successful! You earned ${result.pointsAwarded} points with a ${result.streak}-day streak!`
              : result.dailyLimitReached
              ? "Daily limit reached, but your streak continues!"
              : "You've already checked in today!",
          streakCount: result.streak,
          dailyLimit: result.dailyLimitReached,
        };
      } else {
        return {
          success: false,
          pointsEarned: 0,
          message: "Failed to record check-in. Please try again.",
        };
      }
    } catch (error) {
      console.error("Error recording daily check-in:", error);
      return {
        success: false,
        pointsEarned: 0,
        message: "An error occurred. Please try again.",
      };
    }
  }

  /**
   * Submit word scramble game result
   * @param userId User ID
   * @param wordsCompleted Number of words correctly unscrambled
   * @param totalWords Total number of words
   * @param timeSeconds Time taken in seconds
   * @returns Game result with points earned
   */
  async submitWordScrambleResult(
    userId: string,
    wordsCompleted: number,
    totalWords: number,
    timeSeconds: number
  ): Promise<GameResult> {
    try {
      const game = this.getGame("wordScramble");
      if (!game) {
        return {
          success: false,
          pointsEarned: 0,
          message: "Game not found",
        };
      }

      // Check if user has reached daily play limit for this game
      const stats = await this.getUserGameStats(userId);
      if (stats.playCount && stats.playCount[game.id] >= game.maxDailyPlays) {
        return {
          success: false,
          pointsEarned: 0,
          message:
            "You've reached the daily play limit for this game. Come back tomorrow!",
        };
      }

      // Calculate points based on words completed and time
      const pointsPerWord = game.pointsPerWin;
      const pointsToAward = wordsCompleted * pointsPerWord;

      if (pointsToAward <= 0) {
        return {
          success: true,
          pointsEarned: 0,
          message: "Try again! Unscramble words correctly to earn points.",
        };
      }

      // Record the result in Firestore
      const gamePlayRef = collection(firestore, "clients", userId, "gamePlays");
      await addDoc(gamePlayRef, {
        userId,
        gameId: game.id,
        score: wordsCompleted,
        totalWords,
        timeSeconds,
        completed: wordsCompleted > 0,
        createdAt: serverTimestamp(),
      });

      // Award loyalty points
      const result = await loyaltyService.awardGamePoints(
        userId,
        game.id,
        pointsToAward,
        {
          score: wordsCompleted,
          details: `Unscrambled ${wordsCompleted}/${totalWords} words in ${timeSeconds} seconds`,
        }
      );

      if (result.success) {
        return {
          success: true,
          pointsEarned: result.pointsAwarded,
          message: result.dailyLimitReached
            ? "Daily limit reached, but you still earned some points!"
            : `Great job! You earned ${result.pointsAwarded} points!`,
          dailyLimit: result.dailyLimitReached,
        };
      } else {
        return {
          success: false,
          pointsEarned: 0,
          message: "Failed to award points. Please try again.",
        };
      }
    } catch (error) {
      console.error("Error submitting word scramble result:", error);
      return {
        success: false,
        pointsEarned: 0,
        message: "An error occurred. Please try again.",
      };
    }
  }

  /**
   * Submit recipe challenge game result
   * @param userId User ID
   * @param correctGuesses Number of recipes correctly guessed
   * @param totalRecipes Total number of recipes
   * @returns Game result with points earned
   */
  async submitRecipeChallengeResult(
    userId: string,
    correctGuesses: number,
    totalRecipes: number
  ): Promise<GameResult> {
    try {
      const game = this.getGame("recipeChallenge");
      if (!game) {
        return {
          success: false,
          pointsEarned: 0,
          message: "Game not found",
        };
      }

      // Check if user has reached daily play limit for this game
      const stats = await this.getUserGameStats(userId);
      if (stats.playCount && stats.playCount[game.id] >= game.maxDailyPlays) {
        return {
          success: false,
          pointsEarned: 0,
          message:
            "You've reached the daily play limit for this game. Come back tomorrow!",
        };
      }

      // Calculate points based on correct guesses
      const pointsToAward = correctGuesses * game.pointsPerWin;

      if (pointsToAward <= 0) {
        return {
          success: true,
          pointsEarned: 0,
          message: "Try again! Guess recipes correctly to earn points.",
        };
      }

      // Record the result in Firestore
      const gamePlayRef = collection(firestore, "clients", userId, "gamePlays");
      await addDoc(gamePlayRef, {
        userId,
        gameId: game.id,
        score: correctGuesses,
        totalRecipes,
        completed: correctGuesses > 0,
        createdAt: serverTimestamp(),
      });

      // Award loyalty points
      const result = await loyaltyService.awardGamePoints(
        userId,
        game.id,
        pointsToAward,
        {
          score: correctGuesses,
          details: `Guessed ${correctGuesses}/${totalRecipes} recipes correctly`,
        }
      );

      if (result.success) {
        return {
          success: true,
          pointsEarned: result.pointsAwarded,
          message: result.dailyLimitReached
            ? "Daily limit reached, but you still earned some points!"
            : `Great job! You earned ${result.pointsAwarded} points!`,
          dailyLimit: result.dailyLimitReached,
        };
      } else {
        return {
          success: false,
          pointsEarned: 0,
          message: "Failed to award points. Please try again.",
        };
      }
    } catch (error) {
      console.error("Error submitting recipe challenge result:", error);
      return {
        success: false,
        pointsEarned: 0,
        message: "An error occurred. Please try again.",
      };
    }
  }

  /**
   * Get user's game stats
   * @param userId User ID
   * @returns Game stats
   */
  async getUserGameStats(userId: string): Promise<{
    dailyPointsEarned: number;
    dailyPointsLimit: number;
    checkInStreak: number;
    lastCheckIn: Date | null;
    lastPlayed: { [gameId: string]: Date } | null;
    playCount?: { [gameId: string]: number };
  }> {
    try {
      const stats = await loyaltyService.getGameStats(userId);

      if (!stats) {
        return {
          dailyPointsEarned: 0,
          dailyPointsLimit: loyaltyService.pointsConfig.dailyGameLimit,
          checkInStreak: 0,
          lastCheckIn: null,
          lastPlayed: null,
          playCount: {},
        };
      }

      return stats;
    } catch (error) {
      console.error("Error getting user game stats:", error);
      return {
        dailyPointsEarned: 0,
        dailyPointsLimit: loyaltyService.pointsConfig.dailyGameLimit,
        checkInStreak: 0,
        lastCheckIn: null,
        lastPlayed: null,
        playCount: {},
      };
    }
  }
}

// Export singleton instance
export const gameService = new GameService();
