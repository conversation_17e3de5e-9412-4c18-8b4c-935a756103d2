import { Card, CardContent } from "@/components/ui/card";
import { format } from "date-fns";
import { Table, Reservation } from "@/types/dashboard";

interface TableCardProps {
  table: Table;
  reservations: Reservation[];
  getTableStatusColor: (tableId: string) => string;
  onSelect: (table: Table) => void;
}

export const TableCard = ({
  table,
  reservations,
  getTableStatusColor,
  onSelect,
}: TableCardProps) => {
  return (
    <Card
      className={`${getTableStatusColor(
        table.id
      )} cursor-pointer hover:shadow-lg transition-shadow`}
      onClick={() => onSelect(table)}
    >
      <CardContent className="pt-6">
        <p className="font-medium">{table.name}</p>
        <p className="text-sm text-muted-foreground">
          Capacity: {table.capacity} people
        </p>
        <div className="mt-2 text-sm">
          {reservations
            .filter((r) => r.tableId === table.id && r.status !== "cancelled")
            .sort(
              (a, b) =>
                new Date(`${a.date}T${a.arrivalTime}`).getTime() -
                new Date(`${b.date}T${b.arrivalTime}`).getTime()
            )
            .slice(0, 2)
            .map((res, idx) => (
              <div key={idx} className="text-xs mt-1">
                {format(
                  new Date(`${res.date}T${res.arrivalTime}`),
                  "dd MMM HH:mm"
                )}{" "}
                - {res.status}
              </div>
            ))}
        </div>
      </CardContent>
    </Card>
  );
};
