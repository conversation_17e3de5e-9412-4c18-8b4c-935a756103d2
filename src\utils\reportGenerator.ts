import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';
import html2canvas from 'html2canvas';
import { format } from 'date-fns';

// Types for report data
export interface ReportOptions {
  reportType: 'summary' | 'detailed' | 'custom';
  dateRange: 'week' | 'month' | 'year' | 'custom';
  customStartDate?: Date;
  customEndDate?: Date;
  includeCharts?: boolean;
  includeReviews?: boolean;
  includeFinancials?: boolean;
  includeCustomerMetrics?: boolean;
  restaurantName: string;
  restaurantId: string;
}

export interface ReviewData {
  id: string;
  userName: string;
  rating: number;
  comment: string;
  createdAt: Date | { toDate: () => Date } | { seconds: number; nanoseconds: number } | string | number;
}

export interface OrderData {
  date: string;
  count: number;
  revenue: number;
}

export interface MenuItemData {
  name: string;
  count: number;
}

export interface CustomerMetrics {
  newCustomers: number;
  returningCustomers: number;
  averageOrderValue: number;
  customerRetentionRate: number;
  orderFrequency: number;
}

export interface ReportData {
  orders: OrderData[];
  menuItems: MenuItemData[];
  reviews: ReviewData[];
  customerMetrics?: CustomerMetrics;
  totalOrders: number;
  totalRevenue: number;
  averageRating: number;
}

// Function to generate a business review report
export const generateBusinessReport = async (
  reportData: ReportData,
  options: ReportOptions,
  chartRefs?: {
    orderChartRef?: React.RefObject<HTMLDivElement>;
    revenueChartRef?: React.RefObject<HTMLDivElement>;
    menuChartRef?: React.RefObject<HTMLDivElement>;
    reviewChartRef?: React.RefObject<HTMLDivElement>;
  }
): Promise<jsPDF> => {
  // Create a new PDF document
  const doc = new jsPDF({
    orientation: 'portrait',
    unit: 'mm',
    format: 'a4',
  });

  const pageWidth = doc.internal.pageSize.getWidth();
  const pageHeight = doc.internal.pageSize.getHeight();
  const margin = 15;
  const contentWidth = pageWidth - 2 * margin;

  // Add report title
  doc.setFontSize(20);
  doc.setFont('helvetica', 'bold');
  doc.text(`${options.restaurantName} - Business Review`, pageWidth / 2, margin, { align: 'center' });

  // Add report subtitle with date range
  doc.setFontSize(12);
  doc.setFont('helvetica', 'normal');
  let dateRangeText = '';

  switch (options.dateRange) {
    case 'week':
      dateRangeText = 'Past Week';
      break;
    case 'month':
      dateRangeText = 'Past Month';
      break;
    case 'year':
      dateRangeText = 'Past Year';
      break;
    case 'custom':
      if (options.customStartDate && options.customEndDate) {
        dateRangeText = `${format(options.customStartDate, 'MMM dd, yyyy')} - ${format(options.customEndDate, 'MMM dd, yyyy')}`;
      } else {
        dateRangeText = 'Custom Date Range';
      }
      break;
  }

  doc.text(`Report Type: ${options.reportType.charAt(0).toUpperCase() + options.reportType.slice(1)}`, pageWidth / 2, margin + 8, { align: 'center' });
  doc.text(`Date Range: ${dateRangeText}`, pageWidth / 2, margin + 14, { align: 'center' });
  doc.text(`Generated on: ${format(new Date(), 'MMM dd, yyyy')}`, pageWidth / 2, margin + 20, { align: 'center' });

  // Add horizontal line
  doc.setDrawColor(200, 200, 200);
  doc.line(margin, margin + 25, pageWidth - margin, margin + 25);

  let yPosition = margin + 30;

  // Add executive summary
  doc.setFontSize(16);
  doc.setFont('helvetica', 'bold');
  doc.text('Executive Summary', margin, yPosition);
  yPosition += 8;

  doc.setFontSize(11);
  doc.setFont('helvetica', 'normal');

  // Summary metrics
  const summaryData = [
    ['Total Orders', reportData.totalOrders.toString()],
    ['Total Revenue', `${reportData.totalRevenue.toFixed(2)} AZN`],
    ['Average Order Value', `${(reportData.totalRevenue / reportData.totalOrders).toFixed(2)} AZN`],
    ['Average Rating', `${reportData.averageRating.toFixed(1)} / 5.0`],
  ];

  if (reportData.customerMetrics) {
    summaryData.push(
      ['Customer Retention Rate', `${reportData.customerMetrics.customerRetentionRate.toFixed(1)}%`],
      ['New Customers', reportData.customerMetrics.newCustomers.toString()],
      ['Returning Customers', reportData.customerMetrics.returningCustomers.toString()],
      ['Order Frequency', reportData.customerMetrics.orderFrequency.toFixed(1)]
    );
  }

  autoTable(doc, {
    startY: yPosition,
    head: [['Metric', 'Value']],
    body: summaryData,
    theme: 'grid',
    headStyles: {
      fillColor: [66, 66, 66],
      textColor: 255,
      fontStyle: 'bold',
    },
    styles: {
      fontSize: 10,
      cellPadding: 3,
    },
  });

  // @ts-expect-error - jsPDF-AutoTable adds this property
  yPosition = doc.lastAutoTable?.finalY || yPosition + 50;
  yPosition += 10;

  // Add charts if requested
  if (options.includeCharts && chartRefs) {
    // Add order trend chart
    if (chartRefs.orderChartRef?.current) {
      doc.setFontSize(16);
      doc.setFont('helvetica', 'bold');
      doc.text('Order Trends', margin, yPosition);
      yPosition += 8;

      try {
        const canvas = await html2canvas(chartRefs.orderChartRef.current);
        const imgData = canvas.toDataURL('image/png');
        const imgWidth = contentWidth;
        const imgHeight = (canvas.height * imgWidth) / canvas.width;

        doc.addImage(imgData, 'PNG', margin, yPosition, imgWidth, imgHeight);
        yPosition += imgHeight + 10;

        // Add page break if needed
        if (yPosition > pageHeight - margin) {
          doc.addPage();
          yPosition = margin;
        }
      } catch (error) {
        console.error('Error capturing order chart:', error);
      }
    }

    // Add revenue chart
    if (chartRefs.revenueChartRef?.current) {
      doc.setFontSize(16);
      doc.setFont('helvetica', 'bold');
      doc.text('Revenue Analysis', margin, yPosition);
      yPosition += 8;

      try {
        const canvas = await html2canvas(chartRefs.revenueChartRef.current);
        const imgData = canvas.toDataURL('image/png');
        const imgWidth = contentWidth;
        const imgHeight = (canvas.height * imgWidth) / canvas.width;

        doc.addImage(imgData, 'PNG', margin, yPosition, imgWidth, imgHeight);
        yPosition += imgHeight + 10;

        // Add page break if needed
        if (yPosition > pageHeight - margin) {
          doc.addPage();
          yPosition = margin;
        }
      } catch (error) {
        console.error('Error capturing revenue chart:', error);
      }
    }

    // Add menu items chart
    if (chartRefs.menuChartRef?.current) {
      doc.setFontSize(16);
      doc.setFont('helvetica', 'bold');
      doc.text('Popular Menu Items', margin, yPosition);
      yPosition += 8;

      try {
        const canvas = await html2canvas(chartRefs.menuChartRef.current);
        const imgData = canvas.toDataURL('image/png');
        const imgWidth = contentWidth;
        const imgHeight = (canvas.height * imgWidth) / canvas.width;

        doc.addImage(imgData, 'PNG', margin, yPosition, imgWidth, imgHeight);
        yPosition += imgHeight + 10;

        // Add page break if needed
        if (yPosition > pageHeight - margin) {
          doc.addPage();
          yPosition = margin;
        }
      } catch (error) {
        console.error('Error capturing menu chart:', error);
      }
    }
  }

  // Add detailed order data for detailed reports
  if (options.reportType === 'detailed' && reportData.orders.length > 0) {
    doc.addPage();
    yPosition = margin;

    doc.setFontSize(16);
    doc.setFont('helvetica', 'bold');
    doc.text('Detailed Order Data', margin, yPosition);
    yPosition += 8;

    autoTable(doc, {
      startY: yPosition,
      head: [['Date', 'Orders', 'Revenue (AZN)']],
      body: reportData.orders.map(order => [
        order.date,
        order.count.toString(),
        order.revenue.toFixed(2)
      ]),
      theme: 'grid',
      headStyles: {
        fillColor: [66, 66, 66],
        textColor: 255,
        fontStyle: 'bold',
      },
      styles: {
        fontSize: 10,
        cellPadding: 3,
      },
    });

    // @ts-expect-error - jsPDF-AutoTable adds this property
    yPosition = doc.lastAutoTable?.finalY || yPosition + 50;
    yPosition += 10;
  }

  // Add popular menu items for detailed reports
  if (options.reportType === 'detailed' && reportData.menuItems.length > 0) {
    // Add page break if needed
    if (yPosition > pageHeight - margin - 50) {
      doc.addPage();
      yPosition = margin;
    }

    doc.setFontSize(16);
    doc.setFont('helvetica', 'bold');
    doc.text('Popular Menu Items', margin, yPosition);
    yPosition += 8;

    autoTable(doc, {
      startY: yPosition,
      head: [['Item Name', 'Orders']],
      body: reportData.menuItems.map(item => [
        item.name,
        item.count.toString()
      ]),
      theme: 'grid',
      headStyles: {
        fillColor: [66, 66, 66],
        textColor: 255,
        fontStyle: 'bold',
      },
      styles: {
        fontSize: 10,
        cellPadding: 3,
      },
    });

    // @ts-expect-error - jsPDF-AutoTable adds this property
    yPosition = doc.lastAutoTable?.finalY || yPosition + 50;
    yPosition += 10;
  }

  // Add reviews if requested
  if (options.includeReviews && reportData.reviews.length > 0) {
    doc.addPage();
    yPosition = margin;

    doc.setFontSize(16);
    doc.setFont('helvetica', 'bold');
    doc.text('Customer Reviews', margin, yPosition);
    yPosition += 8;

    // Format reviews for the table
    const reviewsForTable = reportData.reviews.map(review => {
      // Format date
      let dateStr = 'Unknown';
      try {
        let reviewDate: Date;

        if (review.createdAt instanceof Date) {
          reviewDate = review.createdAt;
        } else if (typeof review.createdAt === 'object' && review.createdAt !== null) {
          // Check for Firestore Timestamp with toDate method
          const timestampWithMethod = review.createdAt as { toDate?: () => Date };
          if (typeof timestampWithMethod.toDate === 'function') {
            reviewDate = timestampWithMethod.toDate();
          } else {
            // Check for Firestore Timestamp-like object
            const timestamp = review.createdAt as { seconds?: number; nanoseconds?: number };
            if (timestamp.seconds && timestamp.nanoseconds) {
              // Handle Firestore timestamp format
              reviewDate = new Date(timestamp.seconds * 1000);
            } else {
              // Try to convert other objects to date
              reviewDate = new Date(review.createdAt as unknown as string | number);
            }
          }
        } else if (typeof review.createdAt === 'string') {
          reviewDate = new Date(review.createdAt);
        } else if (typeof review.createdAt === 'number') {
          reviewDate = new Date(review.createdAt);
        } else {
          reviewDate = new Date(); // Fallback to current date
        }

        dateStr = format(reviewDate, 'MMM dd, yyyy');
      } catch (error) {
        console.error('Error formatting review date:', error);
      }

      // Truncate long comments
      const maxCommentLength = 80;
      const comment = review.comment.length > maxCommentLength
        ? review.comment.substring(0, maxCommentLength) + '...'
        : review.comment;

      return [
        dateStr,
        review.userName,
        `${review.rating.toFixed(1)} / 5.0`,
        comment
      ];
    });

    autoTable(doc, {
      startY: yPosition,
      head: [['Date', 'Customer', 'Rating', 'Comment']],
      body: reviewsForTable,
      theme: 'grid',
      headStyles: {
        fillColor: [66, 66, 66],
        textColor: 255,
        fontStyle: 'bold',
      },
      styles: {
        fontSize: 9,
        cellPadding: 3,
      },
      columnStyles: {
        0: { cellWidth: 25 },
        1: { cellWidth: 30 },
        2: { cellWidth: 20 },
        3: { cellWidth: 'auto' },
      },
    });
  }

  // Add footer with page numbers
  const totalPages = doc.getNumberOfPages();
  for (let i = 1; i <= totalPages; i++) {
    doc.setPage(i);
    doc.setFontSize(10);
    doc.setTextColor(100);
    doc.text(
      `Page ${i} of ${totalPages}`,
      pageWidth / 2,
      pageHeight - 10,
      { align: 'center' }
    );
    doc.text(
      `${options.restaurantName} - Business Review Report`,
      pageWidth / 2,
      pageHeight - 5,
      { align: 'center' }
    );
  }

  return doc;
};

// Function to save the report as PDF
export const saveBusinessReport = async (
  reportData: ReportData,
  options: ReportOptions,
  chartRefs?: {
    orderChartRef?: React.RefObject<HTMLDivElement>;
    revenueChartRef?: React.RefObject<HTMLDivElement>;
    menuChartRef?: React.RefObject<HTMLDivElement>;
    reviewChartRef?: React.RefObject<HTMLDivElement>;
  }
): Promise<void> => {
  try {
    const doc = await generateBusinessReport(reportData, options, chartRefs);

    // Generate filename based on restaurant name and date
    const dateStr = format(new Date(), 'yyyy-MM-dd');
    const filename = `${options.restaurantName.replace(/\s+/g, '_')}_Business_Review_${dateStr}.pdf`;

    // Save the PDF
    doc.save(filename);
  } catch (error) {
    console.error('Error generating business report:', error);
    throw error;
  }
};
