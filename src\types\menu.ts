export interface MenuItem {
  itemId: string;
  restaurantId: string;
  name: string;
  description: string;
  price: number;
  category: string;
  imageUrl?: string;
  available: boolean;
  dietary: string[];
  spicyLevel?: "none" | "mild" | "medium" | "hot" | "extra hot";
  allergens?: string[];
  ingredients?: string[];

  // Basic nutrition information
  calories?: number;
  servingSize?: string;
  servingsPerItem?: number;

  // Macronutrients
  protein?: number; // in grams
  carbs?: number; // in grams
  fat?: number; // in grams

  // Detailed nutrition information
  fiber?: number; // in grams
  sugar?: number; // in grams
  sodium?: number; // in milligrams
  cholesterol?: number; // in milligrams

  // Additional nutrition information
  vitamins?: {
    vitaminA?: number;
    vitaminC?: number;
    vitaminD?: number;
    calcium?: number;
    iron?: number;
  };

  // Health labels
  healthLabels?: string[]; // e.g., "Low Fat", "High Protein", "Low Sodium"

  preparationTime?: string;
  isSignatureDish?: boolean;
  isSeasonalDish?: boolean;

  // Promotion fields
  isSpecialOffer?: boolean;
  isChefRecommendation?: boolean;
  isPopular?: boolean;
  isLimitedTimeOffer?: boolean;
  promotionEndDate?: Date;
  promotionDescription?: string;
  discountPercentage?: number;
}
