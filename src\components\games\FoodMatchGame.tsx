import React, { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { gameService } from "@/services/GameService";
import { FoodMatchCard } from "@/types/games";
import { ArrowLeft, Clock, Trophy } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Progress } from "@/components/ui/progress";
import { motion } from "framer-motion";

// Add CSS for card flipping
const cardStyles = `
  .backface-hidden {
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
    position: absolute;
    width: 100%;
    height: 100%;
  }

  .card-container {
    perspective: 1000px;
    transform-style: preserve-3d;
    position: relative;
    width: 100%;
    height: 100%;
  }

  .card-front {
    transform: rotateY(0deg);
    z-index: 2;
  }

  .card-back {
    transform: rotateY(180deg);
  }
`;

interface FoodMatchGameProps {
  onBack: () => void;
  onComplete: () => void;
  userId: string;
}

export const FoodMatchGame: React.FC<FoodMatchGameProps> = ({
  onBack,
  onComplete,
  userId,
}) => {
  const [cards, setCards] = useState<
    (FoodMatchCard & {
      flipped: boolean;
      matched: boolean;
      pairId: string;
    })[]
  >([]);
  const [flippedCards, setFlippedCards] = useState<number[]>([]);
  const [matchedPairs, setMatchedPairs] = useState<string[]>([]);
  const [gameStarted, setGameStarted] = useState(false);
  const [gameCompleted, setGameCompleted] = useState(false);
  const [timer, setTimer] = useState(0);
  const [timerRunning, setTimerRunning] = useState(false);
  const [result, setResult] = useState<{
    success: boolean;
    pointsEarned: number;
    message: string;
  } | null>(null);
  const [, setLoading] = useState(false);

  // Initialize game
  useEffect(() => {
    const foodCards = gameService.getFoodMatchCards(8);
    const initialCards = foodCards.map((card) => ({
      ...card,
      flipped: false,
      matched: false,
      pairId: card.pairId || "",
    }));
    setCards(initialCards);
  }, []);

  // Timer effect
  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (timerRunning) {
      interval = setInterval(() => {
        setTimer((prevTimer) => prevTimer + 1);
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [timerRunning]);

  // Start game
  const handleStartGame = () => {
    setGameStarted(true);
    setTimerRunning(true);
  };

  // Handle card click
  const handleCardClick = (index: number) => {
    // Prevent clicking if already two cards are flipped or this card is already flipped/matched
    if (
      flippedCards.length === 2 ||
      cards[index].flipped ||
      cards[index].matched
    ) {
      return;
    }

    // Flip the card
    const updatedCards = [...cards];
    updatedCards[index].flipped = true;
    setCards(updatedCards);

    // Add to flipped cards
    const newFlippedCards = [...flippedCards, index];
    setFlippedCards(newFlippedCards);

    // Check for match if two cards are flipped
    if (newFlippedCards.length === 2) {
      const firstCardIndex = newFlippedCards[0];
      const secondCardIndex = newFlippedCards[1];
      const firstCard = cards[firstCardIndex];
      const secondCard = cards[secondCardIndex];

      if (firstCard.pairId === secondCard.pairId) {
        // Match found
        setTimeout(() => {
          const updatedCards = [...cards];
          updatedCards[firstCardIndex].matched = true;
          updatedCards[secondCardIndex].matched = true;
          setCards(updatedCards);
          setFlippedCards([]);
          setMatchedPairs([...matchedPairs, firstCard.pairId]);

          // Check if game is completed
          if (matchedPairs.length + 1 === cards.length / 2) {
            handleGameComplete();
          }
        }, 500);
      } else {
        // No match, flip back after delay
        setTimeout(() => {
          const updatedCards = [...cards];
          updatedCards[firstCardIndex].flipped = false;
          updatedCards[secondCardIndex].flipped = false;
          setCards(updatedCards);
          setFlippedCards([]);
        }, 1000);
      }
    }
  };

  // Handle game completion
  const handleGameComplete = async () => {
    setTimerRunning(false);
    setGameCompleted(true);
    setLoading(true);

    try {
      const result = await gameService.submitFoodMatchResult(
        userId,
        cards.length / 2, // Number of pairs
        timer
      );
      setResult(result);
      if (result.success && result.pointsEarned > 0) {
        onComplete();
      }
    } catch (error) {
      console.error("Error submitting game result:", error);
      setResult({
        success: false,
        pointsEarned: 0,
        message: "An error occurred. Please try again.",
      });
    } finally {
      setLoading(false);
    }
  };

  // Format time display
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, "0")}:${secs
      .toString()
      .padStart(2, "0")}`;
  };

  return (
    <>
      <style>{cardStyles}</style>
      <Card className="w-full">
        <CardContent className="p-6">
          <div className="flex justify-between items-center mb-6">
            <Button
              variant="ghost"
              size="sm"
              onClick={onBack}
              className="flex items-center"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Games
            </Button>
            <div className="flex items-center">
              <Clock className="h-4 w-4 mr-2 text-muted-foreground" />
              <span className="text-lg font-medium">{formatTime(timer)}</span>
            </div>
          </div>

          <div className="text-center mb-6">
            <h2 className="text-2xl font-bold mb-2">Food Match Challenge</h2>
            <p className="text-muted-foreground">
              Match all the food pairs to earn points!
            </p>
          </div>

          {!gameStarted ? (
            <div className="text-center py-8">
              <p className="mb-6">
                Flip cards to find matching pairs of food items. Match all pairs
                to complete the challenge and earn points!
              </p>
              <Button onClick={handleStartGame} size="lg">
                Start Game
              </Button>
            </div>
          ) : gameCompleted ? (
            <div className="text-center py-8">
              <div className="mb-6">
                <Trophy className="h-16 w-16 mx-auto text-primary mb-4" />
                <h3 className="text-xl font-bold mb-2">Game Completed!</h3>
                <p className="text-muted-foreground mb-2">
                  Time: {formatTime(timer)}
                </p>
                {result && (
                  <Alert
                    className={`mt-4 ${
                      result.success ? "bg-primary/10" : "bg-destructive/10"
                    }`}
                  >
                    <AlertTitle>
                      {result.success ? "Success!" : "Oops!"}
                    </AlertTitle>
                    <AlertDescription>{result.message}</AlertDescription>
                    {result.pointsEarned > 0 && (
                      <div className="mt-2 font-medium">
                        +{result.pointsEarned} points earned!
                      </div>
                    )}
                  </Alert>
                )}
              </div>
              <Button onClick={onBack}>Return to Games</Button>
            </div>
          ) : (
            <>
              <div className="mb-4">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm font-medium">Progress</span>
                  <span className="text-sm">
                    {matchedPairs.length} / {cards.length / 2} pairs
                  </span>
                </div>
                <Progress
                  value={(matchedPairs.length / (cards.length / 2)) * 100}
                  className="h-2"
                />
              </div>

              <div className="grid grid-cols-4 md:grid-cols-5 lg:grid-cols-6 gap-2 mb-6">
                {cards.map((card, index) => (
                  <motion.div
                    key={card.id}
                    className={`aspect-square rounded-lg overflow-hidden cursor-pointer ${
                      card.flipped || card.matched
                        ? "pointer-events-none"
                        : "hover:shadow-md"
                    }`}
                    onClick={() => handleCardClick(index)}
                    whileHover={{ scale: 1.03 }}
                    whileTap={{ scale: 0.97 }}
                    animate={{
                      opacity: card.matched ? 0.7 : 1,
                      boxShadow:
                        card.flipped || card.matched
                          ? "0 4px 8px rgba(0,0,0,0.1)"
                          : "0 2px 4px rgba(0,0,0,0.05)",
                    }}
                    transition={{
                      duration: 0.3,
                      ease: "easeInOut",
                    }}
                  >
                    <div
                      className="relative w-full h-full card-container"
                      style={{
                        transform:
                          card.flipped || card.matched
                            ? "rotateY(180deg)"
                            : "rotateY(0deg)",
                        transition: "transform 0.6s ease-in-out",
                        transformStyle: "preserve-3d",
                      }}
                    >
                      <div
                        className="backface-hidden card-front"
                        style={{
                          backgroundColor: "#f0f2f5",
                          borderRadius: "0.5rem",
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                          boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
                          border: "1px solid #e1e4e8",
                        }}
                      >
                        <span className="text-xl font-bold text-gray-500">
                          ?
                        </span>
                      </div>
                      <div
                        className="backface-hidden card-back"
                        style={{
                          backgroundColor: "#ffffff",
                          borderRadius: "0.5rem",
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                          boxShadow: "0 2px 4px rgba(0,0,0,0.05)",
                          border: "1px solid #e1e4e8",
                        }}
                      >
                        <div className="flex flex-col items-center justify-center p-1">
                          <span className="text-3xl">{card.emoji}</span>
                          <span className="text-[10px] text-gray-500 font-medium mt-1">
                            {card.name}
                          </span>
                        </div>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </>
          )}
        </CardContent>
      </Card>
    </>
  );
};
