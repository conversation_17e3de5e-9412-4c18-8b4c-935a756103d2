
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';

interface ToggleFilterProps {
  label: string;
  value: boolean;
  onChange: (value: boolean) => void;
  description?: string;
}

export function ToggleFilter({
  label,
  value,
  onChange,
  description
}: ToggleFilterProps) {
  return (
    <div className="flex items-center justify-between space-x-2">
      <div className="space-y-0.5">
        <Label htmlFor={`toggle-${label}`} className="text-sm font-medium">
          {label}
        </Label>
        {description && (
          <p className="text-xs text-muted-foreground">{description}</p>
        )}
      </div>
      <Switch
        id={`toggle-${label}`}
        checked={value}
        onCheckedChange={onChange}
      />
    </div>
  );
}
