import { useState, useEffect, useRef, useCallback } from "react";
import { useParams, useNavigate, NavLink, useLocation } from "react-router-dom";

import { Loading } from "@/components/ui/loading";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/providers/AuthProvider";
import { Restaurant } from "@/types/restaurant";
import { RestaurantHero } from "@/components/restaurant/RestaurantHero";
import { RestaurantInfo } from "@/components/restaurant/RestaurantInfo";
import { RestaurantFeatures } from "@/components/restaurant/RestaurantFeatures";
import { RestaurantReviews } from "@/components/restaurant/RestaurantReviews";
import { RestaurantMenu } from "@/components/restaurant/RestaurantMenu";
import { AlertTriangle, ShoppingCart } from "lucide-react";

import { MetaTags } from "@/components/ui/meta-tags";

// Import our custom hooks
import { useRealtimeMenu } from "@/lib/react-query/hooks/useMenu";
import { useRealtimeReviews } from "@/lib/react-query/hooks/useReviews";
import { useRestaurantByUsername } from "@/lib/react-query/hooks/useRestaurants";

// Floating Cart Button Component
interface FloatingCartButtonProps {
  cartItemCount: number;
  username: string;
}

const FloatingCartButton = ({
  cartItemCount,
  username,
}: FloatingCartButtonProps) => {
  const navigate = useNavigate();
  const [isAnimating, setIsAnimating] = useState(false);

  // Different styles based on whether the cart has items
  const hasItems = cartItemCount > 0;

  // Add animation effect when cart count changes
  useEffect(() => {
    if (cartItemCount > 0) {
      setIsAnimating(true);
      const timer = setTimeout(() => setIsAnimating(false), 500);
      return () => clearTimeout(timer);
    }
  }, [cartItemCount]);

  return (
    <div className="fixed bottom-4 sm:bottom-6 right-4 sm:right-6 z-50">
      <Button
        onClick={() => navigate(`/restaurants/${username}/cart`)}
        className={`h-12 sm:h-14 px-4 sm:px-5 rounded-full shadow-lg transition-all duration-300 hover:shadow-xl hover:scale-105 ${
          hasItems
            ? "bg-primary text-primary-foreground"
            : "bg-card border border-border hover:bg-accent"
        } ${isAnimating ? "animate-bounce" : ""}`}
        aria-label="View Cart"
      >
        <ShoppingCart
          className={`h-4 sm:h-5 w-4 sm:w-5 mr-1.5 sm:mr-2 ${
            !hasItems && "text-muted-foreground"
          }`}
        />
        <span
          className={`text-sm sm:text-base font-medium ${
            !hasItems && "text-muted-foreground"
          }`}
        >
          {hasItems ? (
            <>
              <span className="hidden xs:inline">View Cart</span>
              <span className="xs:hidden">Cart</span>
              <span className="ml-1 font-bold">({cartItemCount})</span>
            </>
          ) : (
            <>
              <span className="hidden xs:inline">View Cart</span>
              <span className="xs:hidden">Cart</span>
            </>
          )}
        </span>
      </Button>
    </div>
  );
};

export const RestaurantPage = () => {
  const menuRef = useRef<HTMLDivElement>(null);
  const { username } = useParams<{ username: string }>();
  const { user } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  const [restaurant, setRestaurant] = useState<Restaurant | null>(null);
  const [selectedItemId, setSelectedItemId] = useState<string | null>(null);
  const [cartItemCount, setCartItemCount] = useState<number>(0);

  // Handle deep linking for menu items
  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const itemId = params.get("item");
    if (itemId) {
      setSelectedItemId(itemId);
    }
  }, [location.search]);

  const scrollToMenu = useCallback(() => {
    menuRef.current?.scrollIntoView({ behavior: "smooth", block: "start" });
  }, []);

  // Import our custom hook for fetching restaurant by username
  const {
    data: initialRestaurantData,
    isLoading: isRestaurantLoading,
    error: restaurantError,
    isError: isRestaurantError,
  } = useRestaurantByUsername(username);

  useEffect(() => {
    if (initialRestaurantData) {
      setRestaurant(initialRestaurantData);
    } else if (
      !isRestaurantLoading &&
      !isRestaurantError &&
      initialRestaurantData === null
    ) {
      setRestaurant(null);
    }
  }, [initialRestaurantData, isRestaurantLoading, isRestaurantError]);

  const restaurantId = restaurant?.id;
  const {
    menu,
    isLoading: isMenuLoading,
    error: menuError,
  } = useRealtimeMenu(restaurantId);
  const {
    reviews,
    setReviews,
    error: reviewsError,
  } = useRealtimeReviews(restaurantId);

  // Load cart from localStorage and update cart item count
  useEffect(() => {
    if (!restaurantId) return;

    // Function to update cart count from localStorage
    const updateCartCountFromStorage = () => {
      const storedCart = localStorage.getItem(`cart_${restaurantId}`);
      if (storedCart) {
        try {
          const cartItems = JSON.parse(storedCart);
          const count = cartItems.reduce(
            (total: number, item: { quantity: number }) =>
              total + (item.quantity || 0),
            0
          );
          setCartItemCount(count);
        } catch (error) {
          console.error("Error parsing cart data:", error);
          setCartItemCount(0);
        }
      } else {
        setCartItemCount(0);
      }
    };

    // Initial load from localStorage
    updateCartCountFromStorage();

    // Handle custom cart-updated event (for same-window updates)
    const handleCartUpdated = (e: CustomEvent) => {
      const detail = e.detail as { restaurantId: string; count: number };
      if (detail.restaurantId === restaurantId) {
        setCartItemCount(detail.count);
      }
    };

    // Handle storage event (for cross-window updates)
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === `cart_${restaurantId}`) {
        updateCartCountFromStorage();
      }
    };

    // Add event listeners
    window.addEventListener("storage", handleStorageChange);
    window.addEventListener("cart-updated", handleCartUpdated as EventListener);

    // Clean up event listeners
    return () => {
      window.removeEventListener("storage", handleStorageChange);
      window.removeEventListener(
        "cart-updated",
        handleCartUpdated as EventListener
      );
    };
  }, [restaurantId]);

  const overallError = restaurantError || menuError || reviewsError;

  if (isRestaurantLoading) {
    return <Loading text="Loading Restaurant..." />;
  }

  if (isRestaurantError || overallError) {
    const errorMessage =
      (restaurantError as Error)?.message ||
      (overallError as Error)?.message ||
      "Failed to load restaurant data. Please try again later.";
    return (
      <div className=" mx-auto flex flex-col items-center justify-center min-h-[70vh] p-4 text-center">
        <Alert variant="destructive" className="max-w-md">
          {" "}
          <AlertTriangle className="h-5 w-5" />{" "}
          <AlertTitle>Error Loading Restaurant</AlertTitle>{" "}
          <AlertDescription>
            {" "}
            {errorMessage === "Restaurant not found"
              ? "Sorry, the restaurant you are looking for could not be found."
              : errorMessage}{" "}
          </AlertDescription>{" "}
        </Alert>
        <Button onClick={() => navigate(-1)} variant="outline" className="mt-6">
          {" "}
          Go Back{" "}
        </Button>
      </div>
    );
  }

  if (!restaurant) {
    return (
      <div className=" mx-auto flex flex-col items-center justify-center min-h-[70vh] p-4 text-center">
        <Alert variant="default" className="max-w-md">
          {" "}
          <AlertTriangle className="h-5 w-5" />{" "}
          <AlertTitle>Restaurant Not Found</AlertTitle>{" "}
          <AlertDescription>
            The restaurant profile you requested does not exist or may have been
            removed. Please check the URL and try again.
          </AlertDescription>
        </Alert>
        <NavLink
          to="/restaurants"
          className="mt-4 inline-block px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors"
        >
          Go to Restaurants
        </NavLink>
      </div>
    );
  }

  return (
    <section className="max-w-screen-xl mx-auto px-2 py-4 sm:px-4 overflow-hidden">
      {/* Add meta tags for social sharing */}
      <MetaTags
        title={`${restaurant.restaurantName} | Qonai Food Ordering`}
        description={
          restaurant.description ||
          `Order food online from ${
            restaurant.restaurantName
          }. ${restaurant.cuisines?.join(", ")}`
        }
        imageUrl={restaurant.imageUrl}
        type="restaurant"
      />
      <RestaurantHero
        restaurant={restaurant}
        scrollToMenu={scrollToMenu}
        reviews={reviews}
      />
      <div className="flex flex-col lg:flex-row gap-4 sm:gap-6 lg:gap-8 mt-4 sm:mt-6 lg:mt-8 w-full">
        <div className="space-y-4 sm:space-y-6 lg:space-y-8 w-full lg:w-1/3 overflow-hidden">
          <RestaurantInfo restaurant={restaurant} />
          <RestaurantFeatures restaurant={restaurant} />
          <RestaurantReviews
            restaurant={restaurant}
            reviews={reviews}
            setReviews={setReviews}
            setRestaurant={setRestaurant}
            user={user}
          />
        </div>
        <div
          ref={menuRef}
          className="w-full lg:w-2/3 lg:sticky lg:top-6 lg:self-start overflow-hidden"
        >
          <RestaurantMenu
            menu={menu}
            loading={isMenuLoading}
            restaurantId={restaurant.id}
            selectedItemId={selectedItemId}
          />
        </div>
      </div>

      {/* Floating Cart Button */}
      {username && (
        <FloatingCartButton cartItemCount={cartItemCount} username={username} />
      )}
    </section>
  );
};
