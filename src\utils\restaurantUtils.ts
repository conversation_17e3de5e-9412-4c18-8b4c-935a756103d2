import { Restaurant as BaseRestaurant, WorkingHours } from "@/types";
import { Restaurant as RestaurantType } from "@/types/restaurant";

/**
 * Determines if a restaurant is currently open based on its working hours
 * @param restaurant The restaurant to check
 * @param currentTime Optional Date object to check against (defaults to current time)
 * @returns Boolean indicating if the restaurant is open
 */
export const isRestaurantOpen = (
  restaurant: BaseRestaurant | RestaurantType,
  currentTime: Date = new Date()
): boolean => {
  // If the restaurant has a static isOpen field that's true and autoUpdateStatus is false, respect that
  // (this is the field shown on the restaurant card)
  if (restaurant.isOpen === true && restaurant.autoUpdateStatus === false) {
    console.log(
      `Restaurant ${restaurant.restaurantName} is manually set to open`
    );
    return true;
  }

  // If no working hours are defined, default to closed
  if (!restaurant.workingHours || restaurant.workingHours.length === 0) {
    return false;
  }

  // Get current day and time
  const daysOfWeek = [
    "sunday",
    "monday",
    "tuesday",
    "wednesday",
    "thursday",
    "friday",
    "saturday",
  ];
  const currentDay = daysOfWeek[currentTime.getDay()];

  // Format current time as HH:MM
  const hours = currentTime.getHours().toString().padStart(2, "0");
  const minutes = currentTime.getMinutes().toString().padStart(2, "0");
  const currentTimeString = `${hours}:${minutes}`;

  // Find today's working hours
  const todayHours = restaurant.workingHours.find(
    (hours: WorkingHours) => hours.day.toLowerCase() === currentDay
  );

  // If no hours defined for today or explicitly marked as closed, restaurant is closed
  if (
    !todayHours ||
    !todayHours.isOpen ||
    !todayHours.openTime ||
    !todayHours.closeTime
  ) {
    return false;
  }

  // For debugging
  const restaurantName = restaurant.restaurantName || "Unknown";
  const day = currentDay;

  // Handle restaurants that close after midnight (e.g., 00:00)
  // If closeTime is "00:00", treat it as midnight
  if (todayHours.closeTime === "00:00") {
    const isOpen =
      currentTimeString >= todayHours.openTime || currentTimeString === "00:00";
    console.log(
      `Restaurant ${restaurantName} on ${day} at ${currentTimeString}: Midnight closing case, isOpen=${isOpen}`
    );
    return isOpen;
  }

  // Handle cases where closing time is after midnight (e.g., 02:00)
  if (todayHours.closeTime < todayHours.openTime) {
    // If current time is after opening time OR before closing time (next day)
    const isOpen =
      currentTimeString >= todayHours.openTime ||
      currentTimeString <= todayHours.closeTime;
    console.log(
      `Restaurant ${restaurantName} on ${day} at ${currentTimeString}: After-midnight case, isOpen=${isOpen}`
    );
    return isOpen;
  }

  // Normal case: opening and closing times are on the same day
  const isOpen =
    currentTimeString >= todayHours.openTime &&
    currentTimeString <= todayHours.closeTime;
  console.log(
    `Restaurant ${restaurantName} on ${day} at ${currentTimeString}: Normal case, isOpen=${isOpen}`
  );
  return isOpen;
};

/**
 * Filters a list of restaurants to only include those that are currently open
 * @param restaurants Array of restaurants to filter
 * @returns Filtered array of restaurants
 */
export const filterOpenRestaurants = <
  T extends BaseRestaurant | RestaurantType
>(
  restaurants: T[]
): T[] => {
  return restaurants.filter((restaurant) => isRestaurantOpen(restaurant));
};
