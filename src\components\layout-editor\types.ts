// src/components/layout-editor/types.ts
export type ElementType =
  | "table-round"
  | "table-rect"
  | "chair"
  | "window"
  | "door"
  | "stairs"
  | "wall"
  | "private-room"
  | "bar"
  | "kitchen";

export interface Element {
  id: string;
  type: ElementType;
  x: number; // Unzoomed x position
  y: number; // Unzoomed y position
  width: number;
  height: number;
  rotation: number;
  zIndex: number;
  opacity: number;
  borderRadius: number;
  isLocked: boolean;
  name?: string;
  capacity?: number;
  color: string;
  borderColor: string;
  isGrouped?: boolean;
  groupId?: string;
  tableId?: string; // Represents the actual Table ID this element corresponds to
  isNonClickable?: boolean; // Flag to indicate if element should not be clickable in reservation view
}

// You might add other shared types here if needed
