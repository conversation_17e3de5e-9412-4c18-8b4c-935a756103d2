import { useRef, useEffect, useState, useCallback } from "react";

const MEAL_EMOJIS = [
  "🍔",
  "🍕",
  "🍣",
  "🍜",
  "🥗",
  "🍟",
  "🌭",
  "🍿",
  "🥓",
  "🥚",
  "🍳",
  "🧇",
  "🥞",
  "🧈",
  "🍞",
  "🥐",
  "🥨",
  "🥯",
  "🥖",
  "🫓",
  "🧀",
  "🥙",
  "🥪",
  "🌮",
  "🌯",
  "🫔",
  "🥫",
  "🍖",
  "🍗",
  "🥩",
  "🍠",
  "🥟",
  "🍘",
  "🍛",
  "🍜",
  "🍢",
  "🥘",
  "🍰",
  "🍫",
  "🍭",
  "🍡",
  "🍩",
  "🍪",
  "🍨",
  "🧁",
  "🍺",
  "🍵",
] as const;

interface SquaresProps {
  direction?: "right" | "left" | "up" | "down" | "diagonal";
  speed?: number;
  squareSize?: number;
  hoverFillColor?: string;
  borderColor?: string;
  className?: string;
}

export function Squares({
  direction = "right",
  speed = 1,
  squareSize = 40,
  hoverFillColor = "#222",
  borderColor = "#ccc",
  className,
}: SquaresProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const requestRef = useRef<number>();
  const numSquaresX = useRef<number>();
  const numSquaresY = useRef<number>();
  const gridOffset = useRef({ x: 0, y: 0 });
  const [hoveredSquare, setHoveredSquare] = useState<{
    x: number;
    y: number;
  } | null>(null);

  const emojiPositions = useRef<{ x: number; y: number; emoji: string }[]>([]);

  const handleMouseMove = useCallback(
    (event: MouseEvent, canvas: HTMLCanvasElement) => {
      const rect = canvas.getBoundingClientRect();
      const mouseX = event.clientX - rect.left;
      const mouseY = event.clientY - rect.top;

      const startX = Math.floor(gridOffset.current.x / squareSize) * squareSize;
      const startY = Math.floor(gridOffset.current.y / squareSize) * squareSize;

      const hoveredSquareX = Math.floor(
        (mouseX + gridOffset.current.x - startX) / squareSize
      );
      const hoveredSquareY = Math.floor(
        (mouseY + gridOffset.current.y - startY) / squareSize
      );

      setHoveredSquare({ x: hoveredSquareX, y: hoveredSquareY });
    },
    [squareSize]
  );

  const handleMouseLeave = useCallback(() => {
    setHoveredSquare(null);
  }, []);

  const drawGrid = useCallback(
    (ctx: CanvasRenderingContext2D, canvas: HTMLCanvasElement) => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      const startX = Math.floor(gridOffset.current.x / squareSize) * squareSize;
      const startY = Math.floor(gridOffset.current.y / squareSize) * squareSize;

      ctx.lineWidth = 0.5;
      for (let x = startX; x < canvas.width + squareSize; x += squareSize) {
        for (let y = startY; y < canvas.height + squareSize; y += squareSize) {
          const squareX = x - (gridOffset.current.x % squareSize);
          const squareY = y - (gridOffset.current.y % squareSize);

          if (
            hoveredSquare &&
            Math.floor((x - startX) / squareSize) === hoveredSquare.x &&
            Math.floor((y - startY) / squareSize) === hoveredSquare.y
          ) {
            ctx.fillStyle = hoverFillColor;
            ctx.fillRect(squareX, squareY, squareSize, squareSize);
          }

          ctx.strokeStyle = borderColor;
          ctx.strokeRect(squareX, squareY, squareSize, squareSize);
        }
      }

      emojiPositions.current.forEach(({ x, y, emoji }) => {
        ctx.font = `${squareSize - 10}px serif`;
        ctx.fillStyle = "#fff";
        ctx.fillText(emoji, x, y);
      });

      const gradient = ctx.createRadialGradient(
        canvas.width / 2,
        canvas.height / 2,
        0,
        canvas.width / 2,
        canvas.height / 2,
        Math.sqrt(Math.pow(canvas.width, 2) + Math.pow(canvas.height, 2)) / 2
      );
      gradient.addColorStop(0, "rgba(255, 255, 255, 0)");
      gradient.addColorStop(1, "#ffffff");
      ctx.fillStyle = gradient;
      ctx.fillRect(0, 0, canvas.width, canvas.height);
    },
    [squareSize, hoverFillColor, hoveredSquare, borderColor]
  );

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    const resizeCanvas = () => {
      canvas.width = canvas.offsetWidth;
      canvas.height = canvas.offsetHeight;
      numSquaresX.current = Math.ceil(canvas.width / squareSize) + 1;
      numSquaresY.current = Math.ceil(canvas.height / squareSize) + 1;

      if (!emojiPositions.current.length) {
        emojiPositions.current = Array.from({ length: 3 }, () => ({
          x: Math.random() * canvas.width,
          y: Math.random() * canvas.height,
          emoji: MEAL_EMOJIS[Math.floor(Math.random() * MEAL_EMOJIS.length)],
        }));
      }
    };

    const animate = () => {
      const effectiveSpeed = Math.max(speed, 0.1);

      switch (direction) {
        case "right":
          gridOffset.current.x =
            (gridOffset.current.x - effectiveSpeed + squareSize) % squareSize;
          emojiPositions.current.forEach((pos) => {
            pos.x = (pos.x - effectiveSpeed + canvas.width) % canvas.width;
          });
          break;
        case "left":
          gridOffset.current.x =
            (gridOffset.current.x + effectiveSpeed + squareSize) % squareSize;
          emojiPositions.current.forEach((pos) => {
            pos.x = (pos.x + effectiveSpeed + canvas.width) % canvas.width;
          });
          break;
        case "up":
          gridOffset.current.y =
            (gridOffset.current.y + effectiveSpeed + squareSize) % squareSize;
          emojiPositions.current.forEach((pos) => {
            pos.y = (pos.y - effectiveSpeed + canvas.height) % canvas.height;
          });
          break;
        case "down":
          gridOffset.current.y =
            (gridOffset.current.y - effectiveSpeed + squareSize) % squareSize;
          emojiPositions.current.forEach((pos) => {
            pos.y = (pos.y + effectiveSpeed + canvas.height) % canvas.height;
          });
          break;
        case "diagonal":
          gridOffset.current.x =
            (gridOffset.current.x - effectiveSpeed + squareSize) % squareSize;
          gridOffset.current.y =
            (gridOffset.current.y - effectiveSpeed + squareSize) % squareSize;
          emojiPositions.current.forEach((pos) => {
            pos.x = (pos.x - effectiveSpeed + canvas.width) % canvas.width;
            pos.y = (pos.y - effectiveSpeed + canvas.height) % canvas.height;
          });
          break;
      }

      drawGrid(ctx, canvas);
      requestRef.current = requestAnimationFrame(animate);
    };

    const mouseMoveHandler = (e: MouseEvent) => handleMouseMove(e, canvas);

    resizeCanvas();
    animate();

    window.addEventListener("resize", resizeCanvas);
    canvas.addEventListener("mousemove", mouseMoveHandler);
    canvas.addEventListener("mouseleave", handleMouseLeave);

    return () => {
      window.removeEventListener("resize", resizeCanvas);
      canvas.removeEventListener("mousemove", mouseMoveHandler);
      canvas.removeEventListener("mouseleave", handleMouseLeave);
      if (requestRef.current) {
        cancelAnimationFrame(requestRef.current);
      }
    };
  }, [
    direction,
    speed,
    squareSize,
    drawGrid,
    handleMouseMove,
    handleMouseLeave,
  ]);

  return (
    <div className={`squares-container bg-white ${className}`}>
      <canvas ref={canvasRef} className="w-full h-full border-none block" />
    </div>
  );
}
