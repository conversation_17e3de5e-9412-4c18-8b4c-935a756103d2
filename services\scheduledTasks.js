/**
 * Scheduled tasks service for periodic maintenance operations
 */
const firestoreService = require('./firestoreService');
const logger = require('../utils/logger');

// Interval in milliseconds (10 minutes)
const CLEANUP_INTERVAL = 10 * 60 * 1000;

let cleanupIntervalId = null;

/**
 * Start the scheduled cleanup of expired pending subscriptions
 */
const startScheduledCleanup = () => {
  if (cleanupIntervalId) {
    logger.warn('Scheduled cleanup already running');
    return;
  }

  logger.info(`Starting scheduled cleanup to run every ${CLEANUP_INTERVAL / 60000} minutes`);
  
  // Run immediately on startup
  runCleanupTask();
  
  // Then schedule for regular intervals
  cleanupIntervalId = setInterval(runCleanupTask, CLEANUP_INTERVAL);
};

/**
 * Stop the scheduled cleanup
 */
const stopScheduledCleanup = () => {
  if (cleanupIntervalId) {
    clearInterval(cleanupIntervalId);
    cleanupIntervalId = null;
    logger.info('Scheduled cleanup stopped');
  }
};

/**
 * Run the cleanup task
 */
const runCleanupTask = async () => {
  try {
    logger.info('Running scheduled cleanup of expired pending subscriptions');
    
    const deletedCount = await firestoreService.cleanupExpiredSubscriptions();
    
    logger.info(`Scheduled cleanup completed: ${deletedCount} expired subscriptions removed`);
  } catch (error) {
    logger.error('Error in scheduled cleanup task:', error);
  }
};

module.exports = {
  startScheduledCleanup,
  stopScheduledCleanup,
  runCleanupTask,
};
