import React, { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { ScrollArea } from "@/components/ui/scroll-area";
import { PointTransaction } from "@/types/loyalty";
import { loyaltyService } from "@/services/LoyaltyService";
import { useAuth } from "@/providers/AuthProvider";
import { format } from "date-fns";
import { ArrowDownCircle, ArrowUpCircle, History } from "lucide-react";

interface PointTransactionsCardProps {
  limit?: number;
}

export const PointTransactionsCard: React.FC<PointTransactionsCardProps> = ({
  limit = 10,
}) => {
  const { user } = useAuth();
  const [transactions, setTransactions] = useState<PointTransaction[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchTransactions = async () => {
      if (!user) return;

      setLoading(true);
      try {
        const data = await loyaltyService.getPointTransactions(user.uid, limit);
        setTransactions(data);
      } catch (error) {
        console.error("Error fetching point transactions:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchTransactions();
  }, [user, limit]);

  // Get transaction icon based on type
  const getTransactionIcon = (transaction: PointTransaction) => {
    if (transaction.points > 0) {
      return <ArrowUpCircle className="h-4 w-4 text-green-500" />;
    } else {
      return <ArrowDownCircle className="h-4 w-4 text-red-500" />;
    }
  };

  // Get transaction color based on points
  const getTransactionColor = (points: number) => {
    return points > 0 ? "text-green-600" : "text-red-600";
  };

  // Format transaction date
  const formatTransactionDate = (timestamp: unknown) => {
    try {
      const date =
        typeof timestamp === "object" &&
        timestamp !== null &&
        "toDate" in timestamp &&
        typeof timestamp.toDate === "function"
          ? timestamp.toDate()
          : new Date(timestamp as string | number);
      return format(date, "MMM d, yyyy • h:mm a");
    } catch {
      return "Invalid date";
    }
  };

  // Get transaction type label
  const getTransactionTypeLabel = (type: string) => {
    switch (type) {
      case "order":
        return "Order";
      case "review":
        return "Review";
      case "referral":
        return "Referral";
      case "redemption":
        return "Redemption";
      case "challenge":
        return "Challenge";
      case "bonus":
        return "Bonus";
      default:
        return type.charAt(0).toUpperCase() + type.slice(1);
    }
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>
            <Skeleton className="h-8 w-48" />
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {Array.from({ length: 5 }).map((_, index) => (
              <div key={index} className="flex items-center justify-between">
                <div className="space-y-1">
                  <Skeleton className="h-4 w-32" />
                  <Skeleton className="h-3 w-24" />
                </div>
                <Skeleton className="h-6 w-16" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <History className="mr-2 h-5 w-5 text-primary" />
          Point Transactions
        </CardTitle>
      </CardHeader>
      <CardContent>
        {transactions.length === 0 ? (
          <p className="text-sm text-muted-foreground text-center py-4">
            No transactions yet. Start earning points by placing orders, writing
            reviews, or referring friends!
          </p>
        ) : (
          <ScrollArea className="h-[300px] pr-4">
            <div className="space-y-4">
              {transactions.map((transaction) => (
                <div
                  key={transaction.id}
                  className="flex items-start justify-between border-b pb-3 last:border-0"
                >
                  <div className="flex items-start space-x-3">
                    <div className="mt-0.5">
                      {getTransactionIcon(transaction)}
                    </div>
                    <div>
                      <p className="text-sm font-medium">
                        {transaction.description}
                      </p>
                      <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                        <span>{getTransactionTypeLabel(transaction.type)}</span>
                        <span>•</span>
                        <span>
                          {formatTransactionDate(transaction.createdAt)}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div
                    className={`text-sm font-bold ${getTransactionColor(
                      transaction.points
                    )}`}
                  >
                    {transaction.points > 0 ? "+" : ""}
                    {transaction.points} pts
                  </div>
                </div>
              ))}
            </div>
          </ScrollArea>
        )}
      </CardContent>
    </Card>
  );
};
