import React, { useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, SelectRangeEventHandler, SelectSingleEventHandler, DateRange } from 'react-day-picker';
import { format, addMonths, isSameMonth } from 'date-fns';
import { cn } from '@/lib/utils';
import { ChevronLeft, ChevronRight } from 'lucide-react';

// Custom navigation components to avoid nested button issue
const CustomNavigation = ({
  onPreviousClick,
  onNextClick,
  hasNext,
  hasPrevious,
  currentMonth,
  numberOfMonths = 1
}: {
  onPreviousClick: () => void;
  onNextClick: () => void;
  hasNext: boolean;
  hasPrevious: boolean;
  currentMonth: Date;
  numberOfMonths?: number;
}) => {
  // Format the month display based on number of months
  const formatMonthDisplay = () => {
    if (numberOfMonths === 1) {
      return format(currentMonth, 'MMMM yyyy');
    } else {
      const lastMonth = addMonths(currentMonth, numberOfMonths - 1);
      if (isSameMonth(currentMonth, lastMonth)) {
        return format(currentMonth, 'MMMM yyyy');
      } else if (currentMonth.getFullYear() === lastMonth.getFullYear()) {
        return `${format(currentMonth, 'MMMM')} - ${format(lastMonth, 'MMMM')} ${format(currentMonth, 'yyyy')}`;
      } else {
        return `${format(currentMonth, 'MMM yyyy')} - ${format(lastMonth, 'MMM yyyy')}`;
      }
    }
  };

  return (
    <div className="flex flex-col space-y-2 mb-2">
      <div className="text-center font-medium">
        {formatMonthDisplay()}
      </div>
      <div className="flex justify-between items-center px-1">
        <div
          role="button"
          tabIndex={0}
          onClick={hasPrevious ? onPreviousClick : undefined}
          onKeyDown={(e) => {
            if (hasPrevious && (e.key === "Enter" || e.key === " ")) {
              e.preventDefault();
              onPreviousClick();
            }
          }}
          className={cn(
            "h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100 flex items-center justify-center rounded-full hover:bg-muted",
            !hasPrevious && "opacity-20 cursor-not-allowed"
          )}
          aria-label="Previous month"
        >
          <ChevronLeft className="h-4 w-4" />
        </div>

        <div
          role="button"
          tabIndex={0}
          onClick={hasNext ? onNextClick : undefined}
          onKeyDown={(e) => {
            if (hasNext && (e.key === "Enter" || e.key === " ")) {
              e.preventDefault();
              onNextClick();
            }
          }}
          className={cn(
            "h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100 flex items-center justify-center rounded-full hover:bg-muted",
            !hasNext && "opacity-20 cursor-not-allowed"
          )}
          aria-label="Next month"
        >
          <ChevronRight className="h-4 w-4" />
        </div>
      </div>
    </div>
  );
};

export type CalendarProps = Omit<React.ComponentProps<typeof DayPicker>, 'selected' | 'onSelect'> & {
  className?: string;
  selected?: Date | DateRange | undefined;
  onSelect?: SelectSingleEventHandler | SelectRangeEventHandler;
};

export function CustomCalendar({
  className,
  classNames,
  showOutsideDays = true,
  mode = "single",
  selected,
  onSelect,
  numberOfMonths = 1,
  disabled,
  ...props
}: CalendarProps) {
  const [month, setMonth] = React.useState<Date>(props.defaultMonth || new Date());

  // Update month when selected date changes
  useEffect(() => {
    if (mode === "single" && selected instanceof Date) {
      setMonth(selected);
    } else if (mode === "range" && selected && "from" in selected && selected.from) {
      setMonth(selected.from);
    }
  }, [selected, mode]);

  const handlePreviousClick = () => {
    const previousMonth = new Date(month);
    previousMonth.setMonth(month.getMonth() - 1);
    setMonth(previousMonth);
  };

  const handleNextClick = () => {
    const nextMonth = new Date(month);
    nextMonth.setMonth(month.getMonth() + 1);
    setMonth(nextMonth);
  };

  // Handle selection based on mode
  const handleSelect = (value: Date | undefined, selectedDay: Date) => {
    if (mode === "single") {
      (onSelect as SelectSingleEventHandler)?.(value, selectedDay, {}, {} as React.MouseEvent);
    } else if (mode === "range") {
      (onSelect as SelectRangeEventHandler)?.(value as unknown as DateRange | undefined, selectedDay, {}, {} as React.MouseEvent);
    }
  };

  // Check if we can navigate to previous/next month
  const hasPrevious = true; // Always allow going to previous months
  const hasNext = true; // Always allow going to future months

  return (
    <div className={cn("p-3 bg-white rounded-md border shadow-sm", className)}>
      <CustomNavigation
        onPreviousClick={handlePreviousClick}
        onNextClick={handleNextClick}
        hasPrevious={hasPrevious}
        hasNext={hasNext}
        currentMonth={month}
        numberOfMonths={numberOfMonths}
      />

      <DayPicker
        month={month}
        onMonthChange={setMonth}
        showOutsideDays={showOutsideDays}
        className="p-0"
        classNames={{
          months: cn("flex", numberOfMonths > 1 ? "flex-row space-x-4" : "flex-col space-y-4"),
          month: "space-y-4",
          caption: "hidden", // Hide the default caption as we have our own
          table: "w-full border-collapse",
          head_row: "flex w-full",
          head_cell: "text-muted-foreground rounded-md w-9 font-normal text-[0.8rem] flex-1 text-center",
          row: "flex w-full mt-2",
          cell: "relative p-0 text-center text-sm focus-within:relative focus-within:z-20 [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md",
          day: cn(
            "h-9 w-9 p-0 font-normal aria-selected:opacity-100 hover:bg-accent hover:text-accent-foreground rounded-full mx-auto flex items-center justify-center"
          ),
          day_selected: "bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",
          day_today: "bg-accent text-accent-foreground",
          day_outside: "text-muted-foreground opacity-50",
          day_disabled: "text-muted-foreground opacity-50",
          day_hidden: "invisible",
          day_range_middle: "aria-selected:bg-accent aria-selected:text-accent-foreground",
          day_range_end: "aria-selected:bg-primary aria-selected:text-primary-foreground",
          day_range_start: "aria-selected:bg-primary aria-selected:text-primary-foreground",
          ...classNames,
        }}
        components={{
          // @ts-expect-error - Custom component
          Caption: () => null // We're using our own caption
        }}
        mode={mode}
        selected={selected}
        onSelect={handleSelect}
        numberOfMonths={numberOfMonths}
        disabled={disabled}
        {...props}
      />
    </div>
  );
}
