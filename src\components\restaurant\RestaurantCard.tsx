import { Card, CardContent } from "@/components/ui/card"; // Removed CardHeader as we structure content manually
import { Button } from "@/components/ui/button";
import { type Restaurant } from "@/types/restaurant"; // Use specific import
import { Link } from "react-router-dom";
import { Badge } from "@/components/ui/badge";
import { Star, MapPin, Navigation } from "lucide-react"; // Import icons
import { FollowButton } from "./FollowButton";
import { FollowerCount } from "./FollowerCount";
import { useAuth } from "@/providers/AuthProvider";

interface RestaurantCardProps {
  restaurant: Restaurant;
}

export function RestaurantCard({ restaurant }: RestaurantCardProps) {
  const { user, userRole } = useAuth();
  // Helper for formatting ratings
  const formattedRating = restaurant.weightedRating !== undefined
    ? restaurant.weightedRating.toFixed(1)
    : restaurant.rating !== undefined
    ? restaurant.rating.toFixed(1)
    : "0.0";
  // Helper for formatting distance
  const formattedDistance =
    typeof restaurant.distance === "number"
      ? `${restaurant.distance.toFixed(1)} km`
      : null;

  return (
    <Card className="group flex flex-col h-full overflow-hidden rounded-lg border border-border/40 bg-card text-card-foreground shadow-sm transition-all duration-300 ease-in-out hover:shadow-lg hover:border-primary/20">
      {/* Image Section */}
      <div className="relative aspect-[16/9] w-full overflow-hidden"> {/* Improved aspect ratio */}
        {restaurant.imageUrl ? (
          <img
            src={restaurant.imageUrl}
            alt={`Image of ${restaurant.restaurantName}`}
            className="w-full h-full object-cover transition-transform duration-500 ease-in-out group-hover:scale-105"
            loading="lazy" // Add lazy loading
          />
        ) : (
          <div className="w-full h-full bg-muted flex items-center justify-center text-muted-foreground">
            No Image
          </div>
        )}
        {/* Enhanced gradient overlay at the bottom */}
        <div className="absolute bottom-0 left-0 w-full h-1/3 bg-gradient-to-t from-black/60 to-transparent pointer-events-none" />
      </div>

      {/* Content Section */}
      <CardContent className="p-5 flex-grow flex flex-col"> {/* Increased padding */}
        {/* Top Row: Status and Rating */}
        <div className="flex justify-between items-center mb-2">
          <div className="flex items-center gap-1">
            {restaurant.isOpen ? (
              <Badge
                variant="default"
                className="bg-green-100/80 text-green-800 border-green-200 hover:bg-green-100 px-2.5 py-0.5 text-xs font-medium rounded-full flex items-center gap-1"
              >
                <span className="w-1.5 h-1.5 rounded-full bg-green-500 animate-pulse"></span>
                Open
              </Badge>
            ) : (
              <Badge
                variant="destructive"
                className="bg-red-100/80 text-red-800 border-red-200 hover:bg-red-100 px-2.5 py-0.5 text-xs font-medium rounded-full flex items-center gap-1"
              >
                <span className="w-1.5 h-1.5 rounded-full bg-red-500"></span>
                Closed
              </Badge>
            )}
            <FollowerCount
              restaurantId={restaurant.id}
              variant="outline"
              className="text-xs py-0.5 px-2 rounded-full bg-blue-50/50 border-blue-200/50 text-blue-700"
            />
          </div>
          <div className="flex items-center gap-1">
            <Badge
              variant="outline"
              className="flex items-center gap-1 px-2.5 py-0.5 border-amber-400/50 rounded-full bg-amber-50/50"
            >
              <Star className="w-3 h-3 text-amber-500 fill-amber-500" />
              <span className="text-xs font-semibold text-amber-600">{formattedRating}</span>
            </Badge>
          </div>
        </div>

        {/* Restaurant Name */}
        <h3 className="text-lg font-semibold leading-snug tracking-tight mb-1.5 group-hover:text-primary transition-colors">
          {restaurant.restaurantName || "Unnamed Restaurant"}
        </h3>

        {/* Address */}
        <div className="flex items-start gap-1.5 text-sm text-muted-foreground mb-3">
          <MapPin className="w-4 h-4 mt-0.5 shrink-0 text-primary/60" />
          <span className="line-clamp-2 text-gray-600">
            {restaurant.address || "Address not available"}
          </span>
        </div>

        {/* Cuisine Badges & Distance */}
        <div className="flex flex-wrap items-center gap-x-2 gap-y-1 mb-4">
            {/* Distance Badge (if available) */}
            {formattedDistance && (
                <Badge variant="secondary" className="flex items-center gap-1 text-xs py-0.5 px-2 rounded-full bg-blue-50 border-blue-100 text-blue-700">
                    <Navigation className="w-3 h-3" />
                    {formattedDistance}
                </Badge>
            )}
            {/* Cuisine Badges */}
            {restaurant.cuisines?.slice(0, 3).map((cuisine) => ( // Limit initial badges shown
                <Badge key={cuisine} variant="outline" className="text-xs py-0.5 px-2 rounded-full bg-gray-50 border-gray-200/80 text-gray-700 hover:bg-gray-100">
                    {cuisine}
                </Badge>
            ))}
            {/* Optional: Show more cuisines indicator */}
            {(restaurant.cuisines?.length ?? 0) > 3 && (
                <Badge variant="outline" className="text-xs py-0.5 px-2 rounded-full bg-gray-50 border-gray-200/80 text-gray-600 hover:bg-gray-100">
                    +{(restaurant.cuisines?.length ?? 0) - 3} more
                </Badge>
            )}
        </div>

        {/* Spacer to push button down */}
        <div className="flex-grow" />

        {/* Action Buttons */}
        <div className="mt-auto flex flex-col gap-2">
          <Link
            to={`/restaurants/${restaurant.username}`}
            className="w-full" // Ensure button is at the bottom
            aria-label={`View details for ${restaurant.restaurantName}`}
          >
            <Button
              variant="outline" // Use outline for a more subtle look
              size="sm" // Smaller button
              className="w-full transition-all duration-200 border-primary/50 text-primary hover:bg-primary/10 hover:text-primary hover:border-primary font-medium rounded-full"
            >
              View Details
            </Button>
          </Link>

          {user && userRole === "client" && (
            <FollowButton
              restaurantId={restaurant.id}
              restaurantName={restaurant.restaurantName}
              restaurantUsername={restaurant.username}
              variant="outline"
              size="sm"
              className="w-full rounded-full"
            />
          )}
        </div>
      </CardContent>
    </Card>
  );
}
