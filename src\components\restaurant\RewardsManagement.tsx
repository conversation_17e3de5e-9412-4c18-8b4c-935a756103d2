import React, { useState } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Reward } from "@/types/rewards";
import {
  useRestaurantRewards,
  useCreate<PERSON>ew<PERSON>,
  useUpdateReward,
  useDeleteReward,
} from "@/lib/react-query/hooks/useRewards";
import { useAuth } from "@/providers/AuthProvider";

import { Timestamp } from "firebase/firestore";
import {
  Gift,
  Trash2,
  Edit,
  Plus,
  AlertCircle,
  Utensils,
  Tag,
} from "lucide-react";
import { toast } from "sonner";
import { useMenuItems } from "@/lib/react-query/hooks/useMenu";

interface RewardsManagementProps {
  restaurantId: string;
}

export const RewardsManagement: React.FC<RewardsManagementProps> = ({
  restaurantId,
}) => {
  const { user } = useAuth();
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedReward, setSelectedReward] = useState<Reward | null>(null);

  // Fetch menu items for the restaurant
  const { data: menuItems = [], isLoading: isLoadingMenu } =
    useMenuItems(restaurantId);

  // Form state
  const [formData, setFormData] = useState<Partial<Reward>>({
    name: "",
    description: "",
    pointsCost: 100,
    type: "discount",
    discountValue: 10,
    discountType: "percentage",
    isActive: true,
    availableQuantity: undefined,
    termsAndConditions: "",
    minimumOrderValue: undefined,
    menuItemId: undefined,
  });

  // Queries
  const {
    data: rewards = [],
    isLoading: isLoadingRewards,
    isError: isRewardsError,
  } = useRestaurantRewards(restaurantId);

  // Mutations
  const { mutate: createReward, isPending: isCreating } = useCreateReward();
  const { mutate: updateReward, isPending: isUpdating } = useUpdateReward();
  const { mutate: deleteReward, isPending: isDeleting } = useDeleteReward();

  // Handle form input changes
  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;

    if (
      name === "pointsCost" ||
      name === "discountValue" ||
      name === "availableQuantity" ||
      name === "minimumOrderValue"
    ) {
      setFormData({
        ...formData,
        [name]: value === "" ? undefined : Number(value),
      });
    } else {
      setFormData({
        ...formData,
        [name]: value,
      });
    }
  };

  // Handle select changes
  const handleSelectChange = (name: string, value: string) => {
    setFormData({
      ...formData,
      [name]: value,
    });
  };

  // Handle switch changes
  const handleSwitchChange = (name: string, checked: boolean) => {
    setFormData({
      ...formData,
      [name]: checked,
    });
  };

  // Handle date changes
  const handleDateChange = (name: string, date: Date | undefined) => {
    setFormData({
      ...formData,
      [name]: date ? Timestamp.fromDate(date) : undefined,
    });
  };

  // Reset form
  const resetForm = () => {
    setFormData({
      name: "",
      description: "",
      pointsCost: 100,
      type: "discount",
      discountValue: 10,
      discountType: "percentage",
      isActive: true,
      availableQuantity: undefined,
      termsAndConditions: "",
      minimumOrderValue: undefined,
      menuItemId: undefined,
    });
    setSelectedReward(null);
  };

  // Open edit dialog
  const handleEditClick = (reward: Reward) => {
    setSelectedReward(reward);
    setFormData({
      name: reward.name,
      description: reward.description,
      pointsCost: reward.pointsCost,
      type: reward.type,
      discountValue: reward.discountValue,
      discountType: reward.discountType,
      isActive: reward.isActive,
      availableQuantity: reward.availableQuantity,
      termsAndConditions: reward.termsAndConditions,
      minimumOrderValue: reward.minimumOrderValue,
      expiresAt: reward.expiresAt,
      menuItemId: reward.menuItemId,
    });
    setEditDialogOpen(true);
  };

  // Open delete dialog
  const handleDeleteClick = (reward: Reward) => {
    setSelectedReward(reward);
    setDeleteDialogOpen(true);
  };

  // Create reward
  const handleCreateReward = () => {
    if (!user) return;

    if (!formData.name || !formData.description || !formData.pointsCost) {
      toast.error("Please fill in all required fields");
      return;
    }

    if (formData.type === "freeItem" && !formData.menuItemId) {
      toast.error("Please select a menu item for free item rewards");
      return;
    }

    const rewardData: Omit<
      Reward,
      "id" | "createdAt" | "updatedAt" | "createdBy"
    > = {
      name: formData.name,
      description: formData.description,
      pointsCost: formData.pointsCost,
      type: formData.type as
        | "discount"
        | "freeItem"
        | "freeDelivery"
        | "vipAccess"
        | "other",
      isActive: formData.isActive ?? true,
      restaurantId,
      isGlobal: false,
      isFeatured: false,
    };

    if (formData.type === "discount") {
      rewardData.discountValue = formData.discountValue;
      rewardData.discountType = formData.discountType as "percentage" | "fixed";
    }

    if (formData.availableQuantity !== undefined) {
      rewardData.availableQuantity = formData.availableQuantity;
    }

    if (formData.termsAndConditions) {
      rewardData.termsAndConditions = formData.termsAndConditions;
    }

    if (formData.minimumOrderValue !== undefined) {
      rewardData.minimumOrderValue = formData.minimumOrderValue;
    }

    if (formData.expiresAt) {
      rewardData.expiresAt = formData.expiresAt as Timestamp;
    }

    if (formData.menuItemId) {
      rewardData.menuItemId = formData.menuItemId;
    }

    createReward(
      { reward: rewardData, userId: user.uid },
      {
        onSuccess: () => {
          setCreateDialogOpen(false);
          resetForm();
        },
      }
    );
  };

  // Update reward
  const handleUpdateReward = () => {
    if (!selectedReward) return;

    if (!formData.name || !formData.description || !formData.pointsCost) {
      toast.error("Please fill in all required fields");
      return;
    }

    if (formData.type === "freeItem" && !formData.menuItemId) {
      toast.error("Please select a menu item for free item rewards");
      return;
    }

    const rewardData: Partial<Reward> = {
      name: formData.name,
      description: formData.description,
      pointsCost: formData.pointsCost,
      type: formData.type as
        | "discount"
        | "freeItem"
        | "freeDelivery"
        | "vipAccess"
        | "other",
      isActive: formData.isActive,
    };

    if (formData.type === "discount") {
      rewardData.discountValue = formData.discountValue;
      rewardData.discountType = formData.discountType as "percentage" | "fixed";
    }

    if (formData.availableQuantity !== undefined) {
      rewardData.availableQuantity = formData.availableQuantity;
    } else {
      rewardData.availableQuantity = undefined;
    }

    if (formData.termsAndConditions) {
      rewardData.termsAndConditions = formData.termsAndConditions;
    }

    if (formData.minimumOrderValue !== undefined) {
      rewardData.minimumOrderValue = formData.minimumOrderValue;
    } else {
      rewardData.minimumOrderValue = undefined;
    }

    if (formData.expiresAt) {
      rewardData.expiresAt = formData.expiresAt as Timestamp;
    } else {
      rewardData.expiresAt = undefined;
    }

    if (formData.menuItemId) {
      rewardData.menuItemId = formData.menuItemId;
    } else {
      rewardData.menuItemId = undefined;
    }

    updateReward(
      { rewardId: selectedReward.id, data: rewardData, restaurantId },
      {
        onSuccess: () => {
          setEditDialogOpen(false);
          resetForm();
        },
      }
    );
  };

  // Delete reward
  const handleDeleteReward = () => {
    if (!selectedReward) return;

    deleteReward(
      { rewardId: selectedReward.id, restaurantId },
      {
        onSuccess: () => {
          setDeleteDialogOpen(false);
        },
      }
    );
  };

  // Helper function to get menu item name by ID
  const getMenuItemName = (menuItemId?: string) => {
    if (!menuItemId) return null;
    const menuItem = menuItems.find((item) => item.itemId === menuItemId);
    return menuItem?.name || "Unknown Item";
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="flex items-center">
          <Gift className="mr-2 h-5 w-5 text-primary" />
          Loyalty Rewards Management
        </CardTitle>
        <Button onClick={() => setCreateDialogOpen(true)}>
          <Plus className="mr-2 h-4 w-4" />
          Create Reward
        </Button>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="active">
          <TabsList className="w-full mb-4">
            <TabsTrigger value="active" className="flex-1">
              Active Rewards
            </TabsTrigger>
            <TabsTrigger value="inactive" className="flex-1">
              Inactive Rewards
            </TabsTrigger>
          </TabsList>

          <TabsContent value="active">
            {isLoadingRewards ? (
              <div className="text-center py-8">
                <p className="text-muted-foreground">Loading rewards...</p>
              </div>
            ) : isRewardsError ? (
              <div className="text-center py-8">
                <AlertCircle className="h-8 w-8 text-destructive mx-auto mb-2" />
                <p className="text-muted-foreground">
                  Failed to load rewards. Please try again later.
                </p>
              </div>
            ) : rewards.filter((r) => r.isActive).length === 0 ? (
              <div className="text-center py-8">
                <Gift className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                <p className="text-muted-foreground">
                  No active rewards. Create your first reward to engage
                  customers!
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Reward</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead>Points</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {rewards
                      .filter((reward) => reward.isActive)
                      .map((reward) => (
                        <TableRow key={reward.id}>
                          <TableCell>
                            <div>
                              <p className="font-medium">{reward.name}</p>
                              <p className="text-xs text-muted-foreground line-clamp-1">
                                {reward.description}
                              </p>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="space-y-1">
                              {reward.type === "discount" ? (
                                <>
                                  <Badge variant="outline">
                                    {reward.discountValue}
                                    {reward.discountType === "percentage"
                                      ? "%"
                                      : "$"}{" "}
                                    Discount
                                  </Badge>
                                  {reward.menuItemId && (
                                    <div className="text-xs text-muted-foreground">
                                      For: {getMenuItemName(reward.menuItemId)}
                                    </div>
                                  )}
                                </>
                              ) : reward.type === "freeItem" ? (
                                <>
                                  <Badge variant="outline">Free Item</Badge>
                                  {reward.menuItemId && (
                                    <div className="text-xs text-muted-foreground">
                                      Item: {getMenuItemName(reward.menuItemId)}
                                    </div>
                                  )}
                                </>
                              ) : reward.type === "freeDelivery" ? (
                                <Badge variant="outline">Free Delivery</Badge>
                              ) : (
                                <Badge variant="outline">Other</Badge>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>{reward.pointsCost}</TableCell>
                          <TableCell>
                            {reward.expiresAt &&
                            reward.expiresAt.toDate() < new Date() ? (
                              <Badge variant="destructive">Expired</Badge>
                            ) : reward.availableQuantity !== undefined &&
                              reward.availableQuantity <= 0 ? (
                              <Badge variant="destructive">Out of stock</Badge>
                            ) : (
                              <Badge variant="default">Active</Badge>
                            )}
                          </TableCell>
                          <TableCell className="text-right">
                            <div className="flex justify-end space-x-2">
                              <Button
                                variant="outline"
                                size="icon"
                                onClick={() => handleEditClick(reward)}
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="outline"
                                size="icon"
                                onClick={() => handleDeleteClick(reward)}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </TabsContent>

          <TabsContent value="inactive">
            {isLoadingRewards ? (
              <div className="text-center py-8">
                <p className="text-muted-foreground">Loading rewards...</p>
              </div>
            ) : isRewardsError ? (
              <div className="text-center py-8">
                <AlertCircle className="h-8 w-8 text-destructive mx-auto mb-2" />
                <p className="text-muted-foreground">
                  Failed to load rewards. Please try again later.
                </p>
              </div>
            ) : rewards.filter((r) => !r.isActive).length === 0 ? (
              <div className="text-center py-8">
                <Gift className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                <p className="text-muted-foreground">No inactive rewards.</p>
              </div>
            ) : (
              <div className="space-y-4">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Reward</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead>Points</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {rewards
                      .filter((reward) => !reward.isActive)
                      .map((reward) => (
                        <TableRow key={reward.id}>
                          <TableCell>
                            <div>
                              <p className="font-medium">{reward.name}</p>
                              <p className="text-xs text-muted-foreground line-clamp-1">
                                {reward.description}
                              </p>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="space-y-1">
                              {reward.type === "discount" ? (
                                <>
                                  <Badge variant="outline">
                                    {reward.discountValue}
                                    {reward.discountType === "percentage"
                                      ? "%"
                                      : "$"}{" "}
                                    Discount
                                  </Badge>
                                  {reward.menuItemId && (
                                    <div className="text-xs text-muted-foreground">
                                      For: {getMenuItemName(reward.menuItemId)}
                                    </div>
                                  )}
                                </>
                              ) : reward.type === "freeItem" ? (
                                <>
                                  <Badge variant="outline">Free Item</Badge>
                                  {reward.menuItemId && (
                                    <div className="text-xs text-muted-foreground">
                                      Item: {getMenuItemName(reward.menuItemId)}
                                    </div>
                                  )}
                                </>
                              ) : reward.type === "freeDelivery" ? (
                                <Badge variant="outline">Free Delivery</Badge>
                              ) : (
                                <Badge variant="outline">Other</Badge>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>{reward.pointsCost}</TableCell>
                          <TableCell>
                            <Badge variant="secondary">Inactive</Badge>
                          </TableCell>
                          <TableCell className="text-right">
                            <div className="flex justify-end space-x-2">
                              <Button
                                variant="outline"
                                size="icon"
                                onClick={() => handleEditClick(reward)}
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="outline"
                                size="icon"
                                onClick={() => handleDeleteClick(reward)}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </TabsContent>
        </Tabs>

        {/* Create Reward Dialog */}
        <Dialog open={createDialogOpen} onOpenChange={setCreateDialogOpen}>
          <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Create New Reward</DialogTitle>
              <DialogDescription>
                Create a new loyalty reward for your customers.
              </DialogDescription>
            </DialogHeader>

            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="name">Reward Name *</Label>
                <Input
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  placeholder="e.g., 10% Off Your Next Order"
                />
              </div>

              <div className="grid gap-2">
                <Label htmlFor="description">Description *</Label>
                <Textarea
                  id="description"
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  placeholder="Describe the reward"
                  rows={3}
                />
              </div>

              <div className="grid gap-2">
                <Label htmlFor="type">Reward Type *</Label>
                <select
                  id="type"
                  name="type"
                  value={formData.type}
                  onChange={(e) => handleSelectChange("type", e.target.value)}
                  className="flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                >
                  <option value="">Select reward type</option>
                  <option value="discount">Discount</option>
                  <option value="freeItem">Free Item</option>
                  <option value="freeDelivery">Free Delivery</option>
                  <option value="vipAccess">VIP Access</option>
                  <option value="other">Other</option>
                </select>
              </div>

              {formData.type === "discount" && (
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="grid gap-2">
                      <Label htmlFor="discountValue">Discount Value *</Label>
                      <Input
                        id="discountValue"
                        name="discountValue"
                        type="number"
                        value={formData.discountValue}
                        onChange={handleInputChange}
                        min={1}
                      />
                    </div>

                    <div className="grid gap-2">
                      <Label htmlFor="discountType">Discount Type *</Label>
                      <select
                        id="discountType"
                        name="discountType"
                        value={formData.discountType}
                        onChange={(e) =>
                          handleSelectChange("discountType", e.target.value)
                        }
                        className="flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                      >
                        <option value="">Select type</option>
                        <option value="percentage">Percentage (%)</option>
                        <option value="fixed">Fixed Amount ($)</option>
                      </select>
                    </div>
                  </div>

                  {/* Optional Menu Item Selection for Discount */}
                  <div className="grid gap-2">
                    <Label htmlFor="discountMenuItemId">
                      Apply to Specific Item (Optional)
                    </Label>
                    {isLoadingMenu ? (
                      <div className="text-sm text-muted-foreground">
                        Loading menu items...
                      </div>
                    ) : (
                      <select
                        id="discountMenuItemId"
                        name="menuItemId"
                        value={formData.menuItemId || ""}
                        onChange={(e) =>
                          handleSelectChange("menuItemId", e.target.value)
                        }
                        className="flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                      >
                        <option value="">Apply to entire order</option>
                        {menuItems.map((item) => (
                          <option key={item.itemId} value={item.itemId}>
                            {item.name} - ${item.price}
                          </option>
                        ))}
                      </select>
                    )}
                    <div className="flex items-center text-xs text-muted-foreground">
                      <Tag className="h-3 w-3 mr-1" />
                      <span>Leave empty to apply discount to entire order</span>
                    </div>
                  </div>
                </div>
              )}

              {formData.type === "freeItem" && (
                <div className="grid gap-2">
                  <Label htmlFor="menuItemId">Select Menu Item *</Label>
                  {isLoadingMenu ? (
                    <div className="text-sm text-muted-foreground">
                      Loading menu items...
                    </div>
                  ) : (
                    <select
                      id="menuItemId"
                      name="menuItemId"
                      value={formData.menuItemId || ""}
                      onChange={(e) =>
                        handleSelectChange("menuItemId", e.target.value)
                      }
                      className="flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                    >
                      <option value="">Select a menu item</option>
                      {menuItems.map((item) => (
                        <option key={item.itemId} value={item.itemId}>
                          {item.name} - ${item.price}
                        </option>
                      ))}
                    </select>
                  )}
                  <div className="flex items-center text-xs text-muted-foreground">
                    <Utensils className="h-3 w-3 mr-1" />
                    <span>This will be the free item customers receive</span>
                  </div>
                </div>
              )}

              <div className="grid gap-2">
                <Label htmlFor="pointsCost">Points Cost *</Label>
                <Input
                  id="pointsCost"
                  name="pointsCost"
                  type="number"
                  value={formData.pointsCost}
                  onChange={handleInputChange}
                  min={1}
                />
              </div>

              <Separator />

              <div className="grid gap-2">
                <Label htmlFor="availableQuantity">
                  Available Quantity (Optional)
                </Label>
                <Input
                  id="availableQuantity"
                  name="availableQuantity"
                  type="number"
                  value={
                    formData.availableQuantity === undefined
                      ? ""
                      : formData.availableQuantity
                  }
                  onChange={handleInputChange}
                  min={0}
                  placeholder="Leave empty for unlimited"
                />
              </div>

              <div className="grid gap-2">
                <Label htmlFor="minimumOrderValue">
                  Minimum Order Value (Optional)
                </Label>
                <Input
                  id="minimumOrderValue"
                  name="minimumOrderValue"
                  type="number"
                  value={
                    formData.minimumOrderValue === undefined
                      ? ""
                      : formData.minimumOrderValue
                  }
                  onChange={handleInputChange}
                  min={0}
                  placeholder="Leave empty for no minimum"
                />
              </div>

              <div className="grid gap-2">
                <Label htmlFor="expiresAt">Expiration Date (Optional)</Label>
                <input
                  id="expiresAt"
                  name="expiresAt"
                  type="date"
                  value={
                    formData.expiresAt
                      ? formData.expiresAt.toDate().toISOString().split("T")[0]
                      : ""
                  }
                  onChange={(e) => {
                    const date = e.target.value
                      ? new Date(e.target.value)
                      : undefined;
                    handleDateChange("expiresAt", date);
                  }}
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                />
              </div>

              <div className="grid gap-2">
                <Label htmlFor="termsAndConditions">
                  Terms & Conditions (Optional)
                </Label>
                <Textarea
                  id="termsAndConditions"
                  name="termsAndConditions"
                  value={formData.termsAndConditions}
                  onChange={handleInputChange}
                  placeholder="Any restrictions or terms"
                  rows={2}
                />
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="isActive"
                  checked={formData.isActive}
                  onCheckedChange={(checked) =>
                    handleSwitchChange("isActive", checked)
                  }
                />
                <Label htmlFor="isActive">Active</Label>
              </div>
            </div>

            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => {
                  setCreateDialogOpen(false);
                  resetForm();
                }}
                disabled={isCreating}
              >
                Cancel
              </Button>
              <Button onClick={handleCreateReward} disabled={isCreating}>
                {isCreating ? "Creating..." : "Create Reward"}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Edit Reward Dialog */}
        <Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
          <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Edit Reward</DialogTitle>
              <DialogDescription>
                Update the details of this loyalty reward.
              </DialogDescription>
            </DialogHeader>

            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="edit-name">Reward Name *</Label>
                <Input
                  id="edit-name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                />
              </div>

              <div className="grid gap-2">
                <Label htmlFor="edit-description">Description *</Label>
                <Textarea
                  id="edit-description"
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  rows={3}
                />
              </div>

              <div className="grid gap-2">
                <Label htmlFor="edit-type">Reward Type *</Label>
                <select
                  id="edit-type"
                  name="type"
                  value={formData.type}
                  onChange={(e) => handleSelectChange("type", e.target.value)}
                  className="flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                >
                  <option value="">Select reward type</option>
                  <option value="discount">Discount</option>
                  <option value="freeItem">Free Item</option>
                  <option value="freeDelivery">Free Delivery</option>
                  <option value="vipAccess">VIP Access</option>
                  <option value="other">Other</option>
                </select>
              </div>

              {formData.type === "discount" && (
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="grid gap-2">
                      <Label htmlFor="edit-discountValue">
                        Discount Value *
                      </Label>
                      <Input
                        id="edit-discountValue"
                        name="discountValue"
                        type="number"
                        value={formData.discountValue}
                        onChange={handleInputChange}
                        min={1}
                      />
                    </div>

                    <div className="grid gap-2">
                      <Label htmlFor="edit-discountType">Discount Type *</Label>
                      <select
                        id="edit-discountType"
                        name="discountType"
                        value={formData.discountType}
                        onChange={(e) =>
                          handleSelectChange("discountType", e.target.value)
                        }
                        className="flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                      >
                        <option value="">Select type</option>
                        <option value="percentage">Percentage (%)</option>
                        <option value="fixed">Fixed Amount ($)</option>
                      </select>
                    </div>
                  </div>

                  {/* Optional Menu Item Selection for Discount */}
                  <div className="grid gap-2">
                    <Label htmlFor="edit-discountMenuItemId">
                      Apply to Specific Item (Optional)
                    </Label>
                    {isLoadingMenu ? (
                      <div className="text-sm text-muted-foreground">
                        Loading menu items...
                      </div>
                    ) : (
                      <select
                        id="edit-discountMenuItemId"
                        name="menuItemId"
                        value={formData.menuItemId || ""}
                        onChange={(e) =>
                          handleSelectChange("menuItemId", e.target.value)
                        }
                        className="flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                      >
                        <option value="">Apply to entire order</option>
                        {menuItems.map((item) => (
                          <option key={item.itemId} value={item.itemId}>
                            {item.name} - ${item.price}
                          </option>
                        ))}
                      </select>
                    )}
                    <div className="flex items-center text-xs text-muted-foreground">
                      <Tag className="h-3 w-3 mr-1" />
                      <span>Leave empty to apply discount to entire order</span>
                    </div>
                  </div>
                </div>
              )}

              {formData.type === "freeItem" && (
                <div className="grid gap-2">
                  <Label htmlFor="edit-menuItemId">Select Menu Item *</Label>
                  {isLoadingMenu ? (
                    <div className="text-sm text-muted-foreground">
                      Loading menu items...
                    </div>
                  ) : (
                    <select
                      id="edit-menuItemId"
                      name="menuItemId"
                      value={formData.menuItemId || ""}
                      onChange={(e) =>
                        handleSelectChange("menuItemId", e.target.value)
                      }
                      className="flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                    >
                      <option value="">Select a menu item</option>
                      {menuItems.map((item) => (
                        <option key={item.itemId} value={item.itemId}>
                          {item.name} - ${item.price}
                        </option>
                      ))}
                    </select>
                  )}
                  <div className="flex items-center text-xs text-muted-foreground">
                    <Utensils className="h-3 w-3 mr-1" />
                    <span>This will be the free item customers receive</span>
                  </div>
                </div>
              )}

              <div className="grid gap-2">
                <Label htmlFor="edit-pointsCost">Points Cost *</Label>
                <Input
                  id="edit-pointsCost"
                  name="pointsCost"
                  type="number"
                  value={formData.pointsCost}
                  onChange={handleInputChange}
                  min={1}
                />
              </div>

              <Separator />

              <div className="grid gap-2">
                <Label htmlFor="edit-availableQuantity">
                  Available Quantity (Optional)
                </Label>
                <Input
                  id="edit-availableQuantity"
                  name="availableQuantity"
                  type="number"
                  value={
                    formData.availableQuantity === undefined
                      ? ""
                      : formData.availableQuantity
                  }
                  onChange={handleInputChange}
                  min={0}
                  placeholder="Leave empty for unlimited"
                />
              </div>

              <div className="grid gap-2">
                <Label htmlFor="edit-minimumOrderValue">
                  Minimum Order Value (Optional)
                </Label>
                <Input
                  id="edit-minimumOrderValue"
                  name="minimumOrderValue"
                  type="number"
                  value={
                    formData.minimumOrderValue === undefined
                      ? ""
                      : formData.minimumOrderValue
                  }
                  onChange={handleInputChange}
                  min={0}
                  placeholder="Leave empty for no minimum"
                />
              </div>

              <div className="grid gap-2">
                <Label htmlFor="edit-expiresAt">
                  Expiration Date (Optional)
                </Label>
                <input
                  id="edit-expiresAt"
                  name="expiresAt"
                  type="date"
                  value={
                    formData.expiresAt
                      ? formData.expiresAt.toDate().toISOString().split("T")[0]
                      : ""
                  }
                  onChange={(e) => {
                    const date = e.target.value
                      ? new Date(e.target.value)
                      : undefined;
                    handleDateChange("expiresAt", date);
                  }}
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                />
              </div>

              <div className="grid gap-2">
                <Label htmlFor="edit-termsAndConditions">
                  Terms & Conditions (Optional)
                </Label>
                <Textarea
                  id="edit-termsAndConditions"
                  name="termsAndConditions"
                  value={formData.termsAndConditions}
                  onChange={handleInputChange}
                  placeholder="Any restrictions or terms"
                  rows={2}
                />
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="edit-isActive"
                  checked={formData.isActive}
                  onCheckedChange={(checked) =>
                    handleSwitchChange("isActive", checked)
                  }
                />
                <Label htmlFor="edit-isActive">Active</Label>
              </div>
            </div>

            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => {
                  setEditDialogOpen(false);
                  resetForm();
                }}
                disabled={isUpdating}
              >
                Cancel
              </Button>
              <Button onClick={handleUpdateReward} disabled={isUpdating}>
                {isUpdating ? "Updating..." : "Update Reward"}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Delete Confirmation Dialog */}
        <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Delete Reward</DialogTitle>
              <DialogDescription>
                Are you sure you want to delete this reward? This action cannot
                be undone.
              </DialogDescription>
            </DialogHeader>

            {selectedReward && (
              <div className="py-4">
                <p className="font-medium">{selectedReward.name}</p>
                <p className="text-sm text-muted-foreground mt-1">
                  {selectedReward.description}
                </p>
              </div>
            )}

            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setDeleteDialogOpen(false)}
                disabled={isDeleting}
              >
                Cancel
              </Button>
              <Button
                variant="destructive"
                onClick={handleDeleteReward}
                disabled={isDeleting}
              >
                {isDeleting ? "Deleting..." : "Delete Reward"}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </CardContent>
    </Card>
  );
};
