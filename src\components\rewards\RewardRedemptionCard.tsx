import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { RewardRedemption } from "@/types/rewards";
import { Ticket, Clock, Check, Copy, Store } from "lucide-react";
import { format, formatDistanceToNow } from "date-fns";
import { toast } from "sonner";

interface RewardRedemptionCardProps {
  redemption: RewardRedemption;
}

export const RewardRedemptionCard: React.FC<RewardRedemptionCardProps> = ({
  redemption,
}) => {
  const isExpired = redemption.expiresAt.toDate() < new Date();

  const copyCodeToClipboard = () => {
    navigator.clipboard.writeText(redemption.code);
    toast.success("Redemption code copied to clipboard");
  };

  const formatExpirationDate = (timestamp: { toDate: () => Date }) => {
    const date = timestamp.toDate();
    const now = new Date();

    if (date < now) {
      return "Expired";
    }

    // If less than 7 days away, show relative time
    if (date.getTime() - now.getTime() < 7 * 24 * 60 * 60 * 1000) {
      return `Expires ${formatDistanceToNow(date, { addSuffix: true })}`;
    }

    // Otherwise show the date
    return `Expires on ${format(date, "MMM d, yyyy")}`;
  };

  return (
    <Card className="overflow-hidden hover:shadow-md transition-shadow duration-200">
      <CardContent className="p-5">
        <div className="flex items-start justify-between mb-3">
          <div className="flex-1">
            <h3 className="font-semibold text-base leading-tight">
              {redemption.reward?.name || "Reward"}
            </h3>
            <p className="text-sm text-muted-foreground line-clamp-2 mt-2 leading-relaxed">
              {redemption.reward?.description || ""}
            </p>
            {redemption.restaurantId && (
              <div className="flex items-center text-xs text-muted-foreground mt-2">
                <Store className="h-3 w-3 mr-1 text-primary" />
                <span className="font-medium">Restaurant reward</span>
              </div>
            )}
          </div>
          <Badge
            variant={
              redemption.isUsed
                ? "outline"
                : isExpired
                ? "destructive"
                : "default"
            }
            className="ml-3 whitespace-nowrap font-medium"
          >
            {redemption.isUsed ? (
              <span className="flex items-center">
                <Check className="h-3 w-3 mr-1" /> Used
              </span>
            ) : isExpired ? (
              "Expired"
            ) : (
              "Active"
            )}
          </Badge>
        </div>

        <div className="mt-4 p-4 bg-gradient-to-r from-muted to-muted/50 rounded-lg border">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Ticket className="h-4 w-4 mr-2 text-primary" />
              <span className="font-mono font-semibold text-lg tracking-wide">
                {redemption.code}
              </span>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={copyCodeToClipboard}
              title="Copy code"
              className="hover:bg-background/50"
            >
              <Copy className="h-4 w-4" />
            </Button>
          </div>
        </div>

        <div className="flex items-center justify-between mt-4 text-xs text-muted-foreground">
          <div className="font-medium">
            Redeemed on {format(redemption.redeemedAt.toDate(), "MMM d, yyyy")}
          </div>
          <div className="flex items-center">
            <Clock className="h-3 w-3 mr-1" />
            <span className="font-medium">
              {formatExpirationDate(redemption.expiresAt)}
            </span>
          </div>
        </div>

        {redemption.reward?.termsAndConditions && (
          <div className="mt-4 p-3 bg-muted/50 rounded-md text-xs text-muted-foreground">
            <strong className="text-foreground">Terms:</strong>{" "}
            {redemption.reward.termsAndConditions}
          </div>
        )}
      </CardContent>
    </Card>
  );
};
