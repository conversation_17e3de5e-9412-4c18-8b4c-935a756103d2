/**
 * Email templates for various notifications
 */

/**
 * Generate OTP verification email template
 */
const getOtpVerificationTemplate = (name, otp) => {
  return `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9f9f9;">
      <div style="background-color: #ffffff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
        <h1 style="color: #333333; text-align: center;">Verify Your Subscription</h1>
        <p style="color: #666666; line-height: 1.6;">Hello ${name || "there"},</p>
        <p style="color: #666666; line-height: 1.6;">Please use the following OTP to verify your subscription:</p>
        <h2 style="color: #ff6200; text-align: center; font-size: 24px; margin: 20px 0;">${otp}</h2>
        <p style="color: #666666; line-height: 1.6;">This code is valid for 10 minutes.</p>
      </div>
    </div>
  `;
};

/**
 * Generate subscription confirmation email template
 */
const getSubscriptionConfirmedTemplate = (name, unsubscribeLink, isResubscribe = false) => {
  return `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9f9f9;">
      <div style="background-color: #ffffff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
        <h1 style="color: #333333; text-align: center;">${isResubscribe ? "Welcome Back!" : "Thank You for Subscribing!"}</h1>
        <p style="color: #666666; line-height: 1.6;">Hello ${name || "there"},</p>
        <p style="color: #666666; line-height: 1.6;">You have successfully ${isResubscribe ? "resubscribed to" : "subscribed to"} our newsletter.</p>
        <p style="color: #666666; line-height: 1.6;">You'll now receive our latest updates and news.</p>
        <p style="color: #999999; font-size: 12px; text-align: center; margin-top: 20px;">
          If you no longer wish to receive these emails, you can
          <a href="${unsubscribeLink}" style="color: #999999; text-decoration: underline;">unsubscribe here</a>.
        </p>
      </div>
    </div>
  `;
};

/**
 * Generate unsubscription confirmation email template
 */
const getUnsubscriptionConfirmedTemplate = () => {
  return `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9f9f9;">
      <div style="background-color: #ffffff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
        <h1 style="color: #333333; text-align: center;">We're sorry to see you go!</h1>
        <p style="color: #666666; line-height: 1.6;">You have successfully unsubscribed from our newsletter.</p>
        <p style="color: #666666; line-height: 1.6;">If this was a mistake, you can resubscribe anytime.</p>
        <div style="text-align: center; margin-top: 20px;">
          <a href="https://qonai.me/subscribe" style="background-color: #ff6200; color: #ffffff; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
            Resubscribe
          </a>
        </div>
      </div>
    </div>
  `;
};

/**
 * Generate order notification email template
 */
const getOrderEmailTemplate = (data) => {
  const { recipientName, orderDetails, restaurantName, status, orderId } = data;

  let statusText = "";
  let actionText = "";

  switch (status) {
    case "pending":
      statusText = "New Order Received";
      actionText = "Your order has been received and is awaiting confirmation.";
      break;
    case "preparing":
      statusText = "Order Accepted";
      actionText = "Your order is now being prepared.";
      break;
    case "ready":
      statusText = "Order Ready";
      actionText = "Your order is ready for pickup at your table.";
      break;
    case "completed":
      statusText = "Order Completed";
      actionText = "Thank you for dining with us!";
      break;
    case "cancelled":
      statusText = "Order Cancelled";
      actionText = "Your order has been cancelled.";
      break;
    default:
      statusText = "Order Update";
      actionText = "There has been an update to your order.";
  }

  return `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9f9f9;">
      <div style="background-color: #ffffff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
        <h1 style="color: #333333; text-align: center;">${statusText}</h1>
        <p style="color: #666666; line-height: 1.6;">Hello ${recipientName || "there"},</p>
        <p style="color: #666666; line-height: 1.6;">${actionText}</p>

        <div style="margin: 20px 0; padding: 15px; background-color: #f5f5f5; border-radius: 5px;">
          <h3 style="margin-top: 0; color: #333333;">Order Details</h3>
          <p style="margin: 5px 0; color: #666666;"><strong>Order ID:</strong> ${orderId}</p>
          <p style="margin: 5px 0; color: #666666;"><strong>Restaurant:</strong> ${restaurantName}</p>
          <p style="margin: 5px 0; color: #666666;"><strong>Status:</strong> ${status}</p>

          ${orderDetails ? `
          <h4 style="margin: 15px 0 5px; color: #333333;">Items:</h4>
          <ul style="padding-left: 20px; color: #666666;">
            ${orderDetails.items.map(item => `
              <li>${item.quantity}x ${item.name} - ${item.price * item.quantity} AZN</li>
            `).join('')}
          </ul>
          <p style="margin: 10px 0; font-weight: bold; color: #333333;">Total: ${orderDetails.totalPrice} AZN</p>
          ` : ''}
        </div>

        <p style="color: #666666; line-height: 1.6;">Thank you for choosing ${restaurantName}!</p>
      </div>
    </div>
  `;
};

/**
 * Generate reservation notification email template
 */
const getReservationEmailTemplate = (data) => {
  const { recipientName, reservationDetails, restaurantName, status, reservationId } = data;

  let statusText = "";
  let actionText = "";

  switch (status) {
    case "pending":
      statusText = "New Reservation";
      actionText = "Your reservation has been received and is awaiting confirmation.";
      break;
    case "confirmed":
      statusText = "Reservation Confirmed";
      actionText = "Your reservation has been confirmed.";
      break;
    case "active":
      statusText = "Table Seated";
      actionText = "You have been seated at your table. Enjoy your meal!";
      break;
    case "completed":
      statusText = "Reservation Completed";
      actionText = "Thank you for dining with us!";
      break;
    case "cancelled":
      statusText = "Reservation Cancelled";
      actionText = "Your reservation has been cancelled.";
      break;
    case "no-show":
      statusText = "Missed Reservation";
      actionText = "You missed your reservation. Please contact the restaurant if you need to reschedule.";
      break;
    default:
      statusText = "Reservation Update";
      actionText = "There has been an update to your reservation.";
  }

  return `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9f9f9;">
      <div style="background-color: #ffffff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
        <h1 style="color: #333333; text-align: center;">${statusText}</h1>
        <p style="color: #666666; line-height: 1.6;">Hello ${recipientName || "there"},</p>
        <p style="color: #666666; line-height: 1.6;">${actionText}</p>

        <div style="margin: 20px 0; padding: 15px; background-color: #f5f5f5; border-radius: 5px;">
          <h3 style="margin-top: 0; color: #333333;">Reservation Details</h3>
          <p style="margin: 5px 0; color: #666666;"><strong>Reservation ID:</strong> ${reservationId}</p>
          <p style="margin: 5px 0; color: #666666;"><strong>Restaurant:</strong> ${restaurantName}</p>
          <p style="margin: 5px 0; color: #666666;"><strong>Status:</strong> ${status}</p>

          ${reservationDetails ? `
          <p style="margin: 5px 0; color: #666666;"><strong>Date:</strong> ${reservationDetails.date}</p>
          <p style="margin: 5px 0; color: #666666;"><strong>Time:</strong> ${reservationDetails.arrivalTime}</p>
          <p style="margin: 5px 0; color: #666666;"><strong>Party Size:</strong> ${reservationDetails.partySize}</p>
          ${reservationDetails.notes ? `<p style="margin: 5px 0; color: #666666;"><strong>Notes:</strong> ${reservationDetails.notes}</p>` : ''}
          ` : ''}
        </div>

        <p style="color: #666666; line-height: 1.6;">Thank you for choosing ${restaurantName}!</p>
      </div>
    </div>
  `;
};

/**
 * Generate referral email template
 */
const getReferralEmailTemplate = (data) => {
  const { referrerName, referralCode, referralLink, bonusPoints } = data;

  return `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9f9f9;">
      <div style="background-color: #ffffff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
        <h1 style="color: #333333; text-align: center;">You've Been Invited to Qonai!</h1>
        <p style="color: #666666; line-height: 1.6;">Hello,</p>
        <p style="color: #666666; line-height: 1.6;">${referrerName} thinks you'd love Qonai, the AI-based restaurant and menu recommendation platform that personalizes dining suggestions based on your preferences.</p>
        <p style="color: #666666; line-height: 1.6;">Discover restaurants tailored to your taste, dietary needs, and preferences with our smart recommendation engine, plus enjoy food delivery, loyalty rewards, and many more features!</p>

        <div style="background-color: #f5f5f5; border-radius: 8px; padding: 15px; margin: 20px 0;">
          <p style="color: #333333; font-weight: bold; margin-bottom: 10px;">Join with their referral code and you'll both get rewards:</p>
          <ul style="color: #666666; line-height: 1.6;">
            <li>You'll receive ${bonusPoints} bonus points when you sign up</li>
            <li>${referrerName} will earn points when you join</li>
            <li>Get personalized AI-powered restaurant and menu recommendations</li>
            <li>Access detailed nutrition information and dietary filters</li>
            <li>Schedule orders for future dates and times</li>
            <li>Earn points with every order, review, and referral</li>
          </ul>
        </div>

        <div style="text-align: center; margin: 25px 0;">
          <p style="font-weight: bold; color: #333333; margin-bottom: 10px;">Your Referral Code:</p>
          <div style="background-color: #f0f0f0; display: inline-block; padding: 10px 20px; border-radius: 4px; font-size: 18px; font-weight: bold; letter-spacing: 2px; color: #333333;">${referralCode}</div>
        </div>

        <div style="text-align: center; margin: 25px 0;">
          <a href="${referralLink}" style="background-color: #ff6200; color: #ffffff; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold; display: inline-block;">Join Qonai Now</a>
        </div>

        <p style="color: #666666; line-height: 1.6; text-align: center; font-size: 14px;">Or copy and paste this link into your browser:</p>
        <p style="color: #0066cc; line-height: 1.6; text-align: center; font-size: 14px; word-break: break-all;">${referralLink}</p>

        <hr style="border: none; border-top: 1px solid #eeeeee; margin: 30px 0;">

        <p style="color: #999999; font-size: 12px; text-align: center;">
          This invitation was sent to you by ${referrerName} through Qonai. If you don't want to receive these emails in the future, please ignore this message.
        </p>
      </div>
    </div>
  `;
};

module.exports = {
  getOtpVerificationTemplate,
  getSubscriptionConfirmedTemplate,
  getUnsubscriptionConfirmedTemplate,
  getOrderEmailTemplate,
  getReservationEmailTemplate,
  getReferralEmailTemplate
};
