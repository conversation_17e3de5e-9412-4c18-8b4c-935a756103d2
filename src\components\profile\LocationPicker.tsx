import { useState, useEffect } from "react";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  use<PERSON>ap<PERSON><PERSON>s,
  useMap,
} from "react-leaflet";
import { useGeolocated } from "react-geolocated";
import { Button } from "@/components/ui/button";
import { Loading } from "@/components/ui/loading";
import { toast } from "sonner";
import "leaflet/dist/leaflet.css";
import L from "leaflet";

// Fix Leaflet default marker icon issue
delete (L.Icon.Default.prototype as { _getIconUrl?: string })._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl:
    "https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-icon-2x.png",
  iconUrl:
    "https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-icon.png",
  shadowUrl:
    "https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-shadow.png",
});

interface LocationPickerProps {
  initialLocation?: { latitude: number; longitude: number };
  onLocationSelect: (lat: number, lng: number) => Promise<void>;
}

const MapView: React.FC<{ center: [number, number]; zoom: number }> = ({
  center,
  zoom,
}) => {
  const map = useMap();

  useEffect(() => {
    map.setView(center, zoom);
  }, [center, zoom, map]);

  return null;
};

export const LocationPicker: React.FC<LocationPickerProps> = ({
  initialLocation,
  onLocationSelect,
}) => {
  const { coords, isGeolocationAvailable, isGeolocationEnabled } =
    useGeolocated({
      positionOptions: {
        enableHighAccuracy: true,
        maximumAge: 0,
        timeout: 5000,
      },
      watchPosition: false,
      userDecisionTimeout: undefined,
      geolocationProvider: navigator.geolocation,
      isOptimisticGeolocationEnabled: true,
    });

  const [position, setPosition] = useState<[number, number]>(() => {
    if (initialLocation) {
      return [initialLocation.latitude, initialLocation.longitude];
    }
    return [40.3777, 49.892]; // Default location (Central Baku)
  });

  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (initialLocation) {
      setPosition([initialLocation.latitude, initialLocation.longitude]);
    } else if (coords) {
      setPosition([coords.latitude, coords.longitude]);
      onLocationSelect(coords.latitude, coords.longitude);
    }
  }, [initialLocation, coords, onLocationSelect]);

  const handleGetLocation = () => {
    setIsLoading(true);
    if (!navigator.geolocation) {
      toast.error("Geolocation is not supported by your browser");
      setIsLoading(false);
      return;
    }

    navigator.geolocation.getCurrentPosition(
      async (position) => {
        const { latitude, longitude } = position.coords;
        setPosition([latitude, longitude]);
        await onLocationSelect(latitude, longitude);
        setIsLoading(false);
      },
      (error) => {
        console.error("Error getting location:", error);
        toast.error("Could not get your location. Please try again.");
        setIsLoading(false);
      },
      {
        enableHighAccuracy: true,
        timeout: 5000,
        maximumAge: 0,
      }
    );
  };

  const MapEvents = () => {
    useMapEvents({
      async click(e) {
        const { lat, lng } = e.latlng;
        setPosition([lat, lng]);
        await onLocationSelect(lat, lng);
      },
    });
    return null;
  };

  return (
    <div className="relative w-full h-[400px] rounded-lg overflow-hidden border">
      {!isGeolocationAvailable && (
        <div className="absolute top-0 left-0 right-0 z-10 bg-yellow-100 p-2 text-sm text-yellow-800">
          Your browser does not support Geolocation
        </div>
      )}
      {!isGeolocationEnabled && isGeolocationAvailable && (
        <div className="absolute top-0 left-0 right-0 z-10 bg-yellow-100 p-2 text-sm text-yellow-800">
          Please enable location services to use your current location
        </div>
      )}
      <div className="absolute top-2 right-2 z-[1000]">
        <Button
          variant="secondary"
          size="sm"
          onClick={handleGetLocation}
          disabled={isLoading || !isGeolocationAvailable}
        >
          {isLoading ? (
            <>
              <Loading type="default" />
              <span className="ml-2">Getting location...</span>
            </>
          ) : (
            <>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                className="mr-2 h-4 w-4"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 8c-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4-1.79-4-4-4zm8.94 3c-.46-4.17-3.77-7.48-7.94-7.94V2c0-.55-.45-1-1-1s-1 .45-1 1v1.06C6.83 3.52 3.52 6.83 3.06 11H2c-.55 0-1 .45-1 1s.45 1 1 1h1.06c.46 4.17 3.77 7.48 7.94 7.94V22c0 .55.45 1 1 1s1-.45 1-1v-1.06c4.17-.46 7.48-3.77 7.94-7.94H22c.55 0 1-.45 1-1s-.45-1-1-1h-1.06zM12 19c-3.87 0-7-3.13-7-7s3.13-7 7-7 7 3.13 7 7-3.13 7-7 7z"
                />
              </svg>
              Get My Location
            </>
          )}
        </Button>
      </div>
      <MapContainer
        center={position}
        zoom={13}
        style={{ height: "100%", width: "100%" }}
        className="z-0"
      >
        <MapView center={position} zoom={13} />
        <TileLayer
          url="https://{s}.google.com/vt/lyrs=m&x={x}&y={y}&z={z}"
          maxZoom={20}
          subdomains={["mt0", "mt1", "mt2", "mt3"]}
          attribution="Map data © Google"
        />
        <Marker position={position} />
        <MapEvents />
      </MapContainer>
    </div>
  );
};
