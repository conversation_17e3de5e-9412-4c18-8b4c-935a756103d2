import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";

import { useAuth } from "@/providers/AuthProvider";
import { RewardsMarketplace } from "@/components/rewards/RewardsMarketplace";

interface RewardsCardProps {
  onRewardRedeemed?: () => void;
  userPoints: number;
}

export const RewardsCard: React.FC<RewardsCardProps> = ({
  onRewardRedeemed,
  userPoints,
}) => {
  const { user } = useAuth();

  if (!user) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>
            <Skeleton className="h-8 w-48" />
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Skeleton className="h-8 w-full mb-4" />
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {Array.from({ length: 4 }).map((_, index) => (
              <Skeleton key={index} className="h-32 w-full" />
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <RewardsMarketplace
      userPoints={userPoints}
      onRewardRedeemed={onRewardRedeemed}
    />
  );
};
