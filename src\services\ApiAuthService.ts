/**
 * API Authentication Service
 *
 * Handles secure authentication between the Qonai frontend and the Newsletter API
 */

import { auth } from "@/config/firebase";

// Types
interface ApiAuthOptions {
  maxRetries?: number;
  retryDelay?: number;
  timeout?: number;
}

interface ApiRequestOptions extends RequestInit {
  timeout?: number;
}

interface ApiError extends Error {
  status?: number;
  data?: Record<string, unknown>;
  isNetworkError?: boolean;
  isTimeoutError?: boolean;
}

// Default configuration
const DEFAULT_OPTIONS: ApiAuthOptions = {
  maxRetries: 3,
  retryDelay: 1000,
  timeout: 10000,
};

// Environment variables
const API_BASE_URL =
  import.meta.env.VITE_APP_API_BASE_URL || "http://localhost:3000";
const NEWSLETTER_API_KEY = import.meta.env.VITE_APP_NEWSLETTER_API_KEY;
const NEWSLETTER_API_SECRET = import.meta.env.VITE_APP_NEWSLETTER_API_SECRET;

/**
 * Creates an AbortController with timeout
 * @param timeout Timeout in milliseconds
 * @returns AbortController and signal
 */
const createAbortController = (
  timeout?: number
): { controller: AbortController; signal: AbortSignal } => {
  const controller = new AbortController();
  const { signal } = controller;

  if (timeout) {
    setTimeout(() => controller.abort(), timeout);
  }

  return { controller, signal };
};

/**
 * Generates a secure JWT token for API authentication
 * @returns Promise with the JWT token
 */
const generateApiToken = async (): Promise<string> => {
  try {
    // If we have a Firebase user, use their token for authentication
    const currentUser = auth.currentUser;
    if (currentUser) {
      const idToken = await currentUser.getIdToken(true);
      return idToken;
    }

    // If no user is logged in, use API key/secret pair
    if (NEWSLETTER_API_KEY && NEWSLETTER_API_SECRET) {
      // In a real implementation, we would generate a JWT here
      // For now, we'll use the API key as a fallback
      return NEWSLETTER_API_KEY;
    }

    throw new Error("No authentication method available");
  } catch (error) {
    console.error("Error generating API token:", error);
    throw error;
  }
};

/**
 * Makes an authenticated API request with retry logic
 * @param url API endpoint
 * @param options Request options
 * @param authOptions Authentication options
 * @returns Promise with the response data
 */
export const apiRequest = async <T>(
  url: string,
  options: ApiRequestOptions = {},
  authOptions: ApiAuthOptions = {}
): Promise<T> => {
  const { maxRetries, retryDelay, timeout } = {
    ...DEFAULT_OPTIONS,
    ...authOptions,
  };
  let attempts = 0;

  // Ensure URL is absolute and uses HTTPS
  const fullUrl = url.startsWith("http")
    ? url
    : `${API_BASE_URL}${url.startsWith("/") ? url : `/${url}`}`;

  if (
    !fullUrl.startsWith("https://") &&
    process.env.NODE_ENV === "production"
  ) {
    console.warn("Non-HTTPS URL detected in production environment:", fullUrl);
  }

  // Retry loop
  while (attempts < (maxRetries || 1)) {
    attempts++;

    try {
      // Set up abort controller for timeout
      const { signal } = createAbortController(timeout);

      // Get authentication token
      const token = await generateApiToken();

      // Prepare headers
      const headers = new Headers(options.headers);
      headers.set("X-API-Key", token);
      headers.set("Content-Type", "application/json");

      // Make the request
      const response = await fetch(fullUrl, {
        ...options,
        headers,
        signal,
      });

      // Handle non-2xx responses
      if (!response.ok) {
        const errorData = await response.json().catch(() => null);
        const error = new Error(
          `API error: ${response.status} ${response.statusText}`
        ) as ApiError;
        error.status = response.status;
        error.data = errorData;
        throw error;
      }

      // Parse and return response
      return await response.json();
    } catch (error) {
      const err = error as Error;
      // Handle timeout errors
      if (err.name === "AbortError") {
        const timeoutError = new Error("Request timeout") as ApiError;
        timeoutError.isTimeoutError = true;
        throw timeoutError;
      }

      // Handle network errors
      if (!navigator.onLine || err.message.includes("NetworkError")) {
        const networkError = new Error("Network error") as ApiError;
        networkError.isNetworkError = true;

        // If this is the last attempt, throw the error
        if (attempts >= (maxRetries || 1)) {
          throw networkError;
        }

        // Otherwise wait and retry
        await new Promise((resolve) => setTimeout(resolve, retryDelay));
        continue;
      }

      // For 401/403 errors, don't retry
      if (
        (error as ApiError).status === 401 ||
        (error as ApiError).status === 403
      ) {
        throw error;
      }

      // For other errors, retry if we have attempts left
      if (attempts >= (maxRetries || 1)) {
        throw error;
      }

      // Wait before retrying
      await new Promise((resolve) => setTimeout(resolve, retryDelay));
    }
  }

  // This should never be reached due to the throw in the loop
  throw new Error("Maximum retry attempts exceeded");
};

export default {
  apiRequest,
  generateApiToken,
};
