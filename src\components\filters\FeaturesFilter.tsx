
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { RESTAURANT_OPTIONS } from '@/types';
import { ScrollArea } from '@/components/ui/scroll-area';

interface FeaturesFilterProps {
  value: string[];
  onChange: (value: string[]) => void;
  counts?: Record<string, number>;
}

export function FeaturesFilter({ value, onChange, counts }: FeaturesFilterProps) {
  const handleCheckboxChange = (checked: boolean | 'indeterminate', option: string) => {
    if (checked === 'indeterminate') return;

    if (checked) {
      onChange([...value, option]);
    } else {
      onChange(value.filter(item => item !== option));
    }
  };

  return (
    <div className="space-y-2">
      <p className="text-sm font-medium mb-2">Restaurant Features</p>
      <ScrollArea className="h-[200px] pr-4">
        <div className="space-y-2">
          {RESTAURANT_OPTIONS.features.map((option) => (
            <div key={option} className="flex items-center space-x-2">
              <Checkbox
                id={`feature-${option}`}
                checked={value.includes(option)}
                onCheckedChange={(checked) => handleCheckboxChange(checked, option)}
              />
              <Label
                htmlFor={`feature-${option}`}
                className="text-sm flex-1 cursor-pointer"
              >
                {option}
              </Label>
              {counts && counts[option] !== undefined && (
                <span className="text-xs text-muted-foreground">
                  ({counts[option]})
                </span>
              )}
            </div>
          ))}
        </div>
      </ScrollArea>
    </div>
  );
}
