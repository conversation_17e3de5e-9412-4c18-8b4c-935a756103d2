{"name": "qonai-newsletter-api", "version": "1.0.0", "description": "Newsletter and notification API for Qonai", "main": "server.js", "scripts": {"start": "node server.js", "start:legacy": "node index.js", "dev": "nodemon server.js", "dev:legacy": "nodemon index.js", "test": "echo \"No test specified\" && exit 0", "lint": "eslint .", "lint:fix": "eslint . --fix"}, "keywords": ["newsletter", "email", "notifications", "api"], "author": "Qonai Team", "license": "MIT", "type": "commonjs", "dependencies": {"body-parser": "^1.20.3", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.2", "express-rate-limit": "^7.5.0", "firebase-admin": "^13.1.0", "helmet": "^8.1.0", "joi": "^17.12.2", "mongoose": "^8.12.1", "morgan": "^1.10.0", "nodemailer": "^6.11.0", "winston": "^3.12.0", "xss": "^1.0.15"}, "devDependencies": {"eslint": "^8.57.0", "jest": "^29.7.0", "nodemon": "^3.1.0", "supertest": "^6.3.4"}, "engines": {"node": ">=18.0.0"}}