/**
 * Utility functions for social sharing features
 */

/**
 * Generate a shareable URL for a restaurant
 * @param username The restaurant's username
 * @returns The full URL to the restaurant page
 */
export const getRestaurantShareUrl = (username: string): string => {
  return `${window.location.origin}/restaurants/${username}`;
};

/**
 * Generate a shareable URL for a menu item
 * @param restaurantUsername The restaurant's username
 * @param itemId The menu item's ID
 * @returns The full URL to the menu item
 */
export const getMenuItemShareUrl = (restaurantUsername: string, itemId: string): string => {
  return `${window.location.origin}/restaurants/${restaurantUsername}?item=${itemId}`;
};

/**
 * Generate a shareable URL for an order
 * @param restaurantId The restaurant's ID
 * @param orderId The order's ID
 * @returns The full URL to the order
 */
export const getOrderShareUrl = (restaurantId: string, orderId: string): string => {
  return `${window.location.origin}/receipts/${restaurantId}/${orderId}`;
};

/**
 * Generate hashtags for sharing
 * @param baseHashtags Base hashtags to include
 * @param additionalTags Additional tags to include
 * @returns Array of formatted hashtags
 */
export const generateHashtags = (
  baseHashtags: string[] = ["Qonai", "Food", "Restaurant"],
  additionalTags: string[] = []
): string[] => {
  // Combine base and additional tags, remove spaces, and ensure no duplicates
  return [...new Set([...baseHashtags, ...additionalTags])]
    .map(tag => tag.replace(/\s+/g, ""))
    .filter(tag => tag.length > 0);
};

/**
 * Extract parameters from URL for deep linking
 * @param url The URL to parse
 * @returns Object containing extracted parameters
 */
export const extractDeepLinkParams = (url: string): Record<string, string> => {
  const params: Record<string, string> = {};
  try {
    const urlObj = new URL(url);
    const searchParams = new URLSearchParams(urlObj.search);
    
    // Extract all search parameters
    for (const [key, value] of searchParams.entries()) {
      params[key] = value;
    }
    
    // Extract path parameters
    const pathParts = urlObj.pathname.split('/').filter(part => part.length > 0);
    if (pathParts[0] === 'restaurants' && pathParts.length > 1) {
      params['username'] = pathParts[1];
    } else if (pathParts[0] === 'receipts' && pathParts.length > 2) {
      params['restaurantId'] = pathParts[1];
      params['orderId'] = pathParts[2];
    }
  } catch (error) {
    console.error('Error parsing URL:', error);
  }
  
  return params;
};
