import { useState, useEffect } from "react";
import { useLocation } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";

export function ResubscribePage() {
  const [email, setEmail] = useState("");
  const [token, setToken] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const location = useLocation();

  useEffect(() => {
    // Extract email and token from URL query parameters
    const params = new URLSearchParams(location.search);
    const emailParam = params.get("email");
    const tokenParam = params.get("token");

    if (emailParam) {
      setEmail(emailParam);
    }

    if (tokenParam) {
      setToken(tokenParam);
    }
  }, [location]);

  const handleResubscribe = async () => {
    if (!email) {
      toast.error("Email is required");
      return;
    }

    setIsSubmitting(true);

    try {
      const response = await fetch(
        "http://localhost:3000/api/newsletter/subscribe",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            email,
            token: token || undefined,
          }),
        }
      );

      if (!response.ok) {
        try {
          const errorData = await response.json();
          throw new Error(errorData.error || "Failed to resubscribe");
        } catch {
          const text = await response.text();
          throw new Error(
            `Failed to resubscribe: ${response.status} ${
              text || response.statusText
            }`
          );
        }
      }

      await response.json(); // We don't need the response data
      toast.success("You have successfully resubscribed to our newsletter!");
      setIsSuccess(true);
    } catch (error) {
      console.error("Error resubscribing:", error);
      toast.error(
        error instanceof Error
          ? error.message
          : "Failed to resubscribe. Please try again."
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className=" mx-auto py-16 px-4">
      <Card className="max-w-md mx-auto">
        <CardHeader>
          <CardTitle className="text-2xl font-bold text-center">
            Resubscribe to Newsletter
          </CardTitle>
          <CardDescription className="text-center">
            {isSuccess
              ? "Thank you for resubscribing to our newsletter!"
              : "Please confirm your email address to resubscribe to our newsletter."}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {!isSuccess ? (
            <div className="space-y-4">
              <div className="text-center text-gray-700">
                <p>
                  Email: <span className="font-medium">{email}</span>
                </p>
              </div>
              <div className="flex justify-center">
                <Button
                  onClick={handleResubscribe}
                  disabled={isSubmitting}
                  className="bg-orange-500 hover:bg-orange-600 w-full"
                >
                  {isSubmitting ? "Processing..." : "Confirm Resubscription"}
                </Button>
              </div>
            </div>
          ) : (
            <div className="text-center space-y-4">
              <p className="text-green-600">
                You are now subscribed to receive our latest updates and offers.
              </p>
              <Button
                onClick={() => (window.location.href = "/")}
                className="bg-orange-500 hover:bg-orange-600"
              >
                Return to Home
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
