import {
  ClientDetails,
  CalorieCalculator as CalorieCalculatorType,
  MealPreferences as MealPreferencesType,
  Address as AddressType,
  DietaryGoal as DietaryGoalType,
  NotificationPreferences as NotificationPreferencesType,
} from "@/types";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { CalorieCalculator } from "./CalorieCalculator";
import { MealPreferences } from "./MealPreferences";
import { AddressManager } from "./AddressManager";
import { DietaryGoals } from "./DietaryGoals";
import { NotificationPreferences } from "./NotificationPreferences";
import { useState } from "react";
import { toast } from "sonner";

interface ClientProfileEditProps {
  editData: ClientDetails;
  setEditData: (data: ClientDetails) => void;
}

export const ClientProfileEdit = ({
  editData,
  setEditData,
}: ClientProfileEditProps) => {
  const [activeTab, setActiveTab] = useState("basic");

  const handleSaveCalorieCalculator = (
    calculatorData: CalorieCalculatorType
  ) => {
    setEditData({
      ...editData,
      calorieCalculator: calculatorData,
    });
    toast.success("Calorie calculator data saved");
  };

  const handleSaveMealPreferences = (preferencesData: MealPreferencesType) => {
    setEditData({
      ...editData,
      mealPreferences: preferencesData,
    });
    toast.success("Meal preferences saved");
  };

  const handleSaveAddresses = (addresses: AddressType[]) => {
    setEditData({
      ...editData,
      addresses,
    });
    toast.success("Addresses saved");
  };

  const handleSaveDietaryGoals = (goals: DietaryGoalType[]) => {
    setEditData({
      ...editData,
      dietaryGoals: goals,
    });
    toast.success("Dietary goals saved");
  };

  const handleSaveNotificationPreferences = (
    preferences: NotificationPreferencesType
  ) => {
    setEditData({
      ...editData,
      notificationPreferences: preferences,
    });
    toast.success("Notification preferences saved");
  };

  return (
    <div className="space-y-6">
      <Tabs
        defaultValue="basic"
        value={activeTab}
        onValueChange={setActiveTab}
        className="w-full"
      >
        <TabsList className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-6 mb-6 overflow-x-auto">
          <TabsTrigger
            value="basic"
            className="text-xs md:text-sm whitespace-normal text-center h-auto py-2"
          >
            Basic Info
          </TabsTrigger>
          <TabsTrigger
            value="addresses"
            className="text-xs md:text-sm whitespace-normal text-center h-auto py-2"
          >
            Addresses
          </TabsTrigger>
          <TabsTrigger
            value="meal-preferences"
            className="text-xs md:text-sm whitespace-normal text-center h-auto py-2"
          >
            Meal Preferences
          </TabsTrigger>
          <TabsTrigger
            value="calorie-calculator"
            className="text-xs md:text-sm whitespace-normal text-center h-auto py-2"
          >
            Calorie Calculator
          </TabsTrigger>
          <TabsTrigger
            value="dietary-goals"
            className="text-xs md:text-sm whitespace-normal text-center h-auto py-2"
          >
            Dietary Goals
          </TabsTrigger>
          <TabsTrigger
            value="notifications"
            className="text-xs md:text-sm whitespace-normal text-center h-auto py-2"
          >
            Notifications
          </TabsTrigger>
        </TabsList>

        <TabsContent value="basic" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Basic Information</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="username" className="text-right">
                    Username
                  </Label>
                  <Input
                    id="username"
                    value={editData?.username || ""}
                    onChange={(e) =>
                      setEditData({
                        ...editData,
                        username: e.target.value,
                      })
                    }
                    className="col-span-3"
                  />
                </div>

                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="firstName" className="text-right">
                    First Name
                  </Label>
                  <Input
                    id="firstName"
                    value={editData?.firstName || ""}
                    onChange={(e) =>
                      setEditData({
                        ...editData,
                        firstName: e.target.value,
                      })
                    }
                    className="col-span-3"
                  />
                </div>

                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="lastName" className="text-right">
                    Last Name
                  </Label>
                  <Input
                    id="lastName"
                    value={editData?.lastName || ""}
                    onChange={(e) =>
                      setEditData({
                        ...editData,
                        lastName: e.target.value,
                      })
                    }
                    className="col-span-3"
                  />
                </div>

                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="phone" className="text-right">
                    Phone
                  </Label>
                  <Input
                    id="phone"
                    value={editData?.phone || ""}
                    onChange={(e) =>
                      setEditData({
                        ...editData,
                        phone: e.target.value,
                      })
                    }
                    className="col-span-3"
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="addresses">
          <AddressManager
            addresses={editData.addresses || []}
            onSave={handleSaveAddresses}
          />
        </TabsContent>

        <TabsContent value="meal-preferences">
          <MealPreferences
            initialData={editData.mealPreferences}
            onSave={handleSaveMealPreferences}
          />
        </TabsContent>

        <TabsContent value="calorie-calculator">
          <CalorieCalculator
            initialData={editData.calorieCalculator}
            onSave={handleSaveCalorieCalculator}
          />
        </TabsContent>

        <TabsContent value="dietary-goals">
          <DietaryGoals
            goals={editData.dietaryGoals || []}
            onSave={handleSaveDietaryGoals}
          />
        </TabsContent>

        <TabsContent value="notifications">
          <NotificationPreferences
            initialData={editData.notificationPreferences}
            onSave={handleSaveNotificationPreferences}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
};
