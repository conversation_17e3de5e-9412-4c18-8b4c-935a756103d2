/**
 * Newsletter controller for handling newsletter subscriptions and sending
 */
const firestoreService = require("../services/firestoreService");
const emailService = require("../services/emailService");

/**
 * Subscribe to newsletter
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const subscribe = async (req, res) => {
  try {
    const { email, name } = req.body;

    // Validate required fields
    if (!email) {
      return res.status(400).json({ error: "Email is required" });
    }

    if (!name) {
      return res.status(400).json({ error: "Name is required" });
    }

    // Check if already subscribed
    const existingSubscriber = await firestoreService.getSubscriberByEmail(
      email
    );

    if (existingSubscriber && existingSubscriber.active) {
      return res.status(400).json({ error: "Email is already subscribed" });
    }

    // Send OTP verification email
    const { success, otp } = await emailService.sendOtpVerificationEmail(
      email,
      name
    );

    if (!success) {
      return res
        .status(500)
        .json({ error: "Failed to send verification email" });
    }

    // Create pending subscription
    await firestoreService.createPendingSubscription(email, otp, name);

    res.status(200).json({
      message: "Verification email sent",
      email,
    });
  } catch (error) {
    console.error("Error in subscribe:", error);
    res.status(500).json({ error: "Internal server error" });
  }
};

/**
 * Verify OTP and complete subscription
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const verifySubscription = async (req, res) => {
  try {
    const { email, otp } = req.body;

    if (!email || !otp) {
      return res.status(400).json({ error: "Email and OTP are required" });
    }

    // Verify OTP
    const pendingSubscription = await firestoreService.verifyOtp(email, otp);

    if (!pendingSubscription) {
      return res.status(400).json({ error: "Invalid or expired OTP" });
    }

    // Ensure name is present
    if (!pendingSubscription.name) {
      return res
        .status(400)
        .json({ error: "Name is required for subscription" });
    }

    // Check if already subscribed
    const existingSubscriber = await firestoreService.getSubscriberByEmail(
      email
    );
    const isResubscribe = existingSubscriber && !existingSubscriber.active;

    // Create or reactivate subscriber
    const subscriberId = await firestoreService.createSubscriber({
      email,
      name: pendingSubscription.name,
    });

    // Get subscriber with token
    const subscriber = await firestoreService.getSubscriberByEmail(email);

    // Send confirmation email
    await emailService.sendSubscriptionConfirmationEmail(
      email,
      subscriber.name,
      subscriber.unsubscribeToken,
      isResubscribe
    );

    // Delete the pending subscription after successful verification
    try {
      await firestoreService.cleanupPendingSubscriptions(email);
    } catch (cleanupError) {
      // Log but don't fail the request if cleanup fails
      console.warn(
        `Failed to clean up pending subscription for ${email}:`,
        cleanupError
      );
    }

    // Run cleanup of expired subscriptions in the background
    firestoreService.cleanupExpiredSubscriptions().catch((err) => {
      console.warn("Background cleanup of expired subscriptions failed:", err);
    });

    res.status(200).json({
      message: "Subscription successful",
      subscriberId,
    });
  } catch (error) {
    console.error("Error in verifySubscription:", error);
    res.status(500).json({ error: "Internal server error" });
  }
};

/**
 * Unsubscribe from newsletter
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const unsubscribe = async (req, res) => {
  try {
    const { token } = req.query;

    if (!token) {
      return res.status(400).json({ error: "Token is required" });
    }

    // Get subscriber by token
    const subscriber = await firestoreService.getSubscriberByToken(token);

    if (!subscriber) {
      return res.status(404).json({ error: "Subscriber not found" });
    }

    // Unsubscribe
    await firestoreService.unsubscribeByToken(token);

    // Send confirmation email
    await emailService.sendUnsubscriptionConfirmationEmail(subscriber.email);

    res.status(200).json({ message: "Unsubscribed successfully" });
  } catch (error) {
    console.error("Error in unsubscribe:", error);
    res.status(500).json({ error: "Internal server error" });
  }
};

/**
 * List all subscribers
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const listSubscribers = async (req, res) => {
  try {
    const { active } = req.query;

    // Prepare filter options
    const options = {};
    if (active !== undefined) {
      options.activeOnly = active === "true";
    }

    // Use the firestoreService to get subscribers
    const subscribers = await firestoreService.listSubscribers(options);

    // Log the count
    console.log(`Retrieved ${subscribers.length} subscribers`);

    // Return results
    res.status(200).json(subscribers);
  } catch (error) {
    console.error("Error in listSubscribers:", error);
    res.status(500).json({
      error: "Failed to retrieve subscribers",
      details: error.message,
    });
  }
};

module.exports = {
  subscribe,
  verifySubscription,
  unsubscribe,
  listSubscribers,
};
