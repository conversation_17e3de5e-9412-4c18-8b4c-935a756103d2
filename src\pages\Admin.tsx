import { useAuth } from "@/providers/AuthProvider";
import { <PERSON> } from "react-router-dom";
import { Loading } from "@/components/ui/loading";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  AlertCircle,
  Shield,
  Award,
  Palette,
  Wrench,
  Users,
  Database,
  BarChart,
} from "lucide-react";

export const Admin = () => {
  const { user, loading, error, clearError } = useAuth();

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loading />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen p-4">
        <AlertCircle className="h-12 w-12 text-destructive mb-4" />
        <h1 className="text-2xl font-bold mb-2">Error</h1>
        <p className="text-muted-foreground mb-4">
          {error ? error.toString() : "An error occurred"}
        </p>
        <Button onClick={clearError}>Try Again</Button>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen p-4">
        <AlertCircle className="h-12 w-12 text-destructive mb-4" />
        <h1 className="text-2xl font-bold mb-2">Access Denied</h1>
        <p className="text-muted-foreground mb-4">
          Please log in to access this page.
        </p>
        <Button asChild>
          <Link to="/login">Log In</Link>
        </Button>
      </div>
    );
  }

  if (user.email !== "<EMAIL>") {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen p-4">
        <AlertCircle className="h-12 w-12 text-destructive mb-4" />
        <h1 className="text-2xl font-bold mb-2">Access Denied</h1>
        <p className="text-muted-foreground mb-4">
          You don't have permission to access this page.
        </p>
        <Button asChild>
          <Link to="/">Go Home</Link>
        </Button>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="mb-8">
        <div className="flex items-center mb-4">
          <Shield className="h-6 w-6 text-primary mr-2" />
          <h1 className="text-3xl font-bold">Qonai Admin Panel</h1>
        </div>
        <p className="text-muted-foreground">
          Manage and monitor the Qonai platform
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* Global Rewards Card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Award className="h-5 w-5 text-primary mr-2" />
              Global Rewards
            </CardTitle>
            <CardDescription>
              Manage platform-wide loyalty rewards
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Create and manage global rewards that are available to all users
              across the platform.
            </p>
          </CardContent>
          <CardFooter>
            <Button asChild className="w-full">
              <Link to="/admin-rewards">Manage Global Rewards</Link>
            </Button>
          </CardFooter>
        </Card>

        {/* Email Templates Card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Palette className="h-5 w-5 text-primary mr-2" />
              Email Templates
            </CardTitle>
            <CardDescription>
              Manage email templates and campaigns
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Create, edit, and send email templates for marketing campaigns and
              notifications.
            </p>
          </CardContent>
          <CardFooter>
            <Button asChild className="w-full">
              <Link to="/email-panel">Manage Email Templates</Link>
            </Button>
          </CardFooter>
        </Card>

        {/* Admin Tools Card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Wrench className="h-5 w-5 text-primary mr-2" />
              Admin Tools
            </CardTitle>
            <CardDescription>Maintenance and utility tools</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Access utility tools for database maintenance, data updates, and
              system operations.
            </p>
          </CardContent>
          <CardFooter>
            <Button asChild className="w-full">
              <Link to="/admin-tools">Access Tools</Link>
            </Button>
          </CardFooter>
        </Card>

        {/* User Management Card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="h-5 w-5 text-primary mr-2" />
              User Management
            </CardTitle>
            <CardDescription>Manage users and permissions</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              View and manage user accounts, roles, and permissions across the
              platform.
            </p>
          </CardContent>
          <CardFooter>
            <Button asChild className="w-full" variant="outline">
              <Link to="/admin-users">Manage Users</Link>
            </Button>
          </CardFooter>
        </Card>

        {/* Database Management Card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Database className="h-5 w-5 text-primary mr-2" />
              Database Management
            </CardTitle>
            <CardDescription>Manage database collections</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              View and manage database collections, perform backups, and data
              migrations.
            </p>
          </CardContent>
          <CardFooter>
            <Button asChild className="w-full" variant="outline">
              <Link to="/admin-database">Manage Database</Link>
            </Button>
          </CardFooter>
        </Card>

        {/* Analytics Card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <BarChart className="h-5 w-5 text-primary mr-2" />
              Platform Analytics
            </CardTitle>
            <CardDescription>View platform-wide analytics</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Access comprehensive analytics and reports for the entire
              platform.
            </p>
          </CardContent>
          <CardFooter>
            <Button asChild className="w-full" variant="outline">
              <Link to="/admin-analytics">View Analytics</Link>
            </Button>
          </CardFooter>
        </Card>
      </div>
    </div>
  );
};

export default Admin;
