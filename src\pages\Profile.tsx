import { useState, useEffect } from "react";
import { useAuth } from "@/providers/AuthProvider";
import { firestore } from "@/config/firebase";
import { doc, getDoc, updateDoc, Timestamp } from "firebase/firestore";
import { toast } from "sonner";
import { ClientDetails, RestaurantDetails } from "@/types";
import { RestaurantProfileView } from "@/components/profile/RestaurantProfileView";
import { RestaurantProfileEdit } from "@/components/profile/RestaurantProfileEdit";
import { ClientProfileView } from "@/components/profile/ClientProfileView";
import { ClientProfileEdit } from "@/components/profile/ClientProfileEdit";
import { Button } from "@/components/ui/button";
import { Loading } from "@/components/ui/loading";
import { useDebounce } from "@/hooks/useDebounce";

type UserDetails = ClientDetails | RestaurantDetails;

export const Profile = () => {
  const { user, userRole } = useAuth();
  const [userData, setUserData] = useState<UserDetails | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [editData, setEditData] = useState<UserDetails | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  const [addressQuery, setAddressQuery] = useState("");
  const [addressResults, setAddressResults] = useState([]);
  const [isLoadingAddress, setIsLoadingAddress] = useState(false);
  const [mapPosition, setMapPosition] = useState<[number, number] | null>(null);

  const debouncedAddressQuery = useDebounce(addressQuery, 500);

  useEffect(() => {
    const fetchUserData = async () => {
      if (!user) return;

      try {
        const userDoc = await getDoc(doc(firestore, `${userRole}s`, user.uid));
        if (userDoc.exists()) {
          setUserData(userDoc.data() as UserDetails);
        }
      } catch (error) {
        console.error("Error fetching user data:", error);
        toast.error("Failed to load profile data");
      }
    };

    fetchUserData();
  }, [user, userRole]);

  useEffect(() => {
    if (userData && !editData) {
      setEditData(userData);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [userData]);

  useEffect(() => {
    const searchAddress = async () => {
      if (!debouncedAddressQuery) {
        setAddressResults([]);
        return;
      }

      setIsLoadingAddress(true);
      try {
        const response = await fetch(
          `https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(
            debouncedAddressQuery
          )}`
        );
        const data = await response.json();
        setAddressResults(data);
      } catch (error) {
        console.error("Error searching address:", error);
        toast.error("Failed to search address");
      } finally {
        setIsLoadingAddress(false);
      }
    };

    searchAddress();
  }, [debouncedAddressQuery]);

  const getAddressFromCoordinates = async (lat: number, lng: number) => {
    try {
      const response = await fetch(
        `https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}`
      );
      const data = await response.json();
      return data.display_name;
    } catch (error) {
      console.error("Error getting address:", error);
      toast.error("Failed to get address from location");
      return null;
    }
  };

  const handleSave = async () => {
    if (!user || !editData) return;

    setIsSaving(true);
    try {
      const dataToUpdate = { ...editData };
      delete (dataToUpdate as { createdAt?: Date | Timestamp }).createdAt;

      // Check if all required fields are filled for restaurant
      if (userRole === "restaurant") {
        const isComplete = !!((dataToUpdate as RestaurantDetails).restaurantName &&
          (dataToUpdate as RestaurantDetails).cuisines?.length > 0 &&
          (dataToUpdate as RestaurantDetails).categories?.length > 0 &&
          (dataToUpdate as RestaurantDetails).address &&
          (dataToUpdate as RestaurantDetails).phone &&
          (dataToUpdate as RestaurantDetails).workingHours?.length > 0 &&
          (dataToUpdate as RestaurantDetails).location &&
          (dataToUpdate as RestaurantDetails).location?.latitude &&
          (dataToUpdate as RestaurantDetails).location?.longitude);

        if (isComplete) {
          (dataToUpdate as RestaurantDetails).isActive = true;
          (dataToUpdate as RestaurantDetails).isProfileComplete = true;
        }
      }

      await updateDoc(doc(firestore, `${userRole}s`, user.uid), dataToUpdate);
      setUserData(editData);
      setIsEditing(false);
      toast.success("Profile updated successfully");
    } catch (error) {
      console.error("Error updating profile:", error);
      toast.error("Failed to update profile");
    } finally {
      setIsSaving(false);
    }
  };

  if (!userData || !editData) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loading type="profile" />
      </div>
    );
  }

  return (
    <div className="p-4">
      {!isEditing && (
        <div className="flex justify-end mb-6">
          <Button onClick={() => setIsEditing(true)}>Edit Profile</Button>
        </div>
      )}
      {userRole === "restaurant" ? (
        isEditing ? (
          <RestaurantProfileEdit
            editData={editData as RestaurantDetails}
            setEditData={setEditData as (data: RestaurantDetails) => void}
            addressQuery={addressQuery}
            setAddressQuery={setAddressQuery}
            addressResults={addressResults}
            isLoadingAddress={isLoadingAddress}
            mapPosition={mapPosition}
            setMapPosition={setMapPosition}
            getAddressFromCoordinates={getAddressFromCoordinates}
          />
        ) : (
          <RestaurantProfileView
            userData={userData as RestaurantDetails}
            isSaving={isSaving}
          />
        )
      ) : isEditing ? (
        <ClientProfileEdit
          editData={editData as ClientDetails}
          setEditData={setEditData as (data: ClientDetails) => void}
        />
      ) : (
        <ClientProfileView userData={userData as ClientDetails} />
      )}
      {isEditing && (
        <div className="space-x-2 mt-6 flex justify-end">
          <Button
            variant="outline"
            onClick={() => {
              setEditData(userData);
              setIsEditing(false);
            }}
            disabled={isSaving}
          >
            Cancel
          </Button>
          <Button onClick={handleSave} disabled={isSaving} className="min-w-[120px]">
            {isSaving ? (
              <div className="flex items-center justify-center">
                <Loading type="button" className="min-h-0" />
              </div>
            ) : (
              "Save Changes"
            )}
          </Button>
        </div>
      )}
    </div>
  );
};
