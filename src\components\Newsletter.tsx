import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";

export function Newsletter() {
  const [email, setEmail] = useState("");
  const [name, setName] = useState("");
  const [otp, setOtp] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showOtpInput, setShowOtpInput] = useState(false);
  const [errors, setErrors] = useState<{
    email?: string;
    name?: string;
  }>({});

  const validateForm = () => {
    const newErrors: {
      email?: string;
      name?: string;
    } = {};
    let isValid = true;

    // Validate email
    if (!email) {
      newErrors.email = "Email address is required";
      isValid = false;
    } else if (!email.match(/^[^\s@]+@[^\s@]+\.[^\s@]+$/)) {
      newErrors.email = "Please enter a valid email address";
      isValid = false;
    }

    // Validate name
    if (!name) {
      newErrors.name = "Name is required";
      isValid = false;
    } else if (name.trim().length < 2) {
      newErrors.name = "Name must be at least 2 characters";
      isValid = false;
    }

    setErrors(newErrors);
    return isValid;
  };

  const handleSendOtp = async () => {
    if (!validateForm()) {
      // Show the first error as a toast
      if (errors.name) {
        toast.error(errors.name);
      } else if (errors.email) {
        toast.error(errors.email);
      }
      return;
    }

    setIsSubmitting(true);

    try {
      // Use a hardcoded API URL based on the environment
      // In a production environment, this would be the actual API URL
      const apiUrl = "http://localhost:3000";

      const response = await fetch(`${apiUrl}/api/newsletter/subscribe`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ email, name }),
      });

      if (!response.ok) {
        const text = await response.text();
        try {
          const errorData = JSON.parse(text);
          throw new Error(errorData.error || "Failed to send OTP");
        } catch {
          throw new Error(
            `Failed to send OTP: ${response.status} ${
              text || response.statusText
            }`
          );
        }
      }

      await response.json(); // We don't need the response data
      toast.success("OTP sent to your email! Please check your inbox.");
      setShowOtpInput(true);
    } catch (error) {
      console.error("Error sending OTP:", error);
      toast.error(
        error instanceof Error
          ? error.message
          : "Failed to send OTP. Please try again."
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleVerifyOtp = async () => {
    if (!otp) {
      toast.error("Please enter the OTP");
      return;
    }

    setIsSubmitting(true);

    try {
      // Use a hardcoded API URL based on the environment
      // In a production environment, this would be the actual API URL
      const apiUrl = "http://localhost:3000";

      const response = await fetch(`${apiUrl}/api/newsletter/verify`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ email, otp }),
      });

      if (!response.ok) {
        const text = await response.text();
        try {
          const errorData = JSON.parse(text);
          throw new Error(errorData.error || "Failed to verify OTP");
        } catch {
          throw new Error(
            `Failed to verify OTP: ${response.status} ${
              text || response.statusText
            }`
          );
        }
      }

      await response.json(); // We don't need the response data
      toast.success("Thank you for subscribing! Subscription confirmed.");
      setEmail("");
      setName("");
      setOtp("");
      setShowOtpInput(false);
    } catch (error) {
      console.error("Error verifying OTP:", error);
      toast.error(
        error instanceof Error
          ? error.message
          : "Failed to verify OTP. Please try again."
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  // Reset form when user goes back from OTP input
  const handleGoBack = () => {
    setShowOtpInput(false);
    setOtp("");
  };

  return (
    <section className="py-16 bg-gray-50">
      <div className="mx-auto px-4 text-center">
        <h2 className="text-3xl font-bold mb-4">Stay Updated</h2>
        <p className="text-gray-600 mb-8">
          Subscribe to our newsletter for the latest restaurant recommendations
          and food trends
        </p>
        <div className="max-w-md mx-auto flex flex-col items-center gap-4">
          {!showOtpInput ? (
            <>
              <div className="w-full space-y-1">
                <Label
                  htmlFor="name"
                  className="text-left block text-sm font-medium text-gray-700"
                >
                  Full Name <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="name"
                  type="text"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  placeholder="Enter your full name"
                  className={errors.name ? "border-red-500" : ""}
                  aria-invalid={!!errors.name}
                  aria-describedby={errors.name ? "name-error" : undefined}
                  required
                />
                {errors.name && (
                  <p id="name-error" className="text-sm text-red-500 text-left">
                    {errors.name}
                  </p>
                )}
              </div>

              <div className="w-full space-y-1">
                <Label
                  htmlFor="email"
                  className="text-left block text-sm font-medium text-gray-700"
                >
                  Email Address <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="email"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Enter your email address"
                  className={errors.email ? "border-red-500" : ""}
                  aria-invalid={!!errors.email}
                  aria-describedby={errors.email ? "email-error" : undefined}
                  required
                />
                {errors.email && (
                  <p
                    id="email-error"
                    className="text-sm text-red-500 text-left"
                  >
                    {errors.email}
                  </p>
                )}
              </div>

              <Button
                onClick={handleSendOtp}
                disabled={isSubmitting}
                className="bg-orange-500 hover:bg-orange-600 w-full mt-2"
              >
                {isSubmitting ? "Sending OTP..." : "Subscribe"}
              </Button>
            </>
          ) : (
            <>
              <div className="w-full space-y-1">
                <Label
                  htmlFor="otp"
                  className="text-left block text-sm font-medium text-gray-700"
                >
                  Verification Code
                </Label>
                <Input
                  id="otp"
                  type="text"
                  value={otp}
                  onChange={(e) => setOtp(e.target.value)}
                  placeholder="Enter the 6-digit code"
                  className="flex-1"
                  maxLength={6}
                  required
                />
                <p className="text-sm text-gray-500 text-left">
                  We sent a verification code to {email}
                </p>
              </div>

              <div className="flex w-full gap-2">
                <Button
                  onClick={handleGoBack}
                  variant="outline"
                  className="flex-1"
                  disabled={isSubmitting}
                >
                  Back
                </Button>
                <Button
                  onClick={handleVerifyOtp}
                  disabled={isSubmitting}
                  className="bg-orange-500 hover:bg-orange-600 flex-1"
                >
                  {isSubmitting ? "Verifying..." : "Verify Code"}
                </Button>
              </div>
            </>
          )}
        </div>
      </div>
    </section>
  );
}
