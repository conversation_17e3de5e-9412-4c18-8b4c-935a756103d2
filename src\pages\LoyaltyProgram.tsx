import React, { useState, useEffect } from "react";
import { useAuth } from "@/providers/AuthProvider";
import { useNavigate } from "react-router-dom";
import { LoyaltyStatusCard } from "@/components/loyalty/LoyaltyStatusCard";
import { PointTransactionsCard } from "@/components/loyalty/PointTransactionsCard";
import { RewardsCard } from "@/components/loyalty/RewardsCard";
import { ReferralCard } from "@/components/loyalty/ReferralCard";
import { GamesCard } from "@/components/loyalty/GamesCard";
import { But<PERSON> } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Loading } from "@/components/ui/loading";
import { loyaltyService } from "@/services/LoyaltyService";
import { LoyaltyStatus } from "@/types/loyalty";
import { Award, Gift, History, Users, GamepadIcon } from "lucide-react";

export const LoyaltyProgram: React.FC = () => {
  const { user, userRole } = useAuth();
  const navigate = useNavigate();
  const [loyaltyStatus, setLoyaltyStatus] = useState<LoyaltyStatus | null>(
    null
  );
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("overview");

  useEffect(() => {
    // Redirect if not logged in or not a client
    if (!user) {
      navigate("/auth");
      return;
    }

    if (userRole !== "client") {
      navigate("/");
      return;
    }

    const fetchLoyaltyStatus = async () => {
      setLoading(true);
      try {
        const status = await loyaltyService.getLoyaltyStatus(user.uid);
        setLoyaltyStatus(status);
      } catch (error) {
        console.error("Error fetching loyalty status:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchLoyaltyStatus();
  }, [user, userRole, navigate]);

  const handleJoinLoyalty = async () => {
    if (!user) return;

    try {
      const success = await loyaltyService.initializeUser(
        user.uid,
        user.email || ""
      );
      if (success) {
        // Refresh loyalty status
        const status = await loyaltyService.getLoyaltyStatus(user.uid);
        setLoyaltyStatus(status);
      }
    } catch (error) {
      console.error("Error joining loyalty program:", error);
    }
  };

  const handleRefresh = async () => {
    if (!user) return;

    try {
      const status = await loyaltyService.getLoyaltyStatus(user.uid);
      setLoyaltyStatus(status);
    } catch (error) {
      console.error("Error refreshing loyalty status:", error);
    }
  };

  if (loading) {
    return <Loading text="Loading Loyalty Program..." />;
  }

  if (!loyaltyStatus) {
    return (
      <div className="mx-auto px-4 py-8">
        <div className="max-w-3xl mx-auto">
          <div className="text-center mb-8">
            <Award className="h-16 w-16 mx-auto text-primary mb-4" />
            <h1 className="text-3xl font-bold mb-2">Qonai Loyalty Program</h1>
            <p className="text-muted-foreground">
              Join our loyalty program to earn points with every order, review,
              and referral. Redeem points for discounts, free items, and
              exclusive perks!
            </p>
          </div>

          <div className="bg-card border rounded-lg p-6 shadow-sm">
            <h2 className="text-xl font-semibold mb-4">Program Benefits</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
              <div className="flex items-start space-x-3">
                <div className="bg-primary/10 p-2 rounded-full">
                  <Gift className="h-5 w-5 text-primary" />
                </div>
                <div>
                  <h3 className="font-medium">Earn Points</h3>
                  <p className="text-sm text-muted-foreground">
                    Earn points on every order, review, and referral
                  </p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <div className="bg-primary/10 p-2 rounded-full">
                  <Award className="h-5 w-5 text-primary" />
                </div>
                <div>
                  <h3 className="font-medium">Tier Benefits</h3>
                  <p className="text-sm text-muted-foreground">
                    Unlock exclusive perks as you progress through tiers
                  </p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <div className="bg-primary/10 p-2 rounded-full">
                  <Users className="h-5 w-5 text-primary" />
                </div>
                <div>
                  <h3 className="font-medium">Refer Friends</h3>
                  <p className="text-sm text-muted-foreground">
                    Earn {loyaltyService.pointsConfig.referralPoints} points
                    when friends join using your referral code
                  </p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <div className="bg-primary/10 p-2 rounded-full">
                  <History className="h-5 w-5 text-primary" />
                </div>
                <div>
                  <h3 className="font-medium">Redeem Rewards</h3>
                  <p className="text-sm text-muted-foreground">
                    Use your points for discounts, free items, and more
                  </p>
                </div>
              </div>
            </div>

            <Button onClick={handleJoinLoyalty} className="w-full">
              Join Loyalty Program
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Loyalty Program</h1>
        <Button variant="outline" onClick={handleRefresh}>
          Refresh
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-1">
          <LoyaltyStatusCard />
          <div className="mt-6">
            <ReferralCard />
          </div>
        </div>

        <div className="lg:col-span-2">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="w-full mb-6">
              <TabsTrigger value="overview" className="flex-1">
                <History className="h-4 w-4 mr-2" />
                Transactions
              </TabsTrigger>
              <TabsTrigger value="rewards" className="flex-1">
                <Gift className="h-4 w-4 mr-2" />
                Rewards
              </TabsTrigger>
              <TabsTrigger value="games" className="flex-1">
                <GamepadIcon className="h-4 w-4 mr-2" />
                Games
              </TabsTrigger>
            </TabsList>

            <TabsContent value="overview">
              <PointTransactionsCard limit={20} />
            </TabsContent>

            <TabsContent value="rewards">
              <RewardsCard
                onRewardRedeemed={handleRefresh}
                userPoints={loyaltyStatus?.totalPoints || 0}
              />
            </TabsContent>

            <TabsContent value="games">
              <GamesCard
                userPoints={loyaltyStatus?.totalPoints || 0}
                onPointsEarned={handleRefresh}
              />
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
};
