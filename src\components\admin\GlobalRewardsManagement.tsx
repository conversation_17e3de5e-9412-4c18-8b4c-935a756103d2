import React, { useState } from "react";
import { <PERSON>, Card<PERSON><PERSON>nt, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Reward } from "@/types/rewards";
import {
  useGlobalRewards,
  useCreateReward,
  useUpdateReward,
  useDeleteReward,
} from "@/lib/react-query/hooks/useRewards";
import { use<PERSON><PERSON> } from "@/providers/AuthProvider";

import { Timestamp } from "firebase/firestore";
import { Gift, Trash2, Edit, Plus, AlertCircle, Star } from "lucide-react";
import { toast } from "sonner";

export const GlobalRewardsManagement: React.FC = () => {
  const { user } = useAuth();
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedReward, setSelectedReward] = useState<Reward | null>(null);

  // Form state
  const [formData, setFormData] = useState<Partial<Reward>>({
    name: "",
    description: "",
    pointsCost: 100,
    type: "discount",
    discountValue: 10,
    discountType: "percentage",
    isActive: true,
    isGlobal: true,
    isFeatured: false,
    availableQuantity: undefined,
    termsAndConditions: "",
    minimumOrderValue: undefined,
  });

  // Queries
  const {
    data: rewards = [],
    isLoading: isLoadingRewards,
    isError: isRewardsError,
  } = useGlobalRewards();

  // Mutations
  const { mutate: createReward, isPending: isCreating } = useCreateReward();
  const { mutate: updateReward, isPending: isUpdating } = useUpdateReward();
  const { mutate: deleteReward, isPending: isDeleting } = useDeleteReward();

  // Handle form input changes
  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;

    if (
      name === "pointsCost" ||
      name === "discountValue" ||
      name === "availableQuantity" ||
      name === "minimumOrderValue"
    ) {
      setFormData({
        ...formData,
        [name]: value === "" ? undefined : Number(value),
      });
    } else {
      setFormData({
        ...formData,
        [name]: value,
      });
    }
  };

  // Handle select changes
  const handleSelectChange = (name: string, value: string) => {
    setFormData({
      ...formData,
      [name]: value,
    });
  };

  // Handle switch changes
  const handleSwitchChange = (name: string, checked: boolean) => {
    setFormData({
      ...formData,
      [name]: checked,
    });
  };

  // Handle date changes
  const handleDateChange = (name: string, date: Date | undefined) => {
    setFormData({
      ...formData,
      [name]: date ? Timestamp.fromDate(date) : undefined,
    });
  };

  // Reset form
  const resetForm = () => {
    setFormData({
      name: "",
      description: "",
      pointsCost: 100,
      type: "discount",
      discountValue: 10,
      discountType: "percentage",
      isActive: true,
      isGlobal: true,
      isFeatured: false,
      availableQuantity: undefined,
      termsAndConditions: "",
      minimumOrderValue: undefined,
    });
  };

  // Open edit dialog
  const handleEditClick = (reward: Reward) => {
    setSelectedReward(reward);
    setFormData({
      name: reward.name,
      description: reward.description,
      pointsCost: reward.pointsCost,
      type: reward.type,
      discountValue: reward.discountValue,
      discountType: reward.discountType,
      isActive: reward.isActive,
      isGlobal: true,
      isFeatured: reward.isFeatured,
      availableQuantity: reward.availableQuantity,
      termsAndConditions: reward.termsAndConditions,
      minimumOrderValue: reward.minimumOrderValue,
      expiresAt: reward.expiresAt,
    });
    setEditDialogOpen(true);
  };

  // Open delete dialog
  const handleDeleteClick = (reward: Reward) => {
    setSelectedReward(reward);
    setDeleteDialogOpen(true);
  };

  // Create reward
  const handleCreateReward = () => {
    if (!user) return;

    if (!formData.name || !formData.description || !formData.pointsCost) {
      toast.error("Please fill in all required fields");
      return;
    }

    const rewardData: Omit<
      Reward,
      "id" | "createdAt" | "updatedAt" | "createdBy"
    > = {
      name: formData.name,
      description: formData.description,
      pointsCost: formData.pointsCost,
      type: formData.type as
        | "discount"
        | "freeItem"
        | "freeDelivery"
        | "vipAccess"
        | "other",
      isActive: formData.isActive ?? true,
      isGlobal: true,
      isFeatured: formData.isFeatured ?? false,
    };

    if (formData.type === "discount") {
      rewardData.discountValue = formData.discountValue;
      rewardData.discountType = formData.discountType as "percentage" | "fixed";
    }

    if (formData.availableQuantity !== undefined) {
      rewardData.availableQuantity = formData.availableQuantity;
    }

    if (formData.termsAndConditions) {
      rewardData.termsAndConditions = formData.termsAndConditions;
    }

    if (formData.minimumOrderValue !== undefined) {
      rewardData.minimumOrderValue = formData.minimumOrderValue;
    }

    if (formData.expiresAt) {
      rewardData.expiresAt = formData.expiresAt as Timestamp;
    }

    createReward(
      { reward: rewardData, userId: user.uid },
      {
        onSuccess: () => {
          setCreateDialogOpen(false);
          resetForm();
        },
      }
    );
  };

  // Update reward
  const handleUpdateReward = () => {
    if (!selectedReward) return;

    if (!formData.name || !formData.description || !formData.pointsCost) {
      toast.error("Please fill in all required fields");
      return;
    }

    const rewardData: Partial<Reward> = {
      name: formData.name,
      description: formData.description,
      pointsCost: formData.pointsCost,
      type: formData.type as
        | "discount"
        | "freeItem"
        | "freeDelivery"
        | "vipAccess"
        | "other",
      isActive: formData.isActive,
      isFeatured: formData.isFeatured,
    };

    if (formData.type === "discount") {
      rewardData.discountValue = formData.discountValue;
      rewardData.discountType = formData.discountType as "percentage" | "fixed";
    }

    if (formData.availableQuantity !== undefined) {
      rewardData.availableQuantity = formData.availableQuantity;
    } else {
      rewardData.availableQuantity = undefined;
    }

    if (formData.termsAndConditions) {
      rewardData.termsAndConditions = formData.termsAndConditions;
    }

    if (formData.minimumOrderValue !== undefined) {
      rewardData.minimumOrderValue = formData.minimumOrderValue;
    } else {
      rewardData.minimumOrderValue = undefined;
    }

    if (formData.expiresAt) {
      rewardData.expiresAt = formData.expiresAt as Timestamp;
    } else {
      rewardData.expiresAt = undefined;
    }

    updateReward(
      { rewardId: selectedReward.id, data: rewardData },
      {
        onSuccess: () => {
          setEditDialogOpen(false);
          resetForm();
        },
      }
    );
  };

  // Delete reward
  const handleDeleteReward = () => {
    if (!selectedReward) return;

    deleteReward(
      { rewardId: selectedReward.id },
      {
        onSuccess: () => {
          setDeleteDialogOpen(false);
        },
      }
    );
  };

  // Check if user is admin
  if (!user || user.email !== "<EMAIL>") {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Global Rewards Management</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <AlertCircle className="h-8 w-8 text-destructive mx-auto mb-2" />
            <p className="text-muted-foreground">
              You don't have permission to access this page.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="flex items-center">
          <Gift className="mr-2 h-5 w-5 text-primary" />
          Global Rewards Management
        </CardTitle>
        <Button onClick={() => setCreateDialogOpen(true)}>
          <Plus className="mr-2 h-4 w-4" />
          Create Global Reward
        </Button>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="all">
          <TabsList className="w-full mb-4">
            <TabsTrigger value="all" className="flex-1">
              All Rewards
            </TabsTrigger>
            <TabsTrigger value="featured" className="flex-1">
              Featured Rewards
            </TabsTrigger>
          </TabsList>

          <TabsContent value="all">
            {isLoadingRewards ? (
              <div className="text-center py-8">
                <p className="text-muted-foreground">Loading rewards...</p>
              </div>
            ) : isRewardsError ? (
              <div className="text-center py-8">
                <AlertCircle className="h-8 w-8 text-destructive mx-auto mb-2" />
                <p className="text-muted-foreground">
                  Failed to load rewards. Please try again later.
                </p>
              </div>
            ) : rewards.length === 0 ? (
              <div className="text-center py-8">
                <Gift className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                <p className="text-muted-foreground">
                  No global rewards. Create your first global reward!
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Reward</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead>Points</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {rewards.map((reward) => (
                      <TableRow key={reward.id}>
                        <TableCell>
                          <div className="flex items-center">
                            {reward.isFeatured && (
                              <Star className="h-4 w-4 text-yellow-500 mr-2" />
                            )}
                            <div>
                              <p className="font-medium">{reward.name}</p>
                              <p className="text-xs text-muted-foreground line-clamp-1">
                                {reward.description}
                              </p>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          {reward.type === "discount" ? (
                            <Badge variant="outline">
                              {reward.discountValue}% Discount
                            </Badge>
                          ) : reward.type === "freeItem" ? (
                            <Badge variant="outline">Free Item</Badge>
                          ) : reward.type === "freeDelivery" ? (
                            <Badge variant="outline">Free Delivery</Badge>
                          ) : (
                            <Badge variant="outline">Other</Badge>
                          )}
                        </TableCell>
                        <TableCell>{reward.pointsCost}</TableCell>
                        <TableCell>
                          {!reward.isActive ? (
                            <Badge variant="secondary">Inactive</Badge>
                          ) : reward.expiresAt &&
                            reward.expiresAt.toDate() < new Date() ? (
                            <Badge variant="destructive">Expired</Badge>
                          ) : reward.availableQuantity !== undefined &&
                            reward.availableQuantity <= 0 ? (
                            <Badge variant="destructive">Out of stock</Badge>
                          ) : (
                            <Badge variant="default">Active</Badge>
                          )}
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end space-x-2">
                            <Button
                              variant="outline"
                              size="icon"
                              onClick={() => handleEditClick(reward)}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="icon"
                              onClick={() => handleDeleteClick(reward)}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </TabsContent>

          <TabsContent value="featured">
            {isLoadingRewards ? (
              <div className="text-center py-8">
                <p className="text-muted-foreground">Loading rewards...</p>
              </div>
            ) : isRewardsError ? (
              <div className="text-center py-8">
                <AlertCircle className="h-8 w-8 text-destructive mx-auto mb-2" />
                <p className="text-muted-foreground">
                  Failed to load rewards. Please try again later.
                </p>
              </div>
            ) : rewards.filter((r) => r.isFeatured).length === 0 ? (
              <div className="text-center py-8">
                <Star className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                <p className="text-muted-foreground">
                  No featured rewards. Feature rewards to highlight them to
                  users!
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Reward</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead>Points</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {rewards
                      .filter((reward) => reward.isFeatured)
                      .map((reward) => (
                        <TableRow key={reward.id}>
                          <TableCell>
                            <div className="flex items-center">
                              <Star className="h-4 w-4 text-yellow-500 mr-2" />
                              <div>
                                <p className="font-medium">{reward.name}</p>
                                <p className="text-xs text-muted-foreground line-clamp-1">
                                  {reward.description}
                                </p>
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            {reward.type === "discount" ? (
                              <Badge variant="outline">
                                {reward.discountValue}% Discount
                              </Badge>
                            ) : reward.type === "freeItem" ? (
                              <Badge variant="outline">Free Item</Badge>
                            ) : reward.type === "freeDelivery" ? (
                              <Badge variant="outline">Free Delivery</Badge>
                            ) : (
                              <Badge variant="outline">Other</Badge>
                            )}
                          </TableCell>
                          <TableCell>{reward.pointsCost}</TableCell>
                          <TableCell>
                            {!reward.isActive ? (
                              <Badge variant="secondary">Inactive</Badge>
                            ) : reward.expiresAt &&
                              reward.expiresAt.toDate() < new Date() ? (
                              <Badge variant="destructive">Expired</Badge>
                            ) : reward.availableQuantity !== undefined &&
                              reward.availableQuantity <= 0 ? (
                              <Badge variant="destructive">Out of stock</Badge>
                            ) : (
                              <Badge variant="default">Active</Badge>
                            )}
                          </TableCell>
                          <TableCell className="text-right">
                            <div className="flex justify-end space-x-2">
                              <Button
                                variant="outline"
                                size="icon"
                                onClick={() => handleEditClick(reward)}
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="outline"
                                size="icon"
                                onClick={() => handleDeleteClick(reward)}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </TabsContent>
        </Tabs>

        {/* Create Reward Dialog */}
        <Dialog open={createDialogOpen} onOpenChange={setCreateDialogOpen}>
          <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Create Global Reward</DialogTitle>
              <DialogDescription>
                Create a new global reward available to all users.
              </DialogDescription>
            </DialogHeader>

            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="name">Reward Name *</Label>
                  <Input
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    placeholder="e.g., 10% Off Your Next Order"
                  />
                </div>

                <div className="grid gap-2">
                  <Label htmlFor="type">Reward Type *</Label>
                  <select
                    id="type"
                    name="type"
                    value={formData.type}
                    onChange={(e) => handleSelectChange("type", e.target.value)}
                    className="flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  >
                    <option value="">Select reward type</option>
                    <option value="discount">Discount</option>
                    <option value="freeItem">Free Item</option>
                    <option value="freeDelivery">Free Delivery</option>
                    <option value="vipAccess">VIP Access</option>
                    <option value="other">Other</option>
                  </select>
                </div>
              </div>

              <div className="grid gap-2">
                <Label htmlFor="description">Description *</Label>
                <Textarea
                  id="description"
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  placeholder="Describe the reward"
                  rows={2}
                />
              </div>

              {formData.type === "discount" && (
                <div className="grid grid-cols-2 gap-4">
                  <div className="grid gap-2">
                    <Label htmlFor="discountValue">Discount Value *</Label>
                    <Input
                      id="discountValue"
                      name="discountValue"
                      type="number"
                      value={formData.discountValue}
                      onChange={handleInputChange}
                      min={1}
                    />
                  </div>

                  <div className="grid gap-2">
                    <Label htmlFor="discountType">Discount Type *</Label>
                    <select
                      id="discountType"
                      name="discountType"
                      value={formData.discountType}
                      onChange={(e) =>
                        handleSelectChange("discountType", e.target.value)
                      }
                      className="flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                    >
                      <option value="">Select type</option>
                      <option value="percentage">Percentage (%)</option>
                      <option value="fixed">Fixed Amount ($)</option>
                    </select>
                  </div>
                </div>
              )}

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="pointsCost">Points Cost *</Label>
                  <Input
                    id="pointsCost"
                    name="pointsCost"
                    type="number"
                    value={formData.pointsCost}
                    onChange={handleInputChange}
                    min={1}
                  />
                </div>

                <div className="grid gap-2">
                  <Label htmlFor="availableQuantity">
                    Available Quantity (Optional)
                  </Label>
                  <Input
                    id="availableQuantity"
                    name="availableQuantity"
                    type="number"
                    value={
                      formData.availableQuantity === undefined
                        ? ""
                        : formData.availableQuantity
                    }
                    onChange={handleInputChange}
                    min={0}
                    placeholder="Leave empty for unlimited"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="minimumOrderValue">
                    Minimum Order Value (Optional)
                  </Label>
                  <Input
                    id="minimumOrderValue"
                    name="minimumOrderValue"
                    type="number"
                    value={
                      formData.minimumOrderValue === undefined
                        ? ""
                        : formData.minimumOrderValue
                    }
                    onChange={handleInputChange}
                    min={0}
                    placeholder="Leave empty for no minimum"
                  />
                </div>

                <div className="grid gap-2">
                  <Label htmlFor="expiresAt">Expiration Date (Optional)</Label>
                  <input
                    id="expiresAt"
                    name="expiresAt"
                    type="date"
                    value={
                      formData.expiresAt
                        ? formData.expiresAt
                            .toDate()
                            .toISOString()
                            .split("T")[0]
                        : ""
                    }
                    onChange={(e) => {
                      const date = e.target.value
                        ? new Date(e.target.value)
                        : undefined;
                      handleDateChange("expiresAt", date);
                    }}
                    className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  />
                </div>
              </div>

              <div className="grid gap-2">
                <Label htmlFor="termsAndConditions">
                  Terms & Conditions (Optional)
                </Label>
                <Textarea
                  id="termsAndConditions"
                  name="termsAndConditions"
                  value={formData.termsAndConditions}
                  onChange={handleInputChange}
                  placeholder="Any restrictions or terms"
                  rows={2}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="isActive"
                    checked={formData.isActive}
                    onCheckedChange={(checked) =>
                      handleSwitchChange("isActive", checked)
                    }
                  />
                  <Label htmlFor="isActive">Active</Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="isFeatured"
                    checked={formData.isFeatured}
                    onCheckedChange={(checked) =>
                      handleSwitchChange("isFeatured", checked)
                    }
                  />
                  <Label htmlFor="isFeatured">Featured</Label>
                </div>
              </div>
            </div>

            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => {
                  setCreateDialogOpen(false);
                  resetForm();
                }}
                disabled={isCreating}
              >
                Cancel
              </Button>
              <Button onClick={handleCreateReward} disabled={isCreating}>
                {isCreating ? "Creating..." : "Create Reward"}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Edit Reward Dialog */}
        <Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
          <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Edit Global Reward</DialogTitle>
              <DialogDescription>
                Update the details of this global reward.
              </DialogDescription>
            </DialogHeader>

            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="edit-name">Reward Name *</Label>
                  <Input
                    id="edit-name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                  />
                </div>

                <div className="grid gap-2">
                  <Label htmlFor="edit-type">Reward Type *</Label>
                  <select
                    id="edit-type"
                    name="type"
                    value={formData.type}
                    onChange={(e) => handleSelectChange("type", e.target.value)}
                    className="flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  >
                    <option value="">Select reward type</option>
                    <option value="discount">Discount</option>
                    <option value="freeItem">Free Item</option>
                    <option value="freeDelivery">Free Delivery</option>
                    <option value="vipAccess">VIP Access</option>
                    <option value="other">Other</option>
                  </select>
                </div>
              </div>

              <div className="grid gap-2">
                <Label htmlFor="edit-description">Description *</Label>
                <Textarea
                  id="edit-description"
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  rows={2}
                />
              </div>

              {formData.type === "discount" && (
                <div className="grid grid-cols-2 gap-4">
                  <div className="grid gap-2">
                    <Label htmlFor="edit-discountValue">Discount Value *</Label>
                    <Input
                      id="edit-discountValue"
                      name="discountValue"
                      type="number"
                      value={formData.discountValue}
                      onChange={handleInputChange}
                      min={1}
                    />
                  </div>

                  <div className="grid gap-2">
                    <Label htmlFor="edit-discountType">Discount Type *</Label>
                    <select
                      id="edit-discountType"
                      name="discountType"
                      value={formData.discountType}
                      onChange={(e) =>
                        handleSelectChange("discountType", e.target.value)
                      }
                      className="flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                    >
                      <option value="">Select type</option>
                      <option value="percentage">Percentage (%)</option>
                      <option value="fixed">Fixed Amount ($)</option>
                    </select>
                  </div>
                </div>
              )}

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="edit-pointsCost">Points Cost *</Label>
                  <Input
                    id="edit-pointsCost"
                    name="pointsCost"
                    type="number"
                    value={formData.pointsCost}
                    onChange={handleInputChange}
                    min={1}
                  />
                </div>

                <div className="grid gap-2">
                  <Label htmlFor="edit-availableQuantity">
                    Available Quantity (Optional)
                  </Label>
                  <Input
                    id="edit-availableQuantity"
                    name="availableQuantity"
                    type="number"
                    value={
                      formData.availableQuantity === undefined
                        ? ""
                        : formData.availableQuantity
                    }
                    onChange={handleInputChange}
                    min={0}
                    placeholder="Leave empty for unlimited"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="edit-minimumOrderValue">
                    Minimum Order Value (Optional)
                  </Label>
                  <Input
                    id="edit-minimumOrderValue"
                    name="minimumOrderValue"
                    type="number"
                    value={
                      formData.minimumOrderValue === undefined
                        ? ""
                        : formData.minimumOrderValue
                    }
                    onChange={handleInputChange}
                    min={0}
                    placeholder="Leave empty for no minimum"
                  />
                </div>

                <div className="grid gap-2">
                  <Label htmlFor="edit-expiresAt">
                    Expiration Date (Optional)
                  </Label>
                  <input
                    id="edit-expiresAt"
                    name="expiresAt"
                    type="date"
                    value={
                      formData.expiresAt
                        ? formData.expiresAt
                            .toDate()
                            .toISOString()
                            .split("T")[0]
                        : ""
                    }
                    onChange={(e) => {
                      const date = e.target.value
                        ? new Date(e.target.value)
                        : undefined;
                      handleDateChange("expiresAt", date);
                    }}
                    className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  />
                </div>
              </div>

              <div className="grid gap-2">
                <Label htmlFor="edit-termsAndConditions">
                  Terms & Conditions (Optional)
                </Label>
                <Textarea
                  id="edit-termsAndConditions"
                  name="termsAndConditions"
                  value={formData.termsAndConditions}
                  onChange={handleInputChange}
                  placeholder="Any restrictions or terms"
                  rows={2}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="edit-isActive"
                    checked={formData.isActive}
                    onCheckedChange={(checked) =>
                      handleSwitchChange("isActive", checked)
                    }
                  />
                  <Label htmlFor="edit-isActive">Active</Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="edit-isFeatured"
                    checked={formData.isFeatured}
                    onCheckedChange={(checked) =>
                      handleSwitchChange("isFeatured", checked)
                    }
                  />
                  <Label htmlFor="edit-isFeatured">Featured</Label>
                </div>
              </div>
            </div>

            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => {
                  setEditDialogOpen(false);
                  resetForm();
                }}
                disabled={isUpdating}
              >
                Cancel
              </Button>
              <Button onClick={handleUpdateReward} disabled={isUpdating}>
                {isUpdating ? "Updating..." : "Update Reward"}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Delete Confirmation Dialog */}
        <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Delete Global Reward</DialogTitle>
              <DialogDescription>
                Are you sure you want to delete this global reward? This action
                cannot be undone.
              </DialogDescription>
            </DialogHeader>

            {selectedReward && (
              <div className="py-4">
                <p className="font-medium">{selectedReward.name}</p>
                <p className="text-sm text-muted-foreground mt-1">
                  {selectedReward.description}
                </p>
              </div>
            )}

            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setDeleteDialogOpen(false)}
                disabled={isDeleting}
              >
                Cancel
              </Button>
              <Button
                variant="destructive"
                onClick={handleDeleteReward}
                disabled={isDeleting}
              >
                {isDeleting ? "Deleting..." : "Delete Reward"}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </CardContent>
    </Card>
  );
};
