import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { gameService } from "@/services/GameService";

/**
 * Hook to get all available mini-games
 */
export const useGames = () => {
  return useQuery({
    queryKey: ["games"],
    queryFn: () => gameService.getGames(),
    staleTime: 1000 * 60 * 5, // 5 minutes
  });
};

/**
 * Hook to get user's game stats
 */
export const useGameStats = (userId: string | undefined) => {
  return useQuery({
    queryKey: ["gameStats", userId],
    queryFn: () => (userId ? gameService.getUserGameStats(userId) : null),
    enabled: !!userId,
    staleTime: 1000 * 60, // 1 minute
  });
};

/**
 * Hook to submit food match game result
 */
export const useSubmitFoodMatchResult = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      userId,
      score,
      timeSeconds,
    }: {
      userId: string;
      score: number;
      timeSeconds: number;
    }) => gameService.submitFoodMatchResult(userId, score, timeSeconds),
    onSuccess: (_, variables) => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({
        queryKey: ["gameStats", variables.userId],
      });
      queryClient.invalidateQueries({
        queryKey: ["loyalty", variables.userId],
      });
    },
  });
};

/**
 * Hook to submit trivia game result
 */
export const useSubmitTriviaResult = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      userId,
      correctAnswers,
      totalQuestions,
    }: {
      userId: string;
      correctAnswers: number;
      totalQuestions: number;
    }) =>
      gameService.submitTriviaResult(userId, correctAnswers, totalQuestions),
    onSuccess: (_, variables) => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({
        queryKey: ["gameStats", variables.userId],
      });
      queryClient.invalidateQueries({
        queryKey: ["loyalty", variables.userId],
      });
    },
  });
};

/**
 * Hook to record daily check-in
 */
export const useRecordDailyCheckIn = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (userId: string) => gameService.recordDailyCheckIn(userId),
    onSuccess: (_, userId) => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: ["gameStats", userId] });
      queryClient.invalidateQueries({ queryKey: ["loyalty", userId] });
    },
  });
};
