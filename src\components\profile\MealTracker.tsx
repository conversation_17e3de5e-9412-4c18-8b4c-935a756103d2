import React, { useState, useEffect } from "react";
import { Card, CardContent, Card<PERSON>eader, CardT<PERSON>le, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Meal, MealLog, DietaryGoal } from "@/types";
import { mealTrackingService } from "@/services/mealTrackingService";
import { toast } from "sonner";
import { PlusCircle, Calendar, BarChart, Utensils } from "lucide-react";
import { startOfDay, endOfDay, subDays } from "date-fns";
import { AddMealForm } from "./AddMealForm";
import { MealLogView } from "./MealLogView";
import { NutritionSummary } from "./NutritionSummary";

interface MealTrackerProps {
  userId: string;
  dietaryGoals?: DietaryGoal[];
}

export const MealTracker: React.FC<MealTrackerProps> = ({ userId, dietaryGoals = [] }) => {
  const [activeTab, setActiveTab] = useState("log");
  const [isAddingMeal, setIsAddingMeal] = useState(false);
  const [mealLogs, setMealLogs] = useState<MealLog[]>([]);
  const [recentMeals, setRecentMeals] = useState<Meal[]>([]);
  const [dateRange, setDateRange] = useState({
    start: startOfDay(subDays(new Date(), 7)),
    end: endOfDay(new Date())
  });
  const [isLoading, setIsLoading] = useState(true);

  // Fetch meal logs and recent meals
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      try {
        const logs = await mealTrackingService.getMealLogs(userId, dateRange.start, dateRange.end);
        setMealLogs(logs);

        const meals = await mealTrackingService.getRecentMeals(userId);
        setRecentMeals(meals);
      } catch (error) {
        console.error("Error fetching meal data:", error);
        toast.error("Failed to load meal data");
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [userId, dateRange]);

  // Handle adding a new meal
  const handleAddMeal = async (meal: Omit<Meal, 'id'>) => {
    try {
      const mealId = await mealTrackingService.addMeal(userId, meal);
      if (mealId) {
        toast.success("Meal added successfully");

        // Refresh data
        const logs = await mealTrackingService.getMealLogs(userId, dateRange.start, dateRange.end);
        setMealLogs(logs);

        const meals = await mealTrackingService.getRecentMeals(userId);
        setRecentMeals(meals);

        setIsAddingMeal(false);
      } else {
        toast.error("Failed to add meal");
      }
    } catch (error) {
      console.error("Error adding meal:", error);
      toast.error("An error occurred while adding the meal");
    }
  };

  // Handle deleting a meal
  const handleDeleteMeal = async (mealId: string) => {
    try {
      const success = await mealTrackingService.deleteMeal(userId, mealId);
      if (success) {
        toast.success("Meal deleted successfully");

        // Refresh data
        const logs = await mealTrackingService.getMealLogs(userId, dateRange.start, dateRange.end);
        setMealLogs(logs);

        const meals = await mealTrackingService.getRecentMeals(userId);
        setRecentMeals(meals);
      } else {
        toast.error("Failed to delete meal");
      }
    } catch (error) {
      console.error("Error deleting meal:", error);
      toast.error("An error occurred while deleting the meal");
    }
  };

  // Handle date range change
  const handleDateRangeChange = (start: Date, end: Date) => {
    setDateRange({
      start: startOfDay(start),
      end: endOfDay(end)
    });
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="text-xl font-semibold flex items-center">
          <Utensils className="mr-2 h-5 w-5" />
          Meal Tracker
        </CardTitle>
        <CardDescription>
          Track your meals and monitor your nutrition goals
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {isAddingMeal ? (
          <AddMealForm
            onSubmit={handleAddMeal}
            onCancel={() => setIsAddingMeal(false)}
            recentMeals={recentMeals}
            dietaryGoals={dietaryGoals}
          />
        ) : (
          <>
            <div className="flex justify-between items-center">
              <Tabs defaultValue={activeTab} value={activeTab} onValueChange={setActiveTab} className="w-full">
                <TabsList className="grid grid-cols-2 mb-4">
                  <TabsTrigger value="log" className="flex items-center">
                    <Calendar className="mr-2 h-4 w-4" />
                    Meal Log
                  </TabsTrigger>
                  <TabsTrigger value="summary" className="flex items-center">
                    <BarChart className="mr-2 h-4 w-4" />
                    Nutrition Summary
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="log" className="space-y-4">
                  <MealLogView
                    mealLogs={mealLogs}
                    isLoading={isLoading}
                    onDeleteMeal={handleDeleteMeal}
                    onDateRangeChange={handleDateRangeChange}
                    dateRange={dateRange}
                  />
                </TabsContent>

                <TabsContent value="summary" className="space-y-4">
                  <NutritionSummary
                    userId={userId}
                    dateRange={dateRange}
                    onDateRangeChange={handleDateRangeChange}
                    dietaryGoals={dietaryGoals}
                  />
                </TabsContent>
              </Tabs>
            </div>

            <Button
              onClick={() => setIsAddingMeal(true)}
              className="w-full"
            >
              <PlusCircle className="mr-2 h-4 w-4" />
              Add Meal
            </Button>
          </>
        )}
      </CardContent>
    </Card>
  );
};
