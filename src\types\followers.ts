import { Timestamp } from "firebase/firestore";

export interface Follower {
  id: string;
  userId: string;
  restaurantId: string;
  createdAt: Date | Timestamp;
  username: string;
  restaurantUsername: string;
}

export interface RestaurantFollower {
  userId: string;
  username: string;
  createdAt: Date | Timestamp;
}

export interface FollowedRestaurant {
  restaurantId: string;
  restaurantName: string;
  restaurantUsername: string;
  imageUrl?: string;
  createdAt: Date | Timestamp;
}
