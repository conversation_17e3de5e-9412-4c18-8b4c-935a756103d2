import { useAuth } from "@/providers/AuthProvider";
import { useLocation, useNavigate } from "react-router-dom";
import { useEffect } from "react";
import { toast } from "sonner";

interface RouteGuardProps {
  children: React.ReactNode;
}

const publicRoutes = ["/", "/auth", "/profile", "/restaurants", "/pricing"];
const restrictedRoutes = ["/admin-panel", "/dashboard", "/restaurant"];

export const RouteGuard = ({ children }: RouteGuardProps) => {
  const { user, loading, userRole, isProfileComplete } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    if (!loading) {
      const currentPath = location.pathname;

      // Check if current path is a restricted route
      const isRestrictedRoute = restrictedRoutes.some((route) =>
        currentPath.toLowerCase().includes(route.toLowerCase())
      );

      // Check if current path is a public route
      const isPublicRoute = publicRoutes.some(
        (route) => currentPath.toLowerCase() === route.toLowerCase()
      );

      // If not logged in and trying to access a non-public route
      if (!user && !isPublicRoute) {
        navigate("/auth");
        toast.error("Please log in to access this page");
        return;
      }

      // If logged in as restaurant but profile is not complete
      if (user && userRole === "restaurant" && !isProfileComplete) {
        if (!isPublicRoute) {
          navigate("/profile");
          toast.error("Please complete your profile to access this feature");
          return;
        }
      }

      // If trying to access restricted routes
      if (isRestrictedRoute) {
        if (!user) {
          navigate("/auth");
          toast.error("Please log in to access this page");
          return;
        }

        if (userRole !== "restaurant") {
          navigate("/");
          toast.error("Only restaurants can access this page");
          return;
        }

        if (!isProfileComplete) {
          navigate("/profile");
          toast.error("Please complete your profile to access this feature");
          return;
        }
      }
    }
  }, [user, loading, navigate, location, userRole, isProfileComplete]);

  if (loading) {
    return null;
  }

  return <>{children}</>;
};
