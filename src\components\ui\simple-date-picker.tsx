import React from 'react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';

interface SimpleDatePickerProps {
  value: Date;
  onChange: (date: Date) => void;
  className?: string;
  disabled?: boolean | ((date: Date) => boolean);
  isDateDisabled?: (date: Date) => boolean;
}

export function SimpleDatePicker({
  value,
  onChange,
  className,
  disabled = false,
  isDateDisabled,
}: SimpleDatePickerProps) {
  // Convert function-based disabled prop to boolean
  const isDisabled = typeof disabled === 'function' ? disabled(value) : disabled;
  // Format date for input
  const dateString = format(value, 'yyyy-MM-dd');

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newDate = new Date(e.target.value);

    // Check if the date is disabled
    if (isDateDisabled && isDateDisabled(newDate)) {
      return;
    }

    // Preserve the time from the original date
    newDate.setHours(
      value.getHours(),
      value.getMinutes(),
      value.getSeconds(),
      value.getMilliseconds()
    );

    onChange(newDate);
  };

  return (
    <div className={cn("space-y-2", className)}>
      <input
        type="date"
        value={dateString}
        onChange={handleChange}
        disabled={isDisabled}
        className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
      />
    </div>
  );
}

interface DateRangePickerProps {
  startDate: Date;
  endDate: Date;
  onStartDateChange: (date: Date) => void;
  onEndDateChange: (date: Date) => void;
  className?: string;
}

export function DateRangePicker({
  startDate,
  endDate,
  onStartDateChange,
  onEndDateChange,
  className,
}: DateRangePickerProps) {
  return (
    <div className={cn("space-y-4", className)}>
      <div className="space-y-2">
        <label className="text-sm font-medium">Start Date</label>
        <SimpleDatePicker
          value={startDate}
          onChange={onStartDateChange}
        />
      </div>

      <div className="space-y-2">
        <label className="text-sm font-medium">End Date</label>
        <SimpleDatePicker
          value={endDate}
          onChange={onEndDateChange}
          isDateDisabled={(date) => date < startDate}
        />
      </div>
    </div>
  );
}
