/**
 * Calculates a weighted rating using a modified Bayesian average
 * This gives more weight to restaurants with more reviews
 *
 * @param rating The current average rating
 * @param reviewCount The number of reviews
 * @param globalMeanRating The global mean rating across all restaurants (default: 3.5)
 * @param minReviewsForConfidence The minimum number of reviews needed for full confidence (default: 5)
 * @returns The weighted rating (0 if no reviews)
 */
export const calculateWeightedRating = (
  rating: number,
  reviewCount: number,
  globalMeanRating: number = 3.5,
  minReviewsForConfidence: number = 5
): number => {
  // If no reviews, return 0
  if (reviewCount === 0) return 0;

  // For very few reviews (1-2), give more weight to the actual rating
  if (reviewCount <= 2) {
    return rating;
  }

  // For a moderate number of reviews (3-5), use a weighted approach that favors the actual rating
  if (reviewCount <= minReviewsForConfidence) {
    const confidence = reviewCount / minReviewsForConfidence;
    return (confidence * rating + (1 - confidence) * globalMeanRating);
  }

  // For many reviews (>5), trust the actual rating more
  return rating;
};

/**
 * Calculates a confidence score (0-1) based on the number of reviews
 * This can be used for sorting or displaying confidence indicators
 *
 * @param reviewCount The number of reviews
 * @param minReviewsForConfidence The minimum number of reviews needed for full confidence (default: 5)
 * @returns A confidence score between 0 and 1
 */
export const calculateConfidenceScore = (
  reviewCount: number,
  minReviewsForConfidence: number = 5
): number => {
  if (reviewCount >= minReviewsForConfidence) return 1;
  return reviewCount / minReviewsForConfidence;
};

/**
 * Calculates a combined score that can be used for sorting restaurants
 * This combines the rating and the number of reviews into a single score
 *
 * @param rating The current average rating
 * @param reviewCount The number of reviews
 * @param globalMeanRating The global mean rating across all restaurants (default: 3.5)
 * @param minReviewsForConfidence The minimum number of reviews needed for full confidence (default: 5)
 * @returns A combined score that can be used for sorting
 */
export const calculateCombinedScore = (
  rating: number,
  reviewCount: number,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  _globalMeanRating: number = 3.5, // Unused but kept for future implementation
  minReviewsForConfidence: number = 5
): number => {
  // If no reviews, return a very low score so these restaurants appear at the end
  if (reviewCount === 0) return -1;

  // Calculate confidence score (0-1)
  const confidenceScore = calculateConfidenceScore(
    reviewCount,
    minReviewsForConfidence
  );

  // For sorting, we want to balance the actual rating with the number of reviews
  // This formula gives higher scores to restaurants with both good ratings and more reviews
  return rating * (0.6 + 0.4 * confidenceScore);
};
