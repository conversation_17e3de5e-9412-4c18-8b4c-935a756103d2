import React, { useState, useEffect, useCallback, useRef } from "react";
import { useParams } from "react-router-dom";
import {
  collection,
  addDoc,
  serverTimestamp,
  doc,
  updateDoc,
  FieldValue,
} from "firebase/firestore";
import { firestore } from "@/config/firebase";
import { useAuth } from "@/providers/AuthProvider";
import { Loading } from "@/components/ui/loading";
import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
} from "@/components/ui/card";
import { toast } from "sonner";
import ReservationForm from "@/components/reservation/ReservationForm";
import ReservationList from "@/components/reservation/ReservationList";
import EditReservationDialog from "@/components/reservation/EditReservationDialog";
import { Canvas } from "@/components/layout-editor/Canvas";
import type { Element } from "@/components/layout-editor/types";
import {
  Restaurant,
  Table,
  Reservation,
  LayoutElement as ReservationLayoutElementType,
} from "@/components/reservation/types";
import {
  isRestaurantOpen,
  isValidDateTime,
  checkTableAvailability,
  fetchRestaurantData,
  fetchTablesAndLayoutData,
  fetchReservationsData,
} from "@/components/reservation/utils";
import { getDefaultColor } from "../components/layout-editor/utils";

// Helper function to adjust color brightness
const adjustColor = (color: string, amount: number): string => {
  // If color is in HSL format
  if (color.startsWith("hsl")) {
    // Extract hue, saturation, and lightness values
    const hslMatch = color.match(/hsl\((\d+),\s*(\d+)%,\s*(\d+)%\)/);
    if (hslMatch) {
      const h = parseInt(hslMatch[1]);
      const s = parseInt(hslMatch[2]);
      // Adjust lightness, keeping it between 0 and 100
      const l = Math.max(0, Math.min(100, parseInt(hslMatch[3]) + amount));
      return `hsl(${h}, ${s}%, ${l}%)`;
    }
  }

  // For hex colors or if HSL parsing fails, return the original color
  return color;
};

export const Reservations = () => {
  const { username } = useParams();
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [isDataReady, setIsDataReady] = useState(false);

  const [restaurant, setRestaurant] = useState<Restaurant | null>(null);
  const [reservations, setReservations] = useState<Reservation[]>([]);
  const [tables, setTables] = useState<Table[]>([]);
  const [layout, setLayout] = useState<Element[]>([]);
  const [selectedTableId, setSelectedTableId] = useState<string>("");

  const [reservationLayoutElements, setReservationLayoutElements] = useState<
    Element[]
  >([]);

  const [editingReservation, setEditingReservation] =
    useState<Reservation | null>(null);
  const [editForm, setEditForm] = useState({
    date: "",
    arrivalTime: "",
    departureTime: "",
    partySize: 2,
    selectedTableId: "",
  });

  const [newReservation, setNewReservation] = useState(() => {
    const now = new Date();
    const bakuOffset = 4 * 60;
    const localOffset = now.getTimezoneOffset();
    const totalOffset = bakuOffset + localOffset;

    now.setMinutes(now.getMinutes() + totalOffset);
    const minutes = Math.ceil(now.getMinutes() / 30) * 30;
    now.setMinutes(minutes, 0, 0);

    const bakuTime = now.toLocaleTimeString("en-US", {
      hour12: false,
      hour: "2-digit",
      minute: "2-digit",
      timeZone: "Asia/Baku",
    });

    return {
      customerName: "",
      customerPhone: "",
      date: now.toLocaleDateString("en-CA", { timeZone: "Asia/Baku" }),
      arrivalTime: bakuTime,
      departureTime: "",
      partySize: 2,
      selectedTableId: "",
    };
  });

  const layoutRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const fetchData = async () => {
      if (!username) return;

      setIsLoading(true);
      try {
        const restaurantData = await fetchRestaurantData(username);
        if (!restaurantData) {
          setRestaurant(null);
          setIsLoading(false);
          return;
        }

        setRestaurant(restaurantData);

        if (restaurantData.workingHours?.length > 0) {
          const data = await fetchTablesAndLayoutData(restaurantData.id);
          if (data) {
            setTables(data.tables);
            const mappedLayout = data.layout.map(
              (el: ReservationLayoutElementType, index: number): Element => ({
                id: el.id,
                tableId: el.tableId,
                type: el.type as Element["type"],
                x: el.x,
                y: el.y,
                width: el.width,
                height: el.height,
                rotation: el.rotation,
                borderRadius: el.borderRadius,
                name: el.name || undefined,
                capacity: el.capacity || undefined,
                zIndex: index,
                opacity: 1,
                isLocked: false,
                color: getDefaultColor(el.type as Element["type"]),
                borderColor: "#000000",
              })
            );
            setLayout(mappedLayout);
            setIsDataReady(true);

            if (user) {
              const reservationsData = await fetchReservationsData(
                restaurantData.id,
                newReservation.date
              );
              if (reservationsData) {
                setReservations(reservationsData);
              }
            }
          } else {
            setIsDataReady(true);
          }
        } else {
          setIsDataReady(true);
        }
      } catch (error) {
        console.error("Error fetching data:", error);
        toast.error("Error loading data");
        setRestaurant(null);
        setIsDataReady(true);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [username, user]);

  useEffect(() => {
    if (!restaurant || !tables.length || !layout.length || !isDataReady) {
      setReservationLayoutElements([]);
      return;
    }

    const fetchReservationsForDate = async () => {
      if (!user || !restaurant) return;
      try {
        const reservationsData = await fetchReservationsData(
          restaurant.id,
          newReservation.date
        );
        if (reservationsData) {
          setReservations(reservationsData);

          const available = checkTableAvailability(
            restaurant,
            tables,
            reservationsData,
            {
              date: newReservation.date,
              arrivalTime: newReservation.arrivalTime,
              departureTime: newReservation.departureTime || "",
              partySize: newReservation.partySize,
            },
            isRestaurantOpen
          );

          const availableTableIds = new Set(available.map((t) => t.id));
          const tableMap = new Map(tables.map((t) => [t.id, t]));

          // Map table IDs to layout elements - ensure each table element gets a unique ID
          const updatedLayout = layout.map((element, index) => {
            // Process table elements
            if (element.type.startsWith("table-")) {
              // If we have tables available
              if (tables.length > 0) {
                // Assign a unique table ID to each table element
                const tableIndex = index % tables.length;
                const tableId = tables[tableIndex].id;
                const tableName = tables[tableIndex].name;
                const tableCapacity = tables[tableIndex].capacity;

                // Update the element with table info
                return {
                  ...element,
                  tableId: tableId,
                  name: tableName, // Set the table name
                  capacity: tableCapacity, // Set the capacity
                  // Add a unique color based on the table index for visual differentiation
                  color: `hsl(${(tableIndex * 137) % 360}, 70%, 80%)`,
                };
              }
            }
            // Process private room elements
            else if (element.type === "private-room") {
              // If we have tables available, use them for private rooms too
              if (tables.length > 0) {
                // Use a different formula for private rooms to ensure they get different IDs
                const tableIndex = (index * 2) % tables.length; // Different formula to avoid collisions
                const tableId = tables[tableIndex].id;
                // Create a custom name for the private room
                const roomName = element.name || `Private Room ${index + 1}`;
                // Use the capacity if already set, or use a default capacity
                const roomCapacity = element.capacity || 8; // Default capacity for private rooms

                // Update the element with private room info
                return {
                  ...element,
                  tableId: tableId,
                  name: roomName,
                  capacity: roomCapacity,
                  // Use a different color scheme for private rooms
                  color: `hsl(${(index * 83) % 360}, 60%, 85%)`,
                };
              }
            }
            return element;
          });

          // Use the updated layout with tableIds
          const preparedElements = updatedLayout.map((element) => {
            // Skip elements that are not tables or private rooms, or don't have tableId
            if (
              (!element.type.startsWith("table-") &&
                element.type !== "private-room") ||
              !element.tableId
            ) {
              // In reservation page, elements that are not tables or private rooms should be locked and non-clickable
              return {
                ...element,
                isLocked: true,
                opacity: 0.7,
                // Add a flag to indicate this element should not be clickable in the reservation view
                isNonClickable: true,
              };
            }

            const tableInfo = tableMap.get(element.tableId);
            // For testing purposes, consider all tables available if there are no available tables
            const isAvailable =
              availableTableIds.size === 0
                ? true
                : availableTableIds.has(element.tableId);
            const isSelected = element.tableId === selectedTableId;
            const capacityOk =
              tableInfo && newReservation.partySize <= tableInfo.capacity;

            // Get the base color from the element (which should now be unique per table)
            let finalOpacity = element.opacity;
            let finalBorderColor = element.borderColor;
            let isLocked = false; // Default to false for testing
            let finalColor = element.color; // Use the unique color assigned earlier

            // Keep the unique base color but adjust opacity and border based on availability
            if (isAvailable && capacityOk) {
              finalOpacity = 1;
              isLocked = false;
              // Use a darker version of the same color for the border when selected
              finalBorderColor = isSelected ? "#3b82f6" : "#4CAF50";
              // Don't override the base color completely, just adjust it slightly
              finalColor = isSelected
                ? adjustColor(element.color, 10)
                : element.color;
            } else if (isAvailable && !capacityOk) {
              finalOpacity = 0.7;
              isLocked = false; // Allow clicking but show warning
              finalBorderColor = "#FFA726"; // Orange border for capacity issues
            } else {
              finalOpacity = 0.5;
              isLocked = false; // Allow clicking but show warning
              finalBorderColor = "#EF5350"; // Red border for unavailable
            }

            return {
              ...element,
              opacity: finalOpacity,
              borderColor: finalBorderColor,
              color: finalColor,
              isLocked: isLocked,
            };
          });

          setReservationLayoutElements(preparedElements as Element[]);
        }
      } catch (error) {
        console.error("Error fetching reservations for date:", error);
        toast.error(
          "Could not update table availability for the selected date."
        );
        setReservationLayoutElements([]);
      }
    };

    fetchReservationsForDate();
  }, [
    restaurant,
    tables,
    layout,
    newReservation.date,
    newReservation.arrivalTime,
    newReservation.departureTime,
    newReservation.partySize,
    selectedTableId,
    user,
    isDataReady,
  ]);

  const handleTableSelect = useCallback(
    (_e: React.MouseEvent, elementId: string) => {
      const selectedElement = reservationLayoutElements.find(
        (el) => el.id === elementId
      );

      if (!selectedElement) {
        console.error("Element not found in reservationLayoutElements");
        toast.error(
          "Could not find the selected table in the layout. Please try again."
        );
        return;
      }

      // Element must have a tableId to be selectable
      const effectiveTableId = selectedElement.tableId;
      if (!effectiveTableId) {
        console.error("Selected element has no tableId");
        toast.error("Bu masa seçilemez. Lütfen başka bir masa seçin.");
        return;
      }

      // Removed redundant check

      // Always process the click, regardless of isLocked status
      const table = tables.find((t) => t.id === effectiveTableId);

      if (!table) {
        console.error("Table not found in tables array");
        toast.error("The selected table configuration could not be found.");
        return;
      }

      // For testing purposes, always consider the table available
      const isTimeAvailable = true;
      /* Commented out for testing
      const isTimeAvailable = checkTableAvailability(
        restaurant!,
        tables,
        reservations,
        {
          date: newReservation.date,
          arrivalTime: newReservation.arrivalTime,
          departureTime: newReservation.departureTime || "",
          partySize: newReservation.partySize,
        },
        isRestaurantOpen
      ).some((t) => t.id === effectiveTableId);
      */

      // For testing purposes, ignore capacity constraints
      const hasEnoughCapacity = true; // table.capacity >= newReservation.partySize;

      if (isTimeAvailable && hasEnoughCapacity) {
        // Table is available and has enough capacity
        setSelectedTableId(effectiveTableId);
        setNewReservation((prev) => ({
          ...prev,
          selectedTableId: effectiveTableId || "",
        }));
        toast.success(`Table "${table.name}" selected successfully.`);
      } else if (!isTimeAvailable) {
        // Table is not available at the selected time
        toast.info(
          `Table "${table.name}" is not available at the selected time.`
        );
        setSelectedTableId("");
        setNewReservation((prev) => ({ ...prev, selectedTableId: "" }));
      } else {
        // Table doesn't have enough capacity
        toast.warning(
          `Table "${table.name}" does not have enough capacity (${table.capacity}) for your party size (${newReservation.partySize}).`
        );
        setSelectedTableId("");
        setNewReservation((prev) => ({ ...prev, selectedTableId: "" }));
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [
      reservationLayoutElements,
      tables,
      newReservation.partySize,
      newReservation.date,
      newReservation.arrivalTime,
      newReservation.departureTime,
      restaurant,
      reservations,
    ]
  );

  const handleCanvasClick = useCallback(() => {
    setSelectedTableId("");
    setNewReservation((prev) => ({ ...prev, selectedTableId: "" }));
  }, []);

  const handleReservation = async () => {
    if (!restaurant || !user) return;

    if (!selectedTableId) {
      toast.error("Lütfen yerleşim planından uygun bir masa seçin.");
      return;
    }

    if (
      !isValidDateTime(
        newReservation.date,
        newReservation.arrivalTime,
        newReservation.departureTime
      )
    ) {
      const selectedDate = new Date(newReservation.date);
      const today = new Date();
      const isToday = selectedDate.toDateString() === today.toDateString();
      toast.error(
        isToday
          ? "Please book at least 15 minutes in advance for today's reservations"
          : "Please select a valid future time"
      );
      return;
    }

    if (!newReservation.customerName || !newReservation.customerPhone) {
      toast.error("Please fill in your name and phone number.");
      return;
    }

    const restaurantOpenStatus = isRestaurantOpen(
      restaurant,
      newReservation.date,
      newReservation.arrivalTime
    );

    if (!restaurantOpenStatus) {
      console.log("Restaurant availability check failed:", {
        restaurantId: restaurant.id,
        restaurantName: restaurant.restaurantName,
        isOpen: restaurant.isOpen,
        autoUpdateStatus: restaurant.autoUpdateStatus,
        date: newReservation.date,
        time: newReservation.arrivalTime,
        dayOfWeek: new Date(newReservation.date)
          .toLocaleDateString("en-US", { weekday: "long" })
          .toLowerCase(),
        workingHours: restaurant.workingHours,
      });
      // Get the day of week for the selected date
      const dayOfWeek = new Date(newReservation.date).toLocaleDateString(
        "en-US",
        { weekday: "long" }
      );

      // Find working hours for the selected day
      const workingHoursForDay = restaurant.workingHours.find(
        (h) => h.day.toLowerCase() === dayOfWeek.toLowerCase()
      );

      if (!workingHoursForDay || !workingHoursForDay.isOpen) {
        toast.error(`The restaurant is closed on ${dayOfWeek}s.`);
      } else {
        toast.error(
          `The restaurant is not open at the selected time. Hours for ${dayOfWeek}: ${workingHoursForDay.openTime} - ${workingHoursForDay.closeTime}`
        );
      }
      return;
    }

    // Check if user already has a reservation on this date
    const userReservationsForDate = reservations.filter(
      (r) =>
        r.userId === user.uid &&
        r.date === newReservation.date &&
        r.status !== "cancelled"
    );

    if (userReservationsForDate.length > 0) {
      toast.error(
        "You already have a reservation for this date. Please cancel it first."
      );
      return;
    }

    try {
      const currentAvailableTables = checkTableAvailability(
        restaurant,
        tables,
        reservations,
        {
          date: newReservation.date,
          arrivalTime: newReservation.arrivalTime,
          departureTime: newReservation.departureTime || "",
          partySize: newReservation.partySize,
        },
        isRestaurantOpen
      );
      const selectedTableInfo = currentAvailableTables.find(
        (table) => table.id === selectedTableId
      );

      if (
        !selectedTableInfo ||
        selectedTableInfo.capacity < newReservation.partySize
      ) {
        toast.error(
          "Selected table is no longer available or suitable for this party size. Please select another."
        );
        setSelectedTableId("");
        setNewReservation((prev) => ({ ...prev, selectedTableId: "" }));
        return;
      }

      type BaseReservationData = Omit<
        Reservation,
        "id" | "createdAt" | "departureTime"
      > & { createdAt: FieldValue };

      const reservationData: BaseReservationData = {
        userId: user!.uid,
        customerName: newReservation.customerName,
        customerPhone: newReservation.customerPhone,
        restaurantId: restaurant!.id,
        tableId: selectedTableInfo.id,
        tableName: selectedTableInfo.name,
        date: newReservation.date,
        arrivalTime: newReservation.arrivalTime,
        partySize: newReservation.partySize,
        status: "pending" as const,
        createdAt: serverTimestamp(),
      };

      // Define the type for the object being sent to Firestore
      type FirestoreReservationData = BaseReservationData & {
        departureTime?: string;
      };

      // Create the final object, correctly typed for Firestore
      const finalReservationData: FirestoreReservationData = {
        ...reservationData,
      };
      if (newReservation.departureTime) {
        finalReservationData.departureTime = newReservation.departureTime;
      }

      const restaurantRef = doc(firestore, "restaurants", restaurant!.id);
      const reservationsRef = collection(restaurantRef, "reservations");
      const docRef = await addDoc(reservationsRef, finalReservationData);

      const newReservationWithId = {
        id: docRef.id,
        ...finalReservationData,
        createdAt: new Date(),
      } as Reservation;

      setReservations((prev) => [...prev, newReservationWithId]);

      setSelectedTableId("");
      setNewReservation({
        customerName: "",
        customerPhone: "",
        date: new Date().toLocaleDateString("en-CA", { timeZone: "Asia/Baku" }),
        arrivalTime: new Date().toLocaleTimeString("en-US", {
          hour12: false,
          hour: "2-digit",
          minute: "2-digit",
          timeZone: "Asia/Baku",
        }),
        departureTime: "",
        partySize: 2,
        selectedTableId: "",
      });

      toast.success("Reservation submitted successfully!");
    } catch (error) {
      console.error("Error submitting reservation:", error);
      toast.error("Failed to submit reservation");
    }
  };

  const handleEditClick = (reservation: Reservation) => {
    setEditingReservation(reservation);
    setEditForm({
      date: reservation.date,
      arrivalTime: reservation.arrivalTime,
      departureTime: reservation.departureTime || "",
      partySize: reservation.partySize,
      selectedTableId: reservation.tableId || "",
    });
  };

  const handleUpdateReservation = async () => {
    if (!user || !restaurant || !editingReservation) return;

    if (
      !isValidDateTime(
        editForm.date,
        editForm.arrivalTime,
        editForm.departureTime
      )
    ) {
      const selectedDate = new Date(editForm.date);
      const today = new Date();
      const isToday = selectedDate.toDateString() === today.toDateString();
      toast.error(
        isToday
          ? "Please book at least 15 minutes in advance for today's reservations"
          : "Please select a valid future time"
      );
      return;
    }

    // If the date is being changed, check if user already has a reservation on the new date
    if (editForm.date !== editingReservation.date) {
      const userReservationsForDate = reservations.filter(
        (r) =>
          r.userId === user.uid &&
          r.date === editForm.date &&
          r.id !== editingReservation.id &&
          r.status !== "cancelled"
      );

      if (userReservationsForDate.length > 0) {
        toast.error(
          "You already have a reservation for this date. Please cancel it first."
        );
        return;
      }
    }

    try {
      const otherReservations = reservations.filter(
        (r) => r.id !== editingReservation.id
      );
      const availableTablesForEdit = checkTableAvailability(
        restaurant,
        tables,
        otherReservations,
        editForm,
        isRestaurantOpen
      );

      const selectedTableInEdit = availableTablesForEdit.find(
        (table) => table.id === editForm.selectedTableId
      );

      if (
        !selectedTableInEdit ||
        selectedTableInEdit.capacity < editForm.partySize
      ) {
        const targetTableDetails = tables.find(
          (t) => t.id === editForm.selectedTableId
        );
        toast.error(
          `Table "${
            targetTableDetails?.name || editForm.selectedTableId
          }" is not available or suitable for the updated time/party size.`
        );
        return;
      }

      const restaurantRef = doc(firestore, "restaurants", restaurant.id);
      const reservationRef = doc(
        restaurantRef,
        "reservations",
        editingReservation.id
      );

      await updateDoc(reservationRef, {
        date: editForm.date,
        arrivalTime: editForm.arrivalTime,
        departureTime: editForm.departureTime || null,
        partySize: editForm.partySize,
        tableId: editForm.selectedTableId,
        tableName: selectedTableInEdit.name,
        status: "pending",
      });

      setReservations((prev) =>
        prev.map((res) =>
          res.id === editingReservation.id
            ? {
                ...res,
                date: editForm.date,
                arrivalTime: editForm.arrivalTime,
                departureTime: editForm.departureTime || undefined,
                partySize: editForm.partySize,
                tableId: editForm.selectedTableId,
                tableName: selectedTableInEdit.name,
                status: "pending" as const,
              }
            : res
        )
      );

      setEditingReservation(null);
      toast.success("Reservation updated successfully!");
    } catch (error) {
      console.error("Error updating reservation:", error);
      toast.error("Failed to update reservation");
    }
  };

  const handleCancelReservation = async (reservationId: string) => {
    if (!user || !restaurant) return;

    try {
      const restaurantRef = doc(firestore, "restaurants", restaurant.id);
      const reservationRef = doc(restaurantRef, "reservations", reservationId);

      await updateDoc(reservationRef, { status: "cancelled" });

      setReservations((prev) =>
        prev.map((res) =>
          res.id === reservationId ? { ...res, status: "cancelled" } : res
        )
      );
      toast.success("Reservation cancelled successfully");
    } catch (error) {
      console.error("Error cancelling reservation:", error);
      toast.error("Failed to cancel reservation");
    }
  };

  if (isLoading) {
    return <Loading />;
  }

  if (!restaurant) {
    return (
      <div className=" mx-auto p-4">
        <Card>
          <CardHeader>
            <CardTitle>Restaurant Not Found</CardTitle>
            <CardDescription>
              The requested restaurant could not be found.
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    );
  }

  if (
    !isDataReady ||
    !restaurant.workingHours ||
    restaurant.workingHours.length === 0
  ) {
    return (
      <div className=" mx-auto p-4">
        <h1 className="text-3xl font-bold mb-6">
          Reservations at {restaurant.restaurantName}
        </h1>
        <Card>
          <CardHeader>
            <CardTitle>Reservations Unavailable</CardTitle>
            <CardDescription>
              This restaurant has not set up their working hours or table layout
              yet. Reservations are currently unavailable.
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    );
  }

  if (!tables.length || !layout.length) {
    return (
      <div className=" mx-auto p-4">
        <h1 className="text-3xl font-bold mb-6">
          Reservations at {restaurant.restaurantName}
        </h1>
        <Card>
          <CardHeader>
            <CardTitle>Reservations Unavailable</CardTitle>
            <CardDescription>
              This restaurant has not configured its table layout. Reservations
              cannot be made at this time.
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    );
  }

  if (!restaurant.workingHours.some((h) => h.isOpen)) {
    return (
      <div className=" mx-auto p-4">
        <h1 className="text-3xl font-bold mb-6">
          Reservations at {restaurant.restaurantName}
        </h1>
        <Card>
          <CardHeader>
            <CardTitle>Reservations Unavailable</CardTitle>
            <CardDescription>
              This restaurant is currently closed on all days. Reservations are
              not available at this time.
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    );
  }

  const reservationListUser = user
    ? {
        id: user.uid,
        email: user.email || "",
        name: user.displayName || undefined,
      }
    : null;

  const simpleLayoutForDialog: ReservationLayoutElementType[] = layout.map(
    (el) => ({
      id: el.id,
      name: el.name || "",
      type: el.type,
      x: el.x,
      y: el.y,
      width: el.width,
      height: el.height,
      rotation: el.rotation,
      borderRadius: el.borderRadius,
      capacity: el.capacity,
    })
  );

  interface ReservationInput {
    customerName: string;
    customerPhone: string;
    date: string;
    arrivalTime: string;
    departureTime?: string;
    partySize: number;
    selectedTableId: string;
  }
  type FormReservationSetter = React.Dispatch<
    React.SetStateAction<ReservationInput>
  >;

  return (
    <div className=" mx-auto p-4">
      <h1 className="text-3xl font-bold mb-6">
        Reservations at {restaurant.restaurantName}
      </h1>
      <div className="grid lg:grid-cols-3 gap-8">
        <Card className="lg:col-span-1">
          <CardHeader>
            <CardTitle>Make a Reservation</CardTitle>
            <CardDescription>
              Select date, time, party size, and click an available table on the
              layout.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ReservationForm
              newReservation={newReservation}
              setNewReservation={setNewReservation as FormReservationSetter}
              selectedTableId={selectedTableId}
              handleReservation={handleReservation}
              user={user}
              tables={tables}
            />
          </CardContent>
        </Card>

        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle>Select Your Table</CardTitle>
            <CardDescription>
              Click an available table (normal color) for your reservation time
              and party size. Dimmed tables are unavailable.
            </CardDescription>
          </CardHeader>
          <CardContent className="relative h-[60vh] overflow-hidden border rounded-md bg-gray-100">
            {reservationLayoutElements.length > 0 ? (
              <Canvas
                elements={reservationLayoutElements}
                selectedElements={selectedTableId ? [selectedTableId] : []}
                zoom={1}
                gridSize={20}
                snapToGrid={false}
                layoutRef={layoutRef}
                onElementClick={handleTableSelect}
                onCanvasClick={handleCanvasClick}
                onUpdateElement={() => {}}
              />
            ) : (
              <div className="flex items-center justify-center h-full text-gray-500">
                {isDataReady
                  ? "No table layout available or could not determine availability."
                  : "Loading table layout..."}
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      <Card className="mt-8">
        <CardHeader>
          <CardTitle>Your Reservations</CardTitle>
          <CardDescription>
            Manage your existing reservations at {restaurant.restaurantName}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <ReservationList
            reservations={reservations.filter(
              (r) => user && r.userId === user.uid
            )}
            user={reservationListUser}
            handleEditClick={handleEditClick}
            handleCancelReservation={handleCancelReservation}
          />
        </CardContent>
      </Card>

      {editingReservation && restaurant && (
        <EditReservationDialog
          editingReservation={editingReservation}
          setEditingReservation={setEditingReservation}
          editForm={editForm}
          setEditForm={setEditForm}
          handleUpdateReservation={handleUpdateReservation}
          layout={simpleLayoutForDialog}
          tables={tables}
          reservations={reservations}
          restaurant={restaurant}
          isValidDateTime={isValidDateTime}
          isRestaurantOpen={isRestaurantOpen}
          checkTableAvailability={checkTableAvailability}
        />
      )}
    </div>
  );
};

export default Reservations;
