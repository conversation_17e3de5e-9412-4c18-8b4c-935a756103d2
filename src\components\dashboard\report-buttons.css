/* Consistent styling for report buttons */
.report-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 500;
  padding: 0.5rem 0.75rem;
  cursor: pointer;
  border-radius: 0.375rem;
  transition: all 0.2s ease-in-out;
  border: 1px solid transparent;
  background-color: transparent;
}

.report-button:hover {
  background-color: hsl(var(--muted) / 0.5);
  border-color: hsl(var(--border) / 0.5);
}

.report-button:active {
  background-color: hsl(var(--muted) / 0.7);
  transform: translateY(1px);
}

.report-button svg {
  width: 1rem;
  height: 1rem;
}

/* Ensure dropdown menu items have consistent styling */
[role="menuitem"] {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 500;
  padding: 0.5rem;
  cursor: pointer;
  transition: background-color 0.2s ease-in-out;
}

/* Override default dropdown menu item hover and active styles */
.report-button[role="menuitem"]:hover {
  background-color: hsl(var(--muted) / 0.5);
  border-color: hsl(var(--border) / 0.5);
}

.report-button[role="menuitem"]:active {
  background-color: hsl(var(--muted) / 0.7);
  transform: translateY(1px);
}
