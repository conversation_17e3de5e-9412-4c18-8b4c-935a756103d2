import { useState } from "react";
import { Button } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON>Header,
  <PERSON><PERSON>Title,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
// Checkbox import removed as it's not used
// import { Checkbox } from "@/components/ui/checkbox";
import { SimpleDatePicker } from "@/components/ui/simple-date-picker";
// Removed unused imports
import { FileText, Loader2 } from "lucide-react";
import { toast } from "sonner";
import "./report-buttons.css";
import {
  ReportOptions,
  ReportData,
  saveBusinessReport
} from "@/utils/reportGenerator";

interface ReportGeneratorProps {
  restaurantName: string;
  restaurantId: string;
  orderData: { date: string; count: number; revenue: number }[];
  menuItemData: { name: string; count: number }[];
  reviewData: { id: string; userName: string; rating: number; comment: string; createdAt: Date | { toDate: () => Date } | { seconds: number; nanoseconds: number } | string | number }[];
  customerMetrics?: {
    newCustomers: number;
    returningCustomers: number;
    averageOrderValue: number;
    customerRetentionRate: number;
    orderFrequency: number;
  };
  orderChartRef?: React.RefObject<HTMLDivElement>;
  revenueChartRef?: React.RefObject<HTMLDivElement>;
  menuChartRef?: React.RefObject<HTMLDivElement>;
  reviewChartRef?: React.RefObject<HTMLDivElement>;
}

export const ReportGenerator = ({
  restaurantName,
  restaurantId,
  orderData,
  menuItemData,
  reviewData,
  customerMetrics,
  orderChartRef,
  revenueChartRef,
  menuChartRef,
  reviewChartRef,
}: ReportGeneratorProps) => {
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);

  // Report options
  const [reportType, setReportType] = useState<"summary" | "detailed" | "custom">("summary");
  const [dateRange, setDateRange] = useState<"week" | "month" | "year" | "custom">("month");
  const [customStartDate, setCustomStartDate] = useState<Date | undefined>(undefined);
  const [customEndDate, setCustomEndDate] = useState<Date | undefined>(undefined);
  const [includeCharts, setIncludeCharts] = useState(true);
  const [includeReviews, setIncludeReviews] = useState(true);
  const [includeFinancials, setIncludeFinancials] = useState(true);
  const [includeCustomerMetrics, setIncludeCustomerMetrics] = useState(true);

  const handleGenerateReport = async () => {
    try {
      setLoading(true);

      // Validate custom date range if selected
      if (dateRange === "custom" && (!customStartDate || !customEndDate)) {
        toast.error("Please select both start and end dates for custom date range");
        setLoading(false);
        return;
      }

      // Calculate total orders and revenue
      const totalOrders = orderData.reduce((sum, item) => sum + item.count, 0);
      const totalRevenue = orderData.reduce((sum, item) => sum + item.revenue, 0);

      // Calculate average rating
      const totalRating = reviewData.reduce((sum, review) => sum + review.rating, 0);
      const averageRating = reviewData.length > 0 ? totalRating / reviewData.length : 0;

      // Prepare report data
      const reportData: ReportData = {
        orders: orderData,
        menuItems: menuItemData,
        reviews: reviewData,
        customerMetrics: customerMetrics,
        totalOrders,
        totalRevenue,
        averageRating,
      };

      // Prepare report options
      const reportOptions: ReportOptions = {
        reportType,
        dateRange,
        customStartDate,
        customEndDate,
        includeCharts,
        includeReviews,
        includeFinancials,
        includeCustomerMetrics,
        restaurantName,
        restaurantId,
      };

      // Generate and save the report
      await saveBusinessReport(
        reportData,
        reportOptions,
        {
          orderChartRef,
          revenueChartRef,
          menuChartRef,
          reviewChartRef,
        }
      );

      toast.success("Business review report generated successfully");
      setOpen(false);
    } catch (error) {
      console.error("Error generating report:", error);
      toast.error("Failed to generate report. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <div className="report-button">
          <FileText className="h-4 w-4 mr-2" />
          Generate Report
        </div>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Generate Business Review Report</DialogTitle>
          <DialogDescription>
            Create a printable report of your business performance. Customize the
            report options below.
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="report-type" className="text-right">
              Report Type
            </Label>
            <Select
              value={reportType}
              onValueChange={(value) => setReportType(value as "summary" | "detailed" | "custom")}
            >
              <SelectTrigger id="report-type" className="col-span-3">
                <SelectValue placeholder="Select report type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="summary">Summary Report</SelectItem>
                <SelectItem value="detailed">Detailed Report</SelectItem>
                <SelectItem value="custom">Custom Report</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="date-range" className="text-right">
              Date Range
            </Label>
            <Select
              value={dateRange}
              onValueChange={(value) => setDateRange(value as "week" | "month" | "year" | "custom")}
            >
              <SelectTrigger id="date-range" className="col-span-3">
                <SelectValue placeholder="Select date range" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="week">Past Week</SelectItem>
                <SelectItem value="month">Past Month</SelectItem>
                <SelectItem value="year">Past Year</SelectItem>
                <SelectItem value="custom">Custom Range</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {dateRange === "custom" && (
            <>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="start-date" className="text-right">
                  Start Date
                </Label>
                <div className="col-span-3">
                  <SimpleDatePicker
                    value={customStartDate || new Date()}
                    onChange={setCustomStartDate}
                    className="w-full"
                  />
                </div>
              </div>

              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="end-date" className="text-right">
                  End Date
                </Label>
                <div className="col-span-3">
                  <SimpleDatePicker
                    value={customEndDate || new Date()}
                    onChange={setCustomEndDate}
                    disabled={(date) => customStartDate ? date < customStartDate : false}
                    className="w-full"
                  />
                </div>
              </div>
            </>
          )}

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="include-charts" className="text-right">
              Include Charts
            </Label>
            <div className="col-span-3 flex items-center space-x-2">
              <Switch
                id="include-charts"
                checked={includeCharts}
                onCheckedChange={setIncludeCharts}
              />
              <Label htmlFor="include-charts" className="text-sm">
                Include visual charts in the report
              </Label>
            </div>
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="include-reviews" className="text-right">
              Include Reviews
            </Label>
            <div className="col-span-3 flex items-center space-x-2">
              <Switch
                id="include-reviews"
                checked={includeReviews}
                onCheckedChange={setIncludeReviews}
              />
              <Label htmlFor="include-reviews" className="text-sm">
                Include customer reviews in the report
              </Label>
            </div>
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="include-financials" className="text-right">
              Include Financials
            </Label>
            <div className="col-span-3 flex items-center space-x-2">
              <Switch
                id="include-financials"
                checked={includeFinancials}
                onCheckedChange={setIncludeFinancials}
              />
              <Label htmlFor="include-financials" className="text-sm">
                Include financial data in the report
              </Label>
            </div>
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="include-customer-metrics" className="text-right">
              Customer Metrics
            </Label>
            <div className="col-span-3 flex items-center space-x-2">
              <Switch
                id="include-customer-metrics"
                checked={includeCustomerMetrics}
                onCheckedChange={setIncludeCustomerMetrics}
              />
              <Label htmlFor="include-customer-metrics" className="text-sm">
                Include customer metrics in the report
              </Label>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => setOpen(false)}>
            Cancel
          </Button>
          <Button onClick={handleGenerateReport} disabled={loading}>
            {loading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Generating...
              </>
            ) : (
              <>Generate Report</>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
