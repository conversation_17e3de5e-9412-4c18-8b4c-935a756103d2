import { useState } from "react";
import { Bell, X, ShoppingBag, Calendar } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { format } from "date-fns";
import { Timestamp } from "firebase/firestore";
import { useAuth } from "@/providers/AuthProvider";

// Define a local notification type that matches the expected structure
type NotificationType =
  | "new_order"
  | "order_preparing"
  | "order_ready"
  | "order_completed"
  | "order_cancelled"
  | "reservation_new"
  | "reservation_confirmed"
  | "reservation_cancelled"
  | "reservation_reminder"
  | "reservation_noshow"
  | "reservation_completed"
  | "system";

interface NotificationData {
  id: string;
  type: string;
  title: string;
  message: string;
  orderId?: string;
  reservationId?: string;
  read: boolean;
  createdAt: Timestamp;
}

interface NotificationsPanelProps {
  onViewOrder?: (orderId: string) => void;
  onViewReservation?: (reservationId: string) => void;
}

export const NotificationsPanel = ({
  onViewOrder,
  onViewReservation,
}: NotificationsPanelProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const {
    notifications,
    unreadNotificationsCount,
    markNotificationAsRead,
    markAllNotificationsAsRead,
    deleteNotification
  } = useAuth();

  // Notification handling moved to AuthProvider

  const getNotificationIcon = (type: NotificationType) => {
    if (type.startsWith("order_") || type === "new_order") {
      return <ShoppingBag className="h-5 w-5 text-primary" />;
    }
    if (type.startsWith("reservation_")) {
      return <Calendar className="h-5 w-5 text-primary" />;
    }
    return <Bell className="h-5 w-5 text-primary" />;
  };

  const handleNotificationClick = (notification: NotificationData) => {
    if (!notification.read) {
      markNotificationAsRead(notification.id);
    }

    if (notification.orderId && onViewOrder) {
      onViewOrder(notification.orderId);
    } else if (notification.reservationId && onViewReservation) {
      onViewReservation(notification.reservationId);
    }
  };

  return (
    <div className="relative">
      <Button
        variant="outline"
        size="icon"
        className="relative"
        onClick={() => setIsOpen(!isOpen)}
      >
        <Bell className="h-5 w-5" />
        {unreadNotificationsCount > 0 && (
          <Badge
            className="absolute -top-2 -right-2 px-1.5 py-0.5 min-w-[1.25rem] h-5 flex items-center justify-center bg-primary text-white"
            variant="default"
          >
            {unreadNotificationsCount}
          </Badge>
        )}
      </Button>

      {isOpen && (
        <Card className="absolute right-0 mt-2 w-80 sm:w-96 z-50 shadow-lg">
          <CardContent className="p-0">
            <div className="p-4 flex items-center justify-between">
              <h3 className="font-medium">Notifications</h3>
              {unreadNotificationsCount > 0 && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-xs h-8"
                  onClick={markAllNotificationsAsRead}
                >
                  Mark all as read
                </Button>
              )}
            </div>
            <Separator />
            <div className="max-h-[400px] overflow-y-auto">
              {notifications.length === 0 ? (
                <div className="py-8 text-center">
                  <Bell className="h-8 w-8 mx-auto text-muted-foreground mb-2" />
                  <p className="text-muted-foreground">No notifications</p>
                </div>
              ) : (
                <div>
                  {notifications.map((notification) => (
                    <div
                      key={notification.id}
                      className={`p-4 border-b last:border-b-0 hover:bg-muted/50 cursor-pointer ${
                        !notification.read ? "bg-primary/5" : ""
                      }`}
                      onClick={() => handleNotificationClick(notification)}
                    >
                      <div className="flex gap-3">
                        <div className="mt-0.5">
                          {getNotificationIcon(notification.type as NotificationType)}
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex justify-between items-start">
                            <h4 className="font-medium text-sm">
                              {notification.title}
                            </h4>
                            <div className="flex items-center gap-1">
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-6 w-6"
                                onClick={(e: React.MouseEvent) => {
                                  e.stopPropagation();
                                  deleteNotification(notification.id);
                                }}
                              >
                                <X className="h-3.5 w-3.5" />
                              </Button>
                              {!notification.read && (
                                <div className="h-2 w-2 rounded-full bg-primary" />
                              )}
                            </div>
                          </div>
                          <p className="text-sm text-muted-foreground line-clamp-2">
                            {notification.message}
                          </p>
                          <p className="text-xs text-muted-foreground mt-1">
                            {(() => {
                              try {
                                let dateObj;
                                if (notification.createdAt instanceof Date) {
                                  dateObj = notification.createdAt;
                                } else if (typeof notification.createdAt === 'object' && notification.createdAt !== null) {
                                  // Check for Firestore Timestamp with toDate method
                                  const timestampWithMethod = notification.createdAt as { toDate?: () => Date };
                                  if (typeof timestampWithMethod.toDate === 'function') {
                                    dateObj = timestampWithMethod.toDate();
                                  } else {
                                    // Check for Firestore Timestamp-like object
                                    const timestamp = notification.createdAt as { seconds?: number; nanoseconds?: number };
                                    if (timestamp.seconds && timestamp.nanoseconds) {
                                      // Handle Firestore timestamp format
                                      dateObj = new Date(timestamp.seconds * 1000);
                                    } else {
                                      // Try to convert other objects to date
                                      try {
                                        dateObj = new Date(notification.createdAt as unknown as string | number);
                                      } catch {
                                        return "Just now";
                                      }
                                    }
                                  }
                                } else if (typeof notification.createdAt === 'string') {
                                  dateObj = new Date(notification.createdAt);
                                } else if (typeof notification.createdAt === 'number') {
                                  dateObj = new Date(notification.createdAt);
                                } else {
                                  return "Just now";
                                }

                                // Validate the date
                                if (isNaN(dateObj.getTime())) {
                                  return "Just now";
                                }

                                return format(dateObj, "MMM d, h:mm a");
                              } catch (error) {
                                console.error("Date formatting error:", error);
                                return "Just now";
                              }
                            })()}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
