import { useState, useEffect } from "react";
import { Link, NavLink, useNavigate } from "react-router-dom";
import {
  Utensils,
  Menu,
  User,
  LogOut,
  LayoutDashboard,
  Settings,
  Palette,
  Home,
  Building,
  Info,
  Tags,
  Award,
  Shield,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { useAuth } from "@/providers/AuthProvider";
import { Separator } from "@/components/ui/separator";
import { OfflineIndicator } from "@/components/OfflineIndicator";
import { PointsBalance } from "@/components/loyalty/PointsBalance";
import { useLoyaltyStatus } from "@/lib/react-query/hooks/useLoyalty";

const navItems = [
  { title: "Home", href: "/", icon: Home },
  { title: "Restaurants", href: "/restaurants", icon: Building },
  { title: "About", href: "/about", icon: Info },
  { title: "Pricing", href: "/pricing", icon: Tags },
];

// Commented out for now as we're not using these props
// interface HeaderProps {
//   onSoundToggle?: () => void;
//   isSoundEnabled?: boolean;
// }

export const Header = () => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const { user, userRole, logOut } = useAuth();
  const navigate = useNavigate();

  // Fetch user's loyalty status for points display
  const { data: loyaltyStatus } = useLoyaltyStatus(user?.uid);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };
    window.addEventListener("scroll", handleScroll);

    handleScroll();
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const getInitials = (name?: string | null): string => {
    if (!name) return "??";
    return name
      .split(" ")
      .map((n) => n[0])
      .slice(0, 2)
      .join("")
      .toUpperCase();
  };

  const handleNavigate = (path: string) => {
    navigate(path);
    setIsMobileMenuOpen(false);
  };

  const handleLogout = () => {
    logOut();
    setIsMobileMenuOpen(false);
  };

  return (
    <header
      className={cn(
        "sticky top-0 z-50 w-full transition-all duration-300 ease-in-out",
        isScrolled
          ? "bg-background/95 backdrop-blur-sm border-b border-border/50 shadow-sm"
          : "bg-transparent border-b border-transparent"
      )}
    >
      <div className="px-4 sm:px-2 lg:px-4">
        {" "}
        {/* Use container for centering */}
        <div className="flex h-16 items-center justify-between">
          {/* Logo */}
          <Link
            to="/"
            className="flex items-center gap-2 focus:outline-none focus-visible:ring-2 focus-visible:ring-ring rounded-md"
            aria-label="Go to Homepage"
          >
            <Utensils
              className={cn(
                "h-6 w-6 text-primary transition-colors",
                isScrolled ? "text-primary" : "text-primary"
              )}
            />
            <span
              className={cn(
                "text-xl font-bold transition-colors",
                isScrolled ? "text-foreground" : "text-primary"
              )}
            >
              Qonai
            </span>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center gap-2 lg:gap-4">
            {navItems.map((item) => (
              <NavLink
                key={item.title}
                to={item.href}
                className={({ isActive }) =>
                  cn(
                    "rounded-md px-3 py-1.5 text-sm font-medium transition-colors hover:text-primary focus:outline-none focus-visible:ring-2 focus-visible:ring-ring",
                    isActive
                      ? "text-primary"
                      : isScrolled
                      ? "text-foreground hover:text-primary"
                      : "text-primary hover:text-primary/80",
                    !isScrolled && !isActive && "text-primary"
                  )
                }
              >
                {item.title}
              </NavLink>
            ))}
          </nav>

          {/* Right Side Actions */}
          <div className="flex items-center gap-2 md:gap-3">
            {/* Offline Indicator */}
            <div className="hidden md:block">
              <OfflineIndicator />
            </div>

            {/* User Menu / Login Button */}
            {user ? (
              <div className="flex items-center gap-3">
                {/* Points Balance for Client Users */}
                {userRole === "client" && loyaltyStatus && (
                  <PointsBalance
                    points={loyaltyStatus.totalPoints}
                    variant="outline"
                    size="sm"
                    className="hidden sm:flex"
                  />
                )}

                <DropdownMenu modal={false}>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      className={cn(
                        "relative h-9 w-9 rounded-full focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
                        isScrolled ? "" : "hover:bg-primary/10"
                      )}
                    >
                      <Avatar className="h-8 w-8">
                        <AvatarImage
                          src={user.photoURL || ""}
                          alt={user.displayName ?? "User Avatar"}
                        />
                        <AvatarFallback>
                          {getInitials(user.displayName || user.email)}
                        </AvatarFallback>
                      </Avatar>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-56">
                    <DropdownMenuLabel className="font-normal">
                      <div className="flex flex-col space-y-1">
                        <p className="text-sm font-medium leading-none">
                          {user.displayName || "User"}
                        </p>
                        <p className="text-xs leading-none text-muted-foreground">
                          {user.email}
                        </p>
                      </div>
                    </DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem
                      onSelect={() => handleNavigate("/profile")}
                    >
                      <User className="mr-2 h-4 w-4" />
                      <span>Profile</span>
                    </DropdownMenuItem>
                    {userRole === "client" && (
                      <DropdownMenuItem
                        onSelect={() => handleNavigate("/loyalty")}
                      >
                        <Award className="mr-2 h-4 w-4" />
                        <span>Loyalty Program</span>
                      </DropdownMenuItem>
                    )}
                    <DropdownMenuItem
                      onSelect={() => handleNavigate("/dashboard")}
                    >
                      <LayoutDashboard className="mr-2 h-4 w-4" />
                      <span>Dashboard</span>
                    </DropdownMenuItem>
                    {userRole === "restaurant" && (
                      <DropdownMenuItem
                        onSelect={() => handleNavigate("/admin-panel")}
                      >
                        <Settings className="mr-2 h-4 w-4" />
                        <span>Admin Panel</span>
                      </DropdownMenuItem>
                    )}
                    {user.email === "<EMAIL>" && (
                      <>
                        <DropdownMenuItem
                          onSelect={() => handleNavigate("/admin")}
                        >
                          <Shield className="mr-2 h-4 w-4" />
                          <span>Admin Panel</span>
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onSelect={() => handleNavigate("/admin-rewards")}
                        >
                          <Award className="mr-2 h-4 w-4" />
                          <span>Global Rewards</span>
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onSelect={() => handleNavigate("/email-panel")}
                        >
                          <Palette className="mr-2 h-4 w-4" />
                          <span>Email Template</span>
                        </DropdownMenuItem>
                      </>
                    )}
                    <DropdownMenuSeparator />
                    <DropdownMenuItem
                      onSelect={handleLogout}
                      className="text-red-600 focus:text-red-600 focus:bg-red-50"
                    >
                      <LogOut className="mr-2 h-4 w-4" />
                      <span>Log out</span>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            ) : (
              <Button
                variant={isScrolled ? "outline" : "ghost"}
                size="sm"
                onClick={() => handleNavigate("/auth")}
                className={cn(
                  !isScrolled && "text-primary hover:bg-primary/10"
                )}
              >
                Login
              </Button>
            )}

            {/* Mobile Menu Trigger */}
            <Sheet
              modal={false}
              open={isMobileMenuOpen}
              onOpenChange={setIsMobileMenuOpen}
            >
              <SheetTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className={cn(
                    "md:hidden rounded-full",
                    isScrolled
                      ? "text-foreground hover:bg-accent"
                      : "text-primary hover:bg-primary/10"
                  )}
                  aria-label="Open main menu"
                >
                  <Menu className="h-5 w-5" />
                </Button>
              </SheetTrigger>
              <SheetContent
                side="right"
                className="w-full max-w-xs sm:max-w-sm"
                hideCloseButton
              >
                <SheetHeader className="border-b pb-4 mb-4">
                  <SheetTitle className="flex items-center gap-2">
                    <Utensils className="h-5 w-5 text-primary" />
                    <span className="text-lg font-semibold">Qonai</span>
                  </SheetTitle>
                  {/* Kapatma butonu kaldırıldı - Sheet bileşeni zaten otomatik olarak bir X butonu ekliyor */}
                </SheetHeader>

                <nav className="flex flex-col space-y-2 mb-6">
                  {navItems.map((item) => (
                    <NavLink
                      key={item.title}
                      to={item.href}
                      onClick={() => setIsMobileMenuOpen(false)}
                      className={({ isActive }) =>
                        cn(
                          "flex items-center gap-3 rounded-md px-3 py-2 text-base font-medium transition-colors hover:bg-accent",
                          isActive
                            ? "text-primary bg-accent"
                            : "text-muted-foreground"
                        )
                      }
                    >
                      <item.icon className="h-5 w-5" />
                      {item.title}
                    </NavLink>
                  ))}
                </nav>

                <Separator className="my-4" />

                {/* Mobile Offline Indicator */}
                <div className="mb-4 px-3">
                  <OfflineIndicator />
                </div>

                <Separator className="my-4" />

                {/* Mobile User Actions */}
                <div className="flex flex-col space-y-2">
                  {user ? (
                    <>
                      <Button
                        variant="ghost"
                        className="justify-start gap-3 px-3"
                        onClick={() => handleNavigate("/profile")}
                      >
                        <User className="h-5 w-5 text-muted-foreground" />{" "}
                        Profile
                      </Button>
                      {userRole === "client" && (
                        <Button
                          variant="ghost"
                          className="justify-start gap-3 px-3"
                          onClick={() => handleNavigate("/loyalty")}
                        >
                          <Award className="h-5 w-5 text-muted-foreground" />{" "}
                          Loyalty Program
                        </Button>
                      )}
                      <Button
                        variant="ghost"
                        className="justify-start gap-3 px-3"
                        onClick={() => handleNavigate("/dashboard")}
                      >
                        <LayoutDashboard className="h-5 w-5 text-muted-foreground" />{" "}
                        Dashboard
                      </Button>
                      {userRole === "restaurant" && (
                        <Button
                          variant="ghost"
                          className="justify-start gap-3 px-3"
                          onClick={() => handleNavigate("/admin-panel")}
                        >
                          <Settings className="h-5 w-5 text-muted-foreground" />{" "}
                          Admin Panel
                        </Button>
                      )}
                      {user.email === "<EMAIL>" && (
                        <>
                          <Button
                            variant="ghost"
                            className="justify-start gap-3 px-3"
                            onClick={() => handleNavigate("/admin")}
                          >
                            <Shield className="h-5 w-5 text-muted-foreground" />{" "}
                            Admin Panel
                          </Button>
                          <Button
                            variant="ghost"
                            className="justify-start gap-3 px-3"
                            onClick={() => handleNavigate("/admin-rewards")}
                          >
                            <Award className="h-5 w-5 text-muted-foreground" />{" "}
                            Global Rewards
                          </Button>
                          <Button
                            variant="ghost"
                            className="justify-start gap-3 px-3"
                            onClick={() => handleNavigate("/email-panel")}
                          >
                            <Palette className="h-5 w-5 text-muted-foreground" />{" "}
                            Email Template
                          </Button>
                        </>
                      )}
                      <Button
                        variant="ghost"
                        className="justify-start gap-3 px-3 text-red-600 hover:text-red-600 hover:bg-red-50"
                        onClick={handleLogout}
                      >
                        <LogOut className="h-5 w-5" /> Log out
                      </Button>
                    </>
                  ) : (
                    <Button
                      className="w-full"
                      onClick={() => handleNavigate("/auth")}
                    >
                      Login / Register
                    </Button>
                  )}
                </div>
              </SheetContent>
            </Sheet>
          </div>
        </div>
      </div>
    </header>
  );
};
