// src/components/layout-editor/PropertiesPanel.tsx
import React from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";
import { Switch } from "@/components/ui/switch";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Trash2,
  AlignLeft,
  AlignRight,
  AlignStartVertical,
  AlignEndVertical,
  Copy,
  Lock,
  Unlock,
} from "lucide-react";
import type { Element } from "./types";

interface PropertiesPanelProps {
  selectedElements: string[];
  elements: Element[];
  onUpdateElement: (
    id: string,
    updates: Partial<Element>,
    updateHistory?: boolean
  ) => void;
  onDeleteSelected: () => void;
  onDuplicateSelected: () => void;
  onGroupSelected: () => void;
  onUngroupSelected: () => void;
  onAlign: (direction: "left" | "right" | "top" | "bottom") => void;
  onStartInputDrag: (
    e: React.MouseEvent<HTMLLabelElement>,
    elementId: string,
    property: keyof Element,
    initialValue: number,
    step?: number,
    minValue?: number,
    maxValue?: number
  ) => void;
  setSelectedElements: React.Dispatch<React.SetStateAction<string[]>>;
}

export const PropertiesPanel: React.FC<PropertiesPanelProps> = ({
  selectedElements,
  elements,
  onUpdateElement,
  onDeleteSelected,
  onDuplicateSelected,
  onGroupSelected,
  onUngroupSelected,
  onAlign,
  onStartInputDrag,
  setSelectedElements,
}) => {
  if (selectedElements.length === 0) {
    return (
      <p className="text-sm text-gray-500 px-4">
        No element selected. Click an element to edit.
      </p>
    );
  }

  if (selectedElements.length > 1) {
    // --- Multiple Selection Panel ---
    const areAnyLocked = elements.some(
      (el) => selectedElements.includes(el.id) && el.isLocked
    );
    const canUngroup = elements.some(
      (el) => selectedElements.includes(el.id) && el.isGrouped
    );

    return (
      <div className="space-y-4 p-4">
        <p className="font-semibold">
          {selectedElements.length} elements selected.
        </p>
        {/* Common actions for multiple elements */}
        <div className="grid grid-cols-2 gap-2">
          <TooltipProvider delayDuration={100}>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onAlign("left")}
                >
                  <AlignLeft className="w-4 h-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>Align Left</TooltipContent>
            </Tooltip>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onAlign("right")}
                >
                  <AlignRight className="w-4 h-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>Align Right</TooltipContent>
            </Tooltip>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onAlign("top")}
                >
                  <AlignStartVertical className="w-4 h-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>Align Top</TooltipContent>
            </Tooltip>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onAlign("bottom")}
                >
                  <AlignEndVertical className="w-4 h-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>Align Bottom</TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={onGroupSelected}
          disabled={selectedElements.length < 2}
        >
          {/* <Group className="w-4 h-4 mr-2" /> */} Group Selected (Ctrl+G)
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={onUngroupSelected}
          disabled={!canUngroup}
        >
          {/* <Ungroup className="w-4 h-4 mr-2" /> */} Ungroup Selected
          (Ctrl+Shift+G)
        </Button>
        <Button variant="outline" size="sm" onClick={onDuplicateSelected}>
          <Copy className="w-4 h-4 mr-2" /> Duplicate (Ctrl+D)
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => {
            const newLockedState = !areAnyLocked;
            selectedElements.forEach((id) =>
              onUpdateElement(id, { isLocked: newLockedState })
            );
            // Consider moving toast notification logic to the parent component
            // toast.info(`Elements ${newLockedState ? "locked" : "unlocked"}`);
          }}
        >
          {areAnyLocked ? (
            <Unlock className="w-4 h-4 mr-2" />
          ) : (
            <Lock className="w-4 h-4 mr-2" />
          )}
          Toggle Lock
        </Button>

        <Button variant="destructive" size="sm" onClick={onDeleteSelected}>
          <Trash2 className="w-4 h-4 mr-2" /> Delete Selected (Del)
        </Button>
      </div>
    );
  }

  // --- Single Selection Panel ---
  const element = elements.find((el) => el.id === selectedElements[0]);
  if (!element) {
    return (
      <p className="text-sm text-red-500 px-4">
        Error: Selected element not found.
      </p>
    );
  }

  const handleSingleUngroup = () => {
    setSelectedElements([element.id]);
    setTimeout(() => {
      onUngroupSelected();
    }, 0);
  };

  return (
    <div className="space-y-4 p-4">
      <div>
        <Label htmlFor="el-type">Type</Label>
        <Input id="el-type" value={element.type} disabled className="mt-1" />
      </div>

      {/* Common Name Field */}
      {(element.type.includes("table") ||
        element.type === "private-room" ||
        element.type === "bar" ||
        element.type === "kitchen") && (
        <div>
          <Label htmlFor="el-name" className="cursor-text">
            Name
          </Label>
          <Input
            id="el-name"
            value={element.name || ""}
            placeholder="Optional name"
            onChange={(e) =>
              onUpdateElement(element.id, { name: e.target.value || undefined })
            }
            className="mt-1"
          />
        </div>
      )}

      {/* Capacity Field (Tables and Private Rooms) */}
      {(element.type.includes("table") || element.type === "private-room") && (
        <div>
          <Label
            htmlFor="el-capacity"
            className="cursor-ew-resize"
            onMouseDown={(e) =>
              onStartInputDrag(
                e,
                element.id,
                "capacity",
                element.capacity ?? 0,
                0.1,
                1
              )
            }
            title="Drag horizontally to change capacity"
          >
            Capacity
          </Label>
          <Input
            id="el-capacity"
            type="number"
            min="1"
            value={element.capacity ?? ""}
            onChange={(e) =>
              onUpdateElement(element.id, {
                capacity: Math.max(1, parseInt(e.target.value) || 0),
              })
            }
            className="mt-1"
          />
        </div>
      )}

      {/* Position */}
      <div className="grid grid-cols-2 gap-2">
        <div>
          <Label
            htmlFor="el-x"
            className="cursor-ew-resize"
            onMouseDown={(e) =>
              onStartInputDrag(e, element.id, "x", element.x, 1, 0)
            }
            title="Drag horizontally to change X position"
          >
            X Pos
          </Label>
          <Input
            id="el-x"
            type="number"
            value={Math.round(element.x)}
            onChange={(e) =>
              onUpdateElement(element.id, {
                x: parseFloat(e.target.value) || 0,
              })
            }
            className="mt-1"
          />
        </div>
        <div>
          <Label
            htmlFor="el-y"
            className="cursor-ew-resize"
            onMouseDown={(e) =>
              onStartInputDrag(e, element.id, "y", element.y, 1, 0)
            }
            title="Drag horizontally to change Y position"
          >
            Y Pos
          </Label>
          <Input
            id="el-y"
            type="number"
            value={Math.round(element.y)}
            onChange={(e) =>
              onUpdateElement(element.id, {
                y: parseFloat(e.target.value) || 0,
              })
            }
            className="mt-1"
          />
        </div>
      </div>

      {/* Dimensions */}
      <div className="space-y-1">
        <Label
          htmlFor="el-width"
          className="cursor-ew-resize"
          onMouseDown={(e) =>
            onStartInputDrag(e, element.id, "width", element.width, 1, 10)
          }
          title="Drag horizontally to change width"
        >
          Width ({Math.round(element.width)}px)
        </Label>
        <div className="flex gap-2 items-center mt-1">
          <Input
            id="el-width"
            type="number"
            value={Math.round(element.width)}
            min="20"
            max="1000" // Limit maximum width to prevent oversized elements
            onChange={(e) => {
              // Get canvas dimensions to limit element size
              const canvasEl =
                document.querySelector(".element-render")?.parentElement
                  ?.parentElement;
              const maxWidth = canvasEl ? canvasEl.clientWidth : 1000;

              onUpdateElement(element.id, {
                width: Math.min(
                  maxWidth,
                  Math.max(20, parseFloat(e.target.value) || 20)
                ),
              });
            }}
            className="flex-1"
          />
        </div>
      </div>
      <div className="space-y-1">
        <Label
          htmlFor="el-height"
          className="cursor-ew-resize"
          onMouseDown={(e) =>
            onStartInputDrag(e, element.id, "height", element.height, 1, 10)
          }
          title="Drag horizontally to change height"
        >
          Height ({Math.round(element.height)}px)
        </Label>
        <div className="flex gap-2 items-center mt-1">
          <Input
            id="el-height"
            type="number"
            value={Math.round(element.height)}
            min="20"
            max="1000" // Limit maximum height to prevent oversized elements
            onChange={(e) => {
              // Get canvas dimensions to limit element size
              const canvasEl =
                document.querySelector(".element-render")?.parentElement
                  ?.parentElement;
              const maxHeight = canvasEl ? canvasEl.clientHeight : 600;

              onUpdateElement(element.id, {
                height: Math.min(
                  maxHeight,
                  Math.max(20, parseFloat(e.target.value) || 20)
                ),
              });
            }}
            className="flex-1"
          />
        </div>
      </div>

      {/* Rotation */}
      <div className="space-y-1">
        <Label
          htmlFor="el-rotation"
          className="cursor-ew-resize"
          onMouseDown={(e) =>
            onStartInputDrag(
              e,
              element.id,
              "rotation",
              element.rotation,
              1,
              -360,
              360
            )
          }
          title="Drag horizontally to change rotation"
        >
          Rotation ({Math.round(element.rotation)}°)
        </Label>
        <Slider
          id="el-rotation"
          value={[element.rotation]}
          min={-180}
          max={180}
          step={1}
          onValueChange={([value]) =>
            onUpdateElement(element.id, { rotation: value })
          }
          className="mt-1"
        />
      </div>

      {/* Z-Index */}
      <div>
        <Label
          htmlFor="el-zindex"
          className="cursor-ew-resize"
          onMouseDown={(e) =>
            onStartInputDrag(e, element.id, "zIndex", element.zIndex, 0.1)
          }
          title="Drag horizontally to change stack order (z-index)"
        >
          Stack Order (Z-Index)
        </Label>
        <Input
          id="el-zindex"
          type="number"
          value={element.zIndex}
          step="1"
          onChange={(e) =>
            onUpdateElement(element.id, {
              zIndex: parseInt(e.target.value) || 0,
            })
          }
          className="mt-1"
        />
      </div>

      {/* Opacity */}
      <div className="space-y-1">
        <Label
          htmlFor="el-opacity"
          className="cursor-ew-resize"
          onMouseDown={(e) =>
            onStartInputDrag(
              e,
              element.id,
              "opacity",
              element.opacity,
              0.01,
              0,
              1
            )
          }
          title="Drag horizontally to change opacity"
        >
          Opacity ({Math.round(element.opacity * 100)}%)
        </Label>
        <Slider
          id="el-opacity"
          value={[element.opacity]}
          min={0}
          max={1}
          step={0.01}
          onValueChange={([value]) =>
            onUpdateElement(element.id, { opacity: value })
          }
          className="mt-1"
        />
      </div>

      {/* Border Radius */}
      {element.type !== "table-round" && (
        <div>
          <Label
            htmlFor="el-borderradius"
            className="cursor-ew-resize"
            onMouseDown={(e) =>
              onStartInputDrag(
                e,
                element.id,
                "borderRadius",
                element.borderRadius,
                1,
                0
              )
            }
            title="Drag horizontally to change border radius"
          >
            Border Radius (px)
          </Label>
          <Input
            id="el-borderradius"
            type="number"
            min="0"
            value={element.borderRadius}
            onChange={(e) =>
              onUpdateElement(element.id, {
                borderRadius: Math.max(0, parseInt(e.target.value) || 0),
              })
            }
            className="mt-1"
          />
        </div>
      )}

      {/* Colors */}
      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="el-color" className="block mb-1">
            Color
          </Label>
          <Input
            id="el-color"
            type="color"
            value={element.color}
            onChange={(e) =>
              onUpdateElement(element.id, { color: e.target.value })
            }
            className="w-full h-8 p-0 border-none cursor-pointer"
          />
        </div>
        <div>
          <Label htmlFor="el-bordercolor" className="block mb-1">
            Border
          </Label>
          <Input
            id="el-bordercolor"
            type="color"
            value={element.borderColor}
            onChange={(e) =>
              onUpdateElement(element.id, { borderColor: e.target.value })
            }
            className="w-full h-8 p-0 border-none cursor-pointer"
          />
        </div>
      </div>

      {/* Lock */}
      <div className="flex items-center gap-2 pt-2">
        <Switch
          id="el-lock"
          checked={element.isLocked}
          onCheckedChange={(checked) =>
            onUpdateElement(element.id, { isLocked: checked })
          }
        />
        <Label htmlFor="el-lock" className="cursor-pointer">
          Lock Element
        </Label>
      </div>

      {/* Group Info */}
      {element.isGrouped && element.groupId && (
        <div className="text-xs text-gray-500 pt-1">
          Part of Group:{" "}
          <span
            title={element.groupId}
            className="font-mono bg-gray-100 px-1 rounded"
          >
            {element.groupId.substring(0, 8)}...
          </span>
          {/* Use the specific handler for single ungroup */}
          <Button
            size="sm"
            variant="link"
            onClick={handleSingleUngroup} // Use specific handler
            className="ml-2 h-auto p-0"
          >
            Ungroup
          </Button>
        </div>
      )}

      {/* Actions for Single Element */}
      <Button
        variant="outline"
        size="sm"
        onClick={onDuplicateSelected}
        className="w-full mt-2" // Added some margin
      >
        <Copy className="w-4 h-4 mr-2" /> Duplicate (Ctrl+D)
      </Button>

      {/* Delete */}
      <Button
        variant="destructive"
        onClick={onDeleteSelected}
        className="w-full mt-2" // Added some margin
      >
        <Trash2 className="w-4 h-4 mr-2" /> Delete Element
      </Button>
    </div>
  );
};
