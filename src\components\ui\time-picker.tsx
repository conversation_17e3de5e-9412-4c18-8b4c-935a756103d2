import React from "react";
import { cn } from "@/lib/utils";
import { format, parse, isValid } from "date-fns";

interface TimePickerProps {
  value: Date;
  onChange: (date: Date) => void;
  className?: string;
  disabled?: boolean | ((date: Date) => boolean);
  minTime?: string; // Format: "HH:mm"
  maxTime?: string; // Format: "HH:mm"
  step?: number; // Minutes
}

export function TimePicker({
  value,
  onChange,
  className,
  disabled = false,
  minTime = "00:00",
  maxTime = "23:59",
  step = 15,
}: TimePickerProps) {
  // Convert function-based disabled prop to boolean
  const isDisabled = typeof disabled === 'function' ? disabled(value) : disabled;
  
  // Format time for input
  const timeString = format(value, 'HH:mm');

  // Generate time options
  const generateTimeOptions = () => {
    const options = [];
    const [minHour, minMinute] = minTime.split(':').map(Number);
    const [maxHour, maxMinute] = maxTime.split(':').map(Number);
    
    const minMinutes = minHour * 60 + minMinute;
    const maxMinutes = maxHour * 60 + maxMinute;
    
    for (let minutes = minMinutes; minutes <= maxMinutes; minutes += step) {
      const hour = Math.floor(minutes / 60);
      const minute = minutes % 60;
      const timeValue = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
      options.push(timeValue);
    }
    
    return options;
  };

  const handleChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newTimeStr = e.target.value;
    const newTime = parse(newTimeStr, 'HH:mm', new Date());
    
    if (!isValid(newTime)) return;
    
    // Preserve the date part from the original value
    const newDate = new Date(value);
    newDate.setHours(newTime.getHours(), newTime.getMinutes(), 0, 0);
    
    onChange(newDate);
  };

  return (
    <div className={cn("space-y-2", className)}>
      <select
        value={timeString}
        onChange={handleChange}
        disabled={isDisabled}
        className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
      >
        {generateTimeOptions().map((time) => (
          <option key={time} value={time}>
            {time}
          </option>
        ))}
      </select>
    </div>
  );
}

interface TimeRangePickerProps {
  startTime: Date;
  endTime: Date;
  onStartTimeChange: (date: Date) => void;
  onEndTimeChange: (date: Date) => void;
  className?: string;
  step?: number;
}

export function TimeRangePicker({
  startTime,
  endTime,
  onStartTimeChange,
  onEndTimeChange,
  className,
  step = 15,
}: TimeRangePickerProps) {
  return (
    <div className={cn("space-y-4", className)}>
      <div className="space-y-2">
        <label className="text-sm font-medium">Start Time</label>
        <TimePicker
          value={startTime}
          onChange={onStartTimeChange}
          step={step}
        />
      </div>

      <div className="space-y-2">
        <label className="text-sm font-medium">End Time</label>
        <TimePicker
          value={endTime}
          onChange={onEndTimeChange}
          step={step}
          minTime={format(startTime, 'HH:mm')}
        />
      </div>
    </div>
  );
}
