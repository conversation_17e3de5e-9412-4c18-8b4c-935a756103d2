import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardDescription } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { MealPreferences as MealPreferencesType } from "@/types";
import { Badge } from "@/components/ui/badge";
import { X, Info } from "lucide-react";
import { RESTAURANT_OPTIONS } from "@/types";
import { toast } from "sonner";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Timestamp } from "firebase/firestore";

interface MealPreferencesProps {
  initialData?: MealPreferencesType;
  onSave: (data: MealPreferencesType) => void;
}

export const MealPreferences: React.FC<MealPreferencesProps> = ({
  initialData,
  onSave,
}) => {
  // Create a default meal preferences object with all required arrays initialized
  const defaultMealPreferences: MealPreferencesType = {
    favoriteIngredients: [],
    dislikedIngredients: [],
    allergies: [],
    dietaryRestrictions: [],
    mealFrequency: 3,
    preferredMealTimes: [],
    preferredCuisines: [],
    // Additional fields for recommendations
    preferredCategories: [],
    preferredPriceRange: '$$',
    preferredAtmosphere: [],
    preferredFeatures: [],
    lastUpdated: Timestamp.now()
  };

  // Ensure all array properties exist in the initialData by merging with defaults
  const [formData, setFormData] = useState<MealPreferencesType>(() => {
    if (!initialData) return defaultMealPreferences;

    // Create a new object with all default arrays, then override with initialData values
    return {
      ...defaultMealPreferences,
      ...initialData,
      // Ensure these specific arrays exist even if they're undefined in initialData
      favoriteIngredients: initialData.favoriteIngredients || [],
      dislikedIngredients: initialData.dislikedIngredients || [],
      allergies: initialData.allergies || [],
      dietaryRestrictions: initialData.dietaryRestrictions || [],
      preferredMealTimes: initialData.preferredMealTimes || [],
      preferredCuisines: initialData.preferredCuisines || [],
      preferredCategories: initialData.preferredCategories || [],
      preferredAtmosphere: initialData.preferredAtmosphere || [],
      preferredFeatures: initialData.preferredFeatures || [],
    };
  });

  const [newItem, setNewItem] = useState({
    favoriteIngredient: "",
    dislikedIngredient: "",
    allergy: "",
    mealTime: "",
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setNewItem((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleAddItem = (field: keyof typeof newItem, arrayField: keyof MealPreferencesType) => {
    const value = newItem[field].trim();
    if (!value) return;

    if (Array.isArray(formData[arrayField]) && !formData[arrayField].includes(value)) {
      setFormData((prev) => ({
        ...prev,
        [arrayField]: [...(prev[arrayField] as string[]), value],
      }));
      setNewItem((prev) => ({
        ...prev,
        [field]: "",
      }));
    } else {
      toast.error(`${value} is already in your list`);
    }
  };

  const handleRemoveItem = (arrayField: keyof MealPreferencesType, item: string) => {
    if (Array.isArray(formData[arrayField])) {
      setFormData((prev) => ({
        ...prev,
        [arrayField]: (prev[arrayField] as string[] || []).filter((i) => i !== item),
      }));
    }
  };

  const handleSelectChange = (field: keyof MealPreferencesType, value: string) => {
    if (field === "mealFrequency") {
      setFormData((prev) => ({
        ...prev,
        [field]: parseInt(value),
      }));
    } else if (
      field === "dietaryRestrictions" ||
      field === "preferredCuisines" ||
      field === "preferredCategories" ||
      field === "preferredAtmosphere" ||
      field === "preferredFeatures"
    ) {
      // Handle multi-select fields
      const currentValues = formData[field] as string[] || [];
      if (!currentValues.includes(value)) {
        setFormData((prev) => ({
          ...prev,
          [field]: [...(prev[field] as string[] || []), value],
        }));
      } else {
        toast.error(`${value} is already in your list`);
      }
    }
  };

  const handleSave = () => {
    onSave(formData);
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="text-xl font-semibold">Meal Preferences</CardTitle>
        <CardDescription>
          Your preferences help us personalize your recommendations
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-4">
          <div>
            <Label htmlFor="mealFrequency">How many meals do you eat per day?</Label>
            <Select
              value={formData.mealFrequency.toString()}
              onValueChange={(value) => handleSelectChange("mealFrequency", value)}
            >
              <SelectTrigger id="mealFrequency" className="mt-1">
                <SelectValue placeholder="Select meal frequency" />
              </SelectTrigger>
              <SelectContent>
                {[1, 2, 3, 4, 5, 6].map((num) => (
                  <SelectItem key={num} value={num.toString()}>
                    {num} {num === 1 ? "meal" : "meals"}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label>Preferred Meal Times</Label>
            <div className="flex flex-wrap gap-2 mt-2">
              {formData.preferredMealTimes.map((time) => (
                <Badge key={time} variant="secondary" className="px-2 py-1">
                  {time}
                  <button
                    type="button"
                    onClick={() => handleRemoveItem("preferredMealTimes", time)}
                    className="ml-1 text-muted-foreground hover:text-foreground"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </Badge>
              ))}
            </div>
            <div className="flex gap-2 mt-2">
              <Input
                name="mealTime"
                value={newItem.mealTime}
                onChange={handleInputChange}
                placeholder="e.g., Breakfast at 8 AM"
                className="flex-1"
              />
              <Button
                type="button"
                variant="outline"
                onClick={() => handleAddItem("mealTime", "preferredMealTimes")}
              >
                Add
              </Button>
            </div>
          </div>

          <div>
            <Label>Preferred Cuisines</Label>
            <div className="flex flex-wrap gap-2 mt-2">
              {formData.preferredCuisines.map((cuisine) => (
                <Badge key={cuisine} variant="secondary" className="px-2 py-1">
                  {cuisine}
                  <button
                    type="button"
                    onClick={() => handleRemoveItem("preferredCuisines", cuisine)}
                    className="ml-1 text-muted-foreground hover:text-foreground"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </Badge>
              ))}
            </div>
            <Select
              onValueChange={(value) => handleSelectChange("preferredCuisines", value)}
            >
              <SelectTrigger className="mt-2">
                <SelectValue placeholder="Select cuisine" />
              </SelectTrigger>
              <SelectContent>
                {RESTAURANT_OPTIONS.cuisines.map((cuisine) => (
                  <SelectItem key={cuisine} value={cuisine}>
                    {cuisine}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label>Preferred Restaurant Categories</Label>
            <div className="flex flex-wrap gap-2 mt-2">
              {formData.preferredCategories.map((category) => (
                <Badge key={category} variant="secondary" className="px-2 py-1">
                  {category}
                  <button
                    type="button"
                    onClick={() => handleRemoveItem("preferredCategories", category)}
                    className="ml-1 text-muted-foreground hover:text-foreground"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </Badge>
              ))}
            </div>
            <Select
              onValueChange={(value) => handleSelectChange("preferredCategories", value)}
            >
              <SelectTrigger className="mt-2">
                <SelectValue placeholder="Select category" />
              </SelectTrigger>
              <SelectContent>
                {RESTAURANT_OPTIONS.categories.map((category) => (
                  <SelectItem key={category} value={category}>
                    {category}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="priceRange">Preferred Price Range</Label>
            <Select
              value={formData.preferredPriceRange}
              onValueChange={(value) => setFormData(prev => ({ ...prev, preferredPriceRange: value }))}
            >
              <SelectTrigger id="priceRange" className="mt-1">
                <SelectValue placeholder="Select price range" />
              </SelectTrigger>
              <SelectContent>
                {RESTAURANT_OPTIONS.priceRanges.map((price) => (
                  <SelectItem key={price.value} value={price.value}>
                    {price.value} - {price.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label>Dietary Restrictions</Label>
            <div className="flex flex-wrap gap-2 mt-2">
              {formData.dietaryRestrictions.map((restriction) => (
                <Badge key={restriction} variant="secondary" className="px-2 py-1">
                  {restriction}
                  <button
                    type="button"
                    onClick={() => handleRemoveItem("dietaryRestrictions", restriction)}
                    className="ml-1 text-muted-foreground hover:text-foreground"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </Badge>
              ))}
            </div>
            <Select
              onValueChange={(value) => handleSelectChange("dietaryRestrictions", value)}
            >
              <SelectTrigger className="mt-2">
                <SelectValue placeholder="Select dietary restriction" />
              </SelectTrigger>
              <SelectContent>
                {RESTAURANT_OPTIONS.dietary.map((diet) => (
                  <SelectItem key={diet} value={diet}>
                    {diet}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label>Favorite Ingredients</Label>
            <div className="flex flex-wrap gap-2 mt-2">
              {formData.favoriteIngredients.map((ingredient) => (
                <Badge key={ingredient} variant="secondary" className="px-2 py-1">
                  {ingredient}
                  <button
                    type="button"
                    onClick={() => handleRemoveItem("favoriteIngredients", ingredient)}
                    className="ml-1 text-muted-foreground hover:text-foreground"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </Badge>
              ))}
            </div>
            <div className="flex gap-2 mt-2">
              <Input
                name="favoriteIngredient"
                value={newItem.favoriteIngredient}
                onChange={handleInputChange}
                placeholder="e.g., Avocado"
                className="flex-1"
              />
              <Button
                type="button"
                variant="outline"
                onClick={() => handleAddItem("favoriteIngredient", "favoriteIngredients")}
              >
                Add
              </Button>
            </div>
          </div>

          <div>
            <Label>Disliked Ingredients</Label>
            <div className="flex flex-wrap gap-2 mt-2">
              {formData.dislikedIngredients.map((ingredient) => (
                <Badge key={ingredient} variant="secondary" className="px-2 py-1">
                  {ingredient}
                  <button
                    type="button"
                    onClick={() => handleRemoveItem("dislikedIngredients", ingredient)}
                    className="ml-1 text-muted-foreground hover:text-foreground"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </Badge>
              ))}
            </div>
            <div className="flex gap-2 mt-2">
              <Input
                name="dislikedIngredient"
                value={newItem.dislikedIngredient}
                onChange={handleInputChange}
                placeholder="e.g., Cilantro"
                className="flex-1"
              />
              <Button
                type="button"
                variant="outline"
                onClick={() => handleAddItem("dislikedIngredient", "dislikedIngredients")}
              >
                Add
              </Button>
            </div>
          </div>

          <div>
            <Label>Food Allergies</Label>
            <div className="flex flex-wrap gap-2 mt-2">
              {formData.allergies.map((allergy) => (
                <Badge key={allergy} variant="secondary" className="px-2 py-1">
                  {allergy}
                  <button
                    type="button"
                    onClick={() => handleRemoveItem("allergies", allergy)}
                    className="ml-1 text-muted-foreground hover:text-foreground"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </Badge>
              ))}
            </div>
            <div className="flex gap-2 mt-2">
              <Input
                name="allergy"
                value={newItem.allergy}
                onChange={handleInputChange}
                placeholder="e.g., Peanuts"
                className="flex-1"
              />
              <Button
                type="button"
                variant="outline"
                onClick={() => handleAddItem("allergy", "allergies")}
              >
                Add
              </Button>
            </div>
          </div>

          <div className="mt-6">
            <Label>Preferred Restaurant Atmosphere</Label>
            <div className="flex flex-wrap gap-2 mt-2">
              {formData.preferredAtmosphere.map((atmosphere) => (
                <Badge key={atmosphere} variant="secondary" className="px-2 py-1">
                  {atmosphere}
                  <button
                    type="button"
                    onClick={() => handleRemoveItem("preferredAtmosphere", atmosphere)}
                    className="ml-1 text-muted-foreground hover:text-foreground"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </Badge>
              ))}
            </div>
            <Select
              onValueChange={(value) => handleSelectChange("preferredAtmosphere", value)}
            >
              <SelectTrigger className="mt-2">
                <SelectValue placeholder="Select atmosphere" />
              </SelectTrigger>
              <SelectContent>
                {RESTAURANT_OPTIONS.atmosphere.map((atmosphere) => (
                  <SelectItem key={atmosphere} value={atmosphere}>
                    {atmosphere}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="mt-6">
            <Label>Preferred Restaurant Features</Label>
            <div className="flex flex-wrap gap-2 mt-2">
              {formData.preferredFeatures.map((feature) => (
                <Badge key={feature} variant="secondary" className="px-2 py-1">
                  {feature}
                  <button
                    type="button"
                    onClick={() => handleRemoveItem("preferredFeatures", feature)}
                    className="ml-1 text-muted-foreground hover:text-foreground"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </Badge>
              ))}
            </div>
            <Select
              onValueChange={(value) => handleSelectChange("preferredFeatures", value)}
            >
              <SelectTrigger className="mt-2">
                <SelectValue placeholder="Select features" />
              </SelectTrigger>
              <SelectContent>
                {RESTAURANT_OPTIONS.features.map((feature) => (
                  <SelectItem key={feature} value={feature}>
                    {feature}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="bg-muted p-4 rounded-md flex items-start space-x-3 mt-4">
          <Info className="h-5 w-5 text-muted-foreground flex-shrink-0 mt-0.5" />
          <p className="text-sm text-muted-foreground">
            Your preferences help us provide personalized restaurant and dish recommendations.
            The more information you provide, the better we can tailor suggestions to your taste.
          </p>
        </div>

        <Button onClick={handleSave} className="w-full mt-6">
          Save Preferences
        </Button>
      </CardContent>
    </Card>
  );
};
