import {
  FacebookShareButton,
  TwitterShareButton,
  WhatsappShareButton,
  TelegramShareButton,
  LinkedinShareButton,
  FacebookIcon,
  TwitterIcon,
  WhatsappIcon,
  TelegramIcon,
  LinkedinIcon,
} from "react-share";
import { cn } from "@/lib/utils";

export interface SocialShareButtonsProps {
  url: string;
  title: string;
  description?: string;
  hashtags?: string[];
  className?: string;
  iconSize?: number;
  round?: boolean;
}

export const SocialShareButtons = ({
  url,
  title,
  description = "",
  hashtags = [],
  className,
  iconSize = 40,
  round = true,
}: SocialShareButtonsProps) => {
  return (
    <div className={cn("flex items-center justify-center gap-3", className)}>
      <FacebookShareButton
        url={url}
        title={title}
        hashtag={hashtags.length > 0 ? `#${hashtags[0]}` : undefined}
      >
        <FacebookIcon size={iconSize} round={round} />
      </FacebookShareButton>

      <TwitterShareButton url={url} title={title} hashtags={hashtags}>
        <TwitterIcon size={iconSize} round={round} />
      </TwitterShareButton>

      <WhatsappShareButton url={url} title={`${title}\n${description}`}>
        <WhatsappIcon size={iconSize} round={round} />
      </WhatsappShareButton>

      <TelegramShareButton url={url} title={title}>
        <TelegramIcon size={iconSize} round={round} />
      </TelegramShareButton>

      <LinkedinShareButton url={url} title={title} summary={description}>
        <LinkedinIcon size={iconSize} round={round} />
      </LinkedinShareButton>
    </div>
  );
};
