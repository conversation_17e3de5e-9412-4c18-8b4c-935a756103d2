import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { ChevronDown, ChevronUp, BookOpen } from "lucide-react";

// Import the Source interface from AIChat
interface Source {
  id: string;
  text: string;
  metadata?: {
    documentId?: string;
    field?: string;
    [key: string]: unknown; // Using unknown instead of any for better type safety
  };
}

interface SourcesRendererProps {
  sources: Source[];
}

export const SourcesRenderer = ({ sources }: SourcesRendererProps) => {
  const [isExpanded, setIsExpanded] = useState(false);

  if (!sources || sources.length === 0) {
    return null;
  }

  return (
    <div className="mt-3 pt-2 border-t border-primary/10 text-xs">
      <Button
        variant="ghost"
        size="sm"
        className="h-6 px-2 text-xs text-muted-foreground flex items-center gap-1 hover:text-foreground"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <BookOpen className="w-3 h-3" />
        <span>Sources ({sources.length})</span>
        {isExpanded ? (
          <ChevronUp className="w-3 h-3" />
        ) : (
          <ChevronDown className="w-3 h-3" />
        )}
      </Button>

      {isExpanded && (
        <div className="mt-2 space-y-2">
          {sources.map((source) => (
            <div
              key={source.id}
              className="p-2 rounded bg-muted/50 border border-border/50"
            >
              <div className="flex items-center gap-1 text-primary/80 font-medium mb-1">
                <BookOpen className="w-3 h-3" />
                <span className="truncate">
                  {source.metadata?.documentId || source.id}
                </span>
              </div>
              <p className="text-muted-foreground line-clamp-3">{source.text}</p>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};
