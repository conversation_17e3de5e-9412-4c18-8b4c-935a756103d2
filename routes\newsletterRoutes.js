/**
 * Newsletter routes
 *
 * Handles newsletter subscriptions, verification, and unsubscribing
 */
const express = require("express");
const router = express.Router();
const newsletterController = require("../controllers/newsletterController");
const {
  validateBody,
  validateQuery,
  schemas,
} = require("../middlewares/validators");
const { verifyApiKey } = require("../middlewares/authMiddleware");
const {
  subscriptionLimiter,
  apiLimiter,
} = require("../middlewares/rateLimitMiddleware");
const logger = require("../utils/logger");

/**
 * @route POST /subscribe
 * @desc Subscribe to newsletter
 * @access Public
 */
router.post(
  "/subscribe",
  subscriptionLimiter, // Rate limit subscription attempts
  validateBody(schemas.subscribeSchema), // Validate request body
  (req, res, next) => {
    // Log subscription attempt
    logger.info(`Newsletter subscription attempt: ${req.body.email}`, {
      ip: req.headers["x-forwarded-for"] || req.ip || "unknown",
      source: req.body.source || "website",
      referrer: req.body.referrer || "direct",
    });
    next();
  },
  newsletterController.subscribe
);

/**
 * @route POST /verify
 * @desc Verify OTP and complete subscription
 * @access Public
 */
router.post(
  "/verify",
  subscriptionLimiter, // Rate limit verification attempts
  validateBody(schemas.verifyOtpSchema), // Validate request body
  (req, res, next) => {
    // Log verification attempt
    logger.info(`OTP verification attempt: ${req.body.email}`, {
      ip: req.headers["x-forwarded-for"] || req.ip || "unknown",
      device: req.body.device || "unknown",
    });
    next();
  },
  newsletterController.verifySubscription
);

/**
 * @route GET /unsubscribe
 * @desc Unsubscribe from newsletter
 * @access Public
 */
router.get(
  "/unsubscribe",
  validateQuery(schemas.unsubscribeSchema), // Validate query parameters
  (req, res, next) => {
    // Log unsubscribe attempt
    logger.info(
      `Unsubscribe attempt with token: ${req.query.token.substring(0, 8)}...`,
      {
        ip: req.headers["x-forwarded-for"] || req.ip || "unknown",
        email: req.query.email || "not provided",
        all: req.query.all === "true",
      }
    );
    next();
  },
  newsletterController.unsubscribe
);

/**
 * @route GET /subscribers
 * @desc List all subscribers
 * @access Private (API key required)
 */
router.get(
  "/subscribers",
  apiLimiter, // Rate limit API requests
  verifyApiKey, // Require API key
  (req, res, next) => {
    // Log subscriber list request
    logger.info("Subscriber list requested", {
      clientId: req.apiKey?.clientId || "unknown",
      query: req.query,
    });
    next();
  },
  newsletterController.listSubscribers
);

module.exports = router;
