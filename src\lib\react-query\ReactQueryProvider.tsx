import { QueryClientProvider } from "@tanstack/react-query";
import { queryClient } from "./index";

interface ReactQueryProviderProps {
  children: React.ReactNode;
}

export function ReactQueryProvider({ children }: ReactQueryProviderProps) {
  return (
    <QueryClientProvider client={queryClient}>
      {children}
      {/* DevTools removed to hide the floating icon */}
    </QueryClientProvider>
  );
}
