
import { Slider } from '@/components/ui/slider';

interface DeliveryTimeFilterProps {
  value: [number, number];
  onChange: (value: [number, number]) => void;
  min?: number;
  max?: number;
  step?: number;
}

export function DeliveryTimeFilter({
  value,
  onChange,
  min = 10,
  max = 60,
  step = 5
}: DeliveryTimeFilterProps) {
  const handleValueChange = (newValue: number[]) => {
    onChange([newValue[0], newValue[1]]);
  };

  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <p className="text-sm font-medium">Delivery Time</p>
        <Slider
          defaultValue={value}
          min={min}
          max={max}
          step={step}
          onValueChange={handleValueChange}
          className="mt-2"
        />
      </div>
      <div className="flex justify-between text-xs text-muted-foreground">
        <span>{value[0]} min</span>
        <span>{value[1]} min</span>
      </div>
    </div>
  );
}
