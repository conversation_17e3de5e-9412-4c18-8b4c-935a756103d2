import { firestore } from "@/config/firebase";
import {
  doc,
  collection,
  getDoc,
  getDocs,
  query,
  where,
  orderBy,
  limit,
  Timestamp,
  serverTimestamp,
  increment,
  writeBatch,
  updateDoc,
} from "firebase/firestore";
import {
  PointTransaction,
  LoyaltyReward,
  RedeemedReward,
  Referral,
  LoyaltyStatus,
  LoyaltyTier,
  PointsConfig,
} from "@/types/loyalty";
import { notificationService } from "./NotificationService";

// Default point values for different actions - made harder to earn points
const DEFAULT_POINTS_CONFIG: PointsConfig = {
  orderPointsPerAmount: 25, // 25 points per currency unit (decreased from 50)
  reviewPoints: 75, // 75 points for writing a review (decreased from 100)
  referralPoints: 150, // 150 points for successful referral (decreased from 200)
  referredUserPoints: 75, // 75 points for signing up via referral (decreased from 100)
  challengeCompletionBonus: 15, // 15% bonus for completing challenges (decreased from 20%)
  pointsExpiration: 365, // Points expire after 1 year
  gamePoints: {
    foodMatch: 50, // 50 points for completing food match game
    trivia: 10, // 10 points per correct trivia answer
    dailyCheckIn: 25, // 25 points for daily check-in
    streakBonus: 5, // 5 additional points per day of streak
    maxStreakDays: 7, // Maximum 7 days for streak bonus (up to 35 additional points)
  },
  dailyGameLimit: 150, // Maximum 150 points from games per day
};

// Loyalty tier configuration - increased thresholds to make it harder to level up
export const LOYALTY_TIERS: LoyaltyTier[] = [
  {
    id: "bronze",
    name: "Bronze",
    threshold: 0,
    multiplier: 1.0,
    perks: ["Earn 1x points on all orders"],
  },
  {
    id: "silver",
    name: "Silver",
    threshold: 1500, // Increased from 1000
    multiplier: 1.2,
    perks: [
      "Earn 1.2x points on all orders",
      "Free delivery on orders over 30 AZN",
      "Early access to promotions",
    ],
  },
  {
    id: "gold",
    name: "Gold",
    threshold: 7500, // Increased from 5000
    multiplier: 1.5,
    perks: [
      "Earn 1.5x points on all orders",
      "Free delivery on all orders",
      "Priority customer support",
      "Exclusive monthly rewards",
    ],
  },
  {
    id: "platinum",
    name: "Platinum",
    threshold: 15000, // Increased from 10000
    multiplier: 2.0,
    perks: [
      "Earn 2x points on all orders",
      "Free delivery on all orders",
      "VIP customer support",
      "Exclusive monthly rewards",
      "Birthday surprise gift",
      "Special event invitations",
    ],
  },
];

/**
 * Service for handling loyalty program operations
 */
class LoyaltyService {
  pointsConfig: PointsConfig = DEFAULT_POINTS_CONFIG;

  /**
   * Initialize a user in the loyalty program
   * @param userId User ID
   * @param email User email
   * @returns Success status
   */
  async initializeUser(userId: string, email: string): Promise<boolean> {
    try {
      console.log(
        `Initializing user in loyalty program: ${userId}, email: ${email}`
      );

      // Check for pending referral code in localStorage
      const pendingCode = localStorage.getItem("pendingReferralCode");
      if (pendingCode) {
        console.log(
          `[LOYALTY_SERVICE] Found pending referral code: ${pendingCode}. Redirecting to DirectReferralService.`
        );

        // Import the processReferral function dynamically to avoid circular dependencies
        const { processReferral } = await import("./DirectReferralService");

        // Process the referral first
        const referralResult = await processReferral(userId, pendingCode);
        if (referralResult) {
          console.log(
            `[LOYALTY_SERVICE] Referral processed successfully by DirectReferralService`
          );
          localStorage.removeItem("pendingReferralCode");
          return true; // Referral processing includes loyalty initialization
        } else {
          console.log(
            `[LOYALTY_SERVICE] Referral processing failed, continuing with normal initialization`
          );
        }
      }

      // Verify user exists first
      const clientRef = doc(firestore, "clients", userId);
      const clientDoc = await getDoc(clientRef);

      if (!clientDoc.exists()) {
        console.error(
          "Cannot initialize loyalty - client document not found:",
          userId
        );
        return false;
      }

      // Use email from client document if not provided
      if (!email) {
        const clientData = clientDoc.data();
        email = clientData.email || "";

        if (!email) {
          console.error(
            "Cannot initialize loyalty - no email available for user:",
            userId
          );
          return false;
        }
      }

      // Check if user already has loyalty status
      const statusRef = doc(
        firestore,
        "clients",
        userId,
        "loyaltyStatus",
        "current"
      );
      const statusDoc = await getDoc(statusRef);

      if (statusDoc.exists()) {
        console.log(`User ${userId} already has loyalty status`);
        return true; // User already initialized
      }

      console.log(`Creating new loyalty status for user ${userId}`);

      // Generate a unique referral code
      const referralCode = this.generateReferralCode(email);

      // Create initial loyalty status
      const loyaltyStatus: LoyaltyStatus = {
        userId,
        totalPoints: 0,
        lifetimePoints: 0,
        tier: "bronze",
        nextTierPoints: LOYALTY_TIERS[1].threshold,
        referralCode,
        referralCount: 0,
        joinedAt: Timestamp.now(),
        lastUpdated: Timestamp.now(),
      };

      // Update user document and create loyalty status
      const batch = writeBatch(firestore);

      // Update client document
      batch.update(clientRef, {
        loyaltyEnabled: true,
        loyaltyJoinedAt: serverTimestamp(),
        loyaltyTier: "bronze",
        loyaltyPoints: 0,
        loyaltyLifetimePoints: 0,
        referralCode,
        referralCount: 0,
        earnedBadges: [],
        completedChallenges: [],
      });

      // Create loyalty status document in subcollection
      batch.set(statusRef, loyaltyStatus);

      // Add welcome bonus points
      const welcomeBonus = 100;
      const transactionRef = collection(
        firestore,
        "clients",
        userId,
        "pointTransactions"
      );
      const transactionData: Omit<PointTransaction, "id"> = {
        userId,
        points: welcomeBonus,
        type: "bonus",
        description: "Welcome bonus for joining the loyalty program",
        createdAt: Timestamp.now(),
        expiresAt: Timestamp.fromDate(
          new Date(
            Date.now() +
              this.pointsConfig.pointsExpiration * 24 * 60 * 60 * 1000
          )
        ),
      };

      const transactionDocRef = doc(transactionRef);
      batch.set(transactionDocRef, {
        id: transactionDocRef.id,
        ...transactionData,
      });

      // Update points in loyalty status
      batch.update(statusRef, {
        totalPoints: welcomeBonus,
        lifetimePoints: welcomeBonus,
      });

      console.log(`Committing loyalty initialization batch for user ${userId}`);
      await batch.commit();
      console.log(`Loyalty initialization successful for user ${userId}`);

      // Verify the loyalty status was created
      const verifyStatusDoc = await getDoc(statusRef);
      if (!verifyStatusDoc.exists()) {
        console.error(
          `Failed to create loyalty status for user ${userId} - document not found after batch commit`
        );
        return false;
      }

      // Send welcome notification
      try {
        await notificationService.sendEmailNotification({
          to: email,
          subject: "Welcome to the Qonai Loyalty Program!",
          body: `You've earned ${welcomeBonus} points as a welcome bonus. Start earning more points with every order!`,
          recipientName: "",
          type: "loyalty",
        });
      } catch (emailError) {
        console.error("Error sending welcome email:", emailError);
        // Continue even if email fails
      }

      return true;
    } catch (error) {
      console.error("Error initializing loyalty user:", error);
      if (error instanceof Error) {
        console.error("Error message:", error.message);
        console.error("Error stack:", error.stack);
      }
      return false;
    }
  }

  /**
   * Generate a unique referral code for a user
   * @param email User email
   * @returns Referral code
   */
  private generateReferralCode(email: string): string {
    const prefix = email.split("@")[0].substring(0, 5).toUpperCase();
    const randomPart = Math.random().toString(36).substring(2, 6).toUpperCase();
    return `${prefix}${randomPart}`;
  }

  /**
   * Award points for an order
   * @param userId User ID
   * @param orderId Order ID
   * @param orderAmount Order amount
   * @returns Success status
   */
  async awardOrderPoints(
    userId: string,
    orderId: string,
    orderAmount: number
  ): Promise<boolean> {
    try {
      console.log(
        `Awarding points for order: userId=${userId}, orderId=${orderId}, amount=${orderAmount}`
      );

      // Get user's loyalty status
      const statusRef = doc(
        firestore,
        "clients",
        userId,
        "loyaltyStatus",
        "current"
      );
      const statusDoc = await getDoc(statusRef);

      if (!statusDoc.exists()) {
        console.error("User not in loyalty program:", userId);
        // Initialize the user in the loyalty program
        try {
          // Get user email from clients collection
          const clientRef = doc(firestore, "clients", userId);
          const clientDoc = await getDoc(clientRef);
          if (clientDoc.exists()) {
            const clientData = clientDoc.data();
            const email = clientData.email || "";
            console.log(
              `Initializing user in loyalty program: ${userId}, ${email}`
            );
            await this.initializeUser(userId, email);
            // Retry awarding points after initialization
            return this.awardOrderPoints(userId, orderId, orderAmount);
          } else {
            console.error("Client document not found:", userId);
            return false;
          }
        } catch (initError) {
          console.error(
            "Error initializing user in loyalty program:",
            initError
          );
          return false;
        }
      }

      const status = statusDoc.data() as LoyaltyStatus;
      console.log(
        `User loyalty status: tier=${status.tier}, totalPoints=${status.totalPoints}`
      );

      // Check if points were already awarded for this order
      const existingTransactionQuery = query(
        collection(firestore, "clients", userId, "pointTransactions"),
        where("referenceId", "==", orderId),
        where("type", "==", "order")
      );

      const existingTransactions = await getDocs(existingTransactionQuery);
      if (!existingTransactions.empty) {
        console.log("Points already awarded for this order");
        return true; // Points already awarded
      }

      // Get the order to check its status
      try {
        const clientRef = doc(firestore, "clients", userId);
        const orderRef = doc(clientRef, "orders", orderId);
        const orderDoc = await getDoc(orderRef);

        if (orderDoc.exists()) {
          const orderData = orderDoc.data();
          // Only award points for orders that are being prepared or completed
          if (
            orderData.status !== "preparing" &&
            orderData.status !== "completed"
          ) {
            console.log(
              `Order status is ${orderData.status}, not awarding points yet`
            );
            return false;
          }
        }
      } catch (orderError) {
        console.error("Error checking order status:", orderError);
        // Continue with points awarding even if we can't check the order status
      }

      // Calculate points based on order amount and user's tier
      const tier = LOYALTY_TIERS.find((t) => t.id === status.tier);
      const multiplier = tier ? tier.multiplier : 1.0;
      const basePoints = Math.floor(
        orderAmount * this.pointsConfig.orderPointsPerAmount
      );
      const pointsToAward = Math.floor(basePoints * multiplier);

      console.log(
        `Points calculation: basePoints=${basePoints}, multiplier=${multiplier}, pointsToAward=${pointsToAward}`
      );

      // Add transaction
      const transactionRef = collection(
        firestore,
        "clients",
        userId,
        "pointTransactions"
      );
      const transactionData: Omit<PointTransaction, "id"> = {
        userId,
        points: pointsToAward,
        type: "order",
        description: `Points earned for order #${orderId.substring(0, 8)}`,
        createdAt: Timestamp.now(),
        referenceId: orderId,
        expiresAt: Timestamp.fromDate(
          new Date(
            Date.now() +
              this.pointsConfig.pointsExpiration * 24 * 60 * 60 * 1000
          )
        ),
      };

      const batch = writeBatch(firestore);

      const transactionDocRef = doc(transactionRef);
      batch.set(transactionDocRef, {
        id: transactionDocRef.id,
        ...transactionData,
      });

      // Update user's points
      batch.update(statusRef, {
        totalPoints: increment(pointsToAward),
        lifetimePoints: increment(pointsToAward),
        lastUpdated: serverTimestamp(),
      });

      // Update client document
      const clientRef = doc(firestore, "clients", userId);
      batch.update(clientRef, {
        loyaltyPoints: increment(pointsToAward),
        loyaltyLifetimePoints: increment(pointsToAward),
      });

      // Check if user should be upgraded to a new tier
      const newLifetimePoints = status.lifetimePoints + pointsToAward;
      const currentTierIndex = LOYALTY_TIERS.findIndex(
        (t) => t.id === status.tier
      );
      let newTier = status.tier;
      let tierChanged = false;

      // Check if user qualifies for a higher tier
      for (let i = currentTierIndex + 1; i < LOYALTY_TIERS.length; i++) {
        if (newLifetimePoints >= LOYALTY_TIERS[i].threshold) {
          newTier = LOYALTY_TIERS[i].id;
          tierChanged = true;
        } else {
          break;
        }
      }

      if (tierChanged) {
        batch.update(statusRef, {
          tier: newTier,
          nextTierPoints: this.getNextTierPoints(newTier, newLifetimePoints),
        });

        batch.update(clientRef, {
          loyaltyTier: newTier,
        });
      }

      await batch.commit();

      // Send notification via email
      await notificationService.sendEmailNotification({
        to: "", // We'll need to get the user's email
        subject: "Points Earned!",
        body: `You've earned ${pointsToAward} points for your order.`,
        recipientName: "",
        type: "loyalty",
      });

      // If tier changed, send a congratulatory notification
      if (tierChanged) {
        await notificationService.sendEmailNotification({
          to: "", // We'll need to get the user's email
          subject: "Tier Upgrade!",
          body: `Congratulations! You've been upgraded to ${
            newTier.charAt(0).toUpperCase() + newTier.slice(1)
          } tier.`,
          recipientName: "",
          type: "loyalty",
        });
      }

      return true;
    } catch (error) {
      console.error("Error awarding order points:", error);
      return false;
    }
  }

  /**
   * Get the points needed for the next tier
   * @param currentTier Current tier
   * @param lifetimePoints Lifetime points
   * @returns Points needed for next tier
   */
  private getNextTierPoints(
    currentTier: string,
    lifetimePoints: number
  ): number {
    const currentTierIndex = LOYALTY_TIERS.findIndex(
      (t) => t.id === currentTier
    );

    // If user is at the highest tier, return 0
    if (currentTierIndex === LOYALTY_TIERS.length - 1) {
      return 0;
    }

    // Return points needed for next tier
    const nextTier = LOYALTY_TIERS[currentTierIndex + 1];
    return nextTier.threshold - lifetimePoints;
  }

  /**
   * Award points for writing a review
   * @param userId User ID
   * @param reviewId Review ID
   * @returns Success status
   */
  async awardReviewPoints(userId: string, reviewId: string): Promise<boolean> {
    try {
      // Get user's loyalty status
      const statusRef = doc(
        firestore,
        "clients",
        userId,
        "loyaltyStatus",
        "current"
      );
      const statusDoc = await getDoc(statusRef);

      if (!statusDoc.exists()) {
        console.error("User not in loyalty program:", userId);
        return false;
      }

      // Check if points were already awarded for this review
      const existingTransactionQuery = query(
        collection(firestore, "clients", userId, "pointTransactions"),
        where("referenceId", "==", reviewId),
        where("type", "==", "review")
      );

      const existingTransactions = await getDocs(existingTransactionQuery);
      if (!existingTransactions.empty) {
        console.log("Points already awarded for this review");
        return true; // Points already awarded
      }

      const pointsToAward = this.pointsConfig.reviewPoints;

      // Add transaction
      const transactionRef = collection(
        firestore,
        "clients",
        userId,
        "pointTransactions"
      );
      const transactionData: Omit<PointTransaction, "id"> = {
        userId,
        points: pointsToAward,
        type: "review",
        description: "Points earned for writing a review",
        createdAt: Timestamp.now(),
        referenceId: reviewId,
        expiresAt: Timestamp.fromDate(
          new Date(
            Date.now() +
              this.pointsConfig.pointsExpiration * 24 * 60 * 60 * 1000
          )
        ),
      };

      const batch = writeBatch(firestore);

      const transactionDocRef = doc(transactionRef);
      batch.set(transactionDocRef, {
        id: transactionDocRef.id,
        ...transactionData,
      });

      // Update user's points
      batch.update(statusRef, {
        totalPoints: increment(pointsToAward),
        lifetimePoints: increment(pointsToAward),
        lastUpdated: serverTimestamp(),
      });

      // Update client document
      const clientRef = doc(firestore, "clients", userId);
      batch.update(clientRef, {
        loyaltyPoints: increment(pointsToAward),
        loyaltyLifetimePoints: increment(pointsToAward),
      });

      await batch.commit();

      // Send notification via email
      await notificationService.sendEmailNotification({
        to: "", // We'll need to get the user's email
        subject: "Points Earned!",
        body: `You've earned ${pointsToAward} points for writing a review.`,
        recipientName: "",
        type: "loyalty",
      });

      return true;
    } catch (error) {
      console.error("Error awarding review points:", error);
      return false;
    }
  }

  /**
   * Generate a referral link for a user
   * @param referralCode The user's referral code
   * @returns A shareable referral link
   */
  generateReferralLink(referralCode: string): string {
    return `https://qonai.me/register?ref=${referralCode}`;
  }

  /**
   * Create a referral for a user
   * @param userId User ID
   * @param referredEmail Email of the referred user
   * @returns Referral ID or null
   */
  async createReferral(
    userId: string,
    referredEmail: string
  ): Promise<string | null> {
    try {
      // Get user's loyalty status
      const statusRef = doc(
        firestore,
        "clients",
        userId,
        "loyaltyStatus",
        "current"
      );
      const statusDoc = await getDoc(statusRef);

      if (!statusDoc.exists()) {
        console.error("User not in loyalty program:", userId);
        return null;
      }

      const status = statusDoc.data() as LoyaltyStatus;

      // Check if this email has already been referred
      // We'll still use a global referrals collection to check across all users
      const existingReferralQuery = query(
        collection(firestore, "referrals"),
        where("referredEmail", "==", referredEmail),
        where("status", "in", ["pending", "completed"])
      );

      const existingReferrals = await getDocs(existingReferralQuery);
      if (!existingReferrals.empty) {
        console.log("Email already referred");
        return null; // Email already referred
      }

      // Create referral in both global collection and user's subcollection
      const globalReferralRef = collection(firestore, "referrals");
      const userReferralRef = collection(
        firestore,
        "clients",
        userId,
        "referrals"
      );

      const referralData: Omit<Referral, "id"> = {
        referrerId: userId,
        referralCode: status.referralCode,
        referredEmail,
        status: "pending",
        createdAt: Timestamp.now(),
      };

      // Create in global collection
      const globalReferralDocRef = doc(globalReferralRef);
      const referralId = globalReferralDocRef.id;

      // Create a batch to ensure both writes succeed
      const batch = writeBatch(firestore);

      // Set in global collection
      batch.set(globalReferralDocRef, {
        id: referralId,
        ...referralData,
      });

      // Set in user's subcollection with the same ID
      const userReferralDocRef = doc(userReferralRef, referralId);
      batch.set(userReferralDocRef, {
        id: referralId,
        ...referralData,
      });

      await batch.commit();

      // Get referrer details for the email
      const referrerDoc = await getDoc(doc(firestore, "clients", userId));
      let referrerName = "A friend";
      if (referrerDoc.exists()) {
        const referrerData = referrerDoc.data();
        referrerName = `${referrerData.firstName} ${referrerData.lastName}`;
      }

      // Generate referral link
      const referralLink = this.generateReferralLink(status.referralCode);

      // Send email to the referred user
      try {
        await notificationService.sendEmailNotification({
          to: referredEmail,
          subject: `${referrerName} invited you to join Qonai!`,
          body: "",
          recipientName: "",
          type: "referral",
          data: {
            referrerName,
            referralCode: status.referralCode,
            referralLink,
            bonusPoints: this.pointsConfig.referredUserPoints,
          },
        });
      } catch (emailError) {
        console.error("Error sending referral email:", emailError);
        // Continue even if email fails
      }

      return referralId;
    } catch (error) {
      console.error("Error creating referral:", error);
      return null;
    }
  }

  /**
   * Complete a referral and award points
   * @param referralCode Referral code
   * @param newUserId ID of the new user
   * @returns Success status
   */
  async completeReferral(
    referralCode: string,
    newUserId: string
  ): Promise<boolean> {
    try {
      console.log(`[REFERRAL] ===== STARTING REFERRAL COMPLETION =====`);
      console.log(
        `[REFERRAL] Code: ${referralCode}, New User ID: ${newUserId}`
      );

      // First, ensure the user exists
      const newUserRef = doc(firestore, "clients", newUserId);
      const newUserDoc = await getDoc(newUserRef);

      if (!newUserDoc.exists()) {
        console.error("[REFERRAL] ERROR: New user does not exist:", newUserId);
        return false;
      }
      console.log(`[REFERRAL] New user exists: ${newUserId}`);

      // Find the referral by code - first try to find by exact match
      console.log(
        `[REFERRAL] Searching for referral with code: ${referralCode}`
      );

      // First try to find by exact code match
      const referralsQuery = query(
        collection(firestore, "referrals"),
        where("referralCode", "==", referralCode),
        limit(1)
      );

      let referralsSnapshot = await getDocs(referralsQuery);

      // If not found, try to find by referral code in any field
      if (referralsSnapshot.empty) {
        console.log(
          `[REFERRAL] No exact match found, trying to find referral by code in any field`
        );

        // Get all referrals and filter manually
        const allReferralsQuery = query(
          collection(firestore, "referrals"),
          limit(100)
        );
        const allReferralsSnapshot = await getDocs(allReferralsQuery);

        console.log(
          `[REFERRAL] Found ${allReferralsSnapshot.size} total referrals to check`
        );

        // Log all referrals for debugging
        allReferralsSnapshot.forEach((doc) => {
          const data = doc.data();
          console.log(
            `[REFERRAL] Referral ID: ${doc.id}, Code: ${data.referralCode}, Status: ${data.status}`
          );
        });

        // Try to find a match
        const matchingDocs = allReferralsSnapshot.docs.filter((doc) => {
          const data = doc.data();
          return (
            data.referralCode && data.referralCode.toString() === referralCode
          );
        });

        if (matchingDocs.length > 0) {
          console.log(
            `[REFERRAL] Found ${matchingDocs.length} matching referrals by manual search`
          );
          // Use type assertion to create a compatible object
          /* eslint-disable @typescript-eslint/no-explicit-any */
          referralsSnapshot = {
            empty: false,
            docs: matchingDocs,
            size: matchingDocs.length,
            forEach: (callback: any) => matchingDocs.forEach(callback),
          } as any;
          /* eslint-enable @typescript-eslint/no-explicit-any */
        }
      }

      if (referralsSnapshot.empty) {
        console.error(
          "[REFERRAL] ERROR: No referral found with code:",
          referralCode
        );
        return false;
      }

      const referralDoc = referralsSnapshot.docs[0];
      const referral = referralDoc.data() as Referral;

      console.log(`[REFERRAL] Found referral:`, {
        id: referralDoc.id,
        referralCode: referral.referralCode,
        status: referral.status,
        referrerId: referral.referrerId,
        referredEmail: referral.referredEmail,
      });

      // Check if referral is already completed
      if (referral.status === "completed") {
        console.log("[REFERRAL] Referral already completed:", referralCode);
        return true; // Return true since it's already done
      }

      // Check if referral is expired or not pending
      if (referral.status !== "pending") {
        console.error(
          "[REFERRAL] ERROR: Referral is not pending:",
          referralCode,
          "Status:",
          referral.status
        );
        return false;
      }

      const referrerId = referral.referrerId;
      console.log(
        `[REFERRAL] Processing referral with ID: ${referralDoc.id}, referrer: ${referrerId}`
      );

      // Get referrer's loyalty status
      const statusRef = doc(
        firestore,
        "clients",
        referrerId,
        "loyaltyStatus",
        "current"
      );
      const statusDoc = await getDoc(statusRef);

      if (!statusDoc.exists()) {
        console.error("Referrer not in loyalty program:", referrerId);
        return false;
      }

      console.log(`[REFERRAL] Creating batch for updates`);
      const batch = writeBatch(firestore);

      // Update referral status in global collection
      console.log(`[REFERRAL] Updating global referral status to completed`);
      batch.update(referralDoc.ref, {
        status: "completed",
        referredUserId: newUserId,
        completedAt: serverTimestamp(),
        pointsAwarded: this.pointsConfig.referralPoints,
      });

      // Check if the referral exists in the user's subcollection
      const userReferralRef = doc(
        firestore,
        "clients",
        referrerId,
        "referrals",
        referralDoc.id
      );

      console.log(
        `[REFERRAL] Checking if referral exists in user's subcollection`
      );
      const userReferralDoc = await getDoc(userReferralRef);

      if (userReferralDoc.exists()) {
        console.log(
          `[REFERRAL] Updating existing referral in user's subcollection`
        );
        // Update the existing document
        batch.update(userReferralRef, {
          status: "completed",
          referredUserId: newUserId,
          completedAt: serverTimestamp(),
          pointsAwarded: this.pointsConfig.referralPoints,
        });
      } else {
        // Create the document if it doesn't exist
        console.log(
          "[REFERRAL] Creating referral in user's subcollection as it doesn't exist"
        );
        batch.set(userReferralRef, {
          id: referralDoc.id,
          referrerId: referrerId,
          referralCode: referral.referralCode,
          referredEmail: referral.referredEmail,
          status: "completed",
          referredUserId: newUserId,
          createdAt: referral.createdAt,
          completedAt: serverTimestamp(),
          pointsAwarded: this.pointsConfig.referralPoints,
        });
      }

      // Award points to referrer
      console.log(
        `[REFERRAL] Creating point transaction for referrer: ${this.pointsConfig.referralPoints} points`
      );
      const referrerPointsTransaction: Omit<PointTransaction, "id"> = {
        userId: referrerId,
        points: this.pointsConfig.referralPoints,
        type: "referral",
        description: "Points earned for successful referral",
        createdAt: Timestamp.now(),
        referenceId: referralDoc.id,
        expiresAt: Timestamp.fromDate(
          new Date(
            Date.now() +
              this.pointsConfig.pointsExpiration * 24 * 60 * 60 * 1000
          )
        ),
      };

      const referrerTransactionRef = doc(
        collection(firestore, "clients", referrerId, "pointTransactions")
      );
      batch.set(referrerTransactionRef, {
        id: referrerTransactionRef.id,
        ...referrerPointsTransaction,
      });

      // Update referrer's points and referral count
      console.log(
        `[REFERRAL] Updating referrer's loyalty status with points and referral count`
      );
      batch.update(statusRef, {
        totalPoints: increment(this.pointsConfig.referralPoints),
        lifetimePoints: increment(this.pointsConfig.referralPoints),
        referralCount: increment(1),
        lastUpdated: serverTimestamp(),
      });

      // Update referrer's client document
      console.log(`[REFERRAL] Updating referrer's client document`);
      const referrerClientRef = doc(firestore, "clients", referrerId);
      batch.update(referrerClientRef, {
        loyaltyPoints: increment(this.pointsConfig.referralPoints),
        loyaltyLifetimePoints: increment(this.pointsConfig.referralPoints),
        referralCount: increment(1),
      });

      // Award points to new user
      // First, ensure the new user has a loyalty status
      console.log(
        `[REFERRAL] Initializing loyalty status for new user: ${newUserId}`
      );

      // Get the new user's email from the client document we already fetched
      const newUserEmail =
        newUserDoc.data().email || referral.referredEmail || "";
      console.log(`[REFERRAL] New user email: ${newUserEmail}`);

      // Try to initialize the user's loyalty status
      const initResult = await this.initializeUser(newUserId, newUserEmail);

      if (!initResult) {
        console.error(
          `[REFERRAL] ERROR: Failed to initialize loyalty status for new user: ${newUserId}`
        );

        // Try a different approach - create the loyalty status directly here
        try {
          console.log(
            `[REFERRAL] Attempting direct loyalty status creation for user: ${newUserId}`
          );

          // Generate a referral code
          const referralCode = this.generateReferralCode(newUserEmail);

          // Create a new batch for this operation
          const initBatch = writeBatch(firestore);

          // Create loyalty status document
          const newUserStatusRef = doc(
            firestore,
            "clients",
            newUserId,
            "loyaltyStatus",
            "current"
          );
          const loyaltyStatus: LoyaltyStatus = {
            userId: newUserId,
            totalPoints: 0,
            lifetimePoints: 0,
            tier: "bronze",
            nextTierPoints: LOYALTY_TIERS[1].threshold,
            referralCode,
            referralCount: 0,
            joinedAt: Timestamp.now(),
            lastUpdated: Timestamp.now(),
          };

          initBatch.set(newUserStatusRef, loyaltyStatus);

          // Update client document
          const newUserClientRef = doc(firestore, "clients", newUserId);
          initBatch.update(newUserClientRef, {
            loyaltyEnabled: true,
            loyaltyJoinedAt: serverTimestamp(),
            loyaltyTier: "bronze",
            loyaltyPoints: 0,
            loyaltyLifetimePoints: 0,
            referralCode,
            referralCount: 0,
            earnedBadges: [],
            completedChallenges: [],
          });

          console.log(`[REFERRAL] Committing direct loyalty initialization`);
          await initBatch.commit();
          console.log(`[REFERRAL] Direct loyalty initialization successful`);

          // Verify it worked
          const verifyDoc = await getDoc(newUserStatusRef);
          if (!verifyDoc.exists()) {
            console.error(
              `[REFERRAL] ERROR: Direct loyalty initialization failed - document not created`
            );
            return false;
          }
        } catch (initError) {
          console.error(
            `[REFERRAL] ERROR: Direct loyalty initialization failed:`,
            initError
          );
          return false;
        }
      }

      // Wait a moment to ensure the loyalty status is created
      console.log(
        `[REFERRAL] Waiting for loyalty status to be fully created...`
      );
      await new Promise((resolve) => setTimeout(resolve, 2000));

      // Then add bonus points for being referred
      console.log(
        `[REFERRAL] Creating point transaction for new user: ${this.pointsConfig.referredUserPoints} points`
      );
      const newUserPointsTransaction: Omit<PointTransaction, "id"> = {
        userId: newUserId,
        points: this.pointsConfig.referredUserPoints,
        type: "bonus",
        description: "Bonus points for signing up with a referral",
        createdAt: Timestamp.now(),
        referenceId: referralDoc.id,
        expiresAt: Timestamp.fromDate(
          new Date(
            Date.now() +
              this.pointsConfig.pointsExpiration * 24 * 60 * 60 * 1000
          )
        ),
      };

      const newUserTransactionRef = doc(
        collection(firestore, "clients", newUserId, "pointTransactions")
      );
      batch.set(newUserTransactionRef, {
        id: newUserTransactionRef.id,
        ...newUserPointsTransaction,
      });

      // Update new user's points
      const newUserStatusRef = doc(
        firestore,
        "clients",
        newUserId,
        "loyaltyStatus",
        "current"
      );

      // Verify that the loyalty status document exists before updating it
      console.log(`[REFERRAL] Verifying new user's loyalty status exists`);
      const newUserStatusDoc = await getDoc(newUserStatusRef);
      if (!newUserStatusDoc.exists()) {
        console.error(
          `[REFERRAL] ERROR: New user's loyalty status document doesn't exist after initialization`
        );

        // Try to create it one more time
        console.log(
          `[REFERRAL] Attempting to create loyalty status one more time`
        );
        await this.initializeUser(newUserId, newUserEmail);

        // Check again
        const retryStatusDoc = await getDoc(newUserStatusRef);
        if (!retryStatusDoc.exists()) {
          console.error(
            `[REFERRAL] ERROR: Still cannot create loyalty status document`
          );
          return false;
        }
        console.log(`[REFERRAL] Successfully created loyalty status on retry`);
      }

      console.log(`[REFERRAL] Updating new user's loyalty status with points`);
      batch.update(newUserStatusRef, {
        totalPoints: increment(this.pointsConfig.referredUserPoints),
        lifetimePoints: increment(this.pointsConfig.referredUserPoints),
        lastUpdated: serverTimestamp(),
      });

      // Update new user's client document
      console.log(`[REFERRAL] Updating new user's client document with points`);
      const newUserClientRef = doc(firestore, "clients", newUserId);
      batch.update(newUserClientRef, {
        loyaltyPoints: increment(this.pointsConfig.referredUserPoints),
        loyaltyLifetimePoints: increment(this.pointsConfig.referredUserPoints),
      });

      // Commit all changes in a single batch
      console.log(`[REFERRAL] Committing all changes in batch`);
      try {
        await batch.commit();
        console.log(`[REFERRAL] Batch commit successful`);
      } catch (batchError) {
        console.error(`[REFERRAL] ERROR: Batch commit failed:`, batchError);
        if (batchError instanceof Error) {
          console.error(`[REFERRAL] Error message: ${batchError.message}`);
          console.error(`[REFERRAL] Error stack: ${batchError.stack}`);
        }
        return false;
      }

      // Verify the changes were applied
      console.log(`[REFERRAL] Verifying changes were applied`);

      // Check referral status
      const verifyReferralDoc = await getDoc(referralDoc.ref);
      if (verifyReferralDoc.exists()) {
        const verifyReferral = verifyReferralDoc.data();
        console.log(
          `[REFERRAL] Referral status after update: ${verifyReferral.status}`
        );
      }

      // Check new user's points
      const verifyNewUserStatus = await getDoc(newUserStatusRef);
      if (verifyNewUserStatus.exists()) {
        const statusData = verifyNewUserStatus.data();
        console.log(
          `[REFERRAL] New user points after update: ${statusData.totalPoints}`
        );
      }

      // Send notifications via email
      console.log(`[REFERRAL] Sending notification emails`);

      try {
        // Get referrer's email
        const referrerDoc = await getDoc(doc(firestore, "clients", referrerId));
        if (referrerDoc.exists()) {
          const referrerData = referrerDoc.data();
          const referrerEmail = referrerData.email || "";

          if (referrerEmail) {
            console.log(
              `[REFERRAL] Sending email to referrer: ${referrerEmail}`
            );
            await notificationService.sendEmailNotification({
              to: referrerEmail,
              subject: "Referral Bonus!",
              body: `Your referral was successful! You've earned ${this.pointsConfig.referralPoints} points.`,
              recipientName: `${referrerData.firstName || ""} ${
                referrerData.lastName || ""
              }`,
              type: "loyalty",
            });
          }
        }

        // Use the email we already have for the new user
        if (newUserEmail) {
          console.log(`[REFERRAL] Sending email to new user: ${newUserEmail}`);
          await notificationService.sendEmailNotification({
            to: newUserEmail,
            subject: "Welcome Bonus!",
            body: `You've earned ${this.pointsConfig.referredUserPoints} bonus points for signing up with a referral.`,
            recipientName: `${newUserDoc.data().firstName || ""} ${
              newUserDoc.data().lastName || ""
            }`,
            type: "loyalty",
          });
        }
      } catch (emailError) {
        console.error(
          `[REFERRAL] Error sending notification emails:`,
          emailError
        );
        // Continue even if email sending fails
      }

      console.log(`[REFERRAL] ===== REFERRAL COMPLETION SUCCESSFUL =====`);
      return true;
    } catch (error) {
      console.error("Error completing referral:", error);

      // Log more detailed error information
      if (error instanceof Error) {
        console.error("Error message:", error.message);
        console.error("Error stack:", error.stack);
      }

      // Check if the error is related to the document not existing
      if (
        error instanceof Error &&
        error.message.includes("No document to update")
      ) {
        console.error(
          "The referral document doesn't exist in the user's subcollection"
        );
      }

      return false;
    }
  }

  /**
   * Get available rewards for a user
   * @param userId User ID
   * @returns Array of available rewards
   */
  async getAvailableRewards(userId: string): Promise<LoyaltyReward[]> {
    try {
      // Get user's loyalty status
      const statusRef = doc(
        firestore,
        "clients",
        userId,
        "loyaltyStatus",
        "current"
      );
      const statusDoc = await getDoc(statusRef);

      if (!statusDoc.exists()) {
        console.error("User not in loyalty program:", userId);
        return [];
      }

      const status = statusDoc.data() as LoyaltyStatus;
      const userPoints = status.totalPoints;

      // Get all active rewards
      const rewardsQuery = query(
        collection(firestore, "loyaltyRewards"),
        where("isActive", "==", true),
        orderBy("pointsCost", "asc")
      );

      const rewardsSnapshot = await getDocs(rewardsQuery);
      const rewards = rewardsSnapshot.docs.map(
        (doc) => doc.data() as LoyaltyReward
      );

      // Filter rewards that the user can afford
      return rewards.filter((reward) => reward.pointsCost <= userPoints);
    } catch (error) {
      console.error("Error getting available rewards:", error);
      return [];
    }
  }

  /**
   * Redeem a reward
   * @param userId User ID
   * @param rewardId Reward ID
   * @returns Redeemed reward code or null
   */
  async redeemReward(userId: string, rewardId: string): Promise<string | null> {
    try {
      // Get user's loyalty status
      const statusRef = doc(
        firestore,
        "clients",
        userId,
        "loyaltyStatus",
        "current"
      );
      const statusDoc = await getDoc(statusRef);

      if (!statusDoc.exists()) {
        console.error("User not in loyalty program:", userId);
        return null;
      }

      const status = statusDoc.data() as LoyaltyStatus;
      const userPoints = status.totalPoints;

      // Get the reward
      const rewardRef = doc(firestore, "loyaltyRewards", rewardId);
      const rewardDoc = await getDoc(rewardRef);

      if (!rewardDoc.exists()) {
        console.error("Reward not found:", rewardId);
        return null;
      }

      const reward = rewardDoc.data() as LoyaltyReward;

      // Check if user has enough points
      if (userPoints < reward.pointsCost) {
        console.error("User doesn't have enough points");
        return null;
      }

      // Generate a unique redemption code
      const redemptionCode = this.generateRedemptionCode();

      // Set expiration date (30 days from now)
      const expiresAt = Timestamp.fromDate(
        new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
      );

      // Create redeemed reward in user's subcollection
      const redeemedRewardRef = collection(
        firestore,
        "clients",
        userId,
        "redeemedRewards"
      );
      const redeemedRewardData: Omit<RedeemedReward, "id"> = {
        userId,
        rewardId,
        pointsSpent: reward.pointsCost,
        redeemedAt: Timestamp.now(),
        expiresAt,
        isUsed: false,
        code: redemptionCode,
      };

      const batch = writeBatch(firestore);

      const redeemedRewardDocRef = doc(redeemedRewardRef);
      batch.set(redeemedRewardDocRef, {
        id: redeemedRewardDocRef.id,
        ...redeemedRewardData,
      });

      // Create point transaction (negative points)
      const transactionRef = collection(
        firestore,
        "clients",
        userId,
        "pointTransactions"
      );
      const transactionData: Omit<PointTransaction, "id"> = {
        userId,
        points: -reward.pointsCost,
        type: "redemption",
        description: `Redeemed ${reward.name}`,
        createdAt: Timestamp.now(),
        referenceId: redeemedRewardDocRef.id,
      };

      const transactionDocRef = doc(transactionRef);
      batch.set(transactionDocRef, {
        id: transactionDocRef.id,
        ...transactionData,
      });

      // Update user's points
      batch.update(statusRef, {
        totalPoints: increment(-reward.pointsCost),
        lastUpdated: serverTimestamp(),
      });

      // Update client document
      const clientRef = doc(firestore, "clients", userId);
      batch.update(clientRef, {
        loyaltyPoints: increment(-reward.pointsCost),
      });

      await batch.commit();

      // Send notification via email
      // Get user's email
      const userDoc = await getDoc(doc(firestore, "clients", userId));
      if (userDoc.exists()) {
        const userData = userDoc.data();
        const userEmail = userData.email || "";

        if (userEmail) {
          await notificationService.sendEmailNotification({
            to: userEmail,
            subject: "Reward Redeemed!",
            body: `You've redeemed ${reward.name} for ${reward.pointsCost} points. Your code is ${redemptionCode}.`,
            recipientName: `${userData.firstName || ""} ${
              userData.lastName || ""
            }`,
            type: "loyalty",
          });
        }
      }

      return redemptionCode;
    } catch (error) {
      console.error("Error redeeming reward:", error);
      return null;
    }
  }

  /**
   * Generate a unique redemption code
   * @returns Redemption code
   */
  private generateRedemptionCode(): string {
    const characters = "ABCDEFGHJKLMNPQRSTUVWXYZ23456789"; // Removed confusing characters
    let code = "";
    for (let i = 0; i < 8; i++) {
      code += characters.charAt(Math.floor(Math.random() * characters.length));
    }
    return code;
  }

  /**
   * Get a user's loyalty status
   * @param userId User ID
   * @returns Loyalty status or null
   */
  async getLoyaltyStatus(userId: string): Promise<LoyaltyStatus | null> {
    try {
      const statusRef = doc(
        firestore,
        "clients",
        userId,
        "loyaltyStatus",
        "current"
      );
      const statusDoc = await getDoc(statusRef);

      if (!statusDoc.exists()) {
        return null;
      }

      return statusDoc.data() as LoyaltyStatus;
    } catch (error) {
      console.error("Error getting loyalty status:", error);
      return null;
    }
  }

  /**
   * Get a user's point transactions
   * @param userId User ID
   * @param maxLimit Maximum number of transactions to return
   * @returns Array of point transactions
   */
  async getPointTransactions(
    userId: string,
    maxLimit = 20
  ): Promise<PointTransaction[]> {
    try {
      const transactionsQuery = query(
        collection(firestore, "clients", userId, "pointTransactions"),
        orderBy("createdAt", "desc"),
        limit(maxLimit)
      );

      const transactionsSnapshot = await getDocs(transactionsQuery);
      return transactionsSnapshot.docs.map(
        (doc) => doc.data() as PointTransaction
      );
    } catch (error) {
      console.error("Error getting point transactions:", error);
      return [];
    }
  }

  /**
   * Get a user's redeemed rewards
   * @param userId User ID
   * @param includeUsed Whether to include used rewards
   * @returns Array of redeemed rewards
   */
  async getRedeemedRewards(
    userId: string,
    includeUsed = false
  ): Promise<RedeemedReward[]> {
    try {
      let rewardsQuery;
      const rewardsCollection = collection(
        firestore,
        "clients",
        userId,
        "redeemedRewards"
      );

      if (includeUsed) {
        rewardsQuery = query(rewardsCollection, orderBy("redeemedAt", "desc"));
      } else {
        rewardsQuery = query(
          rewardsCollection,
          where("isUsed", "==", false),
          where("expiresAt", ">", Timestamp.now()),
          orderBy("expiresAt", "asc")
        );
      }

      const rewardsSnapshot = await getDocs(rewardsQuery);
      return rewardsSnapshot.docs.map((doc) => doc.data() as RedeemedReward);
    } catch (error) {
      console.error("Error getting redeemed rewards:", error);
      return [];
    }
  }
  /**
   * Award points for playing a mini-game
   * @param userId User ID
   * @param gameId Game ID
   * @param points Points to award
   * @param gameResult Game result details
   * @returns Success status and points awarded
   */
  async awardGamePoints(
    userId: string,
    gameId: string,
    points: number,
    gameResult: { score?: number; details?: string }
  ): Promise<{
    success: boolean;
    pointsAwarded: number;
    dailyLimitReached: boolean;
    playLimitReached?: boolean;
  }> {
    try {
      // Get user's loyalty status
      const statusRef = doc(
        firestore,
        "clients",
        userId,
        "loyaltyStatus",
        "current"
      );
      const statusDoc = await getDoc(statusRef);

      if (!statusDoc.exists()) {
        console.error("User not in loyalty program:", userId);
        return { success: false, pointsAwarded: 0, dailyLimitReached: false };
      }

      const status = statusDoc.data() as LoyaltyStatus;

      // Check daily game points limit
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const todayTimestamp = Timestamp.fromDate(today);

      // Initialize game stats if they don't exist
      const gameStats = status.gameStats || {
        lastPlayed: {},
        dailyPointsEarned: 0,
        lastPointsReset: todayTimestamp,
        playCount: {},
      };

      // Reset daily points and play counts if it's a new day
      if (
        !gameStats.lastPointsReset ||
        gameStats.lastPointsReset.toDate().getTime() < today.getTime()
      ) {
        gameStats.dailyPointsEarned = 0;
        gameStats.playCount = {};
        gameStats.lastPointsReset = todayTimestamp;
      }

      // Check if user has reached daily limit
      if (
        (gameStats.dailyPointsEarned || 0) >= this.pointsConfig.dailyGameLimit
      ) {
        return {
          success: true,
          pointsAwarded: 0,
          dailyLimitReached: true,
        };
      }

      // Calculate points to award (respect daily limit)
      const remainingDaily =
        this.pointsConfig.dailyGameLimit - (gameStats.dailyPointsEarned || 0);
      const pointsToAward = Math.min(points, remainingDaily);

      if (pointsToAward <= 0) {
        return {
          success: true,
          pointsAwarded: 0,
          dailyLimitReached: true,
        };
      }

      // Add transaction
      const transactionRef = collection(
        firestore,
        "clients",
        userId,
        "pointTransactions"
      );

      const transactionData: Omit<PointTransaction, "id"> = {
        userId,
        points: pointsToAward,
        type: "game",
        description: `Points earned from ${gameId} game`,
        createdAt: Timestamp.now(),
        referenceId: gameId,
        expiresAt: Timestamp.fromDate(
          new Date(
            Date.now() +
              this.pointsConfig.pointsExpiration * 24 * 60 * 60 * 1000
          )
        ),
      };

      const batch = writeBatch(firestore);

      const transactionDocRef = doc(transactionRef);
      batch.set(transactionDocRef, {
        id: transactionDocRef.id,
        ...transactionData,
      });

      // Update game stats
      gameStats.lastPlayed = {
        ...gameStats.lastPlayed,
        [gameId]: Timestamp.now(),
      };

      // Update play count
      gameStats.playCount = gameStats.playCount || {};
      gameStats.playCount[gameId] = (gameStats.playCount[gameId] || 0) + 1;

      gameStats.dailyPointsEarned =
        (gameStats.dailyPointsEarned || 0) + pointsToAward;

      // Update user's points and game stats
      batch.update(statusRef, {
        totalPoints: increment(pointsToAward),
        lifetimePoints: increment(pointsToAward),
        lastUpdated: serverTimestamp(),
        gameStats: gameStats,
      });

      // Update client document
      const clientRef = doc(firestore, "clients", userId);
      batch.update(clientRef, {
        loyaltyPoints: increment(pointsToAward),
        loyaltyLifetimePoints: increment(pointsToAward),
      });

      // Create game play record
      const gamePlayRef = collection(firestore, "clients", userId, "gamePlays");
      const gamePlayData = {
        userId,
        gameId,
        score: gameResult.score || 0,
        pointsEarned: pointsToAward,
        details: gameResult.details || "",
        playedAt: Timestamp.now(),
        completed: true,
      };

      const gamePlayDocRef = doc(gamePlayRef);
      batch.set(gamePlayDocRef, {
        id: gamePlayDocRef.id,
        ...gamePlayData,
      });

      await batch.commit();

      return {
        success: true,
        pointsAwarded: pointsToAward,
        dailyLimitReached:
          gameStats.dailyPointsEarned >= this.pointsConfig.dailyGameLimit,
      };
    } catch (error) {
      console.error("Error awarding game points:", error);
      return { success: false, pointsAwarded: 0, dailyLimitReached: false };
    }
  }

  /**
   * Record a daily check-in and award points
   * @param userId User ID
   * @returns Check-in result with streak and points
   */
  async recordDailyCheckIn(userId: string): Promise<{
    success: boolean;
    pointsAwarded: number;
    streak: number;
    dailyLimitReached: boolean;
  }> {
    try {
      // Get user's loyalty status
      const statusRef = doc(
        firestore,
        "clients",
        userId,
        "loyaltyStatus",
        "current"
      );
      const statusDoc = await getDoc(statusRef);

      if (!statusDoc.exists()) {
        console.error("User not in loyalty program:", userId);
        return {
          success: false,
          pointsAwarded: 0,
          streak: 0,
          dailyLimitReached: false,
        };
      }

      const status = statusDoc.data() as LoyaltyStatus;

      // Initialize game stats if they don't exist
      const gameStats = status.gameStats || {
        lastPlayed: {},
        dailyPointsEarned: 0,
        lastPointsReset: Timestamp.now(),
        checkInStreak: 0,
        lastCheckIn: Timestamp.fromDate(new Date(0)), // Set to epoch start
        playCount: {},
      };

      // Check if already checked in today
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const todayTimestamp = Timestamp.fromDate(today);

      const lastCheckIn = gameStats.lastCheckIn?.toDate() || new Date(0);
      lastCheckIn.setHours(0, 0, 0, 0);

      if (lastCheckIn.getTime() === today.getTime()) {
        return {
          success: true,
          pointsAwarded: 0,
          streak: gameStats.checkInStreak || 0,
          dailyLimitReached: false,
        };
      }

      // Reset daily points if it's a new day
      if (
        !gameStats.lastPointsReset ||
        gameStats.lastPointsReset.toDate().getTime() < today.getTime()
      ) {
        gameStats.dailyPointsEarned = 0;
        gameStats.lastPointsReset = todayTimestamp;
      }

      // Calculate streak
      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);

      let streak = gameStats.checkInStreak || 0;

      if (lastCheckIn.getTime() === yesterday.getTime()) {
        // Consecutive day, increase streak
        streak += 1;
      } else if (lastCheckIn.getTime() < yesterday.getTime()) {
        // Streak broken, reset to 1
        streak = 1;
      }

      // Calculate points to award
      const basePoints = this.pointsConfig.gamePoints.dailyCheckIn;
      const maxStreakDays = this.pointsConfig.gamePoints.maxStreakDays;
      const streakBonus =
        Math.min(streak - 1, maxStreakDays) *
        this.pointsConfig.gamePoints.streakBonus;
      let pointsToAward = basePoints + streakBonus;

      // Check daily limit
      const remainingDaily =
        this.pointsConfig.dailyGameLimit - (gameStats.dailyPointsEarned || 0);
      let dailyLimitReached = false;

      if (remainingDaily < pointsToAward) {
        pointsToAward = remainingDaily;
        dailyLimitReached = true;
      }

      if (pointsToAward <= 0) {
        // Update streak but don't award points
        gameStats.checkInStreak = streak;
        gameStats.lastCheckIn = todayTimestamp;

        await updateDoc(statusRef, {
          gameStats: gameStats,
          lastUpdated: serverTimestamp(),
        });

        return {
          success: true,
          pointsAwarded: 0,
          streak: streak,
          dailyLimitReached: true,
        };
      }

      // Add transaction
      const transactionRef = collection(
        firestore,
        "clients",
        userId,
        "pointTransactions"
      );

      const transactionData: Omit<PointTransaction, "id"> = {
        userId,
        points: pointsToAward,
        type: "game",
        description: `Daily check-in bonus (${streak}-day streak)`,
        createdAt: Timestamp.now(),
        referenceId: "dailyCheckIn",
        expiresAt: Timestamp.fromDate(
          new Date(
            Date.now() +
              this.pointsConfig.pointsExpiration * 24 * 60 * 60 * 1000
          )
        ),
      };

      const batch = writeBatch(firestore);

      const transactionDocRef = doc(transactionRef);
      batch.set(transactionDocRef, {
        id: transactionDocRef.id,
        ...transactionData,
      });

      // Update game stats
      gameStats.checkInStreak = streak;
      gameStats.lastCheckIn = todayTimestamp;
      gameStats.dailyPointsEarned =
        (gameStats.dailyPointsEarned || 0) + pointsToAward;

      // Update user's points and game stats
      batch.update(statusRef, {
        totalPoints: increment(pointsToAward),
        lifetimePoints: increment(pointsToAward),
        lastUpdated: serverTimestamp(),
        gameStats: gameStats,
      });

      // Update client document
      const clientRef = doc(firestore, "clients", userId);
      batch.update(clientRef, {
        loyaltyPoints: increment(pointsToAward),
        loyaltyLifetimePoints: increment(pointsToAward),
      });

      // Create check-in record
      const checkInRef = collection(firestore, "clients", userId, "checkIns");
      const checkInData = {
        userId,
        date: todayTimestamp,
        streak: streak,
        pointsEarned: pointsToAward,
      };

      const checkInDocRef = doc(checkInRef);
      batch.set(checkInDocRef, {
        id: checkInDocRef.id,
        ...checkInData,
      });

      await batch.commit();

      return {
        success: true,
        pointsAwarded: pointsToAward,
        streak: streak,
        dailyLimitReached: dailyLimitReached,
      };
    } catch (error) {
      console.error("Error recording daily check-in:", error);
      return {
        success: false,
        pointsAwarded: 0,
        streak: 0,
        dailyLimitReached: false,
      };
    }
  }

  /**
   * Get user's game stats
   * @param userId User ID
   * @returns Game stats or null
   */
  async getGameStats(userId: string): Promise<{
    dailyPointsEarned: number;
    dailyPointsLimit: number;
    checkInStreak: number;
    lastCheckIn: Date | null;
    lastPlayed: { [gameId: string]: Date } | null;
    playCount?: { [gameId: string]: number };
  } | null> {
    try {
      const statusRef = doc(
        firestore,
        "clients",
        userId,
        "loyaltyStatus",
        "current"
      );
      const statusDoc = await getDoc(statusRef);

      if (!statusDoc.exists()) {
        return null;
      }

      const status = statusDoc.data() as LoyaltyStatus;
      const gameStats = status.gameStats || {};

      // Check if we need to reset daily points
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      let dailyPointsEarned = gameStats.dailyPointsEarned || 0;

      if (gameStats.lastPointsReset) {
        const lastReset = gameStats.lastPointsReset.toDate();
        lastReset.setHours(0, 0, 0, 0);

        if (lastReset.getTime() < today.getTime()) {
          // It's a new day, reset points
          dailyPointsEarned = 0;

          // Update the document
          await updateDoc(statusRef, {
            "gameStats.dailyPointsEarned": 0,
            "gameStats.lastPointsReset": Timestamp.fromDate(today),
          });
        }
      }

      // Convert Firestore timestamps to JS Dates
      const lastPlayed: { [gameId: string]: Date } = {};
      if (gameStats.lastPlayed) {
        Object.entries(gameStats.lastPlayed).forEach(([gameId, timestamp]) => {
          if (timestamp && typeof timestamp.toDate === "function") {
            lastPlayed[gameId] = timestamp.toDate();
          }
        });
      }

      return {
        dailyPointsEarned: dailyPointsEarned,
        dailyPointsLimit: this.pointsConfig.dailyGameLimit,
        checkInStreak: gameStats.checkInStreak || 0,
        lastCheckIn: gameStats.lastCheckIn
          ? gameStats.lastCheckIn.toDate()
          : null,
        lastPlayed: Object.keys(lastPlayed).length > 0 ? lastPlayed : null,
        playCount: gameStats.playCount || {},
      };
    } catch (error) {
      console.error("Error getting game stats:", error);
      return null;
    }
  }
}

// Export singleton instance
export const loyaltyService = new LoyaltyService();
