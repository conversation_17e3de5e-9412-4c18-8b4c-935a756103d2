import { useState, useEffect } from "react";
// Import only what we need
import { useAuth } from "@/providers/AuthProvider";
import { firestore } from "@/config/firebase";
import { collection, onSnapshot, query } from "firebase/firestore";

/**
 * Hook to get and manage the list of restaurant IDs followed by the current user
 * @returns Object containing followed restaurant IDs and loading state
 */
export const useFollowedRestaurants = () => {
  const { user, userRole } = useAuth();
  const [followedIds, setFollowedIds] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (!user || userRole !== "client") {
      setFollowedIds([]);
      setLoading(false);
      return;
    }

    setLoading(true);

    // Set up a real-time listener for the following collection
    const followingRef = collection(firestore, "clients", user.uid, "following");
    const q = query(followingRef);

    const unsubscribe = onSnapshot(q,
      (snapshot) => {
        const ids = snapshot.docs.map(doc => doc.id);
        setFollowedIds(ids);
        setLoading(false);
      },
      (error) => {
        // Log error in useFollowedRestaurants hook
        console.error("Error fetching followed restaurants:", error);
        setLoading(false);
      }
    );

    // Clean up the listener when the component unmounts
    return () => unsubscribe();
  }, [user, userRole]);

  return { followedIds, loading, isClient: userRole === "client" && !!user };
};
