// IndexedDB utility for offline data storage

// Database configuration
const DB_NAME = 'SalexOfflineDB';
const DB_VERSION = 1;

// Store names
export const STORES = {
  RESTAURANTS: 'restaurants',
  MENU_ITEMS: 'menuItems',
  USER_DATA: 'userData',
  ORDER_HISTORY: 'orderHistory',
  OFFLINE_QUEUE: 'offlineQueue',
};

// Initialize the database
export const initDB = (): Promise<IDBDatabase> => {
  return new Promise((resolve, reject) => {
    const request = indexedDB.open(DB_NAME, DB_VERSION);

    request.onerror = (event) => {
      console.error('IndexedDB error:', event);
      reject('Error opening IndexedDB');
    };

    request.onsuccess = (event) => {
      const db = (event.target as IDBOpenDBRequest).result;
      resolve(db);
    };

    request.onupgradeneeded = (event) => {
      const db = (event.target as IDBOpenDBRequest).result;

      // Create object stores if they don't exist
      if (!db.objectStoreNames.contains(STORES.RESTAURANTS)) {
        db.createObjectStore(STORES.RESTAURANTS, { keyPath: 'id' });
      }

      if (!db.objectStoreNames.contains(STORES.MENU_ITEMS)) {
        db.createObjectStore(STORES.MENU_ITEMS, { keyPath: 'id' });
      }

      if (!db.objectStoreNames.contains(STORES.USER_DATA)) {
        db.createObjectStore(STORES.USER_DATA, { keyPath: 'id' });
      }

      if (!db.objectStoreNames.contains(STORES.ORDER_HISTORY)) {
        db.createObjectStore(STORES.ORDER_HISTORY, { keyPath: 'id' });
      }

      // Create offline queue store with auto-incrementing key
      if (!db.objectStoreNames.contains(STORES.OFFLINE_QUEUE)) {
        const offlineQueueStore = db.createObjectStore(STORES.OFFLINE_QUEUE, {
          keyPath: 'id',
          autoIncrement: true
        });
        // Create an index for the timestamp to process in order
        offlineQueueStore.createIndex('timestamp', 'timestamp', { unique: false });
        // Create an index for the status to easily find pending actions
        offlineQueueStore.createIndex('status', 'status', { unique: false });
      }
    };
  });
};

// Generic function to add data to a store
export const addData = async <T>(storeName: string, data: T): Promise<IDBValidKey> => {
  const db = await initDB();

  return new Promise((resolve, reject) => {
    const transaction = db.transaction(storeName, 'readwrite');
    const store = transaction.objectStore(storeName);
    const request = store.add(data);

    request.onsuccess = () => {
      resolve(request.result);
    };

    request.onerror = (event) => {
      console.error(`Error adding data to ${storeName}:`, event);
      reject(`Error adding data to ${storeName}`);
    };

    transaction.oncomplete = () => {
      db.close();
    };
  });
};

// Generic function to update data in a store
export const updateData = async <T>(storeName: string, data: T): Promise<void> => {
  const db = await initDB();

  return new Promise((resolve, reject) => {
    const transaction = db.transaction(storeName, 'readwrite');
    const store = transaction.objectStore(storeName);
    const request = store.put(data);

    request.onsuccess = () => {
      resolve();
    };

    request.onerror = (event) => {
      console.error(`Error updating data in ${storeName}:`, event);
      reject(`Error updating data in ${storeName}`);
    };

    transaction.oncomplete = () => {
      db.close();
    };
  });
};

// Generic function to get data from a store by ID
export const getData = async <T>(storeName: string, id: IDBValidKey): Promise<T | undefined> => {
  const db = await initDB();

  return new Promise((resolve, reject) => {
    const transaction = db.transaction(storeName, 'readonly');
    const store = transaction.objectStore(storeName);
    const request = store.get(id);

    request.onsuccess = () => {
      resolve(request.result as T);
    };

    request.onerror = (event) => {
      console.error(`Error getting data from ${storeName}:`, event);
      reject(`Error getting data from ${storeName}`);
    };

    transaction.oncomplete = () => {
      db.close();
    };
  });
};

// Generic function to get all data from a store
export const getAllData = async <T>(storeName: string): Promise<T[]> => {
  const db = await initDB();

  return new Promise((resolve, reject) => {
    const transaction = db.transaction(storeName, 'readonly');
    const store = transaction.objectStore(storeName);
    const request = store.getAll();

    request.onsuccess = () => {
      resolve(request.result as T[]);
    };

    request.onerror = (event) => {
      console.error(`Error getting all data from ${storeName}:`, event);
      reject(`Error getting all data from ${storeName}`);
    };

    transaction.oncomplete = () => {
      db.close();
    };
  });
};

// Generic function to delete data from a store by ID
export const deleteData = async (storeName: string, id: IDBValidKey): Promise<void> => {
  const db = await initDB();

  return new Promise((resolve, reject) => {
    const transaction = db.transaction(storeName, 'readwrite');
    const store = transaction.objectStore(storeName);
    const request = store.delete(id);

    request.onsuccess = () => {
      resolve();
    };

    request.onerror = (event) => {
      console.error(`Error deleting data from ${storeName}:`, event);
      reject(`Error deleting data from ${storeName}`);
    };

    transaction.oncomplete = () => {
      db.close();
    };
  });
};

// Clear all data from a store
export const clearStore = async (storeName: string): Promise<void> => {
  const db = await initDB();

  return new Promise((resolve, reject) => {
    const transaction = db.transaction(storeName, 'readwrite');
    const store = transaction.objectStore(storeName);
    const request = store.clear();

    request.onsuccess = () => {
      resolve();
    };

    request.onerror = (event) => {
      console.error(`Error clearing store ${storeName}:`, event);
      reject(`Error clearing store ${storeName}`);
    };

    transaction.oncomplete = () => {
      db.close();
    };
  });
};

// Get pending offline actions
export const getPendingOfflineActions = async (): Promise<OfflineAction[]> => {
  const db = await initDB();

  return new Promise((resolve, reject) => {
    const transaction = db.transaction(STORES.OFFLINE_QUEUE, 'readonly');
    const store = transaction.objectStore(STORES.OFFLINE_QUEUE);
    const index = store.index('status');
    const request = index.getAll('pending');

    request.onsuccess = () => {
      resolve(request.result);
    };

    request.onerror = (event) => {
      console.error('Error getting pending offline actions:', event);
      reject('Error getting pending offline actions');
    };

    transaction.oncomplete = () => {
      db.close();
    };
  });
};

// Define OfflineAction type
export interface OfflineAction {
  id?: number;
  type: string;
  payload: unknown;
  endpoint: string;
  method: string;
  timestamp: number;
  status: 'pending' | 'processing' | 'completed' | 'failed';
}

// Add an action to the offline queue
export const addToOfflineQueue = async (action: OfflineAction): Promise<IDBValidKey> => {
  return addData(STORES.OFFLINE_QUEUE, action);
};

// Update the status of an offline action
export const updateOfflineActionStatus = async (
  id: IDBValidKey,
  status: 'pending' | 'processing' | 'completed' | 'failed'
): Promise<void> => {
  const action = await getData<OfflineAction>(STORES.OFFLINE_QUEUE, id);
  if (action) {
    action.status = status;
    await updateData(STORES.OFFLINE_QUEUE, action);
  }
};
