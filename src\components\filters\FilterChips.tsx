
import { X } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';

export interface FilterChip {
  id: string;
  label: string;
  group: string;
}

interface FilterChipsProps {
  filters: FilterChip[];
  onRemove: (filter: FilterChip) => void;
  onClearAll: () => void;
}

export function FilterChips({ filters, onRemove, onClearAll }: FilterChipsProps) {
  if (filters.length === 0) return null;

  return (
    <div className="flex flex-wrap items-center gap-2 mb-4">
      <div className="flex flex-wrap gap-2 flex-1">
        {filters.map((filter) => (
          <Badge
            key={`${filter.group}-${filter.id}`}
            variant="outline"
            className="px-2 py-1 bg-muted/30 hover:bg-muted/50 transition-colors"
          >
            {filter.label}
            <button
              type="button"
              onClick={() => onRemove(filter)}
              className="ml-1 text-muted-foreground hover:text-foreground"
            >
              <X className="h-3 w-3" />
            </button>
          </Badge>
        ))}
      </div>

      {filters.length > 0 && (
        <Button
          variant="ghost"
          size="sm"
          onClick={onClearAll}
          className="text-xs text-muted-foreground hover:text-foreground"
        >
          Clear all
        </Button>
      )}
    </div>
  );
}
