import { useQuery } from "@tanstack/react-query";
import { Loading } from "@/components/ui/loading";
import { RestaurantCard } from "@/components/restaurant/RestaurantCard";
import { Button } from "@/components/ui/button";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { useRef } from "react";
import {
  collection,
  query,
  where,
  limit,
  getDocs,
} from "firebase/firestore";
import { firestore } from "@/config/firebase";
import type { Restaurant } from "@/types/restaurant";
import { calculateWeightedRating, calculateCombinedScore } from "@/utils/ratingUtils";

const fetchFeaturedRestaurants = async (): Promise<Restaurant[]> => {
  const restaurantsRef = collection(firestore, "restaurants");

  // First, get all active restaurants
  const q = query(
    restaurantsRef,
    where("isActive", "==", true),
    limit(50) // Get a larger pool to calculate from
  );

  const querySnapshot = await getDocs(q);

  // Process restaurants and calculate weighted ratings
  const restaurants = querySnapshot.docs.map((doc) => {
    const data = doc.data();
    const rating = data.rating || 0;
    const reviewCount = data.reviewCount || 0;

    // Calculate weighted rating
    const weightedRating = calculateWeightedRating(rating, reviewCount);

    // Calculate combined score for sorting
    const combinedScore = calculateCombinedScore(rating, reviewCount);

    // For restaurants with no reviews, ensure they have a 0 rating instead of null
    // This is just for the database - the UI will still show N/A

    return {
      id: doc.id,
      ...data,
      // Ensure weightedRating is always a number (0 if no reviews)
      weightedRating: weightedRating || 0,
      combinedScore,
      // Ensure reviewCount is always defined
      reviewCount: reviewCount
    } as Restaurant;
  });

  // Sort by combined score and take top 8
  return restaurants
    .sort((a, b) => (b.combinedScore || 0) - (a.combinedScore || 0))
    .slice(0, 8);
};

export function FeaturedRestaurants() {
  // Reference to the scrollable container
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  // Scroll functions
  const scrollLeft = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({ left: -300, behavior: "smooth" });
    }
  };

  const scrollRight = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({ left: 300, behavior: "smooth" });
    }
  };

  const {
    data: restaurants,
    isLoading,
    isError,
    error,
  } = useQuery({
    queryKey: ["featuredRestaurants"],
    queryFn: fetchFeaturedRestaurants,
    staleTime: 60 * 1000, // 1 minute
    gcTime: 5 * 60 * 1000, // 5 minutes
  });

  if (isLoading) {
    return (
      <section className="py-16 md:py-20 bg-gradient-to-b from-white to-gray-50">
        <div className="mx-auto px-4 max-w-7xl">
          <div className="text-center mb-12">
            <h2 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-orange-600 to-orange-400 bg-clip-text text-transparent animate-fadeIn">
              Featured Restaurants
            </h2>
            <div className="w-32 h-1 bg-gradient-to-r from-orange-600 to-orange-400 mx-auto mt-4 rounded-full" />
            <p className="text-gray-600 mt-4 text-lg animate-fadeIn delay-200">
              Discover our highest-rated dining experiences
            </p>
          </div>
          <div className="flex justify-center">
            <Loading />
          </div>
        </div>
      </section>
    );
  }

  if (isError) {
    return (
      <section className="py-16 md:py-20 bg-gradient-to-b from-white to-gray-50">
        <div className="mx-auto px-4 max-w-7xl">
          <div className="text-center mb-12">
            <h2 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-orange-600 to-orange-400 bg-clip-text text-transparent animate-fadeIn">
              Featured Restaurants
            </h2>
            <div className="w-32 h-1 bg-gradient-to-r from-orange-600 to-orange-400 mx-auto mt-4 rounded-full" />
            <p className="text-gray-600 mt-4 text-lg animate-fadeIn delay-200">
              Discover our highest-rated dining experiences
            </p>
          </div>
          <div className="text-center p-8 bg-white/50 rounded-lg shadow-sm border border-red-100">
            <p className="text-red-500 font-medium">Error: {error?.message}</p>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="py-16 md:py-20 bg-gradient-to-b from-white to-gray-50">
      <div className="mx-auto px-4 max-w-7xl">
        <div className="text-center mb-12">
          <h2 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-orange-600 to-orange-400 bg-clip-text text-transparent animate-fadeIn">
            Featured Restaurants
          </h2>
          <div className="w-32 h-1 bg-gradient-to-r from-orange-600 to-orange-400 mx-auto mt-4 rounded-full" />
          <p className="text-gray-600 mt-4 text-lg animate-fadeIn delay-200">
            Discover our highest-rated dining experiences
          </p>
        </div>

        {/* Horizontal scrolling layout with navigation buttons */}
        <div className="relative px-2">
          <div ref={scrollContainerRef} className="overflow-x-auto pb-6 hide-scrollbar">
            <div className="flex gap-6 min-w-max px-2">
              {restaurants?.map((restaurant) => (
                <div key={restaurant.id} className="w-[280px] transform transition-transform duration-300 hover:translate-y-[-5px]">
                  <RestaurantCard restaurant={restaurant} />
                </div>
              ))}
            </div>
          </div>

          {/* Scroll indicators */}
          <div className="absolute left-0 top-1/2 -translate-y-1/2 w-12 h-full bg-gradient-to-r from-white/80 to-transparent pointer-events-none"></div>
          <div className="absolute right-0 top-1/2 -translate-y-1/2 w-12 h-full bg-gradient-to-l from-white/80 to-transparent pointer-events-none"></div>

          {/* Navigation buttons */}
          <Button
            onClick={scrollLeft}
            className="absolute left-2 top-1/2 -translate-y-1/2 rounded-full w-10 h-10 p-0 bg-white/80 text-gray-700 hover:bg-white shadow-md border border-gray-200 z-10"
            aria-label="Scroll left"
          >
            <ChevronLeft className="h-5 w-5" />
          </Button>
          <Button
            onClick={scrollRight}
            className="absolute right-2 top-1/2 -translate-y-1/2 rounded-full w-10 h-10 p-0 bg-white/80 text-gray-700 hover:bg-white shadow-md border border-gray-200 z-10"
            aria-label="Scroll right"
          >
            <ChevronRight className="h-5 w-5" />
          </Button>
        </div>
      </div>
    </section>
  );
}
