<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/x-icon" href="/Qonai.ico" />
    <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <!-- Primary Meta Tags -->
    <title>
      Qonai - AI-Powered Restaurant Discovery & Food Ordering Platform
    </title>
    <meta
      name="title"
      content="Qonai - AI-Powered Restaurant Discovery & Food Ordering Platform"
    />
    <meta
      name="description"
      content="Discover the best restaurants worldwide with Qonai's AI-powered recommendations. Order food online, earn loyalty points, and enjoy personalized dining experiences. Available globally with local restaurant partnerships."
    />
    <meta
      name="keywords"
      content="restaurant, food delivery, AI recommendations, global restaurants, online ordering, food app, dining, loyalty program, Qonai, international food delivery, restaurant discovery, AI dining assistant"
    />
    <meta name="author" content="Qonai Team" />
    <meta name="robots" content="index, follow" />
    <meta name="language" content="English" />
    <meta name="revisit-after" content="7 days" />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://qonai.me/" />
    <meta
      property="og:title"
      content="Qonai - AI-Powered Restaurant Discovery & Food Ordering"
    />
    <meta
      property="og:description"
      content="Discover amazing restaurants worldwide and order delicious food with AI-powered recommendations. Join millions of food lovers globally!"
    />
    <meta property="og:image" content="https://qonai.me/og-image.jpg" />
    <meta property="og:image:width" content="1200" />
    <meta property="og:image:height" content="630" />
    <meta property="og:site_name" content="Qonai" />
    <meta property="og:locale" content="en_US" />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="https://qonai.me/" />
    <meta
      property="twitter:title"
      content="Qonai - AI-Powered Restaurant Discovery & Food Ordering"
    />
    <meta
      property="twitter:description"
      content="Discover amazing restaurants worldwide and order delicious food with AI-powered recommendations. Join millions of food lovers globally!"
    />
    <meta
      property="twitter:image"
      content="https://qonai.me/twitter-image.jpg"
    />
    <meta property="twitter:creator" content="@QonaiApp" />

    <!-- Additional Meta Tags -->
    <meta name="application-name" content="Qonai" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <meta name="apple-mobile-web-app-title" content="Qonai" />
    <meta name="format-detection" content="telephone=no" />
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="msapplication-config" content="/browserconfig.xml" />
    <meta name="msapplication-TileColor" content="#ff6200" />
    <meta name="msapplication-tap-highlight" content="no" />
    <meta name="theme-color" content="#ff6200" />

    <!-- Geo Tags -->
    <meta name="geo.region" content="Global" />
    <meta name="geo.country" content="Worldwide" />
    <meta name="geo.placename" content="Global Service" />

    <!-- Business/App Specific -->
    <meta name="category" content="Food & Dining" />
    <meta name="coverage" content="Worldwide" />
    <meta name="distribution" content="global" />
    <meta name="rating" content="general" />

    <!-- Structured Data for Rich Snippets -->
    <script type="application/ld+json">
      {
        "@context": "https://schema.org",
        "@type": "WebApplication",
        "name": "Qonai",
        "description": "AI-powered restaurant discovery and food ordering platform",
        "url": "https://qonai.me",
        "applicationCategory": "Food & Dining",
        "operatingSystem": "Web, iOS, Android",
        "offers": {
          "@type": "Offer",
          "price": "0",
          "priceCurrency": "AZN"
        },
        "aggregateRating": {
          "@type": "AggregateRating",
          "ratingValue": "4.8",
          "ratingCount": "1250"
        },
        "author": {
          "@type": "Organization",
          "name": "Qonai Team"
        }
      }
    </script>

    <!-- Preconnect to external domains -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link rel="preconnect" href="https://unpkg.com" />

    <!-- Leaflet CSS -->
    <link
      rel="stylesheet"
      href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"
      integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY="
      crossorigin=""
    />

    <!-- PWA Manifest -->
    <link rel="manifest" href="/manifest.json" />
    <script>
      // Service Worker Registration
      if ("serviceWorker" in navigator) {
        window.addEventListener("load", () => {
          navigator.serviceWorker.register("/sw.js");
        });
      }
    </script>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
