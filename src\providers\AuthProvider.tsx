import {
  useState,
  useEffect,
  createContext,
  useContext,
  ReactNode,
  useCallback,
  useMemo,
  useRef,
} from "react";
import {
  User,
  AuthError,
  GoogleAuthProvider,
  signInWithPopup,
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  signOut,
  onAuthStateChanged,
} from "firebase/auth";
import {
  doc,
  getDoc,
  collection,
  query,
  where,
  getDocs,
  FirestoreError,
  Timestamp,
  serverTimestamp,
  writeBatch,
  onSnapshot,
  orderBy,
  updateDoc,
  deleteDoc,
} from "firebase/firestore";
import { auth, firestore } from "@/config/firebase";
import { toast } from "sonner";
import { Loading } from "@/components/ui/loading";
import { notificationService } from "@/services/NotificationService";
import { loyaltyService } from "@/services/LoyaltyService";

// --- Typer (Bunları ayrı bir types dosyasında tutmak daha iyi olabilir) ---
interface ClientDetails {
  username: string;
  firstName: string;
  lastName: string;
  phone: string;
  // Gerekirse diğer istemci alanları
}

interface RestaurantDetails {
  username: string;
  restaurantName: string;
  // isProfileComplete db'den okunacak, burada olmasına gerek yok
  // Gerekirse diğer restoran başlangıç alanları
}

interface UserData {
  role: "restaurant" | "client";
  email: string;
  createdAt: Timestamp | Date;
  // İleride eklenebilecek diğer ortak alanlar
}

interface Notification {
  id: string;
  type: string;
  title: string;
  message: string;
  read: boolean;
  createdAt: Timestamp;
  orderId?: string;
  reservationId?: string;
}

interface NotificationPreferences {
  email: boolean;
  push: boolean;
  sound: boolean;
}

interface AuthContextProps {
  user: User | null;
  loading: boolean; // Genel kimlik doğrulama yükleme durumu
  error: AuthError | FirestoreError | null;
  userRole: "restaurant" | "client" | null;
  isProfileComplete: boolean | null; // null: henüz kontrol edilmedi, false: tamamlanmamış, true: tamamlanmış
  notifications: Notification[];
  unreadNotificationsCount: number;
  notificationPreferences: NotificationPreferences;
  signInWithGoogle: () => Promise<void>;
  signInWithEmail: (email: string, pass: string) => Promise<void>;
  signUpWithEmail: (
    email: string,
    pass: string,
    role: "restaurant" | "client",
    userDetails: ClientDetails | RestaurantDetails
  ) => Promise<void>;
  logOut: () => Promise<void>;
  clearError: () => void;
  // Profil tamamlama durumunu manuel olarak tetiklemek için (örn. profil güncelleme sonrası)
  checkProfileCompletion: (
    uid: string,
    role: "restaurant" | "client"
  ) => Promise<void>;
  // Bildirim işlemleri
  markNotificationAsRead: (notificationId: string) => Promise<void>;
  markAllNotificationsAsRead: () => Promise<void>;
  deleteNotification: (notificationId: string) => Promise<void>;
  updateNotificationPreferences: (
    preferences: Partial<NotificationPreferences>
  ) => Promise<void>;
}
// --- Context ---
const AuthContext = createContext<AuthContextProps | undefined>(undefined);

// --- Provider Component ---
interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState<boolean>(true); // Başlangıçta true olmalı
  const [error, setError] = useState<AuthError | FirestoreError | null>(null);
  const [userRole, setUserRole] = useState<"restaurant" | "client" | null>(
    null
  );
  // Başlangıç durumu null, kontrol edildikten sonra true/false olur
  const [isProfileComplete, setIsProfileComplete] = useState<boolean | null>(
    null
  );
  // Bildirim durumları
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [notificationPreferences, setNotificationPreferences] =
    useState<NotificationPreferences>({
      email: true,
      push: true,
      sound: true,
    });

  // Bildirim dinleyicileri için referanslar
  const notificationsUnsubscribeRef = useRef<(() => void) | null>(null);

  // Gösterilen bildirimleri takip etmek için önbellek
  const shownNotificationsRef = useRef<Set<string>>(new Set());

  // Uygulama başlangıcında bir bayrak
  const isInitialLoadRef = useRef<boolean>(true);

  // Profil tamamlama kontrol fonksiyonu
  const checkProfileCompletion = useCallback(
    async (uid: string, role: "restaurant" | "client") => {
      if (!uid || !role) {
        setIsProfileComplete(false); // Geçersiz giriş varsa tamamlanmamış say
        return;
      }
      try {
        const detailsDocRef = doc(
          firestore,
          role === "restaurant" ? "restaurants" : "clients",
          uid
        );
        const detailsDocSnap = await getDoc(detailsDocRef);

        if (!detailsDocSnap.exists()) {
          console.warn(
            `User details not found in ${role}s collection for UID: ${uid}`
          );
          setIsProfileComplete(false); // Detaylar yoksa tamamlanmamış say
          return;
        }

        const data = detailsDocSnap.data();
        let isComplete = false;
        if (role === "restaurant") {
          // Restoran için DB'deki flag'i oku (Bu flag /profile sayfasında güncellenmeli)
          isComplete = !!data?.isProfileComplete; // Default to false if field doesn't exist
          // Alternatif: Gerekli tüm alanları burada kontrol etmek (daha az verimli)
          /*
         isComplete = !!(
            data.restaurantName &&
            data.cuisines?.length > 0 &&
            data.categories?.length > 0 &&
            data.address &&
            data.phone &&
            data.workingHours?.length > 0 &&
            data.location?.latitude &&
            data.location?.longitude
          );
         */
        } else {
          // İstemci için gerekli alanları kontrol et
          isComplete = !!(data?.firstName && data?.lastName && data?.phone);
        }
        setIsProfileComplete(isComplete);
      } catch (err) {
        console.error("Error checking profile completion:", err);
        setIsProfileComplete(false); // Hata durumunda tamamlanmamış say
        setError(err as FirestoreError);
      }
    },
    []
  );

  // Bildirim işlemleri
  const markNotificationAsRead = useCallback(
    async (notificationId: string) => {
      if (!user || !userRole) return;

      try {
        const notificationRef = doc(
          firestore,
          userRole === "restaurant" ? "restaurants" : "clients",
          user.uid,
          "notifications",
          notificationId
        );

        await updateDoc(notificationRef, { read: true });
      } catch (err) {
        console.error("Error marking notification as read:", err);
        toast.error("Failed to update notification");
      }
    },
    [user, userRole]
  );

  const markAllNotificationsAsRead = useCallback(async () => {
    if (!user || !userRole) return;

    try {
      const batch = writeBatch(firestore);
      const unreadNotifications = notifications.filter((n) => !n.read);

      unreadNotifications.forEach((notification) => {
        const notificationRef = doc(
          firestore,
          userRole === "restaurant" ? "restaurants" : "clients",
          user.uid,
          "notifications",
          notification.id
        );
        batch.update(notificationRef, { read: true });
      });

      await batch.commit();
      toast.success("All notifications marked as read");
    } catch (err) {
      console.error("Error marking all notifications as read:", err);
      toast.error("Failed to update notifications");
    }
  }, [user, userRole, notifications]);

  const deleteNotification = useCallback(
    async (notificationId: string) => {
      if (!user || !userRole) return;

      try {
        const notificationRef = doc(
          firestore,
          userRole === "restaurant" ? "restaurants" : "clients",
          user.uid,
          "notifications",
          notificationId
        );

        await deleteDoc(notificationRef);
        toast.success("Notification removed");
      } catch (err) {
        console.error("Error deleting notification:", err);
        toast.error("Failed to delete notification");
      }
    },
    [user, userRole]
  );

  const updateNotificationPreferences = useCallback(
    async (preferences: Partial<NotificationPreferences>) => {
      if (!user || !userRole) return;

      try {
        const userRef = doc(
          firestore,
          userRole === "restaurant" ? "restaurants" : "clients",
          user.uid
        );

        await updateDoc(userRef, {
          notificationPreferences: {
            ...notificationPreferences,
            ...preferences,
          },
        });

        setNotificationPreferences((prev) => ({ ...prev, ...preferences }));

        // Update sound preference in notification service
        if (preferences.sound !== undefined) {
          notificationService.toggleSound(preferences.sound);
        }

        toast.success("Notification preferences updated");
      } catch (err) {
        console.error("Error updating notification preferences:", err);
        toast.error("Failed to update preferences");
      }
    },
    [user, userRole, notificationPreferences]
  );

  // Bildirim dinleyicisini ayarla
  useEffect(() => {
    if (!user || !userRole) return;

    // Bildirim izni iste
    notificationService.requestNotificationPermission();

    // Bildirim tercihlerini al
    const fetchNotificationPreferences = async () => {
      try {
        const userRef = doc(
          firestore,
          userRole === "restaurant" ? "restaurants" : "clients",
          user.uid
        );
        const userDoc = await getDoc(userRef);

        if (userDoc.exists()) {
          const userData = userDoc.data();
          if (userData.notificationPreferences) {
            setNotificationPreferences(userData.notificationPreferences);
            // Check if sound property exists before using it
            if ("sound" in userData.notificationPreferences) {
              notificationService.toggleSound(
                userData.notificationPreferences.sound
              );
            } else {
              // Use default sound setting if not specified
              notificationService.toggleSound(true);
            }
          } else {
            // Set default notification preferences if none exist
            const defaultPreferences = {
              email: true,
              push: true,
              inApp: true,
              sound: true,
              orderUpdates: true,
              promotions: false,
              newRestaurants: true,
              weeklyDigest: false,
            };
            setNotificationPreferences(defaultPreferences);
            notificationService.toggleSound(true);
          }
        }
      } catch (err) {
        console.error("Error fetching notification preferences:", err);
      }
    };

    fetchNotificationPreferences();

    // Bildirimleri dinle
    const userRef = doc(
      firestore,
      userRole === "restaurant" ? "restaurants" : "clients",
      user.uid
    );
    const notificationsRef = collection(userRef, "notifications");
    const q = query(notificationsRef, orderBy("createdAt", "desc"));

    const unsubscribe = onSnapshot(
      q,
      (snapshot) => {
        const notificationsList: Notification[] = [];

        snapshot.forEach((doc) => {
          notificationsList.push({
            id: doc.id,
            ...doc.data(),
          } as Notification);
        });

        setNotifications(notificationsList);

        // İlk yükleme işlemini tamamla
        if (isInitialLoadRef.current) {
          // İlk yükleme sırasında tüm bildirim ID'lerini önbelleğe ekle
          notificationsList.forEach((notification) => {
            shownNotificationsRef.current.add(notification.id);
          });
          isInitialLoadRef.current = false;
        } else {
          // Yeni bildirim geldiğinde ses çal ve bildirim göster
          snapshot.docChanges().forEach((change) => {
            if (change.type === "added") {
              const notification = {
                id: change.doc.id,
                ...change.doc.data(),
              } as Notification;

              // Bildirim daha önce gösterilmemişse ve okunmamışsa göster
              if (
                !notification.read &&
                !shownNotificationsRef.current.has(notification.id)
              ) {
                // Bildirimi önbelleğe ekle
                shownNotificationsRef.current.add(notification.id);

                // Bildirim göster
                notificationService.notify({
                  title: notification.title,
                  message: notification.message,
                  playSound: notificationPreferences.sound,
                  url: notification.orderId
                    ? `/dashboard?tab=orders&highlight=${notification.orderId}`
                    : notification.reservationId
                    ? `/dashboard?tab=reservations&highlight=${notification.reservationId}`
                    : "/dashboard",
                  data: {
                    notificationId: notification.id,
                    type: notification.type,
                  },
                });
              }
            }
          });
        }
      },
      (error) => {
        console.error("Error fetching notifications:", error);
      }
    );

    // Referansı sakla
    notificationsUnsubscribeRef.current = unsubscribe;

    return () => {
      // Temizleme
      if (notificationsUnsubscribeRef.current) {
        notificationsUnsubscribeRef.current();
        notificationsUnsubscribeRef.current = null;
      }
    };
  }, [user, userRole, notificationPreferences]);

  // Kullanıcı oturum durumunu dinle
  useEffect(() => {
    setLoading(true); // Dinleyici başladığında yükleniyor olarak ayarla

    // Kullanıcı durumunu hızlı bir şekilde kontrol et
    const currentUser = auth.currentUser;
    if (currentUser) {
      setUser(currentUser);
    }

    const unsubscribe = onAuthStateChanged(auth, async (currentUser) => {
      // Kullanıcı durumunu hemen güncelle
      setUser(currentUser);

      if (currentUser) {
        try {
          // Rolü users koleksiyonundan al
          const userDocRef = doc(firestore, "users", currentUser.uid);
          const userDocSnap = await getDoc(userDocRef);

          if (userDocSnap.exists()) {
            const userData = userDocSnap.data() as UserData;
            const role = userData.role;
            setUserRole(role);
            // Rolü aldıktan sonra profil tamamlama durumunu kontrol et
            await checkProfileCompletion(currentUser.uid, role);
          } else {
            console.warn(
              "User role not found in 'users' collection for UID:",
              currentUser.uid
            );
            setUserRole(null);
            setIsProfileComplete(false); // Rol yoksa profil tamamlanmamış
          }
        } catch (err) {
          console.error("Error fetching user role/details:", err);
          setUserRole(null);
          setIsProfileComplete(false);
          setError(err as FirestoreError | AuthError);
          toast.error("Failed to load your profile data.");
        } finally {
          setLoading(false); // Tüm işlemler bittikten sonra yüklenmeyi bitir
        }
      } else {
        // Kullanıcı çıkış yaptı veya yok
        setUserRole(null);
        setIsProfileComplete(false); // Giriş yapmamışsa profil tamamlanmamış
        setError(null); // Hataları temizle
        setLoading(false); // Yüklenmeyi bitir

        // Bildirim dinleyicisini temizle
        if (notificationsUnsubscribeRef.current) {
          notificationsUnsubscribeRef.current();
          notificationsUnsubscribeRef.current = null;
        }

        // Bildirimleri temizle
        setNotifications([]);

        // Önbelleği temizle ve ilk yükleme bayrağını sıfırla
        shownNotificationsRef.current.clear();
        isInitialLoadRef.current = true;
      }
    });

    // Cleanup subscription on unmount
    return () => unsubscribe();
  }, [checkProfileCompletion]); // checkProfileCompletion'ı bağımlılığa ekle

  // --- Authentication Functions ---

  const signInWithGoogle = useCallback(async () => {
    setError(null);
    // Loading state will be handled by onAuthStateChanged
    const provider = new GoogleAuthProvider();
    try {
      await signInWithPopup(auth, provider);
      // Başarılı giriş sonrası onAuthStateChanged tetiklenecek ve rol/profil kontrolü yapacak
    } catch (err) {
      console.error("Google Sign-In Error:", err);
      setError(err as AuthError);
      toast.error("Google Sign-In failed. Please try again.");
    }
    // Loading'i burada false yapmaya gerek yok, onAuthStateChanged hallediyor
  }, []);

  const signInWithEmail = useCallback(async (email: string, pass: string) => {
    setError(null);
    // Loading state will be handled by onAuthStateChanged
    try {
      await signInWithEmailAndPassword(auth, email, pass);
      // Başarılı giriş sonrası onAuthStateChanged tetiklenecek
    } catch (err) {
      console.error("Email Sign-In Error:", err);
      setError(err as AuthError);
      toast.error("Failed to sign in. Check your email and password.");
      throw err; // Hatanın çağrıldığı yere iletilmesi için
    }
  }, []);

  const signUpWithEmail = useCallback(
    async (
      email: string,
      pass: string,
      role: "restaurant" | "client",
      userDetails: ClientDetails | RestaurantDetails
    ) => {
      setError(null);
      setLoading(true); // Signup işlemi boyunca yükleniyor

      const username = (userDetails as ClientDetails | RestaurantDetails)
        .username;
      if (!username?.trim()) {
        setError(new Error("Username is required.") as AuthError);
        setLoading(false);
        toast.error("Username cannot be empty.");
        throw new Error("Username is required.");
      }

      try {
        // Validate required fields
        if (role === "client") {
          const { firstName, lastName, phone } = userDetails as ClientDetails;
          if (!firstName?.trim() || !lastName?.trim() || !phone?.trim()) {
            throw new Error(
              "First name, last name, and phone are required for clients."
            );
          }
        } else {
          const { restaurantName } = userDetails as RestaurantDetails;
          if (!restaurantName?.trim()) {
            throw new Error("Restaurant name is required.");
          }
        }

        // Check if username exists (atomic check might be better with transactions if high concurrency expected)
        const clientsQuery = query(
          collection(firestore, "clients"),
          where("username", "==", username)
        );
        const restaurantsQuery = query(
          collection(firestore, "restaurants"),
          where("username", "==", username)
        );
        const [clientsSnapshot, restaurantsSnapshot] = await Promise.all([
          getDocs(clientsQuery),
          getDocs(restaurantsQuery),
        ]);

        if (!clientsSnapshot.empty || !restaurantsSnapshot.empty) {
          throw new Error("Username already exists. Please choose another.");
        }

        // Create Auth User
        const userCredential = await createUserWithEmailAndPassword(
          auth,
          email,
          pass
        );
        const user = userCredential.user;
        const timestamp = serverTimestamp(); // Use server timestamp

        // Prepare batch write
        const batch = writeBatch(firestore);

        // 1. Set role in 'users' collection
        const userDocRef = doc(firestore, "users", user.uid);
        batch.set(userDocRef, { role, email, createdAt: timestamp });

        // 2. Set details in respective collection
        const commonFields = { email, uid: user.uid, createdAt: timestamp };

        if (role === "restaurant") {
          const restaurantDetails = userDetails as RestaurantDetails;
          const restaurantDocRef = doc(firestore, "restaurants", user.uid);
          const restaurantData = {
            ...commonFields,
            username: restaurantDetails.username,
            restaurantName: restaurantDetails.restaurantName,
            isProfileComplete: false, // Start as incomplete
            // Initialize other required restaurant fields with defaults
            cuisines: [],
            categories: [],
            address: "",
            phone: "",
            location: { latitude: 0, longitude: 0 },
            features: [],
            atmosphere: [],
            dietary: [],
            services: [],
            specialties: [],
            paymentMethods: [],
            certifications: [],
            languages: [],
            isOpen: false,
            isActive: false,
            rating: 0,
            reviewCount: 0, // Add reviewCount
            description: "",
            workingHours: [],
            autoUpdateStatus: true,
            seating: { indoor: false, outdoor: false, totalCapacity: 0 },
            parkingAvailable: false,
            noiseLevel: "moderate",
            dressCode: "casual",
            reservationPolicy: "not required",
          };
          batch.set(restaurantDocRef, restaurantData);
        } else {
          const clientDetails = userDetails as ClientDetails;
          const clientDocRef = doc(firestore, "clients", user.uid);
          const clientData = { ...commonFields, ...clientDetails };
          batch.set(clientDocRef, clientData);
        }

        // Commit batch
        await batch.commit();

        // Manually update local state immediately (onAuthStateChanged will confirm later)
        setUser(user);
        setUserRole(role);
        setIsProfileComplete(role === "client"); // Clients are complete initially based on required fields

        // Initialize loyalty program for client users
        if (role === "client") {
          try {
            // We'll let the useEffect in LoginRegister handle this if there's a referral code
            // Only initialize directly if there's no pending referral code
            if (!localStorage.getItem("pendingReferralCode")) {
              await loyaltyService.initializeUser(user.uid, email);
            }
          } catch (loyaltyError) {
            console.error("Error initializing loyalty program:", loyaltyError);
            // Don't block registration if loyalty initialization fails
          }
        }

        toast.success(
          "Registration successful! Please complete your profile if required."
        );
        // No hard redirect here, let ProtectedRoute handle it based on isProfileComplete state
      } catch (err) {
        console.error("Signup Error:", err);
        const errorMessage =
          err instanceof Error
            ? err.message
            : "An unknown signup error occurred.";
        setError(err as AuthError | FirestoreError);
        toast.error(errorMessage);
        throw err; // Re-throw for form handling
      } finally {
        setLoading(false);
      }
    },
    [] // No dependencies needed if auth/firestore are stable
  );

  const logOut = useCallback(async () => {
    setError(null);
    // Loading state handled by onAuthStateChanged
    try {
      await signOut(auth);
      // State will be reset by onAuthStateChanged
    } catch (err) {
      console.error("Logout Error:", err);
      setError(err as AuthError);
      toast.error("Failed to log out.");
    }
  }, []);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // Okunmamış bildirim sayısını hesapla
  const unreadNotificationsCount = useMemo(() => {
    return notifications.filter((n) => !n.read).length;
  }, [notifications]);

  // Memoize context value to prevent unnecessary re-renders
  const contextValue = useMemo(
    () => ({
      user,
      loading,
      error,
      userRole,
      isProfileComplete,
      notifications,
      unreadNotificationsCount,
      notificationPreferences,
      signInWithGoogle,
      signInWithEmail,
      signUpWithEmail,
      logOut,
      clearError,
      checkProfileCompletion,
      markNotificationAsRead,
      markAllNotificationsAsRead,
      deleteNotification,
      updateNotificationPreferences,
    }),
    [
      user,
      loading,
      error,
      userRole,
      isProfileComplete,
      notifications,
      unreadNotificationsCount,
      notificationPreferences,
      signInWithGoogle,
      signInWithEmail,
      signUpWithEmail,
      logOut,
      clearError,
      checkProfileCompletion,
      markNotificationAsRead,
      markAllNotificationsAsRead,
      deleteNotification,
      updateNotificationPreferences,
    ]
  );

  // Render loading indicator while initial auth state is resolving
  // This prevents rendering children before knowing the user status
  if (loading && !error) {
    return <Loading text="Authenticating..." />; // Veya daha genel bir yükleme ekranı
  }

  return (
    <AuthContext.Provider value={contextValue}>{children}</AuthContext.Provider>
  );
};

// --- Custom Hook ---
// eslint-disable-next-line react-refresh/only-export-components
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};

export default AuthProvider; // Keep default export if needed elsewhere
