import { useState, useEffect } from "react";
import { HtmlEditor } from "./html-editor";
import { toast } from "sonner";
import { TemplateBlocksGrid, TemplateBlock } from "./email-template-blocks";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Eye, Save, Undo, Redo, Co<PERSON>, X } from "lucide-react";

// Define email template settings interface
interface EmailTemplateSettings {
  backgroundColor: string;
  contentWidth: number;
  fontFamily: string;
  fontSize: number;
  textColor: string;
  linkColor: string;
  buttonColor: string;
  buttonTextColor: string;
}

// Default email template settings
const defaultSettings: EmailTemplateSettings = {
  backgroundColor: "#f9f9f9",
  contentWidth: 600,
  fontFamily: "Arial, sans-serif",
  fontSize: 16,
  textColor: "#333333",
  linkColor: "#0066cc",
  buttonColor: "#ff6200",
  buttonTextColor: "#ffffff",
};

// Email template variables interface
interface EmailTemplateVariable {
  name: string;
  description: string;
  defaultValue: string;
}

// Common email template variables
const commonVariables: EmailTemplateVariable[] = [
  {
    name: "name",
    description: "Recipient's name",
    defaultValue: "John Doe",
  },
  {
    name: "unsubscribeLink",
    description: "Link to unsubscribe from emails",
    defaultValue: "#",
  },
  {
    name: "currentDate",
    description: "Current date",
    defaultValue: new Date().toLocaleDateString(),
  },
];

// Email Template Editor Props
interface EmailTemplateEditorProps {
  initialHtml?: string;
  onSave?: (html: string, variables: string[]) => void;
  onPreview?: (html: string) => void;
  className?: string;
}

export function EmailTemplateEditor({
  initialHtml = "",
  onSave,
  onPreview,
  className,
}: EmailTemplateEditorProps) {
  const [html, setHtml] = useState(initialHtml);
  const [settings, setSettings] =
    useState<EmailTemplateSettings>(defaultSettings);
  const [variables] = useState<EmailTemplateVariable[]>(commonVariables);
  const [customVariables] = useState<EmailTemplateVariable[]>([]);
  const [history, setHistory] = useState<string[]>([initialHtml]);
  const [historyIndex, setHistoryIndex] = useState(0);
  const [showPreview, setShowPreview] = useState(false);
  const [previewHtml, setPreviewHtml] = useState("");
  const [showInlinePreview, setShowInlinePreview] = useState(false);
  const [customVariableInput, setCustomVariableInput] = useState("");

  // Add to history when HTML changes
  useEffect(() => {
    if (html !== history[historyIndex]) {
      const newHistory = history.slice(0, historyIndex + 1);
      newHistory.push(html);
      setHistory(newHistory);
      setHistoryIndex(newHistory.length - 1);
    }
  }, [html, history, historyIndex]);

  // Handle undo
  const handleUndo = () => {
    if (historyIndex > 0) {
      setHistoryIndex(historyIndex - 1);
      setHtml(history[historyIndex - 1]);
    }
  };

  // Handle redo
  const handleRedo = () => {
    if (historyIndex < history.length - 1) {
      setHistoryIndex(historyIndex + 1);
      setHtml(history[historyIndex + 1]);
    }
  };

  // Handle adding a template block
  const handleAddBlock = (block: TemplateBlock) => {
    setHtml((prevHtml) => prevHtml + block.html);
  };

  // Handle preview
  const handlePreview = () => {
    // Replace variables with their default values for preview
    let previewContent = html;

    [...variables, ...customVariables].forEach((variable) => {
      const regex = new RegExp(`\\{\\{${variable.name}\\}\\}`, "g");
      previewContent = previewContent.replace(regex, variable.defaultValue);
    });

    setPreviewHtml(previewContent);
    setShowPreview(true);

    if (onPreview) {
      onPreview(previewContent);
    }

    // Show toast notification
    toast.success("Preview Generated", {
      description: "Email preview has been generated with sample data.",
      duration: 2000,
    });
  };

  // Handle save
  const handleSave = () => {
    if (onSave) {
      // Extract all variables from the HTML
      const variableMatches = html.match(/\{\{([^}]+)\}\}/g) || [];
      const extractedVariables = variableMatches.map((match) =>
        match.replace(/\{\{|\}\}/g, "")
      );

      // Remove duplicates
      const uniqueVariables = [...new Set(extractedVariables)];

      onSave(html, uniqueVariables);
    }
  };

  return (
    <div className={`${className} bg-gray-50 min-h-screen`}>
      <div className="max-w-7xl mx-auto p-6">
        {/* Header Section */}
        <div className="mb-6">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-4">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                Email Template Editor
              </h1>
              <p className="text-sm text-gray-600 mt-1">
                Create and customize your email templates
              </p>
            </div>

            <div className="flex items-center gap-3">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowInlinePreview(!showInlinePreview)}
                className="bg-white border-gray-300 hover:bg-gray-50"
              >
                <Eye className="h-4 w-4 mr-2" />
                {showInlinePreview ? "Hide Preview" : "Show Preview"}
              </Button>

              <Button
                variant="default"
                size="sm"
                onClick={handleSave}
                className="bg-blue-600 hover:bg-blue-700 text-white"
              >
                <Save className="h-4 w-4 mr-2" />
                Save Template
              </Button>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="space-y-6">
          {/* Tabs Section - Redesigned */}
          <div className="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
            <Tabs defaultValue="blocks" className="w-full">
              <TabsList className="grid w-full grid-cols-3 bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-gray-200 h-14 rounded-none">
                <TabsTrigger
                  value="blocks"
                  className="data-[state=active]:bg-white data-[state=active]:shadow-lg data-[state=active]:border-b-2 data-[state=active]:border-blue-500 font-semibold text-gray-700 data-[state=active]:text-blue-600 transition-all duration-300"
                >
                  📦 Content Blocks
                </TabsTrigger>
                <TabsTrigger
                  value="settings"
                  className="data-[state=active]:bg-white data-[state=active]:shadow-lg data-[state=active]:border-b-2 data-[state=active]:border-blue-500 font-semibold text-gray-700 data-[state=active]:text-blue-600 transition-all duration-300"
                >
                  ⚙️ Settings
                </TabsTrigger>
                <TabsTrigger
                  value="variables"
                  className="data-[state=active]:bg-white data-[state=active]:shadow-lg data-[state=active]:border-b-2 data-[state=active]:border-blue-500 font-semibold text-gray-700 data-[state=active]:text-blue-600 transition-all duration-300"
                >
                  🔧 Variables
                </TabsTrigger>
              </TabsList>

              <TabsContent value="blocks" className="p-8">
                <div className="space-y-6">
                  <div className="text-center">
                    <h3 className="text-2xl font-bold text-gray-900 mb-3">
                      📦 Content Blocks
                    </h3>
                    <p className="text-gray-600 max-w-2xl mx-auto">
                      Drag and drop these pre-designed blocks into your email
                      template. Each block is responsive and customizable.
                    </p>
                  </div>

                  <div className="bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl p-8">
                    <TemplateBlocksGrid onSelectBlock={handleAddBlock} />
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="settings" className="p-8">
                <div className="space-y-6">
                  <div className="text-center">
                    <h3 className="text-2xl font-bold text-gray-900 mb-3">
                      ⚙️ Template Settings
                    </h3>
                    <p className="text-gray-600 max-w-2xl mx-auto">
                      Customize the overall appearance and styling of your email
                      template.
                    </p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    {/* Layout Settings */}
                    <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-6 space-y-4">
                      <h4 className="text-lg font-semibold text-blue-900 mb-4">
                        📐 Layout
                      </h4>

                      {/* Content Width */}
                      <div>
                        <Label
                          htmlFor="contentWidth"
                          className="text-sm font-medium text-blue-800"
                        >
                          Content Width
                        </Label>
                        <Select
                          value={settings.contentWidth.toString()}
                          onValueChange={(value) =>
                            setSettings({
                              ...settings,
                              contentWidth: parseInt(value),
                            })
                          }
                        >
                          <SelectTrigger className="mt-2 bg-white border-blue-200 focus:border-blue-500">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="480">480px (Mobile)</SelectItem>
                            <SelectItem value="600">
                              600px (Standard)
                            </SelectItem>
                            <SelectItem value="800">800px (Wide)</SelectItem>
                            <SelectItem value="1000">1000px (Full)</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      {/* Background Color */}
                      <div>
                        <Label
                          htmlFor="backgroundColor"
                          className="text-sm font-medium text-blue-800"
                        >
                          Background Color
                        </Label>
                        <div className="flex items-center gap-3 mt-2">
                          <Input
                            id="backgroundColor"
                            type="color"
                            value={settings.backgroundColor}
                            onChange={(e) =>
                              setSettings({
                                ...settings,
                                backgroundColor: e.target.value,
                              })
                            }
                            className="w-16 h-10 p-1 border-2 border-blue-200 rounded-lg"
                          />
                          <Input
                            value={settings.backgroundColor}
                            onChange={(e) =>
                              setSettings({
                                ...settings,
                                backgroundColor: e.target.value,
                              })
                            }
                            className="flex-1 bg-white border-blue-200 focus:border-blue-500"
                            placeholder="#f9f9f9"
                          />
                        </div>
                      </div>
                    </div>

                    {/* Typography Settings */}
                    <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-xl p-6 space-y-4">
                      <h4 className="text-lg font-semibold text-green-900 mb-4">
                        ✍️ Typography
                      </h4>

                      {/* Font Family */}
                      <div>
                        <Label
                          htmlFor="fontFamily"
                          className="text-sm font-medium text-green-800"
                        >
                          Font Family
                        </Label>
                        <Select
                          value={settings.fontFamily}
                          onValueChange={(value) =>
                            setSettings({ ...settings, fontFamily: value })
                          }
                        >
                          <SelectTrigger className="mt-2 bg-white border-green-200 focus:border-green-500">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="Arial, sans-serif">
                              Arial
                            </SelectItem>
                            <SelectItem value="Helvetica, sans-serif">
                              Helvetica
                            </SelectItem>
                            <SelectItem value="Georgia, serif">
                              Georgia
                            </SelectItem>
                            <SelectItem value="Times New Roman, serif">
                              Times New Roman
                            </SelectItem>
                            <SelectItem value="Courier New, monospace">
                              Courier New
                            </SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      {/* Font Size */}
                      <div>
                        <Label
                          htmlFor="fontSize"
                          className="text-sm font-medium text-green-800"
                        >
                          Font Size
                        </Label>
                        <Select
                          value={settings.fontSize.toString()}
                          onValueChange={(value) =>
                            setSettings({
                              ...settings,
                              fontSize: parseInt(value),
                            })
                          }
                        >
                          <SelectTrigger className="mt-2 bg-white border-green-200 focus:border-green-500">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="12">12px</SelectItem>
                            <SelectItem value="14">14px</SelectItem>
                            <SelectItem value="16">16px</SelectItem>
                            <SelectItem value="18">18px</SelectItem>
                            <SelectItem value="20">20px</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      {/* Text Color */}
                      <div>
                        <Label
                          htmlFor="textColor"
                          className="text-sm font-medium text-green-800"
                        >
                          Text Color
                        </Label>
                        <div className="flex items-center gap-3 mt-2">
                          <Input
                            id="textColor"
                            type="color"
                            value={settings.textColor}
                            onChange={(e) =>
                              setSettings({
                                ...settings,
                                textColor: e.target.value,
                              })
                            }
                            className="w-16 h-10 p-1 border-2 border-green-200 rounded-lg"
                          />
                          <Input
                            value={settings.textColor}
                            onChange={(e) =>
                              setSettings({
                                ...settings,
                                textColor: e.target.value,
                              })
                            }
                            className="flex-1 bg-white border-green-200 focus:border-green-500"
                            placeholder="#333333"
                          />
                        </div>
                      </div>
                    </div>

                    {/* Interactive Elements */}
                    <div className="bg-gradient-to-br from-purple-50 to-purple-100 rounded-xl p-6 space-y-4">
                      <h4 className="text-lg font-semibold text-purple-900 mb-4">
                        🎨 Interactive Elements
                      </h4>

                      {/* Link Color */}
                      <div>
                        <Label
                          htmlFor="linkColor"
                          className="text-sm font-medium text-purple-800"
                        >
                          Link Color
                        </Label>
                        <div className="flex items-center gap-3 mt-2">
                          <Input
                            id="linkColor"
                            type="color"
                            value={settings.linkColor}
                            onChange={(e) =>
                              setSettings({
                                ...settings,
                                linkColor: e.target.value,
                              })
                            }
                            className="w-16 h-10 p-1 border-2 border-purple-200 rounded-lg"
                          />
                          <Input
                            value={settings.linkColor}
                            onChange={(e) =>
                              setSettings({
                                ...settings,
                                linkColor: e.target.value,
                              })
                            }
                            className="flex-1 bg-white border-purple-200 focus:border-purple-500"
                            placeholder="#0066cc"
                          />
                        </div>
                      </div>

                      {/* Button Color */}
                      <div>
                        <Label
                          htmlFor="buttonColor"
                          className="text-sm font-medium text-purple-800"
                        >
                          Button Color
                        </Label>
                        <div className="flex items-center gap-3 mt-2">
                          <Input
                            id="buttonColor"
                            type="color"
                            value={settings.buttonColor}
                            onChange={(e) =>
                              setSettings({
                                ...settings,
                                buttonColor: e.target.value,
                              })
                            }
                            className="w-16 h-10 p-1 border-2 border-purple-200 rounded-lg"
                          />
                          <Input
                            value={settings.buttonColor}
                            onChange={(e) =>
                              setSettings({
                                ...settings,
                                buttonColor: e.target.value,
                              })
                            }
                            className="flex-1 bg-white border-purple-200 focus:border-purple-500"
                            placeholder="#ff6200"
                          />
                        </div>
                      </div>

                      {/* Button Text Color */}
                      <div>
                        <Label
                          htmlFor="buttonTextColor"
                          className="text-sm font-medium text-purple-800"
                        >
                          Button Text Color
                        </Label>
                        <div className="flex items-center gap-3 mt-2">
                          <Input
                            id="buttonTextColor"
                            type="color"
                            value={settings.buttonTextColor}
                            onChange={(e) =>
                              setSettings({
                                ...settings,
                                buttonTextColor: e.target.value,
                              })
                            }
                            className="w-16 h-10 p-1 border-2 border-purple-200 rounded-lg"
                          />
                          <Input
                            value={settings.buttonTextColor}
                            onChange={(e) =>
                              setSettings({
                                ...settings,
                                buttonTextColor: e.target.value,
                              })
                            }
                            className="flex-1 bg-white border-purple-200 focus:border-purple-500"
                            placeholder="#ffffff"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="variables" className="p-8">
                <div className="space-y-6">
                  <div className="text-center">
                    <h3 className="text-2xl font-bold text-gray-900 mb-3">
                      🔧 Template Variables
                    </h3>
                    <p className="text-gray-600 max-w-2xl mx-auto">
                      Use these variables in your template to personalize emails
                      for each recipient. Click any variable to copy it.
                    </p>
                  </div>

                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    {/* Common Variables */}
                    <div className="bg-gradient-to-br from-orange-50 to-orange-100 rounded-xl p-6">
                      <h4 className="text-lg font-semibold text-orange-900 mb-4">
                        📋 Common Variables
                      </h4>
                      <div className="space-y-3">
                        {commonVariables.map((variable) => (
                          <div
                            key={variable.name}
                            className="p-4 bg-white rounded-lg border border-orange-200 cursor-pointer hover:border-orange-400 hover:shadow-md transition-all duration-200 group"
                            onClick={() => {
                              navigator.clipboard.writeText(
                                `{{${variable.name}}}`
                              );
                              toast.success(
                                `Copied {{${variable.name}}} to clipboard`
                              );
                            }}
                          >
                            <div className="font-mono text-base font-bold text-orange-700 group-hover:text-orange-600">
                              {`{{${variable.name}}}`}
                            </div>
                            <div className="text-sm text-orange-600 mt-1">
                              {variable.description}
                            </div>
                            <div className="text-xs text-orange-500 mt-2 bg-orange-50 px-2 py-1 rounded">
                              Example: {variable.defaultValue}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Custom Variable Input */}
                    <div className="bg-gradient-to-br from-teal-50 to-teal-100 rounded-xl p-6">
                      <h4 className="text-lg font-semibold text-teal-900 mb-4">
                        ➕ Add Custom Variable
                      </h4>
                      <div className="space-y-4">
                        <div>
                          <Label
                            htmlFor="customVar"
                            className="text-sm font-medium text-teal-800"
                          >
                            Variable Name
                          </Label>
                          <Input
                            id="customVar"
                            placeholder="e.g., companyName, discount, productName"
                            value={customVariableInput}
                            onChange={(e) =>
                              setCustomVariableInput(e.target.value)
                            }
                            className="mt-2 bg-white border-teal-200 focus:border-teal-500"
                          />
                        </div>
                        <Button
                          onClick={() => {
                            if (customVariableInput.trim()) {
                              navigator.clipboard.writeText(
                                `{{${customVariableInput.trim()}}}`
                              );
                              toast.success(
                                `Copied {{${customVariableInput.trim()}}} to clipboard`
                              );
                              setCustomVariableInput("");
                            }
                          }}
                          disabled={!customVariableInput.trim()}
                          className="w-full bg-teal-600 hover:bg-teal-700 text-white font-semibold py-3"
                        >
                          <Copy className="h-4 w-4 mr-2" />
                          Copy Variable
                        </Button>

                        <div className="bg-teal-50 border border-teal-200 rounded-lg p-4">
                          <p className="text-sm text-teal-700">
                            💡 <strong>Tip:</strong> Variables should be
                            lowercase with no spaces. Use camelCase or
                            snake_case for multi-word variables.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </div>

          {/* Editor Section - Full width below tabs */}
          <div className="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
            {/* Editor Toolbar */}
            <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-gradient-to-r from-gray-50 to-gray-100">
              <div className="flex items-center gap-2">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={handleUndo}
                        disabled={historyIndex <= 0}
                        className="h-9 w-9 hover:bg-gray-200"
                      >
                        <Undo className="h-4 w-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Undo</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={handleRedo}
                        disabled={historyIndex >= history.length - 1}
                        className="h-9 w-9 hover:bg-gray-200"
                      >
                        <Redo className="h-4 w-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Redo</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>

              <div className="flex items-center gap-3">
                <span className="text-sm font-medium text-gray-600">
                  📝 Visual Editor
                </span>
                <div className="h-4 w-px bg-gray-300"></div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handlePreview}
                  className="bg-white border-gray-300 hover:bg-gray-50"
                >
                  <Eye className="h-4 w-4 mr-2" />
                  Preview
                </Button>
                <Button
                  variant="default"
                  size="sm"
                  onClick={handleSave}
                  className="bg-blue-600 hover:bg-blue-700 text-white"
                >
                  <Save className="h-4 w-4 mr-2" />
                  Save
                </Button>
              </div>
            </div>

            {/* Editor Content */}
            <div className="p-8 bg-gray-50">
              <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
                <HtmlEditor
                  value={html}
                  onChange={setHtml}
                  className="min-h-[600px] p-6"
                  placeholder="Start designing your email template..."
                />
              </div>
            </div>
          </div>
        </div>

        {/* Preview Dialog */}
        <Dialog open={showPreview} onOpenChange={setShowPreview}>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-auto">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <Eye className="h-5 w-5" />
                Email Preview
              </DialogTitle>
              <DialogDescription>
                Preview of your email template with sample data
              </DialogDescription>
            </DialogHeader>

            <div className="bg-gray-50 p-6 rounded-lg border">
              <div
                dangerouslySetInnerHTML={{ __html: previewHtml }}
                className="prose prose-sm max-w-none"
              />
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={() => setShowPreview(false)}>
                <X className="h-4 w-4 mr-2" />
                Close Preview
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
}
