import { Timestamp } from "firebase/firestore";

export interface OrderItem {
  id: string;
  menuItemId: string;
  name: string;
  price: number;
  quantity: number;
  notes?: string;
  options?: Record<string, string | boolean | number>;
  category?: string; // Added for compatibility with RecommendationService
}

export interface Order {
  id: string;
  restaurantId: string;
  userId: string;
  clientId?: string; // Added for compatibility with RecommendationService
  items: OrderItem[];
  status:
    | "pending"
    | "confirmed"
    | "preparing"
    | "ready"
    | "completed"
    | "cancelled";
  total: number;
  createdAt: Timestamp;
  updatedAt: Timestamp;
  paymentMethod?: string;
  paymentStatus?: "pending" | "paid" | "failed";
  deliveryAddress?: string;
  deliveryMethod?: "pickup" | "delivery";
  deliveryStatus?: "pending" | "in-transit" | "delivered";
  estimatedDeliveryTime?: Timestamp;
  notes?: string;
  // Scheduling fields
  isScheduled?: boolean;
  scheduledFor?: Timestamp;
  reminderSent?: boolean;
  // Coupon/Discount fields
  originalAmount?: number;
  discountAmount?: number;
  appliedCouponCode?: string;
  appliedRewardCode?: string;
  affectedItemId?: string; // ID of the specific item affected by discount/reward
  couponUsed?: boolean;
}
