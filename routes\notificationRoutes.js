/**
 * Notification routes
 *
 * Handles email notifications and subscriber management
 */
const express = require("express");
const router = express.Router();
const notificationController = require("../controllers/notificationController");
const newsletterController = require("../controllers/newsletterController");
const { validateBody, schemas } = require("../middlewares/validators");
const { verifyApiKey } = require("../middlewares/authMiddleware");
const {
  emailLimiter,
  apiLimiter,
} = require("../middlewares/rateLimitMiddleware");
const logger = require("../utils/logger");

/**
 * @route POST /send-notification-email
 * @desc Send notification email
 * @access Private (API key required)
 */
router.post(
  "/send-notification-email",
  emailLimiter, // Rate limit email sending
  verifyApiKey, // Require API key
  validateBody(schemas.notificationEmailSchema), // Validate request body
  (req, res, next) => {
    // Log email request
    logger.info(`Email notification request to: ${req.body.to}`, {
      type: req.body.type || "custom",
      clientId: req.apiKey?.clientId || "unknown",
    });
    next();
  },
  notificationController.sendNotificationEmail
);

/**
 * @route GET /subscribers
 * @desc Get all subscribers
 * @access Private (API key required)
 */
router.get(
  "/subscribers",
  apiLimiter, // Rate limit API requests
  verifyApiKey, // Require API key
  (req, res, next) => {
    // Log subscriber list request
    logger.info("Subscriber list requested", {
      clientId: req.apiKey?.clientId || "unknown",
      query: req.query,
    });
    next();
  },
  newsletterController.listSubscribers
);

// Legacy send newsletter endpoint
router.post("/send-newsletter", async (req, res) => {
  try {
    const { subject, content, testEmail } = req.body;
    const { transporter } = require("../config/nodemailer");
    const { db } = require("../config/firebase");

    if (!subject || !content) {
      return res
        .status(400)
        .json({ error: "Subject and content are required" });
    }

    // Test email
    if (testEmail) {
      const mailOptions = {
        from: process.env.EMAIL_USER,
        to: testEmail,
        subject: subject,
        html: content,
      };
      await transporter.sendMail(mailOptions);
      return res.json({ message: "Test email sent successfully" });
    }

    // Get subscribers - check if we received a list of recipients from the frontend
    let subscribers = [];

    if (req.body.recipients && req.body.recipients.length > 0) {
      // Use the provided recipients list
      const emails = req.body.recipients;

      // Get subscriber details for these emails
      const subscribersSnapshot = await db.collection("subscribers").get();
      const allSubscribers = subscribersSnapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
      }));

      // Match emails with subscriber records
      subscribers = emails.map((email) => {
        const subscriber = allSubscribers.find((s) => s.email === email);
        return (
          subscriber || {
            email,
            name: "there",
            unsubscribeToken: "token-placeholder",
          }
        );
      });

      console.log(
        `Using ${subscribers.length} subscribers from request payload`
      );
    } else {
      // Fall back to querying active subscribers
      const snapshot = await db
        .collection("subscribers")
        .where("subscribed", "==", true)
        .get();

      if (snapshot.empty) {
        return res.status(404).json({ error: "No active subscribers found" });
      }

      subscribers = snapshot.docs.map((doc) => doc.data());
      console.log(`Found ${subscribers.length} active subscribers in database`);
    }

    // Ensure we have subscribers to send to
    if (subscribers.length === 0) {
      return res.status(404).json({ error: "No subscribers to send to" });
    }

    // Send emails
    const emailPromises = subscribers.map((subscriber) => {
      const unsubscribeLink = `https://api.qonai.me/unsubscribe?email=${encodeURIComponent(
        subscriber.email
      )}&token=${subscriber.unsubscribeToken}`;

      const personalizedContent = content
        .replace("{{name}}", subscriber.name || "there")
        .replace("{{unsubscribeLink}}", unsubscribeLink);

      const mailOptions = {
        from: process.env.EMAIL_USER,
        to: subscriber.email,
        subject: subject,
        html: personalizedContent,
      };

      return transporter.sendMail(mailOptions);
    });

    await Promise.all(emailPromises);

    // Log newsletter
    await db.collection("newsletters").add({
      subject,
      content,
      sentAt: new Date(),
      recipientCount: subscribers.length,
    });

    res.json({
      message: "Newsletter sent successfully",
      recipientCount: subscribers.length,
    });
  } catch (error) {
    console.error("Send newsletter error:", error);
    res.status(500).json({
      error: "Failed to send newsletter",
      details: error.message,
    });
  }
});

module.exports = router;
