// Offline Queue Service for handling actions when offline

import {
  addToOfflineQueue,
  getPendingOfflineActions,
  updateOfflineActionStatus,
  deleteData,
  STORES
} from '@/utils/indexedDB';

// Action types
export const OFFLINE_ACTIONS = {
  PLACE_ORDER: 'PLACE_ORDER',
  UPDATE_PROFILE: 'UPDATE_PROFILE',
  ADD_REVIEW: 'ADD_REVIEW',
  FOLLOW_RESTAURANT: 'FOLLOW_RESTAURANT',
  UNFOLLOW_RESTAURANT: 'UNFOLLOW_RESTAURANT',
};

// Check if the device is online
export const isOnline = (): boolean => {
  return navigator.onLine;
};

// Add a listener for online/offline events
export const addConnectivityListeners = (
  onlineCallback: () => void,
  offlineCallback: () => void
): void => {
  window.addEventListener('online', onlineCallback);
  window.addEventListener('offline', offlineCallback);
};

// Remove the listeners
export const removeConnectivityListeners = (
  onlineCallback: () => void,
  offlineCallback: () => void
): void => {
  window.removeEventListener('online', onlineCallback);
  window.removeEventListener('offline', offlineCallback);
};

// Queue an action to be performed when back online
export const queueAction = async (
  type: string,
  payload: unknown,
  endpoint: string,
  method: string = 'POST'
): Promise<IDBValidKey> => {
  const action = {
    type,
    payload,
    endpoint,
    method,
    timestamp: Date.now(),
    status: 'pending' as const,
  };

  return addToOfflineQueue(action);
};

// Process all pending actions in the queue
export const processOfflineQueue = async (): Promise<void> => {
  if (!isOnline()) {
    return;
  }

  const pendingActions = await getPendingOfflineActions();

  if (pendingActions.length === 0) {
    return;
  }

  // Sort actions by timestamp to process in order
  const sortedActions = pendingActions.sort((a, b) => a.timestamp - b.timestamp);

  for (const action of sortedActions) {
    try {
      // Mark action as processing
      if (action.id !== undefined) {
        await updateOfflineActionStatus(action.id, 'processing');
      }

      // Perform the action
      const response = await fetch(action.endpoint, {
        method: action.method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(action.payload),
      });

      if (!response.ok) {
        throw new Error(`Server responded with ${response.status}`);
      }

      // Mark action as completed
      if (action.id !== undefined) {
        await updateOfflineActionStatus(action.id, 'completed');

        // Remove completed action after a delay to avoid cluttering the queue
        setTimeout(() => {
          deleteData(STORES.OFFLINE_QUEUE, action.id!).catch(console.error);
        }, 60000); // Remove after 1 minute
      }

    } catch (error) {
      console.error(`Error processing offline action ${action.type}:`, error);

      // Mark action as failed
      if (action.id !== undefined) {
        await updateOfflineActionStatus(action.id, 'failed');
      }
    }
  }
};

// Retry failed actions
export const retryFailedActions = async (): Promise<void> => {
  const db = await (indexedDB.open('SalexOfflineDB'));

  db.onsuccess = async () => {
    const database = db.result;
    const transaction = database.transaction(STORES.OFFLINE_QUEUE, 'readwrite');
    const store = transaction.objectStore(STORES.OFFLINE_QUEUE);
    const index = store.index('status');
    const request = index.getAll('failed');

    request.onsuccess = async () => {
      const failedActions = request.result;

      for (const action of failedActions) {
        // Reset status to pending
        action.status = 'pending';
        // Update timestamp to current time to move it to the end of the queue
        action.timestamp = Date.now();

        store.put(action);
      }

      transaction.oncomplete = () => {
        database.close();
        // Process the queue again
        if (failedActions.length > 0) {
          processOfflineQueue();
        }
      };
    };
  };
};

// Initialize the offline queue service
export const initOfflineQueue = (): void => {
  // Process queue when coming online
  const handleOnline = () => {
    processOfflineQueue();
  };

  const handleOffline = () => {
  };

  // Add listeners
  addConnectivityListeners(handleOnline, handleOffline);

  // Check if we're online at startup and process queue if needed
  if (isOnline()) {
    processOfflineQueue();
  }
};

// Export the service
export const offlineQueueService = {
  isOnline,
  queueAction,
  processOfflineQueue,
  retryFailedActions,
  initOfflineQueue,
  getPendingOfflineActions, // Add the missing function
};
