import { Card, CardContent } from "@/components/ui/card"; // Use CardContent for padding consistency
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";
import {
  Utensils,
  Al<PERSON><PERSON>riangle,
  BookOpen,
} from "lucide-react"; // Added icons

// Interface matching expected data structure (adjust if needed)
interface MenuItem {
  id?: string; // Added optional ID
  name: string;
  description?: string;
  price: number;
  category?: string;
  imageUrl?: string;
  available?: boolean;
  dietaryInfo?: string[];
  restaurantId: string; // Keep restaurantId for navigation
  username?: string; // Add username for navigation
}

export interface MenuSection {
  title: string;
  restaurantId?: string; // restaurantId might be at section level too
  username?: string; // Add username for navigation
  items: MenuItem[];
}

interface MenuRendererProps {
  data: MenuSection[];
}

const PLACEHOLDER_FOOD_IMG = "/placeholder-food.jpg"; // Define placeholder path

export const MenuRenderer = ({ data }: MenuRendererProps) => {
  const navigate = useNavigate();

  // Fallback for image errors
  const handleImageError = (
    e: React.SyntheticEvent<HTMLImageElement, Event>
  ) => {
    e.currentTarget.src = PLACEHOLDER_FOOD_IMG;
    e.currentTarget.srcset = ""; // Clear srcset as well if it exists
  };

  if (!data || data.length === 0) {
    return (
      <p className="text-muted-foreground text-sm">
        No menu information available.
      </p>
    );
  }

  return (
    <div className="space-y-6 w-full">
      {data.map((section, idx) => (
        <div
          key={`section-${idx}-${section.restaurantId || ""}`}
          className="space-y-4"
        >
          <h3 className="font-semibold text-lg text-primary flex items-center gap-2 border-b pb-2 mb-3 text-wrap break-words">
            <Utensils className="w-5 h-5 flex-shrink-0" />
            <span>{section.title || "Menu Items"}</span>
          </h3>
          <div className="grid grid-cols-1 gap-3">
            {" "}
            {/* Reduced gap slightly */}
            {section.items?.map((item, itemIdx) => {
              // Ensure username is available, prioritize item's, fallback to section's
              const username = item.username || section.username || '';
              const restaurantId = item.restaurantId || section.restaurantId || '';
              const navId = username || restaurantId; // Prefer username for navigation
              const canNavigate = !!navId;

              return (
                <Card
                  key={`item-${itemIdx}-${item.id || item.name}`}
                  className="overflow-hidden transition-all duration-200 border-border/60 hover:border-primary/50 bg-card hover:shadow-md" // Use theme colors
                >
                  <CardContent className="p-3 flex gap-3 rounded-md">
                    {" "}
                    {/* Use CardContent for padding */}
                    {/* Image Section */}
                    <div className="w-20 h-20 md:w-24 md:h-24 rounded-md overflow-hidden flex-shrink-0 border bg-muted shadow-sm">
                      <img
                        src={item.imageUrl || PLACEHOLDER_FOOD_IMG}
                        alt={item.name || "Menu item"}
                        className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                        onError={handleImageError}
                        loading="lazy" // Add lazy loading
                      />
                    </div>
                    {/* Details Section */}
                    <div className="flex-grow flex flex-col justify-between">
                      <div>
                        <div className="flex justify-between items-start gap-2">
                          <h4 className="font-medium text-base leading-snug text-wrap break-words">
                            {item.name || "Unnamed Item"}
                            {item.available === false && ( // Explicit check for false
                              <Badge
                                variant="destructive"
                                className="ml-2 text-xs px-1.5 py-0 h-5 align-middle"
                              >
                                <AlertTriangle className="w-3 h-3 mr-1" />
                                Unavailable
                              </Badge>
                            )}
                          </h4>
                          <Badge
                            variant="secondary"
                            className="text-sm bg-primary/10 text-primary font-bold whitespace-nowrap flex-shrink-0 shadow-sm"
                          >
                            {item.price != null
                              ? `${item.price.toFixed(2)} AZN`
                              : "N/A"}
                          </Badge>
                        </div>
                        {item.description && (
                          <p className="text-xs text-muted-foreground mt-1 line-clamp-2 text-wrap break-words">
                            {item.description}
                          </p>
                        )}

                        {/* Dietary Info */}
                        {item.dietaryInfo && item.dietaryInfo.length > 0 && (
                          <div className="flex flex-wrap gap-1 mt-2">
                            {item.dietaryInfo.map((info, i) => (
                              <Badge
                                key={i}
                                variant="outline"
                                className="text-xs px-1.5 py-0 h-5 border-dashed bg-primary/5 hover:bg-primary/10 transition-colors"
                              >
                                {info}
                              </Badge>
                            ))}
                          </div>
                        )}
                      </div>

                      {/* Action Buttons */}
                      {canNavigate && (
                        <div className="flex flex-wrap gap-2 mt-3 justify-end">
                          {/* Keep only relevant buttons, e.g., view restaurant */}
                          <Button
                            onClick={() =>
                              navigate(`/restaurants/${navId}`)
                            }
                            variant="outline"
                            size="sm"
                            className="rounded-full px-3 h-8 text-xs hover:bg-primary hover:text-primary-foreground transition-colors"
                            title={`View Restaurant ${item.name || navId}`}
                          >
                            <BookOpen className="w-3.5 h-3.5 mr-1" />
                            View Restaurant
                          </Button>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>
      ))}
    </div>
  );
};
