import ReactMarkdown from "react-markdown";
import { But<PERSON> } from "@/components/ui/button";
import { CalendarRange } from "lucide-react";
import { useNavigate } from "react-router-dom";

interface MarkupRendererProps {
  content: string;
}

export const MarkupRenderer = ({ content }: MarkupRendererProps) => {
  const navigate = useNavigate();

  // Convert content to string if it's not already
  const contentStr =
    typeof content === "string" ? content : JSON.stringify(content, null, 2);

  // Check if content mentions reservations
  const showReservationButton =
    contentStr.toLowerCase().includes("reservation") ||
    contentStr.toLowerCase().includes("rezervasiya");

  return (
    <div className="prose prose-sm dark:prose-invert max-w-none break-words text-wrap">
      <ReactMarkdown
        className="text-sm"
        components={{
          // Customize heading styles
          h1: ({ ...props }) => (
            <h1 className="text-xl font-bold mt-4 mb-2" {...props} />
          ),
          h2: ({ ...props }) => (
            <h2 className="text-lg font-bold mt-3 mb-2" {...props} />
          ),
          h3: ({ ...props }) => (
            <h3 className="text-base font-bold mt-3 mb-1" {...props} />
          ),

          // Customize paragraph styles
          p: ({ ...props }) => <p className="mb-2 last:mb-0" {...props} />,

          // Customize list styles
          ul: ({ ...props }) => (
            <ul className="list-disc pl-5 my-2 space-y-1" {...props} />
          ),
          ol: ({ ...props }) => (
            <ol className="list-decimal pl-5 my-2 space-y-1" {...props} />
          ),
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
          li: ({ ordered, ...props }) => <li className="text-sm" {...props} />,

          // Customize code block styles
          code: ({ inline, ...props }) => (
            <code
              className={`${
                inline
                  ? "bg-muted px-1 py-0.5 rounded text-xs font-mono"
                  : "block bg-muted p-2 rounded-md text-xs font-mono overflow-x-auto my-2"
              }`}
              {...props}
            />
          ),

          // Customize link styles
          a: ({ ...props }) => (
            <a
              className="text-primary hover:underline"
              target="_blank"
              rel="noopener noreferrer"
              {...props}
            />
          ),

          // Customize blockquote styles
          blockquote: ({ ...props }) => (
            <blockquote
              className="border-l-4 border-primary/30 pl-4 italic my-2"
              {...props}
            />
          ),

          // Customize horizontal rule
          hr: ({ ...props }) => <hr className="my-4 border-muted" {...props} />,
        }}
      >
        {contentStr}
      </ReactMarkdown>

      {showReservationButton && (
        <div className="mt-4 flex justify-end">
          <Button
            onClick={() => navigate(`/restaurants`)}
            size="sm"
            className="rounded-full px-3 h-8 text-xs hover:bg-primary hover:text-primary-foreground transition-colors"
          >
            <CalendarRange className="w-3.5 h-3.5 mr-1" />
            Make Reservation
          </Button>
        </div>
      )}
    </div>
  );
};
