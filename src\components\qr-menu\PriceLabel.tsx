import React from "react";
import { Badge } from "@/components/ui/badge";

interface PriceLabelProps {
  price: number;
  currency?: string;
}

export const PriceLabel: React.FC<PriceLabelProps> = ({
  price,
  currency = "AZN",
}) => {
  return (
    <Badge className="bg-primary text-primary-foreground font-semibold text-base px-3 py-1.5 shadow-md rounded-lg">
      {price.toFixed(2)} {currency}
    </Badge>
  );
};
