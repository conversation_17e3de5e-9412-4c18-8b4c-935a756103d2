/**
 * Main router
 */
const express = require("express");
const router = express.Router();
const newsletterRoutes = require("./newsletterRoutes");
const notificationRoutes = require("./notificationRoutes");
const bulkEmailRoutes = require("./bulkEmailRoutes");
const {
  validateBody,
  validateQuery,
  schemas,
} = require("../middlewares/validators");
const { verifyApiKey } = require("../middlewares/authMiddleware");
const newsletterController = require("../controllers/newsletterController");
const notificationController = require("../controllers/notificationController");
const logger = require("../utils/logger");

// API health check
router.get("/health", (req, res) => {
  res.status(200).json({ status: "ok", message: "API is running" });
});

// Test email endpoint
router.get("/test-email", async (req, res) => {
  try {
    const { transporter } = require("../config/nodemailer");

    // Send a test email
    const info = await transporter.sendMail({
      from: process.env.EMAIL_USER,
      to: process.env.EMAIL_USER, // Send to yourself
      subject: "Test Email from Newsletter API",
      text: "This is a test email to verify the email sending functionality.",
      html: "<p>This is a test email to verify the email sending functionality.</p>",
    });

    res.status(200).json({
      status: "success",
      message: "Test email sent successfully",
      messageId: info.messageId,
      response: info.response,
    });
  } catch (error) {
    console.error("Test email error:", error);
    res.status(500).json({
      status: "error",
      message: "Failed to send test email",
      error: error.message,
      code: error.code,
      response: error.response,
    });
  }
});

// Email queue monitoring endpoint
router.get("/email/queue-stats", verifyApiKey, async (req, res) => {
  try {
    const emailQueue = require("../services/emailQueue");
    const stats = await emailQueue.getQueueStats();

    res.status(200).json({
      status: "success",
      data: stats,
    });
  } catch (error) {
    logger.error("Error getting queue stats:", { error: error.message });
    res.status(500).json({
      status: "error",
      message: "Failed to get queue statistics",
      error: error.message,
    });
  }
});

// Mount routes with new paths
router.use("/newsletter", newsletterRoutes);
router.use("/bulk-email", bulkEmailRoutes);
router.use("/", notificationRoutes);

// Legacy routes for backward compatibility
router.post(
  "/subscribe",
  validateBody(schemas.subscribeSchema),
  newsletterController.subscribe
);
router.post(
  "/verify-subscription",
  validateBody(schemas.verifyOtpSchema),
  newsletterController.verifySubscription
);
router.get(
  "/unsubscribe",
  validateQuery(schemas.unsubscribeSchema),
  newsletterController.unsubscribe
);
router.post(
  "/send-notification-email",
  validateBody(schemas.notificationEmailSchema),
  notificationController.sendNotificationEmail
);
// Add legacy route for subscribers
router.get("/api/subscribers", newsletterController.listSubscribers);

// Redirect /newsletter to frontend
router.get("/newsletter", (req, res) => {
  res.redirect("https://qonai.me/subscribe");
});

module.exports = router;
