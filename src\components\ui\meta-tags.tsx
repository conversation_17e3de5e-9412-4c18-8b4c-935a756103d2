import { Helmet } from "react-helmet-async";

export interface MetaTagsProps {
  title: string;
  description: string;
  imageUrl?: string;
  url?: string;
  type?: "website" | "article" | "restaurant" | "food" | "product";
}

export const MetaTags = ({
  title,
  description,
  imageUrl,
  url = window.location.href,
  type = "website",
}: MetaTagsProps) => {
  const siteName = "Qonai Food Ordering";
  const defaultImage = `${window.location.origin}/og-image.jpg`;

  return (
    <Helmet>
      {/* Primary Meta Tags */}
      <title>{title}</title>
      <meta name="title" content={title} />
      <meta name="description" content={description} />

      {/* Open Graph / Facebook */}
      <meta property="og:type" content={type} />
      <meta property="og:url" content={url} />
      <meta property="og:title" content={title} />
      <meta property="og:description" content={description} />
      <meta property="og:image" content={imageUrl || defaultImage} />
      <meta property="og:site_name" content={siteName} />

      {/* Twitter */}
      <meta property="twitter:card" content="summary_large_image" />
      <meta property="twitter:url" content={url} />
      <meta property="twitter:title" content={title} />
      <meta property="twitter:description" content={description} />
      <meta property="twitter:image" content={imageUrl || defaultImage} />
    </Helmet>
  );
};
