import { useState } from 'react';

interface UseLoadingReturn {
	isLoading: boolean;
	startLoading: () => void;
	stopLoading: () => void;
}

export const useLoading = (initialState = false): UseLoadingReturn => {
	const [isLoading, setIsLoading] = useState(initialState);

	const startLoading = () => setIsLoading(true);
	const stopLoading = () => setIsLoading(false);

	return { isLoading, startLoading, stopLoading };
};