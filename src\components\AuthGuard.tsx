import { useAuth } from "@/providers/AuthProvider";
import { useLocation, useNavigate } from "react-router-dom";
import { useEffect, ReactNode, useState } from "react";
import { Loading } from "@/components/ui/loading";

interface AuthGuardProps {
  children: ReactNode;
}

// This component prevents logged-in users from accessing auth pages
export const AuthGuard = ({ children }: AuthGuardProps) => {
  const { user, loading } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const [redirecting, setRedirecting] = useState(false);

  useEffect(() => {
    // Only redirect if user is logged in and we're not already redirecting
    if (user && !loading && !redirecting) {
      setRedirecting(true);
      const from = location.state?.from?.pathname || "/";
      // Use a small timeout to ensure the redirect happens after render
      setTimeout(() => {
        navigate(from, { replace: true });
      }, 0);
    }
  }, [user, loading, navigate, location, redirecting]);

  // Show loading states
  if (loading) {
    return <Loading text="Checking authentication..." />;
  }

  if (redirecting || user) {
    return <Loading text="Redirecting..." />;
  }

  // Only render children if user is not logged in
  return <>{children}</>;
};
