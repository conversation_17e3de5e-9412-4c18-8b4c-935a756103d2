import { useState, useEffect } from "react";
import { HtmlEditor } from "./html-editor";
import { toast } from "sonner";
import { TemplateBlocksGrid, TemplateBlock } from "./email-template-blocks";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Eye, Save, Undo, Redo, Co<PERSON>, X } from "lucide-react";

// Define email template settings interface
interface EmailTemplateSettings {
  backgroundColor: string;
  contentWidth: number;
  fontFamily: string;
  fontSize: number;
  textColor: string;
  linkColor: string;
  buttonColor: string;
  buttonTextColor: string;
}

// Default email template settings
const defaultSettings: EmailTemplateSettings = {
  backgroundColor: "#f9f9f9",
  contentWidth: 600,
  fontFamily: "Arial, sans-serif",
  fontSize: 16,
  textColor: "#333333",
  linkColor: "#0066cc",
  buttonColor: "#ff6200",
  buttonTextColor: "#ffffff",
};

// Email template variables interface
interface EmailTemplateVariable {
  name: string;
  description: string;
  defaultValue: string;
}

// Common email template variables
const commonVariables: EmailTemplateVariable[] = [
  {
    name: "name",
    description: "Recipient's name",
    defaultValue: "John Doe",
  },
  {
    name: "unsubscribeLink",
    description: "Link to unsubscribe from emails",
    defaultValue: "#",
  },
  {
    name: "currentDate",
    description: "Current date",
    defaultValue: new Date().toLocaleDateString(),
  },
];

// Email Template Editor Props
interface EmailTemplateEditorProps {
  initialHtml?: string;
  onSave?: (html: string, variables: string[]) => void;
  onPreview?: (html: string) => void;
  className?: string;
}

export function EmailTemplateEditor({
  initialHtml = "",
  onSave,
  onPreview,
  className,
}: EmailTemplateEditorProps) {
  const [html, setHtml] = useState(initialHtml);
  const [settings, setSettings] =
    useState<EmailTemplateSettings>(defaultSettings);
  const [variables] = useState<EmailTemplateVariable[]>(commonVariables);
  const [customVariables] = useState<EmailTemplateVariable[]>([]);
  const [history, setHistory] = useState<string[]>([initialHtml]);
  const [historyIndex, setHistoryIndex] = useState(0);
  const [showPreview, setShowPreview] = useState(false);
  const [previewHtml, setPreviewHtml] = useState("");
  const [showInlinePreview, setShowInlinePreview] = useState(false);
  const [customVariableInput, setCustomVariableInput] = useState("");

  // Add to history when HTML changes
  useEffect(() => {
    if (html !== history[historyIndex]) {
      const newHistory = history.slice(0, historyIndex + 1);
      newHistory.push(html);
      setHistory(newHistory);
      setHistoryIndex(newHistory.length - 1);
    }
  }, [html, history, historyIndex]);

  // Handle undo
  const handleUndo = () => {
    if (historyIndex > 0) {
      setHistoryIndex(historyIndex - 1);
      setHtml(history[historyIndex - 1]);
    }
  };

  // Handle redo
  const handleRedo = () => {
    if (historyIndex < history.length - 1) {
      setHistoryIndex(historyIndex + 1);
      setHtml(history[historyIndex + 1]);
    }
  };

  // Handle adding a template block
  const handleAddBlock = (block: TemplateBlock) => {
    setHtml((prevHtml) => prevHtml + block.html);
  };

  // Handle preview
  const handlePreview = () => {
    // Replace variables with their default values for preview
    let previewContent = html;

    [...variables, ...customVariables].forEach((variable) => {
      const regex = new RegExp(`\\{\\{${variable.name}\\}\\}`, "g");
      previewContent = previewContent.replace(regex, variable.defaultValue);
    });

    setPreviewHtml(previewContent);
    setShowPreview(true);

    if (onPreview) {
      onPreview(previewContent);
    }

    // Show toast notification
    toast.success("Preview Generated", {
      description: "Email preview has been generated with sample data.",
      duration: 2000,
    });
  };

  // Handle save
  const handleSave = () => {
    if (onSave) {
      // Extract all variables from the HTML
      const variableMatches = html.match(/\{\{([^}]+)\}\}/g) || [];
      const extractedVariables = variableMatches.map((match) =>
        match.replace(/\{\{|\}\}/g, "")
      );

      // Remove duplicates
      const uniqueVariables = [...new Set(extractedVariables)];

      onSave(html, uniqueVariables);
    }
  };

  return (
    <div className={`${className} bg-gray-50 min-h-screen`}>
      <div className="max-w-7xl mx-auto p-6">
        {/* Header Section */}
        <div className="mb-6">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-4">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                Email Template Editor
              </h1>
              <p className="text-sm text-gray-600 mt-1">
                Create and customize your email templates
              </p>
            </div>

            <div className="flex items-center gap-3">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowInlinePreview(!showInlinePreview)}
                className="bg-white border-gray-300 hover:bg-gray-50"
              >
                <Eye className="h-4 w-4 mr-2" />
                {showInlinePreview ? "Hide Preview" : "Show Preview"}
              </Button>

              <Button
                variant="outline"
                size="sm"
                onClick={handlePreview}
                className="bg-white border-gray-300 hover:bg-gray-50"
              >
                <Eye className="h-4 w-4 mr-2" />
                Preview Dialog
              </Button>

              <Button
                variant="default"
                size="sm"
                onClick={handleSave}
                className="bg-blue-600 hover:bg-blue-700 text-white"
              >
                <Save className="h-4 w-4 mr-2" />
                Save Template
              </Button>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="space-y-6">
          {/* Tabs Section - Moved to top */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <Tabs defaultValue="blocks" className="w-full">
              <TabsList className="grid w-full grid-cols-3 bg-gray-50 rounded-t-lg h-12">
                <TabsTrigger
                  value="blocks"
                  className="data-[state=active]:bg-white data-[state=active]:shadow-sm font-medium"
                >
                  Content Blocks
                </TabsTrigger>
                <TabsTrigger
                  value="settings"
                  className="data-[state=active]:bg-white data-[state=active]:shadow-sm font-medium"
                >
                  Settings
                </TabsTrigger>
                <TabsTrigger
                  value="variables"
                  className="data-[state=active]:bg-white data-[state=active]:shadow-sm font-medium"
                >
                  Variables
                </TabsTrigger>
              </TabsList>

              <TabsContent value="blocks" className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-6 gap-4">
                  <div className="col-span-full mb-4">
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">
                      Content Blocks
                    </h3>
                    <p className="text-sm text-gray-600">
                      Click on any block to add it to your template. Blocks will
                      be added at the end of your content.
                    </p>
                  </div>

                  <div className="col-span-full">
                    <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-8 gap-3">
                      <TemplateBlocksGrid onSelectBlock={handleAddBlock} />
                    </div>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="settings" className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                  <div className="col-span-full mb-4">
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">
                      Template Settings
                    </h3>
                    <p className="text-sm text-gray-600">
                      Customize the overall appearance and styling of your email
                      template.
                    </p>
                  </div>

                  {/* Background Color */}
                  <div>
                    <Label
                      htmlFor="backgroundColor"
                      className="text-sm font-medium"
                    >
                      Background Color
                    </Label>
                    <div className="flex items-center gap-2 mt-1">
                      <Input
                        id="backgroundColor"
                        type="color"
                        value={settings.backgroundColor}
                        onChange={(e) =>
                          setSettings({
                            ...settings,
                            backgroundColor: e.target.value,
                          })
                        }
                        className="w-12 h-8 p-1 border rounded"
                      />
                      <Input
                        value={settings.backgroundColor}
                        onChange={(e) =>
                          setSettings({
                            ...settings,
                            backgroundColor: e.target.value,
                          })
                        }
                        className="text-xs"
                        placeholder="#f9f9f9"
                      />
                    </div>
                  </div>

                  {/* Content Width */}
                  <div>
                    <Label
                      htmlFor="contentWidth"
                      className="text-sm font-medium"
                    >
                      Content Width
                    </Label>
                    <Select
                      value={settings.contentWidth.toString()}
                      onValueChange={(value) =>
                        setSettings({
                          ...settings,
                          contentWidth: parseInt(value),
                        })
                      }
                    >
                      <SelectTrigger className="mt-1">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="480">480px (Mobile)</SelectItem>
                        <SelectItem value="600">600px (Standard)</SelectItem>
                        <SelectItem value="800">800px (Wide)</SelectItem>
                        <SelectItem value="1000">1000px (Full)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Font Family */}
                  <div>
                    <Label htmlFor="fontFamily" className="text-sm font-medium">
                      Font Family
                    </Label>
                    <Select
                      value={settings.fontFamily}
                      onValueChange={(value) =>
                        setSettings({ ...settings, fontFamily: value })
                      }
                    >
                      <SelectTrigger className="mt-1">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Arial, sans-serif">Arial</SelectItem>
                        <SelectItem value="Helvetica, sans-serif">
                          Helvetica
                        </SelectItem>
                        <SelectItem value="Georgia, serif">Georgia</SelectItem>
                        <SelectItem value="Times New Roman, serif">
                          Times New Roman
                        </SelectItem>
                        <SelectItem value="Courier New, monospace">
                          Courier New
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Font Size */}
                  <div>
                    <Label htmlFor="fontSize" className="text-sm font-medium">
                      Font Size
                    </Label>
                    <Select
                      value={settings.fontSize.toString()}
                      onValueChange={(value) =>
                        setSettings({ ...settings, fontSize: parseInt(value) })
                      }
                    >
                      <SelectTrigger className="mt-1">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="12">12px</SelectItem>
                        <SelectItem value="14">14px</SelectItem>
                        <SelectItem value="16">16px</SelectItem>
                        <SelectItem value="18">18px</SelectItem>
                        <SelectItem value="20">20px</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Text Color */}
                  <div>
                    <Label htmlFor="textColor" className="text-sm font-medium">
                      Text Color
                    </Label>
                    <div className="flex items-center gap-2 mt-1">
                      <Input
                        id="textColor"
                        type="color"
                        value={settings.textColor}
                        onChange={(e) =>
                          setSettings({
                            ...settings,
                            textColor: e.target.value,
                          })
                        }
                        className="w-12 h-8 p-1 border rounded"
                      />
                      <Input
                        value={settings.textColor}
                        onChange={(e) =>
                          setSettings({
                            ...settings,
                            textColor: e.target.value,
                          })
                        }
                        className="text-xs"
                        placeholder="#333333"
                      />
                    </div>
                  </div>

                  {/* Link Color */}
                  <div>
                    <Label htmlFor="linkColor" className="text-sm font-medium">
                      Link Color
                    </Label>
                    <div className="flex items-center gap-2 mt-1">
                      <Input
                        id="linkColor"
                        type="color"
                        value={settings.linkColor}
                        onChange={(e) =>
                          setSettings({
                            ...settings,
                            linkColor: e.target.value,
                          })
                        }
                        className="w-12 h-8 p-1 border rounded"
                      />
                      <Input
                        value={settings.linkColor}
                        onChange={(e) =>
                          setSettings({
                            ...settings,
                            linkColor: e.target.value,
                          })
                        }
                        className="text-xs"
                        placeholder="#0066cc"
                      />
                    </div>
                  </div>

                  {/* Button Color */}
                  <div>
                    <Label
                      htmlFor="buttonColor"
                      className="text-sm font-medium"
                    >
                      Button Color
                    </Label>
                    <div className="flex items-center gap-2 mt-1">
                      <Input
                        id="buttonColor"
                        type="color"
                        value={settings.buttonColor}
                        onChange={(e) =>
                          setSettings({
                            ...settings,
                            buttonColor: e.target.value,
                          })
                        }
                        className="w-12 h-8 p-1 border rounded"
                      />
                      <Input
                        value={settings.buttonColor}
                        onChange={(e) =>
                          setSettings({
                            ...settings,
                            buttonColor: e.target.value,
                          })
                        }
                        className="text-xs"
                        placeholder="#ff6200"
                      />
                    </div>
                  </div>

                  {/* Button Text Color */}
                  <div>
                    <Label
                      htmlFor="buttonTextColor"
                      className="text-sm font-medium"
                    >
                      Button Text Color
                    </Label>
                    <div className="flex items-center gap-2 mt-1">
                      <Input
                        id="buttonTextColor"
                        type="color"
                        value={settings.buttonTextColor}
                        onChange={(e) =>
                          setSettings({
                            ...settings,
                            buttonTextColor: e.target.value,
                          })
                        }
                        className="w-12 h-8 p-1 border rounded"
                      />
                      <Input
                        value={settings.buttonTextColor}
                        onChange={(e) =>
                          setSettings({
                            ...settings,
                            buttonTextColor: e.target.value,
                          })
                        }
                        className="text-xs"
                        placeholder="#ffffff"
                      />
                    </div>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="variables" className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <div className="col-span-full mb-4">
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">
                      Template Variables
                    </h3>
                    <p className="text-sm text-gray-600">
                      Use these variables in your template to personalize emails
                      for each recipient.
                    </p>
                  </div>

                  {/* Common Variables */}
                  <div className="col-span-full">
                    <h4 className="font-medium text-gray-900 mb-3">
                      Common Variables
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                      {commonVariables.map((variable) => (
                        <div
                          key={variable.name}
                          className="p-3 bg-gray-50 rounded-lg border border-gray-200 cursor-pointer hover:bg-gray-100 transition-colors"
                          onClick={() => {
                            navigator.clipboard.writeText(
                              `{{${variable.name}}}`
                            );
                            toast.success(
                              `Copied {{${variable.name}}} to clipboard`
                            );
                          }}
                        >
                          <div className="font-mono text-sm font-medium text-blue-600">
                            {`{{${variable.name}}}`}
                          </div>
                          <div className="text-xs text-gray-600 mt-1">
                            {variable.description}
                          </div>
                          <div className="text-xs text-gray-500 mt-1">
                            Example: {variable.defaultValue}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Custom Variable Input */}
                  <div className="col-span-full mt-6">
                    <h4 className="font-medium text-gray-900 mb-3">
                      Add Custom Variable
                    </h4>
                    <div className="flex gap-2">
                      <Input
                        placeholder="Variable name (e.g., companyName)"
                        value={customVariableInput}
                        onChange={(e) => setCustomVariableInput(e.target.value)}
                        className="flex-1"
                      />
                      <Button
                        onClick={() => {
                          if (customVariableInput.trim()) {
                            navigator.clipboard.writeText(
                              `{{${customVariableInput.trim()}}}`
                            );
                            toast.success(
                              `Copied {{${customVariableInput.trim()}}} to clipboard`
                            );
                            setCustomVariableInput("");
                          }
                        }}
                        disabled={!customVariableInput.trim()}
                        className="bg-blue-600 hover:bg-blue-700"
                      >
                        <Copy className="h-4 w-4 mr-2" />
                        Copy
                      </Button>
                    </div>
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </div>

          {/* Editor Section - Now full width */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            {/* Editor Toolbar */}
            <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-gray-50">
              <div className="flex items-center gap-2">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={handleUndo}
                        disabled={historyIndex <= 0}
                        className="h-8 w-8"
                      >
                        <Undo className="h-4 w-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Undo</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={handleRedo}
                        disabled={historyIndex >= history.length - 1}
                        className="h-8 w-8"
                      >
                        <Redo className="h-4 w-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Redo</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>

              <div className="text-sm text-gray-500">Visual Editor</div>
            </div>

            {/* Editor Content */}
            <div className="p-6">
              <HtmlEditor
                value={html}
                onChange={setHtml}
                className="min-h-[500px]"
                placeholder="Start designing your email template..."
              />
            </div>
          </div>
        </div>

        {/* Inline Preview */}
        {showInlinePreview && (
          <div className="mt-8">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200">
              <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-gray-50">
                <h3 className="text-lg font-semibold text-gray-900">
                  Live Preview
                </h3>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowInlinePreview(false)}
                  className="h-8 w-8"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
              <div className="p-6">
                <div
                  dangerouslySetInnerHTML={{ __html: html }}
                  className="prose prose-sm max-w-none"
                />
              </div>
            </div>
          </div>
        )}

        {/* Preview Dialog */}
        <Dialog open={showPreview} onOpenChange={setShowPreview}>
          <DialogContent className="max-w-4xl max-h-[80vh] overflow-auto">
            <DialogHeader>
              <DialogTitle>Email Template Preview</DialogTitle>
              <DialogDescription>
                This is how your email will look to recipients with sample data.
              </DialogDescription>
            </DialogHeader>
            <div className="mt-4">
              <div
                dangerouslySetInnerHTML={{ __html: previewHtml }}
                className="prose prose-sm max-w-none"
              />
            </div>
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setShowPreview(false)}
                className="mt-4"
              >
                <X className="h-4 w-4 mr-2" />
                Close Preview
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
}
