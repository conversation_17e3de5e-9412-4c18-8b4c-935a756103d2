import { useNavigate } from 'react-router-dom';
import { Card, CardContent } from "@/components/ui/card"; // Use CardContent
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { MapPin, Star, Utensils, CalendarRange, ExternalLink, Clock } from "lucide-react";
// Assuming Restaurant type includes these optional fields based on backend structure
// Make sure this type definition matches your actual data structure
export interface Restaurant {
  id: string;
  username?: string; // For vanity URLs
  restaurantName: string;
  description?: string;
  cuisines?: string[] | string; // Can be array or comma-separated string
  address?: string;
  imageUrl?: string;
  rating?: number | string;
  reviewCount?: number;
  weightedRating?: number;
  isOpen?: boolean;
  distance?: number; // Optional distance
}

interface RestaurantListRendererProps {
  restaurants: Restaurant[];
}

const PLACEHOLDER_RESTAURANT_IMG = "/placeholder-restaurant.jpg"; // Define placeholder

export const RestaurantListRenderer = ({ restaurants }: RestaurantListRendererProps) => {
  const navigate = useNavigate();

  // Fallback for image errors
  const handleImageError = (e: React.SyntheticEvent<HTMLImageElement, Event>) => {
    e.currentTarget.src = PLACEHOLDER_RESTAURANT_IMG;
    e.currentTarget.srcset = "";
  };

  // Helper to format cuisines
  const formatCuisines = (cuisines?: string[] | string): string => {
      if (!cuisines) return 'N/A';
      if (Array.isArray(cuisines)) return cuisines.join(', ');
      return cuisines;
  }

  if (!restaurants || restaurants.length === 0) {
     return <p className="text-muted-foreground text-sm">No restaurants found.</p>;
  }

  return (
    <div className="grid grid-cols-1 gap-3 w-full"> {/* Reduced gap */}
      {restaurants.map((restaurant) => {
         // Prefer username for navigation if available, otherwise use ID
        const navId = restaurant.username || restaurant.id;

        return (
        <Card
          key={restaurant.id}
          className="overflow-hidden transition-all duration-200 border-border/60 hover:border-primary/50 bg-card" // Use theme colors
        >
          <CardContent className="p-3 flex gap-3"> {/* Use CardContent */}
            {/* Image Section */}
            <div className="w-24 h-24 md:w-28 md:h-28 rounded-md overflow-hidden flex-shrink-0 border bg-muted">
              <img
                src={restaurant.imageUrl || PLACEHOLDER_RESTAURANT_IMG}
                alt={restaurant.restaurantName || 'Restaurant'}
                className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                onError={handleImageError}
                loading="lazy" // Add lazy loading
              />
            </div>

            {/* Details Section */}
            <div className="flex-grow flex flex-col justify-between">
              <div>
                <h3 className="font-semibold text-base leading-snug flex items-center gap-2 flex-wrap">
                  {restaurant.restaurantName || "Unnamed Restaurant"}
                  {restaurant.isOpen && (
                    <Badge variant="outline" className="border-green-500 text-green-600 text-xs px-1.5 py-0 h-5 font-normal">
                       <Clock className="w-3 h-3 mr-1" /> Open Now
                    </Badge>
                  )}
                   {!restaurant.isOpen && restaurant.isOpen === false && ( // Explicit check for closed
                    <Badge variant="outline" className="border-destructive/50 text-destructive text-xs px-1.5 py-0 h-5 font-normal">
                       <Clock className="w-3 h-3 mr-1" /> Closed
                    </Badge>
                  )}
                </h3>

                {restaurant.description && (
                  <p className="text-xs text-muted-foreground mt-1 line-clamp-2">
                    {restaurant.description}
                  </p>
                )}

                {/* Info Grid */}
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-x-3 gap-y-1 mt-2">
                  {(restaurant.cuisines) && (
                    <div className="flex items-center gap-1.5 text-xs text-muted-foreground" title="Cuisines">
                      <Utensils className="w-3 h-3 flex-shrink-0" />
                      <span className="truncate">{formatCuisines(restaurant.cuisines)}</span>
                    </div>
                  )}
                  {restaurant.address && (
                    <div className="flex items-center gap-1.5 text-xs text-muted-foreground" title="Address">
                      <MapPin className="w-3 h-3 flex-shrink-0" />
                      <span className="truncate">{restaurant.address}</span>
                    </div>
                  )}
                  <div className="flex items-center gap-1.5 text-xs text-muted-foreground" title="Rating">
                    <Star className="w-3 h-3 text-amber-500 flex-shrink-0" />
                    <span>
                      {restaurant.weightedRating !== undefined
                        ? Number(restaurant.weightedRating).toFixed(1)
                        : restaurant.rating !== undefined
                        ? Number(restaurant.rating).toFixed(1)
                        : "0.0"}
                      {restaurant.reviewCount ? ` (${restaurant.reviewCount})` : ''}
                    </span>
                  </div>
                   {restaurant.distance != null && (
                    <div className="flex items-center gap-1.5 text-xs text-muted-foreground" title="Distance">
                      <MapPin className="w-3 h-3 flex-shrink-0" />
                      <span>{restaurant.distance.toFixed(1)} km away</span>
                    </div>
                  )}
                </div>
              </div>

               {/* Action Buttons */}
              <div className="flex flex-wrap gap-2 mt-3 justify-end">
                <Button
                  onClick={() => navigate(`/restaurants/${navId}`)}
                  variant="default"
                  size="sm"
                  className="rounded-full px-3 h-8 text-xs shadow-sm"
                  title={`Visit page for ${restaurant.restaurantName}`}
                >
                  <ExternalLink className="w-3.5 h-3.5 mr-1" />
                  Visit Page
                </Button>
                {/* Reservation button might need extra checks (e.g., if reservations are enabled) */}
                <Button
                  onClick={() => navigate(`/restaurants/${navId}/reservations`)}
                  variant="outline"
                  size="sm"
                  className="rounded-full px-3 h-8 text-xs"
                   title={`Make reservation at ${restaurant.restaurantName}`}
                >
                  <CalendarRange className="w-3.5 h-3.5 mr-1" />
                  Reserve
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
        )
      })}
    </div>
  );
};