import { GlobalRewardsManagement } from "@/components/admin/GlobalRewardsManagement";
import { useAuth } from "@/providers/AuthProvider";
import { Loading } from "@/components/ui/loading";
import { Button } from "@/components/ui/button";
import { AlertCircle, ArrowLeft } from "lucide-react";
import { Link } from "react-router-dom";

export const AdminRewards = () => {
  const { user, loading, error, clearError } = useAuth();

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loading />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen p-4">
        <AlertCircle className="h-12 w-12 text-destructive mb-4" />
        <h1 className="text-2xl font-bold mb-2">Error</h1>
        <p className="text-muted-foreground mb-4">
          {error ? error.toString() : "An error occurred"}
        </p>
        <Button onClick={clearError}>Try Again</Button>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen p-4">
        <AlertCircle className="h-12 w-12 text-destructive mb-4" />
        <h1 className="text-2xl font-bold mb-2">Access Denied</h1>
        <p className="text-muted-foreground mb-4">
          Please log in to access this page.
        </p>
        <Button asChild>
          <Link to="/login">Log In</Link>
        </Button>
      </div>
    );
  }

  if (user.email !== "<EMAIL>") {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen p-4">
        <AlertCircle className="h-12 w-12 text-destructive mb-4" />
        <h1 className="text-2xl font-bold mb-2">Access Denied</h1>
        <p className="text-muted-foreground mb-4">
          You don't have permission to access this page.
        </p>
        <Button asChild>
          <Link to="/">Go Home</Link>
        </Button>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="mb-8">
        <Button variant="outline" asChild className="mb-4">
          <Link to="/admin">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Admin Panel
          </Link>
        </Button>
        <h1 className="text-3xl font-bold">Qonai Global Rewards Management</h1>
        <p className="text-muted-foreground mt-2">
          Manage platform-wide rewards available to all users
        </p>
      </div>

      <GlobalRewardsManagement />
    </div>
  );
};

export default AdminRewards;
