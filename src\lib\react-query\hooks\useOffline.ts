import { useEffect, useState } from "react";
import { useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

/**
 * Hook to track online/offline status and handle offline capabilities
 */
export function useOfflineStatus() {
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [wasOffline, setWasOffline] = useState(false);
  const queryClient = useQueryClient();

  useEffect(() => {
    // Function to handle online status change
    const handleOnline = () => {
      setIsOnline(true);
      if (wasOffline) {
        toast.success("You are back online! Syncing data...");
        // Refetch all queries when coming back online
        queryClient.refetchQueries();
        setWasOffline(false);
      }
    };

    // Function to handle offline status change
    const handleOffline = () => {
      setIsOnline(false);
      setWasOffline(true);
      toast.warning("You are offline. Some features may be limited.");
    };

    // Add event listeners
    window.addEventListener("online", handleOnline);
    window.addEventListener("offline", handleOffline);

    // Clean up event listeners
    return () => {
      window.removeEventListener("online", handleOnline);
      window.removeEventListener("offline", handleOffline);
    };
  }, [wasOffline, queryClient]);

  return { isOnline, wasOffline };
}

/**
 * Hook to handle offline mutations by storing them in localStorage
 * and replaying them when back online
 */
export function useOfflineMutation() {
  const { isOnline } = useOfflineStatus();
  const queryClient = useQueryClient();

  // Function to store a mutation in localStorage
  const storeMutation = (key: string, mutation: Record<string, unknown>) => {
    try {
      const pendingMutations = JSON.parse(
        localStorage.getItem("pendingMutations") || "[]"
      );

      pendingMutations.push({
        key,
        mutation,
        timestamp: Date.now(),
      });

      localStorage.setItem(
        "pendingMutations",
        JSON.stringify(pendingMutations)
      );

      toast.info("Action saved for when you're back online");
    } catch (error) {
      console.error("Error storing mutation:", error);
    }
  };

  // Replay pending mutations when coming back online
  useEffect(() => {
    if (!isOnline) return;

    // Function to replay pending mutations
    const replayPendingMutations = async () => {
      try {
        const pendingMutationsString = localStorage.getItem("pendingMutations");

        if (!pendingMutationsString) return;

        const pendingMutations = JSON.parse(pendingMutationsString);

        if (pendingMutations.length === 0) return;

        // Clear pending mutations first to prevent duplicate replays
        localStorage.removeItem("pendingMutations");

        toast.info(`Syncing ${pendingMutations.length} pending actions...`);

        // Process each mutation
        for (const { key, mutation } of pendingMutations) {
          try {
            // Execute the mutation using the correct method
            await queryClient
              .getMutationCache()
              .build(queryClient, {
                ...mutation,
                onSuccess: () => {
                  // Invalidate related queries
                  if (mutation.meta?.invalidateQueries) {
                    for (const queryKey of mutation.meta.invalidateQueries) {
                      queryClient.invalidateQueries({ queryKey });
                    }
                  }
                },
              })
              .execute(mutation.variables);
          } catch (error) {
            console.error(`Error replaying mutation ${key}:`, error);
          }
        }

        toast.success("All pending actions have been processed");
      } catch (error) {
        console.error("Error replaying mutations:", error);
        toast.error("Failed to process some offline actions");
      }
    };

    replayPendingMutations();
  }, [isOnline, queryClient]);

  return { isOnline, storeMutation };
}

/**
 * Hook to handle manual refresh of data
 */
export function useManualRefresh() {
  const queryClient = useQueryClient();
  const [isRefreshing, setIsRefreshing] = useState(false);

  const refreshAll = async () => {
    setIsRefreshing(true);
    try {
      await queryClient.refetchQueries();
      toast.success("Data refreshed successfully");
    } catch (error) {
      console.error("Error refreshing data:", error);
      toast.error("Failed to refresh data");
    } finally {
      setIsRefreshing(false);
    }
  };

  const refreshQuery = async (queryKey: unknown[]) => {
    setIsRefreshing(true);
    try {
      await queryClient.refetchQueries({ queryKey });
      toast.success("Data refreshed successfully");
    } catch (error) {
      console.error("Error refreshing data:", error);
      toast.error("Failed to refresh data");
    } finally {
      setIsRefreshing(false);
    }
  };

  return { refreshAll, refreshQuery, isRefreshing };
}

/**
 * Hook to handle background refetching at appropriate intervals
 */
export function useBackgroundRefetch(intervalMs = 5 * 60 * 1000) {
  // Default: 5 minutes
  const queryClient = useQueryClient();
  const { isOnline } = useOfflineStatus();

  useEffect(() => {
    if (!isOnline) return;

    const interval = setInterval(() => {
      // Only refetch if the user is online
      if (navigator.onLine) {
        queryClient.refetchQueries({ type: "active" });
      }
    }, intervalMs);

    return () => clearInterval(interval);
  }, [intervalMs, isOnline, queryClient]);
}
