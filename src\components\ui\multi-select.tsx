import * as React from "react";
import { Check, ChevronsUpDown, X } from "lucide-react"; // İkonları güncelle

import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator, // Ayırıcı ekleyebiliriz
} from "@/components/ui/command"; // Command bileşenlerini import et
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Badge } from "@/components/ui/badge"; // Badge hala gerekebilir (veya kaldırılabilir)
import { ScrollArea } from "@/components/ui/scroll-area"; // Uzun listeler için

export interface MultiSelectOption {
  value: string;
  label: string;
  icon?: React.ComponentType<{ className?: string }>; // Opsiyonel ikon
}

export interface MultiSelectProps {
  options: MultiSelectOption[];
  selected: string[];
  onChange: (value: string[]) => void;
  placeholder?: string;
  // Arama alanı için placeholder
  searchPlaceholder?: string;
  // Açılır liste boşken gösterilecek metin
  emptyIndicator?: string;
  className?: string;
  // Popover içeriğinin genişliğini ayarlamak için
  popoverWidthClass?: string;
  // Seçili öğeleri göstermek için (örneğin, bileşenin altında)
  renderSelectedBelow?: boolean;
}

export function MultiSelect({
  options,
  selected,
  onChange,
  placeholder = "Select options...",
  searchPlaceholder = "Search options...",
  emptyIndicator = "No options found.",
  className,
  popoverWidthClass = "w-full", // Varsayılan olarak tetikleyici genişliği
  renderSelectedBelow = false, // Varsayılan olarak seçili öğeleri aşağıda gösterme
}: MultiSelectProps) {
  const [open, setOpen] = React.useState(false);

  const handleSelect = (value: string) => {
    onChange(
      selected.includes(value)
        ? selected.filter((item) => item !== value)
        : [...selected, value]
    );
  };

  const handleDeselect = (value: string) => {
    onChange(selected.filter((item) => item !== value));
  };

  return (
    <div className={cn("w-full", className)}>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            // Tetikleyici genişliğini ayarla ve yüksekliği otomatik yap
            className={cn(
              "w-full justify-between h-auto min-h-[2.5rem]",
              selected.length > 0 ? "text-foreground" : "text-muted-foreground"
            )}
          >
            <span className="truncate pr-2">
              {" "}
              {/* Taşmaları önle */}
              {selected.length === 0
                ? placeholder
                : selected.length === 1
                ? options.find((opt) => opt.value === selected[0])?.label ??
                  placeholder
                : `${selected.length} selected`}
            </span>
            <ChevronsUpDown className="h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent
          className={cn("p-0 shadow-md", popoverWidthClass)}
          // Portal kullanmak, render sorunlarını önleyebilir
          // portalProps={{ container: document.body }}
          align="start" // Genellikle start veya end daha iyi çalışır
        >
          <Command>
            <CommandInput placeholder={searchPlaceholder} />
            <CommandList>
              {/* Yüksekliği ve kaydırmayı yönetmek için ScrollArea */}
              <ScrollArea className="max-h-[200px] overflow-y-auto">
                <CommandEmpty>{emptyIndicator}</CommandEmpty>
                <CommandGroup>
                  {options.map((option) => {
                    const isSelected = selected.includes(option.value);
                    return (
                      <CommandItem
                        key={option.value}
                        value={option.label} // Arama için etiketi kullan
                        onSelect={() => handleSelect(option.value)} // Doğrudan seçimi işle
                        className={cn(
                          "flex items-center justify-between gap-2 cursor-pointer"
                          // Seçili öğeler için görsel stil (isteğe bağlı)
                          // isSelected ? "bg-accent text-accent-foreground" : ""
                        )}
                      >
                        <div className="flex items-center gap-2 flex-1 min-w-0">
                          {/* İkon varsa göster */}
                          {option.icon && (
                            <option.icon className="h-4 w-4 text-muted-foreground" />
                          )}
                          <span className="truncate">{option.label}</span>
                        </div>
                        {/* Seçim işareti */}
                        <Check
                          className={cn(
                            "h-4 w-4",
                            isSelected ? "opacity-100" : "opacity-0"
                          )}
                        />
                      </CommandItem>
                    );
                  })}
                </CommandGroup>
              </ScrollArea>
            </CommandList>
            {/* İsteğe Bağlı: Tümünü Temizle Düğmesi */}
            {selected.length > 0 && (
              <>
                <CommandSeparator />
                <CommandGroup>
                  <CommandItem
                    onSelect={() => onChange([])}
                    className="flex items-center justify-center text-xs text-muted-foreground cursor-pointer"
                  >
                    Clear selection ({selected.length})
                  </CommandItem>
                </CommandGroup>
              </>
            )}
          </Command>
        </PopoverContent>
      </Popover>

      {/* İsteğe Bağlı: Seçili Öğeleri Aşağıda Göster */}
      {renderSelectedBelow && selected.length > 0 && (
        <div className="mt-2 flex flex-wrap gap-1">
          {selected.map((value) => {
            const option = options.find((opt) => opt.value === value);
            if (!option) return null;
            return (
              <Badge
                key={value}
                variant="secondary"
                className="px-2 py-0.5 text-xs font-normal rounded-sm"
              >
                {option.label}
                <button
                  type="button"
                  className="ml-1 rounded-full outline-none ring-offset-background focus:ring-2 focus:ring-ring focus:ring-offset-1"
                  onClick={() => handleDeselect(value)}
                  aria-label={`Remove ${option.label}`}
                >
                  <X className="h-3 w-3 text-muted-foreground hover:text-foreground" />
                </button>
              </Badge>
            );
          })}
        </div>
      )}
    </div>
  );
}
