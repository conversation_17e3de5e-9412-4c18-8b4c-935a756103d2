import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { DietaryGoal } from "@/types";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { toast } from "sonner";
import { PlusCircle, Trash2, Edit, Check, X, Target } from "lucide-react";
import { Progress } from "@/components/ui/progress";
import { Timestamp } from "firebase/firestore";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { SimpleDatePicker } from "@/components/ui/simple-date-picker";
import { format } from "date-fns";

interface DietaryGoalsProps {
  goals: DietaryGoal[];
  onSave: (goals: DietaryGoal[]) => void;
}

export const DietaryGoals: React.FC<DietaryGoalsProps> = ({
  goals = [],
  onSave,
}) => {
  const [goalsList, setGoalsList] = useState<DietaryGoal[]>(goals);
  const [isAdding, setIsAdding] = useState(false);
  const [editingIndex, setEditingIndex] = useState<number | null>(null);

  const [newGoal, setNewGoal] = useState<DietaryGoal>({
    id: "",
    type: "calorie",
    target: 2000,
    unit: "kcal",
    startDate: Timestamp.now(),
    progress: 0,
    completed: false,
    trackingEnabled: true,
    dailyTarget: 0
  });

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;

    if (editingIndex !== null) {
      // Editing existing goal
      const updatedGoals = [...goalsList];
      updatedGoals[editingIndex] = {
        ...updatedGoals[editingIndex],
        [name]: name === "target" || name === "progress" ? parseFloat(value) : value,
      };
      setGoalsList(updatedGoals);
    } else {
      // Adding new goal
      setNewGoal({
        ...newGoal,
        [name]: name === "target" || name === "progress" ? parseFloat(value) : value,
      });
    }
  };

  const handleSelectChange = (name: string, value: string) => {
    if (editingIndex !== null) {
      // Editing existing goal
      const updatedGoals = [...goalsList];

      // Update unit based on type if it's a type change
      let unit = updatedGoals[editingIndex].unit;
      if (name === "type") {
        unit = getDefaultUnit(value as DietaryGoal["type"]);
      }

      updatedGoals[editingIndex] = {
        ...updatedGoals[editingIndex],
        [name]: value,
        ...(name === "type" && { unit }),
      };
      setGoalsList(updatedGoals);
    } else {
      // Adding new goal
      setNewGoal({
        ...newGoal,
        [name]: value,
        ...(name === "type" && { unit: getDefaultUnit(value as DietaryGoal["type"]) }),
      });
    }
  };

  const handleDateChange = (field: "startDate" | "endDate", date: Date | undefined) => {
    if (!date) return;

    if (editingIndex !== null) {
      // Editing existing goal
      const updatedGoals = [...goalsList];
      updatedGoals[editingIndex] = {
        ...updatedGoals[editingIndex],
        [field]: Timestamp.fromDate(date),
      };
      setGoalsList(updatedGoals);
    } else {
      // Adding new goal
      setNewGoal({
        ...newGoal,
        [field]: Timestamp.fromDate(date),
      });
    }
  };

  const getDefaultUnit = (type: DietaryGoal["type"]): string => {
    switch (type) {
      case "calorie": return "kcal";
      case "protein": return "g";
      case "carbs": return "g";
      case "fat": return "g";
      case "water": return "L";
      case "custom": return "";
      default: return "";
    }
  };

  const validateGoal = (goal: DietaryGoal): boolean => {
    if (goal.target <= 0) {
      toast.error("Target value must be greater than zero");
      return false;
    }
    if (!goal.unit.trim() && goal.type !== "custom") {
      toast.error("Unit is required");
      return false;
    }
    return true;
  };

  const handleAddGoal = () => {
    if (!validateGoal(newGoal)) return;

    setGoalsList([...goalsList, newGoal]);
    setNewGoal({
      id: "",
      type: "calorie",
      target: 2000,
      unit: "kcal",
      startDate: Timestamp.now(),
      progress: 0,
      completed: false,
      trackingEnabled: true,
      dailyTarget: 0
    });
    setIsAdding(false);
    toast.success("Goal added successfully");
  };

  const handleUpdateGoal = () => {
    if (editingIndex === null) return;

    if (!validateGoal(goalsList[editingIndex])) return;

    setEditingIndex(null);
    toast.success("Goal updated successfully");
  };

  const handleDeleteGoal = (index: number) => {
    setGoalsList(goalsList.filter((_, idx) => idx !== index));
    toast.success("Goal deleted successfully");
  };

  const handleToggleComplete = (index: number) => {
    const updatedGoals = [...goalsList];
    updatedGoals[index] = {
      ...updatedGoals[index],
      completed: !updatedGoals[index].completed,
    };
    setGoalsList(updatedGoals);
  };

  const handleSave = () => {
    onSave(goalsList);
    toast.success("Goals saved successfully");
  };

  const formatDate = (date: Date | Timestamp | undefined) => {
    if (!date) return "Not set";

    const dateObj = date instanceof Timestamp ? date.toDate() : date;
    return format(dateObj, "PPP");
  };

  const calculateProgressPercentage = (goal: DietaryGoal) => {
    if (goal.target <= 0) return 0;
    const percentage = (goal.progress / goal.target) * 100;
    return Math.min(Math.max(percentage, 0), 100); // Clamp between 0 and 100
  };

  const renderGoalForm = (goal: DietaryGoal, isNew: boolean = false) => (
    <div className="space-y-4 p-4 border rounded-md bg-muted/30">
      <div className="grid grid-cols-1 gap-4">
        <div className="space-y-2">
          <Label htmlFor={isNew ? "new-type" : `type-${editingIndex}`}>Goal Type</Label>
          <Select
            value={goal.type}
            onValueChange={(value) => handleSelectChange("type", value)}
          >
            <SelectTrigger id={isNew ? "new-type" : `type-${editingIndex}`}>
              <SelectValue placeholder="Select goal type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="calorie">Calorie Intake</SelectItem>
              <SelectItem value="protein">Protein Intake</SelectItem>
              <SelectItem value="carbs">Carbohydrate Intake</SelectItem>
              <SelectItem value="fat">Fat Intake</SelectItem>
              <SelectItem value="water">Water Intake</SelectItem>
              <SelectItem value="custom">Custom Goal</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor={isNew ? "new-target" : `target-${editingIndex}`}>Target Value</Label>
            <Input
              id={isNew ? "new-target" : `target-${editingIndex}`}
              name="target"
              type="number"
              value={goal.target}
              onChange={handleInputChange}
              min="0"
              step="0.1"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor={isNew ? "new-unit" : `unit-${editingIndex}`}>Unit</Label>
            <Input
              id={isNew ? "new-unit" : `unit-${editingIndex}`}
              name="unit"
              value={goal.unit}
              onChange={handleInputChange}
              placeholder="e.g., kcal, g, L"
            />
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label>Start Date</Label>
            <SimpleDatePicker
              value={goal.startDate instanceof Timestamp ? goal.startDate.toDate() : goal.startDate as Date}
              onChange={(date) => handleDateChange("startDate", date)}
              className="w-full"
            />
          </div>

          <div className="space-y-2">
            <Label>End Date (Optional)</Label>
            <SimpleDatePicker
              value={goal.endDate instanceof Timestamp ? goal.endDate.toDate() : goal.endDate as Date || new Date()}
              onChange={(date) => handleDateChange("endDate", date)}
              className="w-full"
            />
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor={isNew ? "new-progress" : `progress-${editingIndex}`}>Current Progress</Label>
          <Input
            id={isNew ? "new-progress" : `progress-${editingIndex}`}
            name="progress"
            type="number"
            value={goal.progress}
            onChange={handleInputChange}
            min="0"
            step="0.1"
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor={isNew ? "new-dailyTarget" : `dailyTarget-${editingIndex}`}>Daily Target (Optional)</Label>
          <Input
            id={isNew ? "new-dailyTarget" : `dailyTarget-${editingIndex}`}
            name="dailyTarget"
            type="number"
            value={goal.dailyTarget || ""}
            onChange={handleInputChange}
            min="0"
            step="0.1"
            placeholder="Leave empty for overall goal only"
          />
          <p className="text-xs text-muted-foreground mt-1">
            Set a daily target to track your progress day by day
          </p>
        </div>

        <div className="flex items-center space-x-2">
          <Checkbox
            id={isNew ? "new-trackingEnabled" : `trackingEnabled-${editingIndex}`}
            checked={goal.trackingEnabled}
            onCheckedChange={(checked) => {
              if (isNew) {
                setNewGoal({
                  ...newGoal,
                  trackingEnabled: !!checked
                });
              } else if (editingIndex !== null) {
                const updatedGoals = [...goalsList];
                updatedGoals[editingIndex] = {
                  ...updatedGoals[editingIndex],
                  trackingEnabled: !!checked
                };
                setGoalsList(updatedGoals);
              }
            }}
          />
          <Label
            htmlFor={isNew ? "new-trackingEnabled" : `trackingEnabled-${editingIndex}`}
            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
          >
            Enable automatic tracking with meals
          </Label>
        </div>

        <div className="space-y-2">
          <Label htmlFor={isNew ? "new-notes" : `notes-${editingIndex}`}>Notes (Optional)</Label>
          <Textarea
            id={isNew ? "new-notes" : `notes-${editingIndex}`}
            name="notes"
            value={goal.notes || ""}
            onChange={handleInputChange}
            placeholder="Add any notes about this goal..."
            rows={3}
          />
        </div>
      </div>

      <div className="flex justify-end space-x-2 pt-2">
        <Button
          variant="outline"
          onClick={() => {
            if (isNew) {
              setIsAdding(false);
            } else {
              setEditingIndex(null);
            }
          }}
        >
          <X className="mr-2 h-4 w-4" />
          Cancel
        </Button>
        <Button
          onClick={isNew ? handleAddGoal : handleUpdateGoal}
        >
          <Check className="mr-2 h-4 w-4" />
          {isNew ? "Add Goal" : "Update Goal"}
        </Button>
      </div>
    </div>
  );

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="text-xl font-semibold">Dietary Goals</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* List of existing goals */}
        {goalsList.length > 0 ? (
          <div className="space-y-4">
            {goalsList.map((goal, index) => (
              <div key={index} className="p-4 border rounded-md relative">
                {editingIndex === index ? (
                  renderGoalForm(goal)
                ) : (
                  <>
                    <div className="absolute top-2 right-2 flex space-x-2">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleToggleComplete(index)}
                        title={goal.completed ? "Mark as incomplete" : "Mark as complete"}
                      >
                        <Check className={`h-4 w-4 ${goal.completed ? "text-green-500" : "text-muted-foreground"}`} />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => setEditingIndex(index)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleDeleteGoal(index)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                    <div className="space-y-3 pr-16">
                      <div className="flex items-center space-x-2">
                        <Target className="h-5 w-5 text-primary" />
                        <h4 className="font-medium text-lg">
                          {goal.type === "custom" ? "Custom Goal" : `${goal.type.charAt(0).toUpperCase() + goal.type.slice(1)} Goal`}
                        </h4>
                        {goal.completed && (
                          <Badge variant="success" className="ml-2">Completed</Badge>
                        )}
                      </div>

                      <p className="text-lg font-semibold">
                        Target: {goal.target} {goal.unit}
                      </p>

                      <div className="space-y-1">
                        <div className="flex justify-between text-sm">
                          <span>Progress: {goal.progress} {goal.unit}</span>
                          <span>{Math.round(calculateProgressPercentage(goal))}%</span>
                        </div>
                        <Progress value={calculateProgressPercentage(goal)} className="h-2" />
                      </div>

                      <div className="grid grid-cols-2 gap-2 text-sm text-muted-foreground">
                        <div>Start: {formatDate(goal.startDate)}</div>
                        {goal.endDate && <div>End: {formatDate(goal.endDate)}</div>}
                      </div>

                      {goal.notes && (
                        <div className="mt-2 text-sm border-t pt-2">
                          <p className="text-muted-foreground">{goal.notes}</p>
                        </div>
                      )}
                    </div>
                  </>
                )}
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center p-6 border border-dashed rounded-md">
            <p className="text-muted-foreground">No dietary goals added yet</p>
          </div>
        )}

        {/* Add new goal form */}
        {isAdding ? (
          renderGoalForm(newGoal, true)
        ) : (
          <Button
            variant="outline"
            className="w-full"
            onClick={() => setIsAdding(true)}
          >
            <PlusCircle className="mr-2 h-4 w-4" />
            Add New Goal
          </Button>
        )}

        {/* Save button */}
        <Button onClick={handleSave} className="w-full mt-6">
          Save All Goals
        </Button>
      </CardContent>
    </Card>
  );
};
