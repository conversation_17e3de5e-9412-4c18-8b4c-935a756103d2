import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, Card<PERSON><PERSON>er, CardHeader, CardTitle } from '@/components/ui/card';
import { updateRestaurantCuisines } from '@/tools/updateRestaurantCuisines';
import { useAuth } from '@/providers/AuthProvider';
import { useNavigate } from 'react-router-dom';

export default function AdminTools() {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<{
    success: boolean;
    message: string;
    updatedCount?: number;
    error?: unknown;
    [key: string]: unknown; // Allow for additional properties
  } | null>(null);

  // Redirect if not logged in
  React.useEffect(() => {
    if (!user) {
      navigate('/auth');
    }
  }, [user, navigate]);

  const handleUpdateCuisines = async () => {
    setLoading(true);
    try {
      const result = await updateRestaurantCuisines();
      setResult(result);
    } catch (error) {
      console.error('Error:', error);
      setResult({ success: false, message: 'Error executing tool', error });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="mx-auto py-10">
      <h1 className="text-3xl font-bold mb-6">Admin Tools</h1>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        <Card>
          <CardHeader>
            <CardTitle>Update Restaurant Cuisines</CardTitle>
            <CardDescription>
              Assign cuisine types to restaurants that don't have them
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-4">
              This tool will assign random cuisine types to all restaurants that don't have a cuisine type set.
              This is needed for the recommendation system to work properly.
            </p>
          </CardContent>
          <CardFooter>
            <Button
              onClick={handleUpdateCuisines}
              disabled={loading}
              className="w-full"
            >
              {loading ? 'Updating...' : 'Update Cuisines'}
            </Button>
          </CardFooter>
        </Card>
      </div>

      {result && (
        <div className="mt-8">
          <h2 className="text-xl font-semibold mb-2">Result</h2>
          <pre className="bg-muted p-4 rounded-md overflow-auto">
            {JSON.stringify(result, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
}
