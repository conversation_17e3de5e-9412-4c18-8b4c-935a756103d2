import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import {
  collection,
  query,
  getDocs,
  getDoc,
  doc,
  onSnapshot,
  orderBy,
  writeBatch,
  serverTimestamp,
  FirestoreError,
  Timestamp,
} from "firebase/firestore";
import { firestore } from "@/config/firebase";
import { queryKeys, CACHE_TIME, STALE_TIME } from "../index";
import { useEffect, useState } from "react";
import { toast } from "sonner";

interface Follower {
  userId: string;
  username: string;
  userAvatarUrl: string | null;
  createdAt: Date;
}

interface Following {
  restaurantId: string;
  restaurantName: string;
  restaurantUsername: string;
  createdAt: Date;
}

/**
 * Hook to fetch followers for a restaurant with caching
 */
export function useRestaurantFollowers(restaurantId: string | undefined) {
  return useQuery({
    queryKey: queryKeys.restaurants.followers(restaurantId || ""),
    queryFn: async () => {
      if (!restaurantId) {
        throw new Error("Restaurant ID is required");
      }

      const followersRef = collection(
        doc(firestore, "restaurants", restaurantId),
        "followers"
      );
      const q = query(followersRef, orderBy("createdAt", "desc"));
      const querySnapshot = await getDocs(q);

      const followers: Follower[] = [];
      querySnapshot.forEach((doc) => {
        const data = doc.data();
        followers.push({
          userId: doc.id,
          ...data,
          createdAt:
            data.createdAt instanceof Timestamp
              ? data.createdAt.toDate()
              : new Date(data.createdAt),
        } as Follower);
      });

      return followers;
    },
    enabled: !!restaurantId,
    staleTime: STALE_TIME,
    gcTime: CACHE_TIME,
  });
}

/**
 * Hook to fetch restaurants followed by a client with caching
 */
export function useClientFollowing(clientId: string | undefined) {
  return useQuery({
    queryKey: queryKeys.clients.following(clientId || ""),
    queryFn: async () => {
      if (!clientId) {
        throw new Error("Client ID is required");
      }

      const followingRef = collection(
        doc(firestore, "clients", clientId),
        "following"
      );
      const q = query(followingRef, orderBy("createdAt", "desc"));
      const querySnapshot = await getDocs(q);

      const following: Following[] = [];
      querySnapshot.forEach((doc) => {
        const data = doc.data();
        following.push({
          restaurantId: doc.id,
          ...data,
          createdAt:
            data.createdAt instanceof Timestamp
              ? data.createdAt.toDate()
              : new Date(data.createdAt),
        } as Following);
      });

      return following;
    },
    enabled: !!clientId,
    staleTime: STALE_TIME,
    gcTime: CACHE_TIME,
  });
}

/**
 * Hook to fetch followers with real-time updates
 */
export function useRealtimeFollowers(restaurantId: string | undefined) {
  const [followers, setFollowers] = useState<Follower[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<FirestoreError | null>(null);

  useEffect(() => {
    if (!restaurantId) {
      setIsLoading(false);
      setFollowers([]);
      setError(null);
      return;
    }

    setIsLoading(true);
    setError(null);

    const followersRef = collection(
      doc(firestore, "restaurants", restaurantId),
      "followers"
    );
    const q = query(followersRef, orderBy("createdAt", "desc"));

    const unsubscribe = onSnapshot(
      q,
      (snapshot) => {
        const fetchedFollowers = snapshot.docs.map((doc) => {
          const data = doc.data();
          return {
            userId: doc.id,
            ...data,
            createdAt:
              data.createdAt instanceof Timestamp
                ? data.createdAt.toDate()
                : new Date(data.createdAt),
          } as Follower;
        });

        setFollowers(fetchedFollowers);
        setIsLoading(false);
      },
      (err: FirestoreError) => {
        console.error("Error fetching followers:", err);
        setError(err);
        toast.error("Failed to load followers");
        setIsLoading(false);
      }
    );

    return () => unsubscribe();
  }, [restaurantId]);

  return { followers, isLoading, error };
}

/**
 * Hook to fetch followed restaurants with real-time updates
 */
export function useRealtimeFollowing(clientId: string | undefined) {
  const [following, setFollowing] = useState<Following[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<FirestoreError | null>(null);

  useEffect(() => {
    if (!clientId) {
      setIsLoading(false);
      setFollowing([]);
      setError(null);
      return;
    }

    setIsLoading(true);
    setError(null);

    const followingRef = collection(
      doc(firestore, "clients", clientId),
      "following"
    );
    const q = query(followingRef, orderBy("createdAt", "desc"));

    const unsubscribe = onSnapshot(
      q,
      (snapshot) => {
        const fetchedFollowing = snapshot.docs.map((doc) => {
          const data = doc.data();
          return {
            restaurantId: doc.id,
            ...data,
            createdAt:
              data.createdAt instanceof Timestamp
                ? data.createdAt.toDate()
                : new Date(data.createdAt),
          } as Following;
        });

        setFollowing(fetchedFollowing);
        setIsLoading(false);
      },
      (err: FirestoreError) => {
        console.error("Error fetching following:", err);
        setError(err);
        toast.error("Failed to load followed restaurants");
        setIsLoading(false);
      }
    );

    return () => unsubscribe();
  }, [clientId]);

  return { following, isLoading, error };
}

/**
 * Hook to check if a user is following a restaurant
 */
export function useIsFollowing(
  clientId: string | undefined,
  restaurantId: string | undefined
) {
  const [isFollowing, setIsFollowing] = useState(false);
  const [isChecking, setIsChecking] = useState(true);

  useEffect(() => {
    if (!clientId || !restaurantId) {
      setIsFollowing(false);
      setIsChecking(false);
      return;
    }

    setIsChecking(true);

    const followingRef = doc(
      collection(doc(firestore, "clients", clientId), "following"),
      restaurantId
    );

    const checkFollowing = async () => {
      try {
        const docSnap = await getDoc(followingRef);
        setIsFollowing(docSnap.exists());
      } catch (err) {
        console.error("Error checking following status:", err);
      } finally {
        setIsChecking(false);
      }
    };

    checkFollowing();
  }, [clientId, restaurantId]);

  return { isFollowing, isChecking };
}

/**
 * Hook to toggle following status with optimistic updates
 */
export function useToggleFollow() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      clientId,
      restaurantId,
      restaurantName,
      restaurantUsername,
      username,
      userAvatarUrl,
    }: {
      clientId: string;
      restaurantId: string;
      restaurantName: string;
      restaurantUsername: string;
      username: string;
      userAvatarUrl: string | null;
    }) => {
      // Check if already following
      const clientFollowingRef = doc(
        collection(doc(firestore, "clients", clientId), "following"),
        restaurantId
      );

      const docSnap = await getDoc(clientFollowingRef);
      const isFollowing = docSnap.exists();

      // Use a batch write for atomicity
      const batch = writeBatch(firestore);

      if (isFollowing) {
        // Unfollow
        const restaurantFollowersRef = doc(
          collection(doc(firestore, "restaurants", restaurantId), "followers"),
          clientId
        );

        batch.delete(clientFollowingRef);
        batch.delete(restaurantFollowersRef);

        // Add to global follower log
        const globalFollowerLogRef = doc(collection(firestore, "followers"));
        batch.set(globalFollowerLogRef, {
          userId: clientId,
          restaurantId,
          username,
          restaurantUsername,
          action: "unfollow",
          createdAt: serverTimestamp(),
        });
      } else {
        // Follow
        const timestamp = serverTimestamp();

        const restaurantFollowersRef = doc(
          collection(doc(firestore, "restaurants", restaurantId), "followers"),
          clientId
        );

        batch.set(clientFollowingRef, {
          restaurantId,
          restaurantName,
          restaurantUsername,
          createdAt: timestamp,
        });

        batch.set(restaurantFollowersRef, {
          userId: clientId,
          username,
          userAvatarUrl,
          createdAt: timestamp,
        });

        // Add to global follower log
        const globalFollowerLogRef = doc(collection(firestore, "followers"));
        batch.set(globalFollowerLogRef, {
          userId: clientId,
          restaurantId,
          username,
          restaurantUsername,
          action: "follow",
          createdAt: timestamp,
        });
      }

      await batch.commit();

      return { isFollowing: !isFollowing };
    },
    onMutate: async (variables) => {
      const { clientId, restaurantId } = variables;

      // Cancel any outgoing refetches
      await queryClient.cancelQueries({
        queryKey: queryKeys.clients.following(clientId),
      });
      await queryClient.cancelQueries({
        queryKey: queryKeys.restaurants.followers(restaurantId),
      });

      // Return the previous values
      return {
        previousFollowing: queryClient.getQueryData(
          queryKeys.clients.following(clientId)
        ),
        previousFollowers: queryClient.getQueryData(
          queryKeys.restaurants.followers(restaurantId)
        ),
      };
    },
    onError: (_, variables, context) => {
      const { clientId, restaurantId } = variables;

      // If the mutation fails, use the context returned from onMutate to roll back
      if (context?.previousFollowing) {
        queryClient.setQueryData(
          queryKeys.clients.following(clientId),
          context.previousFollowing
        );
      }

      if (context?.previousFollowers) {
        queryClient.setQueryData(
          queryKeys.restaurants.followers(restaurantId),
          context.previousFollowers
        );
      }

      toast.error("Failed to update following status");
    },
    onSettled: (_, __, variables) => {
      const { clientId, restaurantId } = variables;

      // Always refetch after error or success to make sure our local data is correct
      queryClient.invalidateQueries({
        queryKey: queryKeys.clients.following(clientId),
      });
      queryClient.invalidateQueries({
        queryKey: queryKeys.restaurants.followers(restaurantId),
      });
    },
  });
}
