import React, { useState } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "@/types";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { format, subDays, addDays, isToday } from "date-fns";
import { ChevronLeft, ChevronRight, Trash2 } from "lucide-react";
// Calendar icon is used in the UI
import { Skeleton } from "@/components/ui/skeleton";
import { Badge } from "@/components/ui/badge";
import { SimpleDatePicker } from "@/components/ui/simple-date-picker";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Timestamp } from "firebase/firestore";

interface MealLogViewProps {
  mealLogs: MealLog[];
  isLoading: boolean;
  onDeleteMeal: (mealId: string) => Promise<void>;
  onDateRangeChange: (start: Date, end: Date) => void;
  dateRange: {
    start: Date;
    end: Date;
  };
}

export const MealLogView: React.FC<MealLogViewProps> = ({
  mealLogs,
  isLoading,
  onDeleteMeal,
  onDateRangeChange,
  dateRange,
}) => {
  const [selectedMealId, setSelectedMealId] = useState<string | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  // Group logs by date
  const groupedLogs = mealLogs.reduce<Record<string, MealLog>>((acc, log) => {
    const date = log.date instanceof Timestamp
      ? log.date.toDate()
      : new Date(log.date);

    const dateKey = format(date, "yyyy-MM-dd");
    acc[dateKey] = log;
    return acc;
  }, {});

  // Handle date navigation
  const handlePreviousDay = () => {
    const newStart = subDays(dateRange.start, 1);
    const newEnd = subDays(dateRange.end, 1);
    onDateRangeChange(newStart, newEnd);
  };

  const handleNextDay = () => {
    const newStart = addDays(dateRange.start, 1);
    const newEnd = addDays(dateRange.end, 1);
    onDateRangeChange(newStart, newEnd);
  };

  const handleDateSelect = (date: Date | undefined) => {
    if (!date) return;
    onDateRangeChange(date, date);
  };

  // Handle meal deletion
  const handleDeleteClick = (mealId: string) => {
    setSelectedMealId(mealId);
    setIsDeleteDialogOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (selectedMealId) {
      await onDeleteMeal(selectedMealId);
      setIsDeleteDialogOpen(false);
      setSelectedMealId(null);
    }
  };

  // Render meal card
  const renderMealCard = (meal: Meal) => {
    return (
      <div key={meal.id} className="p-4 border rounded-md">
        <div className="flex justify-between items-start">
          <div>
            <div className="flex items-center">
              <h4 className="font-medium">{meal.name}</h4>
              <Badge variant="outline" className="ml-2 capitalize">
                {meal.mealType}
              </Badge>
            </div>
            <p className="text-sm text-muted-foreground">
              {meal.date instanceof Timestamp
                ? format(meal.date.toDate(), "p")
                : format(meal.date as Date, "p")}
            </p>
          </div>
          <div className="flex items-center">
            <span className="font-medium mr-2">{meal.calories} kcal</span>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => handleDeleteClick(meal.id)}
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {(meal.protein || meal.carbs || meal.fat) && (
          <div className="grid grid-cols-3 gap-2 mt-2 text-sm">
            {meal.protein !== undefined && (
              <div>
                <span className="text-muted-foreground">Protein:</span> {meal.protein}g
              </div>
            )}
            {meal.carbs !== undefined && (
              <div>
                <span className="text-muted-foreground">Carbs:</span> {meal.carbs}g
              </div>
            )}
            {meal.fat !== undefined && (
              <div>
                <span className="text-muted-foreground">Fat:</span> {meal.fat}g
              </div>
            )}
          </div>
        )}

        {meal.description && (
          <p className="mt-2 text-sm">{meal.description}</p>
        )}

        {meal.ingredients && meal.ingredients.length > 0 && (
          <div className="mt-2">
            <p className="text-sm text-muted-foreground">Ingredients:</p>
            <div className="flex flex-wrap gap-1 mt-1">
              {meal.ingredients.map((ingredient, index) => (
                <Badge key={index} variant="secondary" className="text-xs">
                  {ingredient}
                </Badge>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  };

  // Render day section
  const renderDaySection = (dateKey: string, log: MealLog) => {
    const date = new Date(dateKey);
    const isCurrentDay = isToday(date);

    return (
      <div key={dateKey} className="space-y-4">
        <div className="flex items-center">
          <h3 className="text-lg font-medium">
            {format(date, "EEEE, MMMM d")}
            {isCurrentDay && <span className="ml-2 text-primary">(Today)</span>}
          </h3>
          <Badge className="ml-auto">
            {log.totalCalories} kcal
          </Badge>
        </div>

        <div className="space-y-3">
          {log.meals && log.meals.length > 0 ? (
            log.meals.map((meal) => renderMealCard(meal))
          ) : (
            <p className="text-muted-foreground text-center py-4">
              No meals recorded for this day
            </p>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <Button variant="outline" size="icon" onClick={handlePreviousDay}>
          <ChevronLeft className="h-4 w-4" />
        </Button>

        <div className="min-w-[240px]">
          <SimpleDatePicker
            value={dateRange.start}
            onChange={handleDateSelect}
            className="w-full"
          />
        </div>

        <Button variant="outline" size="icon" onClick={handleNextDay} disabled={isToday(dateRange.end)}>
          <ChevronRight className="h-4 w-4" />
        </Button>
      </div>

      {isLoading ? (
        <div className="space-y-6">
          <Skeleton className="h-6 w-48" />
          <Skeleton className="h-24 w-full" />
          <Skeleton className="h-24 w-full" />
        </div>
      ) : Object.keys(groupedLogs).length > 0 ? (
        <div className="space-y-8">
          {Object.entries(groupedLogs)
            .sort(([dateA], [dateB]) => new Date(dateB).getTime() - new Date(dateA).getTime())
            .map(([dateKey, log]) => renderDaySection(dateKey, log))}
        </div>
      ) : (
        <div className="text-center p-8 border border-dashed rounded-md">
          <p className="text-muted-foreground">No meals recorded for this period</p>
          <p className="text-sm text-muted-foreground mt-1">
            Add your first meal to start tracking
          </p>
        </div>
      )}

      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Meal</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this meal? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleConfirmDelete}>Delete</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};
