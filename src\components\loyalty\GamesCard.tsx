import React, { useState, useEffect } from "react";
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { But<PERSON> } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { useAuth } from "@/providers/AuthProvider";
import { gameService } from "@/services/GameService";
import { MiniGame } from "@/types/games";
import {
  GamepadIcon,
  Calendar,
  Brain,
  Utensils,
  AlertCircle,
  Trophy,
  Text,
  ChefHat,
} from "lucide-react";
import { FoodMatchGame } from "@/components/games/FoodMatchGame";
import { TriviaGame } from "@/components/games/TriviaGame";
import { DailyCheckIn } from "@/components/games/DailyCheckIn";
import { WordScrambleGame } from "@/components/games/WordScrambleGame";
import { RecipeChallengeGame } from "@/components/games/RecipeChallengeGame";

interface GamesCardProps {
  userPoints: number;
  onPointsEarned?: () => void;
}

export const GamesCard: React.FC<GamesCardProps> = ({ onPointsEarned }) => {
  const { user } = useAuth();
  const [games, setGames] = useState<MiniGame[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeGame, setActiveGame] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState("games");
  const [gameStats, setGameStats] = useState<{
    dailyPointsEarned: number;
    dailyPointsLimit: number;
    checkInStreak: number;
    lastCheckIn: Date | null;
    lastPlayed: { [gameId: string]: Date } | null;
    playCount?: { [gameId: string]: number };
  }>({
    dailyPointsEarned: 0,
    dailyPointsLimit: 150,
    checkInStreak: 0,
    lastCheckIn: null,
    lastPlayed: null,
    playCount: {},
  });

  useEffect(() => {
    const fetchGames = async () => {
      setLoading(true);
      try {
        // Get available games
        const availableGames = gameService.getGames();
        setGames(availableGames);

        // Get user's game stats if logged in
        if (user) {
          const stats = await gameService.getUserGameStats(user.uid);
          setGameStats(stats);
        }
      } catch (error) {
        console.error("Error fetching games:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchGames();
  }, [user]);

  const handleGameSelect = (gameId: string) => {
    setActiveGame(gameId);
  };

  const handleBackToGames = () => {
    setActiveGame(null);
  };

  const handlePointsEarned = () => {
    // Refresh game stats
    if (user) {
      gameService.getUserGameStats(user.uid).then((stats) => {
        setGameStats(stats);
      });
    }

    // Call parent callback if provided
    if (onPointsEarned) {
      onPointsEarned();
    }
  };

  // Get icon component based on icon name
  const getGameIcon = (iconName: string) => {
    switch (iconName) {
      case "utensils":
        return <Utensils className="h-5 w-5" />;
      case "brain":
        return <Brain className="h-5 w-5" />;
      case "calendar-check":
        return <Calendar className="h-5 w-5" />;
      case "text":
        return <Text className="h-5 w-5" />;
      case "chef-hat":
        return <ChefHat className="h-5 w-5" />;
      default:
        return <GamepadIcon className="h-5 w-5" />;
    }
  };

  // Render active game component
  const renderGameComponent = () => {
    if (!activeGame || !user) return null;

    switch (activeGame) {
      case "foodMatch":
        return (
          <FoodMatchGame
            onBack={handleBackToGames}
            onComplete={handlePointsEarned}
            userId={user.uid}
          />
        );
      case "culinaryTrivia":
        return (
          <TriviaGame
            onBack={handleBackToGames}
            onComplete={handlePointsEarned}
            userId={user.uid}
          />
        );
      case "dailyCheckIn":
        return (
          <DailyCheckIn
            onBack={handleBackToGames}
            onComplete={handlePointsEarned}
            userId={user.uid}
            streak={gameStats.checkInStreak}
            lastCheckIn={gameStats.lastCheckIn}
          />
        );
      case "wordScramble":
        return (
          <WordScrambleGame
            onBack={handleBackToGames}
            onComplete={handlePointsEarned}
            userId={user.uid}
          />
        );
      case "recipeChallenge":
        return (
          <RecipeChallengeGame
            onBack={handleBackToGames}
            onComplete={handlePointsEarned}
            userId={user.uid}
          />
        );
      default:
        return null;
    }
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>
            <Skeleton className="h-8 w-48" />
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            <Skeleton className="h-4 w-full" />
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {[1, 2, 3].map((i) => (
                <Skeleton key={i} className="h-40 w-full" />
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // If a game is active, render the game component
  if (activeGame) {
    return renderGameComponent();
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center text-xl">
          <GamepadIcon className="mr-3 h-6 w-6 text-primary" />
          Mini-Games
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="w-full mb-6">
            <TabsTrigger value="games" className="flex-1">
              <GamepadIcon className="h-4 w-4 mr-2" />
              Games
            </TabsTrigger>
            <TabsTrigger value="stats" className="flex-1">
              <Trophy className="h-4 w-4 mr-2" />
              Stats
            </TabsTrigger>
          </TabsList>

          <TabsContent value="games" className="space-y-6">
            <div className="flex items-center justify-between mb-4">
              <div>
                <h3 className="text-sm font-medium">Daily Points Earned</h3>
                <p className="text-xs text-muted-foreground">
                  {gameStats.dailyPointsEarned} / {gameStats.dailyPointsLimit}{" "}
                  points
                </p>
              </div>
              <Progress
                value={
                  (gameStats.dailyPointsEarned / gameStats.dailyPointsLimit) *
                  100
                }
                className="w-1/2 h-2"
              />
            </div>

            {gameStats.dailyPointsEarned >= gameStats.dailyPointsLimit && (
              <Alert className="mb-4">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Daily Limit Reached</AlertTitle>
                <AlertDescription>
                  You've reached your daily points limit from games. Come back
                  tomorrow for more!
                </AlertDescription>
              </Alert>
            )}

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {games.map((game) => (
                <Card key={game.id} className="overflow-hidden h-[280px]">
                  <div className="p-5 flex flex-col h-full">
                    <div className="flex items-center mb-3">
                      <div className="bg-primary/10 p-2 rounded-full mr-3">
                        {getGameIcon(game.iconName)}
                      </div>
                      <h3 className="font-medium text-base">{game.name}</h3>
                    </div>
                    <p className="text-sm text-muted-foreground mb-4 min-h-[60px]">
                      {game.description}
                    </p>
                    <div className="flex-1 flex flex-col">
                      <div className="flex items-center justify-between mb-4">
                        <div className="text-xs">
                          <span className="font-medium">Reward:</span>{" "}
                          {game.id === "culinaryTrivia"
                            ? `${game.pointsPerWin} pts per correct answer`
                            : `${game.pointsPerWin} pts`}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {gameStats.playCount &&
                          gameStats.playCount[game.id] !== undefined
                            ? `${gameStats.playCount[game.id]}/${
                                game.maxDailyPlays
                              } plays today`
                            : `0/${game.maxDailyPlays} plays today`}
                        </div>
                      </div>
                      <div className="mt-auto">
                        <Button
                          onClick={() => handleGameSelect(game.id)}
                          className="w-full"
                          disabled={
                            !user ||
                            (gameStats.playCount &&
                              gameStats.playCount[game.id] >=
                                game.maxDailyPlays)
                          }
                        >
                          {gameStats.playCount &&
                          gameStats.playCount[game.id] >= game.maxDailyPlays
                            ? "Daily Limit Reached"
                            : "Play Now"}
                        </Button>
                      </div>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="stats" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardContent className="pt-6">
                  <div className="flex items-center mb-4">
                    <div className="bg-primary/10 p-2 rounded-full mr-3">
                      <Calendar className="h-5 w-5 text-primary" />
                    </div>
                    <div>
                      <h3 className="font-medium">Check-in Streak</h3>
                      <p className="text-sm text-muted-foreground">
                        {gameStats.checkInStreak} days
                      </p>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between text-xs">
                      <span>Streak Progress</span>
                      <span>{gameStats.checkInStreak} / 7 days</span>
                    </div>
                    <Progress
                      value={(Math.min(gameStats.checkInStreak, 7) / 7) * 100}
                      className="h-2"
                    />
                    <p className="text-xs text-muted-foreground">
                      {gameStats.lastCheckIn
                        ? `Last check-in: ${gameStats.lastCheckIn.toLocaleDateString()}`
                        : "No check-ins yet"}
                    </p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="pt-6">
                  <div className="flex items-center mb-4">
                    <div className="bg-primary/10 p-2 rounded-full mr-3">
                      <Trophy className="h-5 w-5 text-primary" />
                    </div>
                    <div>
                      <h3 className="font-medium">Points Summary</h3>
                      <p className="text-sm text-muted-foreground">
                        Daily game points
                      </p>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between text-xs">
                      <span>Daily Points</span>
                      <span>
                        {gameStats.dailyPointsEarned} /{" "}
                        {gameStats.dailyPointsLimit} points
                      </span>
                    </div>
                    <Progress
                      value={
                        (gameStats.dailyPointsEarned /
                          gameStats.dailyPointsLimit) *
                        100
                      }
                      className="h-2"
                    />
                    <p className="text-xs text-muted-foreground">
                      Points reset daily at midnight
                    </p>
                  </div>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <CardTitle className="text-base">Recent Activity</CardTitle>
              </CardHeader>
              <CardContent>
                {gameStats.lastPlayed &&
                Object.keys(gameStats.lastPlayed).length > 0 ? (
                  <div className="space-y-3">
                    {Object.entries(gameStats.lastPlayed).map(
                      ([gameId, date]) => {
                        const game = games.find((g) => g.id === gameId);
                        return game ? (
                          <div
                            key={gameId}
                            className="flex items-center justify-between border-b pb-2 last:border-0"
                          >
                            <div className="flex items-center">
                              <div className="bg-primary/10 p-1.5 rounded-full mr-2">
                                {getGameIcon(game.iconName)}
                              </div>
                              <span className="text-sm">{game.name}</span>
                            </div>
                            <span className="text-xs text-muted-foreground">
                              {date.toLocaleString()}
                            </span>
                          </div>
                        ) : null;
                      }
                    )}
                  </div>
                ) : (
                  <p className="text-sm text-muted-foreground text-center py-4">
                    No recent game activity. Start playing to earn points!
                  </p>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};
