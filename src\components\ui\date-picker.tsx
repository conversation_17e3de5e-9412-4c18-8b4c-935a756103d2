import * as React from "react";
import { format } from "date-fns";
import { Calendar as CalendarIcon } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

interface DatePickerProps {
  selected?: Date;
  onSelect?: (date?: Date) => void;
  disabled?: boolean;
  placeholder?: string;
  className?: string;
  calendarClassName?: string;
  dateFormat?: string;
}

export function DatePicker({
  selected,
  onSelect,
  disabled = false,
  placeholder = "Pick a date",
  className,
  calendarClassName,
  dateFormat = "PPP",
}: DatePickerProps) {
  const [date, setDate] = React.useState<Date | undefined>(selected);
  const [open, setOpen] = React.useState(false);

  // Update internal state when selected prop changes
  React.useEffect(() => {
    setDate(selected);
  }, [selected]);

  const handleSelect = (selectedDate: Date | undefined) => {
    setDate(selectedDate);
    onSelect?.(selectedDate);
    setOpen(false);
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          className={cn(
            "w-full justify-start text-left font-normal",
            !date && "text-muted-foreground",
            className
          )}
          disabled={disabled}
        >
          <CalendarIcon className="mr-2 h-4 w-4" />
          {date ? format(date, dateFormat) : placeholder}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0" align="start">
        <Calendar
          mode="single"
          selected={date}
          onSelect={handleSelect}
          initialFocus
          className={calendarClassName}
        />
      </PopoverContent>
    </Popover>
  );
}
