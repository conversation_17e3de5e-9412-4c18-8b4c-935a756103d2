import { useState, useEffect, useRef, useMemo } from "react";
import { useParams, useNavigate } from "react-router-dom";

import { MenuItem } from "@/types/restaurant";
import { useRealtimeMenu } from "@/lib/react-query/hooks/useMenu";
import { useRestaurantByUsername } from "@/lib/react-query/hooks/useRestaurants";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import { MetaTags } from "@/components/ui/meta-tags";
import { toast } from "sonner";
import {
  ShoppingCart,
  Coffee,
  Utensils,
  Salad,
  Sandwich,
  IceCream,
  Cake,
  Pizza,
  AlertCircle,
  Leaf,
  Search,
  X,
  Award,
  Timer,
  Sparkles,
} from "lucide-react";
import { Input } from "@/components/ui/input";

// Import custom components
import {
  MenuItemCard,
  SpecialOfferCard,
  PopularItemCard,
  RecommendationCard,
  LimitedTimeOfferCard,
} from "@/components/qr-menu/MenuCards";
import { MenuItemDialog } from "@/components/qr-menu/MenuItemDialog";
import { RestaurantRewards } from "@/components/qr-menu/RestaurantRewards";
import { PointsBalance } from "@/components/loyalty/PointsBalance";
import { menuAnalyticsService } from "@/services/MenuAnalyticsService";
import { useAuth } from "@/providers/AuthProvider";
import { useLoyaltyStatus } from "@/lib/react-query/hooks/useLoyalty";

// Category groups with icons
const CATEGORY_GROUPS: Record<
  string,
  { categories: string[]; icon: React.ElementType }
> = {
  "Main Dishes": {
    categories: ["Main Courses", "Grilled Dishes", "Seafood", "Pasta"],
    icon: Utensils,
  },
  Starters: { categories: ["Appetizers", "Soups", "Salads"], icon: Salad },
  "Quick Bites": {
    categories: ["Sandwiches", "Burgers", "Pizza"],
    icon: Sandwich,
  },
  "Healthy Options": {
    categories: ["Vegetarian", "Vegan", "Side Dishes"],
    icon: Leaf,
  },
  Drinks: {
    categories: ["Beverages", "Hot Drinks", "Cold Drinks"],
    icon: Coffee,
  },
  "Special Menus": {
    categories: ["Breakfast", "Kids Menu", "Special Menu"],
    icon: Pizza,
  },
  Desserts: { categories: ["Desserts"], icon: IceCream },
};

// Interface for cart items
interface CartItem {
  item: MenuItem;
  quantity: number;
  notes?: string;
}

// Analytics tracking function
const trackMenuItemClick = async (
  restaurantId: string,
  itemId: string,
  action: string
) => {
  await menuAnalyticsService.trackMenuItemInteraction(
    restaurantId,
    itemId,
    action,
    "qr_code_menu"
  );
};

export const QRCodeMenu = () => {
  const { username } = useParams<{ username: string }>();
  const navigate = useNavigate();
  const { user } = useAuth();
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [cartItemCount, setCartItemCount] = useState(0);
  const [selectedItem, setSelectedItem] = useState<MenuItem | null>(null);
  const [isItemDialogOpen, setIsItemDialogOpen] = useState(false);
  const [specialOffers, setSpecialOffers] = useState<MenuItem[]>([]);
  const [popularItems, setPopularItems] = useState<MenuItem[]>([]);
  const [chefRecommendations, setChefRecommendations] = useState<MenuItem[]>(
    []
  );
  const [limitedTimeOffers, setLimitedTimeOffers] = useState<MenuItem[]>([]);
  const menuRef = useRef<HTMLDivElement>(null);

  // Fetch restaurant data
  const {
    data: restaurant,
    isLoading: isRestaurantLoading,
    error: restaurantError,
  } = useRestaurantByUsername(username);

  // Fetch menu data
  const {
    menu,
    isLoading: isMenuLoading,
    error: menuError,
  } = useRealtimeMenu(restaurant?.id);

  // Fetch user's loyalty status
  const { data: loyaltyStatus } = useLoyaltyStatus(user?.uid);

  // Load cart from localStorage
  useEffect(() => {
    if (!restaurant?.id) return;

    const storedCart = localStorage.getItem(`cart_${restaurant.id}`);
    if (storedCart) {
      try {
        const cartItems = JSON.parse(storedCart);
        const count = cartItems.reduce(
          (total: number, item: CartItem) => total + (item.quantity || 0),
          0
        );
        setCartItemCount(count);
      } catch (error) {
        console.error("Error parsing cart data:", error);
        setCartItemCount(0);
      }
    } else {
      setCartItemCount(0);
    }

    // Set up a storage event listener to update cart count when it changes
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === `cart_${restaurant.id}`) {
        if (e.newValue) {
          try {
            const cartItems = JSON.parse(e.newValue);
            const count = cartItems.reduce(
              (total: number, item: CartItem) => total + (item.quantity || 0),
              0
            );
            setCartItemCount(count);
          } catch (error) {
            console.error("Error parsing cart data from storage event:", error);
            setCartItemCount(0);
          }
        } else {
          setCartItemCount(0);
        }
      }
    };

    window.addEventListener("storage", handleStorageChange);
    return () => window.removeEventListener("storage", handleStorageChange);
  }, [restaurant?.id]);

  // Process menu data to get admin-selected promotions
  useEffect(() => {
    if (!menu || menu.length === 0) return;

    // Get admin-selected special offers
    const offers = menu.filter((item) => item.available && item.isSpecialOffer);
    setSpecialOffers(offers);

    // Get admin-selected popular items
    const popular = menu.filter((item) => item.available && item.isPopular);
    setPopularItems(popular);

    // Get admin-selected chef recommendations
    const recommendations = menu.filter(
      (item) => item.available && item.isChefRecommendation
    );
    setChefRecommendations(recommendations);

    // Get admin-selected limited time offers
    const limitedOffers = menu.filter(
      (item) => item.available && item.isLimitedTimeOffer
    );
    setLimitedTimeOffers(limitedOffers);
  }, [menu]);

  // Add to cart function
  const addToCart = (item: MenuItem) => {
    if (!restaurant?.id) return;

    trackMenuItemClick(restaurant.id, item.itemId, "add_to_cart");

    const storedCart = localStorage.getItem(`cart_${restaurant.id}`);
    let cart: CartItem[] = [];

    if (storedCart) {
      try {
        cart = JSON.parse(storedCart);
      } catch (error) {
        console.error("Error parsing cart data:", error);
      }
    }

    // Check if item already exists in cart
    const existingItemIndex = cart.findIndex(
      (cartItem) =>
        cartItem.item.itemId === item.itemId || cartItem.item.id === item.id
    );

    if (existingItemIndex !== -1) {
      // Update quantity if item exists
      cart[existingItemIndex].quantity += 1;
    } else {
      // Add new item to cart
      cart.push({ item, quantity: 1 });
    }

    localStorage.setItem(`cart_${restaurant.id}`, JSON.stringify(cart));
    setCartItemCount(cart.reduce((total, item) => total + item.quantity, 0));

    toast.success("Added to cart", {
      description: `${item.name} has been added to your cart.`,
      action: {
        label: "View Cart",
        onClick: () => navigate(`/restaurants/${username}/cart`),
      },
    });
  };

  // Handle item click to show details
  const handleItemClick = (item: MenuItem) => {
    if (restaurant?.id) {
      trackMenuItemClick(restaurant.id, item.itemId, "view_details");
    }
    setSelectedItem(item);
    setIsItemDialogOpen(true);
  };

  // Filter menu items based on search query and category
  const filteredMenu = menu?.filter((item) => {
    const matchesSearch =
      searchQuery === "" ||
      item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (item.description &&
        item.description.toLowerCase().includes(searchQuery.toLowerCase()));

    const matchesCategory =
      !selectedCategory || item.category === selectedCategory;

    return matchesSearch && matchesCategory;
  });

  // Group menu items by category
  const menuByCategory = useMemo(() => {
    if (!filteredMenu) return {};

    return filteredMenu.reduce((acc, item) => {
      const category = item.category || "Uncategorized";
      if (!acc[category]) {
        acc[category] = [];
      }
      acc[category].push(item);
      return acc;
    }, {} as Record<string, MenuItem[]>);
  }, [filteredMenu]);

  // Get all unique categories from the menu
  const categories = useMemo(() => {
    if (!menu) return [];

    const uniqueCategories = new Set<string>();
    menu.forEach((item) => {
      if (item.category) {
        uniqueCategories.add(item.category);
      }
    });

    return Array.from(uniqueCategories);
  }, [menu]);

  if (isRestaurantLoading || isMenuLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-muted-foreground">Loading menu...</p>
        </div>
      </div>
    );
  }

  if (restaurantError || menuError || !restaurant) {
    return (
      <div className="flex justify-center items-center min-h-screen p-4">
        <div className="text-center max-w-md">
          <AlertCircle className="h-12 w-12 text-destructive mx-auto" />
          <h2 className="text-xl font-bold mt-4">Menu Not Available</h2>
          <p className="mt-2 text-muted-foreground">
            We couldn't load the menu for this restaurant. Please try again
            later.
          </p>
          <Button className="mt-4" onClick={() => window.location.reload()}>
            Retry
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-screen-xl mx-auto px-4 py-6 pb-24">
      <MetaTags
        title={`${restaurant.restaurantName} Menu | Qonai Food Ordering`}
        description={`Browse the menu for ${restaurant.restaurantName}. Order food online for delivery or pickup.`}
        imageUrl={restaurant.imageUrl}
        type="restaurant"
      />

      {/* Restaurant Header */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-3">
            {restaurant.imageUrl && (
              <img
                src={restaurant.imageUrl}
                alt={restaurant.restaurantName}
                className="w-16 h-16 rounded-full object-cover border-2 border-primary"
              />
            )}
            <div>
              <h1 className="text-2xl font-bold">
                {restaurant.restaurantName}
              </h1>
              <p className="text-muted-foreground text-sm">
                {restaurant.cuisines?.join(", ")}
              </p>
            </div>
          </div>

          {/* Points Balance */}
          {user && loyaltyStatus && (
            <PointsBalance
              points={loyaltyStatus.totalPoints}
              variant="outline"
              size="default"
            />
          )}
        </div>
      </div>

      {/* Search Bar */}
      <div className="relative mb-6">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
        <Input
          type="text"
          placeholder="Search menu items..."
          className="pl-10 pr-4 py-2 w-full"
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
        />
        {searchQuery && (
          <button
            className="absolute right-3 top-1/2 transform -translate-y-1/2"
            onClick={() => setSearchQuery("")}
          >
            <X className="h-4 w-4 text-muted-foreground" />
          </button>
        )}
      </div>

      {/* Menu Content */}
      <Tabs defaultValue="featured" className="w-full">
        <TabsList className="grid grid-cols-2 mb-6">
          <TabsTrigger value="featured">Featured</TabsTrigger>
          <TabsTrigger value="full-menu">Full Menu</TabsTrigger>
        </TabsList>

        {/* Featured Tab */}
        <TabsContent value="featured" className="space-y-8">
          {/* Restaurant Rewards */}
          {restaurant?.id && (
            <RestaurantRewards
              restaurantId={restaurant.id}
              userPoints={loyaltyStatus?.totalPoints || 0}
              onRewardSelect={(rewardId) => {
                if (user) {
                  navigate(`/loyalty?redeem=${rewardId}`);
                } else {
                  toast.info("Please login to redeem rewards");
                }
              }}
            />
          )}

          {/* Special Offers */}
          {specialOffers.length > 0 && (
            <section>
              <h2 className="text-xl font-bold mb-4 flex items-center gap-2 text-primary">
                <Sparkles className="h-5 w-5" />
                Special Offers
              </h2>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-5 sm:gap-6">
                {specialOffers.map((item) => (
                  <SpecialOfferCard
                    key={item.itemId}
                    item={item}
                    onAddToCart={() => addToCart(item)}
                    onClick={() => handleItemClick(item)}
                  />
                ))}
              </div>
            </section>
          )}

          {/* Most Popular */}
          {popularItems.length > 0 && (
            <section>
              <h2 className="text-xl font-bold mb-4 flex items-center gap-2 text-primary">
                <Award className="h-5 w-5" />
                Most Popular
              </h2>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-5 sm:gap-6">
                {popularItems.map((item) => (
                  <PopularItemCard
                    key={item.itemId}
                    item={item}
                    onAddToCart={() => addToCart(item)}
                    onClick={() => handleItemClick(item)}
                  />
                ))}
              </div>
            </section>
          )}

          {/* Chef's Recommendations */}
          {chefRecommendations.length > 0 && (
            <section>
              <h2 className="text-xl font-bold mb-4 flex items-center gap-2 text-primary">
                <Utensils className="h-5 w-5" />
                Chef's Recommendations
              </h2>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-5 sm:gap-6">
                {chefRecommendations.map((item) => (
                  <RecommendationCard
                    key={item.itemId}
                    item={item}
                    onAddToCart={() => addToCart(item)}
                    onClick={() => handleItemClick(item)}
                  />
                ))}
              </div>
            </section>
          )}

          {/* Limited Time Offers */}
          {limitedTimeOffers.length > 0 && (
            <section>
              <h2 className="text-xl font-bold mb-4 flex items-center gap-2 text-primary">
                <Timer className="h-5 w-5" />
                Limited Time Offers
              </h2>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-5 sm:gap-6">
                {limitedTimeOffers.map((item) => (
                  <LimitedTimeOfferCard
                    key={item.itemId}
                    item={item}
                    onAddToCart={() => addToCart(item)}
                    onClick={() => handleItemClick(item)}
                  />
                ))}
              </div>
            </section>
          )}
        </TabsContent>

        {/* Full Menu Tab */}
        <TabsContent value="full-menu" className="space-y-6">
          {/* Category Filter */}
          <ScrollArea className="w-full whitespace-nowrap pb-2">
            <div className="flex space-x-2">
              <Button
                variant={selectedCategory === null ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedCategory(null)}
                className="flex-shrink-0"
              >
                All
              </Button>
              {categories.map((category) => (
                <Button
                  key={category}
                  variant={
                    selectedCategory === category ? "default" : "outline"
                  }
                  size="sm"
                  onClick={() => setSelectedCategory(category)}
                  className="flex-shrink-0"
                >
                  {category}
                </Button>
              ))}
            </div>
          </ScrollArea>

          {/* Menu Items by Category */}
          <div className="space-y-8" ref={menuRef}>
            {Object.entries(menuByCategory).map(([category, items]) => {
              if (items.length === 0) return null;

              // Find the category group this category belongs to
              const categoryGroup = Object.entries(CATEGORY_GROUPS).find(
                ([, group]) => group.categories.includes(category)
              );

              const Icon = categoryGroup ? categoryGroup[1].icon : Cake;

              return (
                <div
                  key={category}
                  id={category.toLowerCase().replace(/\s+/g, "-")}
                  className="scroll-mt-24"
                >
                  <h3 className="text-xl font-semibold mb-4 capitalize flex items-center gap-2">
                    <Icon className="h-5 w-5 text-primary" />
                    {category}
                  </h3>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-5 sm:gap-6">
                    {items.map((item) => (
                      <MenuItemCard
                        key={item.itemId}
                        item={item}
                        onAddToCart={() => addToCart(item)}
                        onClick={() => handleItemClick(item)}
                      />
                    ))}
                  </div>
                </div>
              );
            })}
          </div>
        </TabsContent>
      </Tabs>

      {/* Item Detail Dialog */}
      {selectedItem && (
        <MenuItemDialog
          item={selectedItem}
          open={isItemDialogOpen}
          onOpenChange={setIsItemDialogOpen}
          onAddToCart={() => addToCart(selectedItem)}
        />
      )}

      {/* Floating Cart Button */}
      <FloatingCartButton
        cartItemCount={cartItemCount}
        username={username || ""}
      />
    </div>
  );
};

// Floating Cart Button Component
interface FloatingCartButtonProps {
  cartItemCount: number;
  username: string;
}

const FloatingCartButton = ({
  cartItemCount,
  username,
}: FloatingCartButtonProps) => {
  const navigate = useNavigate();
  const [isAnimating, setIsAnimating] = useState(false);

  // Different styles based on whether the cart has items
  const hasItems = cartItemCount > 0;

  // Add animation effect when cart count changes
  useEffect(() => {
    if (cartItemCount > 0) {
      setIsAnimating(true);
      const timer = setTimeout(() => setIsAnimating(false), 500);
      return () => clearTimeout(timer);
    }
  }, [cartItemCount]);

  return (
    <div className="fixed bottom-4 sm:bottom-6 right-4 sm:right-6 z-50">
      <Button
        onClick={() => navigate(`/restaurants/${username}/cart`)}
        className={`h-12 sm:h-14 px-4 sm:px-5 rounded-full shadow-lg transition-all duration-300 hover:shadow-xl hover:scale-105 ${
          hasItems
            ? "bg-primary text-primary-foreground"
            : "bg-card border border-border hover:bg-accent"
        } ${isAnimating ? "animate-bounce" : ""}`}
        aria-label="View Cart"
      >
        <ShoppingCart
          className={`h-4 sm:h-5 w-4 sm:w-5 mr-1.5 sm:mr-2 ${
            !hasItems && "text-muted-foreground"
          }`}
        />
        <span
          className={`text-sm sm:text-base font-medium ${
            !hasItems && "text-muted-foreground"
          }`}
        >
          {hasItems ? (
            <>
              <span className="hidden xs:inline">View Cart</span>
              <span className="xs:hidden">Cart</span>
              <span className="ml-1 font-bold">({cartItemCount})</span>
            </>
          ) : (
            <>
              <span className="hidden xs:inline">View Cart</span>
              <span className="xs:hidden">Cart</span>
            </>
          )}
        </span>
      </Button>
    </div>
  );
};
