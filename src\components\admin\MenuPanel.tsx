import { useState } from "react";
import { Loader2, Award, Sparkles, Utensils, Timer } from "lucide-react";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { MenuItemDialog } from "./MenuItemDialog";
import { MenuItem } from "@/types/menu";

interface MenuPanelProps {
  menuItems: MenuItem[];
  isTableLoading: boolean;
  onAddItem: (item: Omit<MenuItem, "itemId" | "restaurantId">) => Promise<void>;
  onUpdateItem: (item: MenuItem) => Promise<void>;
  onDeleteItem: (itemId: string) => Promise<void>;
}

export const MenuPanel = ({
  menuItems,
  isTableLoading,
  onAddItem,
  onUpdateItem,
  onDeleteItem,
}: MenuPanelProps) => {
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [selectedItem, setSelectedItem] = useState<MenuItem | null>(null);
  const [isAddLoading, setIsAddLoading] = useState(false);
  const [isUpdateLoading, setIsUpdateLoading] = useState(false);
  const [isDeleteLoading, setIsDeleteLoading] = useState(false);

  const handleAddItem = async (
    item: Omit<MenuItem, "itemId" | "restaurantId">
  ) => {
    setIsAddLoading(true);
    try {
      await onAddItem(item);
      setIsAddDialogOpen(false);
    } catch (error) {
      console.error("Error adding item:", error);
    } finally {
      setIsAddLoading(false);
    }
  };

  const handleUpdateItem = async (
    item: Omit<MenuItem, "itemId" | "restaurantId">
  ) => {
    setIsUpdateLoading(true);
    try {
      if (!selectedItem) return;
      await onUpdateItem({
        ...item,
        itemId: selectedItem.itemId,
        restaurantId: selectedItem.restaurantId,
      });
      setIsEditDialogOpen(false);
    } catch (error) {
      console.error("Error updating item:", error);
    } finally {
      setIsUpdateLoading(false);
    }
  };

  const handleDeleteItem = async (itemId: string) => {
    setIsDeleteLoading(true);
    try {
      await onDeleteItem(itemId);
    } catch (error) {
      console.error("Error deleting item:", error);
    } finally {
      setIsDeleteLoading(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Menu Items</CardTitle>
        <CardDescription>Add, update, and delete menu items.</CardDescription>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Category</TableHead>
              <TableHead>Price</TableHead>
              <TableHead>Available</TableHead>
              <TableHead>Promotions</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isTableLoading ? (
              <TableRow>
                <TableCell colSpan={6} className="h-24 text-center">
                  <div className="flex items-center justify-center">
                    <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                  </div>
                </TableCell>
              </TableRow>
            ) : menuItems.length === 0 ? (
              <TableRow>
                <TableCell
                  colSpan={6}
                  className="h-24 text-center text-muted-foreground"
                >
                  No menu items found
                </TableCell>
              </TableRow>
            ) : (
              menuItems.map((item) => (
                <TableRow key={item.itemId}>
                  <TableCell>{item.name}</TableCell>
                  <TableCell>{item.category}</TableCell>
                  <TableCell>{item.price} AZN</TableCell>
                  <TableCell>
                    {item.available ? (
                      <span className="text-green-500">Yes</span>
                    ) : (
                      <span className="text-red-500">No</span>
                    )}
                  </TableCell>
                  <TableCell>
                    <div className="flex flex-wrap gap-1">
                      {item.isSpecialOffer && (
                        <Badge
                          variant="outline"
                          className="bg-orange-50 text-orange-600 border-orange-200 flex items-center gap-1"
                        >
                          <Sparkles className="h-3 w-3" />
                          <span className="text-[10px]">Special</span>
                        </Badge>
                      )}
                      {item.isPopular && (
                        <Badge
                          variant="outline"
                          className="bg-red-50 text-red-600 border-red-200 flex items-center gap-1"
                        >
                          <Award className="h-3 w-3" />
                          <span className="text-[10px]">Popular</span>
                        </Badge>
                      )}
                      {item.isChefRecommendation && (
                        <Badge
                          variant="outline"
                          className="bg-blue-50 text-blue-600 border-blue-200 flex items-center gap-1"
                        >
                          <Utensils className="h-3 w-3" />
                          <span className="text-[10px]">Chef's</span>
                        </Badge>
                      )}
                      {item.isLimitedTimeOffer && (
                        <Badge
                          variant="outline"
                          className="bg-red-50 text-red-600 border-red-200 flex items-center gap-1"
                        >
                          <Timer className="h-3 w-3" />
                          <span className="text-[10px]">Limited</span>
                        </Badge>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Button
                      variant="outline"
                      size="sm"
                      className="mr-2"
                      onClick={() => {
                        setSelectedItem(item);
                        setIsEditDialogOpen(true);
                      }}
                      disabled={isUpdateLoading || isDeleteLoading}
                    >
                      Edit
                    </Button>
                    <Button
                      variant="destructive"
                      size="sm"
                      onClick={() => handleDeleteItem(item.itemId)}
                      disabled={isDeleteLoading || isUpdateLoading}
                    >
                      {isDeleteLoading ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Deleting...
                        </>
                      ) : (
                        "Delete"
                      )}
                    </Button>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </CardContent>
      <CardFooter>
        <Button onClick={() => setIsAddDialogOpen(true)}>Add Menu Item</Button>
      </CardFooter>

      <MenuItemDialog
        mode="add"
        open={isAddDialogOpen}
        onOpenChange={setIsAddDialogOpen}
        onSubmit={handleAddItem}
        isLoading={isAddLoading}
      />

      {selectedItem && (
        <MenuItemDialog
          mode="edit"
          open={isEditDialogOpen}
          onOpenChange={setIsEditDialogOpen}
          onSubmit={handleUpdateItem}
          isLoading={isUpdateLoading}
          initialData={selectedItem}
        />
      )}
    </Card>
  );
};
