import { useState, useEffect, useCallback } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
// Button import removed as it's not used
// import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { InfoIcon, AlertTriangle, TrendingUp, BarChart4, LineChart, Calendar } from "lucide-react";
import {
  LineChart as ReLineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  BarChart,
  Bar,
  Area,
  AreaChart,
  ComposedChart
} from "recharts";
import {
  linearRegressionForecast,
  movingAverageForecast,
  weightedMovingAverageForecast,
  exponentialSmoothingForecast,
  seasonalForecast,
  generateBestForecast
} from "@/utils/forecastingService";
import { format, subDays, addDays, parseISO } from "date-fns";

// Colors are defined in the chart components directly

import { Order, Reservation } from "@/types/dashboard";

interface ForecastingTabProps {
  orders: Order[];
  reservations: Reservation[];
  parseOrderDate: (date: string | { seconds: number; nanoseconds: number } | Date | number | unknown) => Date | null;
  parseReservationDate: (date: string | { seconds: number; nanoseconds: number } | Date | number | unknown) => Date | null;
}

export const ForecastingTab = ({
  orders,
  reservations,
  parseOrderDate,
  parseReservationDate
}: ForecastingTabProps) => {
  // State for forecast settings
  const [forecastType, setForecastType] = useState<"orders" | "revenue" | "reservations">("orders");
  const [forecastPeriod, setForecastPeriod] = useState<"week" | "month" | "quarter">("week");
  const [forecastAlgorithm, setForecastAlgorithm] = useState<"auto" | "linear" | "moving" | "weighted" | "exponential" | "seasonal">("auto");

  // Define data point type
  interface DataPoint {
    date: string;
    value: number;
  }

  // State for forecast data
  const [historicalData, setHistoricalData] = useState<DataPoint[]>([]);
  const [forecastData, setForecastData] = useState<DataPoint[]>([]);
  const [confidence, setConfidence] = useState<number>(0);
  const [algorithmUsed, setAlgorithmUsed] = useState<string>("");
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState<boolean>(false);

  // Calculate days to forecast based on period
  const getForecastDays = useCallback(() => {
    switch (forecastPeriod) {
      case "week": return 7;
      case "month": return 30;
      case "quarter": return 90;
      default: return 7;
    }
  }, [forecastPeriod]);

  // Generate forecast based on historical data
  const generateForecast = useCallback((data: DataPoint[]) => {
    try {
      const forecastDays = getForecastDays();
      let forecastResult;

      switch (forecastAlgorithm) {
        case "linear":
          forecastResult = linearRegressionForecast(data, forecastDays);
          break;
        case "moving":
          forecastResult = movingAverageForecast(data, forecastDays);
          break;
        case "weighted":
          forecastResult = weightedMovingAverageForecast(data, forecastDays);
          break;
        case "exponential":
          forecastResult = exponentialSmoothingForecast(data, forecastDays);
          break;
        case "seasonal":
          forecastResult = seasonalForecast(data, forecastDays);
          break;
        case "auto":
        default:
          forecastResult = generateBestForecast(data, forecastDays);
          break;
      }

      setForecastData(forecastResult.data);
      setConfidence(forecastResult.confidence);
      setAlgorithmUsed(forecastResult.algorithm);
      setError(null);
    } catch (error) {
      console.error("Error generating forecast:", error);
      setError("Failed to generate forecast. Please try a different algorithm or time period.");
      setForecastData([]);
      setConfidence(0);
      setAlgorithmUsed("");
    }
  }, [forecastAlgorithm, getForecastDays]);

  // Process historical data based on forecast type
  const processHistoricalData = useCallback(() => {
    setError(null);
    setLoading(true);

    try {
      // Get last 90 days of data for analysis
      const endDate = new Date();
      const startDate = subDays(endDate, 90);

      let processedData: { date: string; value: number }[] = [];

      if (forecastType === "orders") {
        // Process order count data
        const ordersByDate = new Map<string, number>();

        orders.forEach(order => {
          const orderDate = parseOrderDate(order.orderDate);
          if (orderDate && orderDate >= startDate && orderDate <= endDate) {
            const dateStr = format(orderDate, "yyyy-MM-dd");
            ordersByDate.set(dateStr, (ordersByDate.get(dateStr) || 0) + 1);
          }
        });

        // Fill in missing dates with zero orders
        let currentDate = startDate;
        while (currentDate <= endDate) {
          const dateStr = format(currentDate, "yyyy-MM-dd");
          if (!ordersByDate.has(dateStr)) {
            ordersByDate.set(dateStr, 0);
          }
          currentDate = addDays(currentDate, 1);
        }

        // Convert to array and sort by date
        processedData = Array.from(ordersByDate.entries())
          .map(([date, count]) => ({ date, value: count }))
          .sort((a, b) => a.date.localeCompare(b.date));

      } else if (forecastType === "revenue") {
        // Process revenue data
        const revenueByDate = new Map<string, number>();

        orders.forEach(order => {
          const orderDate = parseOrderDate(order.orderDate);
          if (orderDate && orderDate >= startDate && orderDate <= endDate) {
            const dateStr = format(orderDate, "yyyy-MM-dd");
            revenueByDate.set(dateStr, (revenueByDate.get(dateStr) || 0) + order.totalPrice);
          }
        });

        // Fill in missing dates with zero revenue
        let currentDate = startDate;
        while (currentDate <= endDate) {
          const dateStr = format(currentDate, "yyyy-MM-dd");
          if (!revenueByDate.has(dateStr)) {
            revenueByDate.set(dateStr, 0);
          }
          currentDate = addDays(currentDate, 1);
        }

        // Convert to array and sort by date
        processedData = Array.from(revenueByDate.entries())
          .map(([date, revenue]) => ({ date, value: revenue }))
          .sort((a, b) => a.date.localeCompare(b.date));

      } else if (forecastType === "reservations") {
        // Process reservation count data
        const reservationsByDate = new Map<string, number>();

        reservations.forEach(reservation => {
          const reservationDate = parseReservationDate(reservation.date);
          if (reservationDate && reservationDate >= startDate && reservationDate <= endDate) {
            const dateStr = format(reservationDate, "yyyy-MM-dd");
            reservationsByDate.set(dateStr, (reservationsByDate.get(dateStr) || 0) + 1);
          }
        });

        // Fill in missing dates with zero reservations
        let currentDate = startDate;
        while (currentDate <= endDate) {
          const dateStr = format(currentDate, "yyyy-MM-dd");
          if (!reservationsByDate.has(dateStr)) {
            reservationsByDate.set(dateStr, 0);
          }
          currentDate = addDays(currentDate, 1);
        }

        // Convert to array and sort by date
        processedData = Array.from(reservationsByDate.entries())
          .map(([date, count]) => ({ date, value: count }))
          .sort((a, b) => a.date.localeCompare(b.date));
      }

      setHistoricalData(processedData);

      // Generate forecast if we have enough data
      if (processedData.length >= 7) {
        generateForecast(processedData);
      } else {
        setError("Insufficient historical data for forecasting. Need at least 7 days of data.");
        setForecastData([]);
        setConfidence(0);
        setAlgorithmUsed("");
      }
    } catch (error) {
      console.error("Error processing historical data:", error);
      setError("Failed to process historical data. Please try again.");
      setForecastData([]);
      setConfidence(0);
      setAlgorithmUsed("");
    } finally {
      setLoading(false);
    }
  }, [forecastType, orders, reservations, parseOrderDate, parseReservationDate, generateForecast]);

  // Update forecast when settings change
  useEffect(() => {
    processHistoricalData();
  }, [forecastType, forecastPeriod, forecastAlgorithm, processHistoricalData]);

  // Prepare combined data for charts (historical + forecast)
  const combinedChartData = [...historicalData.slice(-30), ...forecastData].map(item => ({
    ...item,
    date: format(parseISO(item.date), "MMM dd"),
    isForecast: historicalData.findIndex(h => h.date === item.date) === -1
  }));

  // Format y-axis values based on forecast type
  const formatYAxis = (value: number) => {
    if (forecastType === "revenue") {
      return `${value.toFixed(0)} AZN`;
    }
    return value.toFixed(0);
  };

  // Get title and description based on forecast type
  const getTitle = () => {
    switch (forecastType) {
      case "orders": return "Order Forecast";
      case "revenue": return "Revenue Forecast";
      case "reservations": return "Reservation Forecast";
      default: return "Forecast";
    }
  };

  const getDescription = () => {
    switch (forecastType) {
      case "orders": return "Predicted number of orders";
      case "revenue": return "Predicted revenue in AZN";
      case "reservations": return "Predicted number of reservations";
      default: return "Forecast";
    }
  };

  // Get confidence level color
  const getConfidenceColor = () => {
    if (confidence >= 80) return "text-green-500";
    if (confidence >= 60) return "text-yellow-500";
    return "text-orange-500";
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                {getTitle()}
              </CardTitle>
              <CardDescription>
                {getDescription()} for the next {forecastPeriod}
              </CardDescription>
            </div>

            <div className="flex flex-col sm:flex-row gap-2">
              <div className="flex items-center space-x-2">
                <Label htmlFor="forecast-type">Forecast</Label>
                <Select
                  value={forecastType}
                  onValueChange={(value) => setForecastType(value as "orders" | "revenue" | "reservations")}
                >
                  <SelectTrigger id="forecast-type" className="w-[140px]">
                    <SelectValue placeholder="Select type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="orders">Orders</SelectItem>
                    <SelectItem value="revenue">Revenue</SelectItem>
                    <SelectItem value="reservations">Reservations</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-center space-x-2">
                <Label htmlFor="forecast-period">Period</Label>
                <Select
                  value={forecastPeriod}
                  onValueChange={(value) => setForecastPeriod(value as "week" | "month" | "quarter")}
                >
                  <SelectTrigger id="forecast-period" className="w-[140px]">
                    <SelectValue placeholder="Select period" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="week">Next Week</SelectItem>
                    <SelectItem value="month">Next Month</SelectItem>
                    <SelectItem value="quarter">Next Quarter</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-center space-x-2">
                <Label htmlFor="forecast-algorithm">Algorithm</Label>
                <Select
                  value={forecastAlgorithm}
                  onValueChange={(value) => setForecastAlgorithm(value as "auto" | "linear" | "moving" | "weighted" | "exponential" | "seasonal")}
                >
                  <SelectTrigger id="forecast-algorithm" className="w-[160px]">
                    <SelectValue placeholder="Select algorithm" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="auto">Auto (Best Fit)</SelectItem>
                    <SelectItem value="linear">Linear Regression</SelectItem>
                    <SelectItem value="moving">Moving Average</SelectItem>
                    <SelectItem value="weighted">Weighted Average</SelectItem>
                    <SelectItem value="exponential">Exponential Smoothing</SelectItem>
                    <SelectItem value="seasonal">Seasonal</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
        </CardHeader>

        <CardContent>
          {error ? (
            <Alert variant="destructive" className="mb-4">
              <AlertTriangle className="h-4 w-4" />
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          ) : loading ? (
            <div className="flex items-center justify-center h-[400px]">
              <div className="flex flex-col items-center gap-2">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                <p className="text-sm text-muted-foreground">Generating forecast...</p>
              </div>
            </div>
          ) : (
            <>
              {confidence > 0 && (
                <div className="mb-4 flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-6 p-3 bg-muted/30 rounded-md">
                  <div className="flex items-center gap-2">
                    <InfoIcon className="h-4 w-4 text-blue-500" />
                    <span className="text-sm font-medium">Algorithm:</span>
                    <span className="text-sm">{algorithmUsed}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <TrendingUp className={`h-4 w-4 ${getConfidenceColor()}`} />
                    <span className="text-sm font-medium">Confidence:</span>
                    <span className={`text-sm font-medium ${getConfidenceColor()}`}>{confidence}%</span>
                  </div>
                  <div className="flex-1"></div>
                  <div className="text-xs text-muted-foreground">
                    Based on {historicalData.length} days of historical data
                  </div>
                </div>
              )}

              <Tabs defaultValue="line" className="w-full">
                <TabsList className="mb-4">
                  <TabsTrigger value="line" className="flex items-center gap-1">
                    <LineChart className="h-4 w-4" />
                    <span>Line</span>
                  </TabsTrigger>
                  <TabsTrigger value="area" className="flex items-center gap-1">
                    <BarChart4 className="h-4 w-4" />
                    <span>Area</span>
                  </TabsTrigger>
                  <TabsTrigger value="bar" className="flex items-center gap-1">
                    <BarChart4 className="h-4 w-4" />
                    <span>Bar</span>
                  </TabsTrigger>
                  <TabsTrigger value="combined" className="flex items-center gap-1">
                    <Calendar className="h-4 w-4" />
                    <span>Combined</span>
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="line" className="h-[400px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <ReLineChart
                      data={combinedChartData}
                      margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="date" />
                      <YAxis tickFormatter={formatYAxis} />
                      <Tooltip
                        formatter={(value) => [
                          forecastType === "revenue" ? `${value} AZN` : value,
                          forecastType === "orders" ? "Orders" :
                          forecastType === "revenue" ? "Revenue" : "Reservations"
                        ]}
                        labelFormatter={(label) => `Date: ${label}`}
                      />
                      <Legend />
                      <Line
                        type="monotone"
                        dataKey="value"
                        name="Historical"
                        stroke="#8884d8"
                        strokeWidth={2}
                        dot={{ r: 2 }}
                        activeDot={{ r: 6 }}
                        connectNulls
                      />
                      <Line
                        type="monotone"
                        dataKey="value"
                        name="Forecast"
                        stroke="#82ca9d"
                        strokeWidth={2}
                        strokeDasharray="5 5"
                        dot={{ r: 2 }}
                        activeDot={{ r: 6 }}
                        connectNulls
                      />
                    </ReLineChart>
                  </ResponsiveContainer>
                </TabsContent>

                <TabsContent value="area" className="h-[400px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <AreaChart
                      data={combinedChartData}
                      margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="date" />
                      <YAxis tickFormatter={formatYAxis} />
                      <Tooltip
                        formatter={(value) => [
                          forecastType === "revenue" ? `${value} AZN` : value,
                          forecastType === "orders" ? "Orders" :
                          forecastType === "revenue" ? "Revenue" : "Reservations"
                        ]}
                        labelFormatter={(label) => `Date: ${label}`}
                      />
                      <Legend />
                      <Area
                        type="monotone"
                        dataKey="value"
                        name="Historical"
                        stroke="#8884d8"
                        fill="#8884d8"
                        fillOpacity={0.3}
                        strokeWidth={2}
                        dot={{ r: 2 }}
                        activeDot={{ r: 6 }}
                        connectNulls
                      />
                      <Area
                        type="monotone"
                        dataKey="value"
                        name="Forecast"
                        stroke="#82ca9d"
                        fill="#82ca9d"
                        fillOpacity={0.3}
                        strokeWidth={2}
                        strokeDasharray="5 5"
                        dot={{ r: 2 }}
                        activeDot={{ r: 6 }}
                        connectNulls
                      />
                    </AreaChart>
                  </ResponsiveContainer>
                </TabsContent>

                <TabsContent value="bar" className="h-[400px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={combinedChartData}
                      margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="date" />
                      <YAxis tickFormatter={formatYAxis} />
                      <Tooltip
                        formatter={(value) => [
                          forecastType === "revenue" ? `${value} AZN` : value,
                          forecastType === "orders" ? "Orders" :
                          forecastType === "revenue" ? "Revenue" : "Reservations"
                        ]}
                        labelFormatter={(label) => `Date: ${label}`}
                      />
                      <Legend />
                      <Bar
                        dataKey="value"
                        name="Historical"
                        fill="#8884d8"
                        radius={[4, 4, 0, 0]}
                      />
                      <Bar
                        dataKey="value"
                        name="Forecast"
                        fill="#82ca9d"
                        radius={[4, 4, 0, 0]}
                        fillOpacity={0.8}
                      />
                    </BarChart>
                  </ResponsiveContainer>
                </TabsContent>

                <TabsContent value="combined" className="h-[400px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <ComposedChart
                      data={combinedChartData}
                      margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="date" />
                      <YAxis tickFormatter={formatYAxis} />
                      <Tooltip
                        formatter={(value) => [
                          forecastType === "revenue" ? `${value} AZN` : value,
                          forecastType === "orders" ? "Orders" :
                          forecastType === "revenue" ? "Revenue" : "Reservations"
                        ]}
                        labelFormatter={(label) => `Date: ${label}`}
                      />
                      <Legend />
                      <Bar
                        dataKey="value"
                        name="Historical"
                        fill="#8884d8"
                        radius={[4, 4, 0, 0]}
                        barSize={20}
                      />
                      <Line
                        type="monotone"
                        dataKey="value"
                        name="Forecast"
                        stroke="#ff7300"
                        strokeWidth={2}
                        dot={{ r: 3 }}
                        activeDot={{ r: 6 }}
                      />
                    </ComposedChart>
                  </ResponsiveContainer>
                </TabsContent>
              </Tabs>

              {forecastData.length > 0 && (
                <div className="mt-6">
                  <h3 className="text-lg font-medium mb-2">Forecast Summary</h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <Card>
                      <CardHeader className="py-3">
                        <CardTitle className="text-sm font-medium">Average {forecastType === "orders" ? "Orders" : forecastType === "revenue" ? "Revenue" : "Reservations"}</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold">
                          {forecastType === "revenue"
                            ? `${(forecastData.reduce((sum, item) => sum + item.value, 0) / forecastData.length).toFixed(2)} AZN`
                            : (forecastData.reduce((sum, item) => sum + item.value, 0) / forecastData.length).toFixed(1)}
                        </div>
                        <p className="text-xs text-muted-foreground">
                          Average per day over the forecast period
                        </p>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader className="py-3">
                        <CardTitle className="text-sm font-medium">Total {forecastType === "orders" ? "Orders" : forecastType === "revenue" ? "Revenue" : "Reservations"}</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold">
                          {forecastType === "revenue"
                            ? `${forecastData.reduce((sum, item) => sum + item.value, 0).toFixed(2)} AZN`
                            : forecastData.reduce((sum, item) => sum + item.value, 0).toFixed(0)}
                        </div>
                        <p className="text-xs text-muted-foreground">
                          Total over the forecast period
                        </p>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader className="py-3">
                        <CardTitle className="text-sm font-medium">Growth Trend</CardTitle>
                      </CardHeader>
                      <CardContent>
                        {(() => {
                          // Calculate growth trend
                          const firstValue = forecastData[0]?.value || 0;
                          const lastValue = forecastData[forecastData.length - 1]?.value || 0;
                          const growthPercent = firstValue > 0
                            ? ((lastValue - firstValue) / firstValue) * 100
                            : 0;

                          const isPositive = growthPercent >= 0;

                          return (
                            <>
                              <div className={`text-2xl font-bold ${isPositive ? 'text-green-500' : 'text-red-500'}`}>
                                {isPositive ? '+' : ''}{growthPercent.toFixed(1)}%
                              </div>
                              <p className="text-xs text-muted-foreground">
                                From beginning to end of forecast period
                              </p>
                            </>
                          );
                        })()}
                      </CardContent>
                    </Card>
                  </div>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Forecasting Insights</CardTitle>
          <CardDescription>
            Understanding the forecast and its implications
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <h3 className="text-lg font-medium mb-2">About the Forecast</h3>
              <p className="text-sm text-muted-foreground">
                This forecast uses {algorithmUsed || "statistical algorithms"} to predict future {forecastType} based on your historical data.
                The confidence level of {confidence || "N/A"}% indicates how reliable the forecast is expected to be.
              </p>
            </div>

            <div>
              <h3 className="text-lg font-medium mb-2">Business Implications</h3>
              <ul className="list-disc list-inside space-y-1 text-sm text-muted-foreground">
                <li>
                  {forecastType === "orders" && "Plan your staffing and inventory based on predicted order volumes."}
                  {forecastType === "revenue" && "Make financial decisions based on projected revenue streams."}
                  {forecastType === "reservations" && "Optimize table arrangements and staff scheduling based on expected reservations."}
                </li>
                <li>Identify potential peak periods and prepare accordingly.</li>
                <li>Use the forecast to set realistic goals and targets.</li>
                <li>Compare actual performance against forecasts to improve business operations.</li>
              </ul>
            </div>

            <div>
              <h3 className="text-lg font-medium mb-2">Forecast Limitations</h3>
              <ul className="list-disc list-inside space-y-1 text-sm text-muted-foreground">
                <li>Forecasts are based on historical patterns and may not account for unexpected events.</li>
                <li>The further into the future, the less accurate the forecast typically becomes.</li>
                <li>Seasonal patterns require sufficient historical data to be accurately predicted.</li>
                <li>Major changes in business operations or market conditions can affect forecast accuracy.</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
