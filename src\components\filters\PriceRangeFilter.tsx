import { ToggleGroup, ToggleGroupItem } from '@/components/ui/toggle-group';

interface PriceRangeFilterProps {
  value: string[];
  onChange: (value: string[]) => void;
  counts?: Record<string, number>;
}

export function PriceRangeFilter({ value, onChange, counts }: PriceRangeFilterProps) {
  // Handle toggle group value changes
  const handleValueChange = (newValue: string[]) => {
    onChange(newValue);
  };

  // Define price range options with actual price ranges
  const priceRangeOptions = [
    { value: "M", label: "Budget-friendly", display: "<15₼", priceRange: "Under 15₼" },
    { value: "MM", label: "Moderate", display: "15-30₼", priceRange: "15₼ to 30₼" },
    { value: "MMM", label: "Upscale", display: "30-50₼", priceRange: "30₼ to 50₼" },
    { value: "MMMM", label: "Fine dining", display: ">50₼", priceRange: "Over 50₼" },
  ];

  return (
    <div className="space-y-2">
      <p className="text-sm font-medium mb-2">Price Range</p>
      <ToggleGroup
        type="multiple"
        value={value}
        onValueChange={handleValueChange}
        className="justify-start"
      >
        {priceRangeOptions.map((price) => {
          const count = counts?.[price.value] || 0;
          return (
            <ToggleGroupItem
              key={price.value}
              value={price.value}
              aria-label={`Price range: ${price.priceRange}`}
              className="data-[state=on]:bg-primary data-[state=on]:text-primary-foreground"
              title={`${price.priceRange} (${count} restaurants)`}
              disabled={count === 0}
            >
              {price.display}
              {counts && <span className="ml-1 text-xs opacity-70">({count})</span>}
            </ToggleGroupItem>
          );
        })}
      </ToggleGroup>
      <div className="flex justify-between text-xs text-muted-foreground mt-1">
        <span>Budget</span>
        <span>Luxury</span>
      </div>
    </div>
  );
}
