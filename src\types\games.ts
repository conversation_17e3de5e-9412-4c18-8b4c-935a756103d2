import { Timestamp } from "firebase/firestore";

/**
 * Base interface for all mini-games
 */
export interface MiniGame {
  id: string;
  name: string;
  description: string;
  iconName: string;
  pointsPerWin: number;
  maxDailyPlays: number;
  isActive: boolean;
}

/**
 * Food Match game card interface
 */
export interface FoodMatchCard {
  id: string;
  name: string;
  emoji: string;
  pairId?: string;
}

/**
 * Trivia question interface
 */
export interface TriviaQuestion {
  id: string;
  question: string;
  options: string[];
  correctAnswer: number; // Index of the correct answer in options array
  difficulty: "easy" | "medium" | "hard";
  category:
    | "cuisines"
    | "ingredients"
    | "cooking"
    | "restaurants"
    | "nutrition";
  points: number; // Points awarded for correct answer
}

/**
 * Game play record interface
 */
export interface GamePlayRecord {
  id: string;
  userId: string;
  gameId: string;
  score: number;
  pointsEarned: number;
  playedAt: Timestamp;
  completed: boolean;
}

/**
 * Daily check-in record interface
 */
export interface CheckInRecord {
  id: string;
  userId: string;
  date: Timestamp;
  streak: number;
  pointsEarned: number;
}

/**
 * Game result interface for returning results to the UI
 */
export interface GameResult {
  success: boolean;
  pointsEarned: number;
  message: string;
  newTotal?: number;
  dailyLimit?: boolean;
  streakCount?: number;
}
