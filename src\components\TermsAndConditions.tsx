import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Di<PERSON>Trigger,
} from "@/components/ui/dialog";
import { ScrollArea } from "@/components/ui/scroll-area";

export function TermsAndConditions({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <Dialog modal={false}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="max-w-3xl max-h-[80vh]" hideCloseButton>
        <DialogHeader>
          <DialogTitle>Terms and Conditions</DialogTitle>
        </DialogHeader>
        <ScrollArea className="h-[60vh] pr-4">
          <div className="space-y-4">
            <h2 className="text-xl font-semibold">1. Acceptance of Terms</h2>
            <p>
              By accessing and using Qonai, you agree to be bound by these Terms
              and Conditions. If you do not agree to all these terms, you may
              not use our services.
            </p>

            <h2 className="text-xl font-semibold">2. User Accounts</h2>
            <p>
              2.1. You must provide accurate and complete information when
              creating an account.
            </p>
            <p>
              2.2. You are responsible for maintaining the confidentiality of
              your account credentials.
            </p>
            <p>
              2.3. You must immediately notify us of any unauthorized use of
              your account.
            </p>

            <h2 className="text-xl font-semibold">3. Restaurant Accounts</h2>
            <p>
              3.1. Restaurants must provide valid business information and
              maintain accurate menu items and prices.
            </p>
            <p>
              3.2. Restaurants are responsible for the quality and safety of
              food items.
            </p>
            <p>
              3.3. Restaurants must comply with all applicable food safety and
              health regulations.
            </p>

            <h2 className="text-xl font-semibold">4. Client Accounts</h2>
            <p>4.1. Clients must provide accurate delivery information.</p>
            <p>
              4.2. Clients are responsible for ensuring they are available to
              receive their orders.
            </p>

            <h2 className="text-xl font-semibold">5. Privacy</h2>
            <p>
              5.1. We collect and process personal data as described in our
              Privacy Policy.
            </p>
            <p>
              5.2. By using our services, you consent to our data practices.
            </p>

            <h2 className="text-xl font-semibold">6. Payments</h2>
            <p>
              6.1. All payments must be made through our approved payment
              systems.
            </p>
            <p>6.2. Prices are subject to change without notice.</p>

            <h2 className="text-xl font-semibold">7. Termination</h2>
            <p>
              7.1. We reserve the right to terminate or suspend accounts that
              violate these terms.
            </p>
            <p>
              7.2. Users may terminate their accounts at any time by contacting
              support.
            </p>

            <h2 className="text-xl font-semibold">8. Changes to Terms</h2>
            <p>8.1. We may modify these terms at any time without notice.</p>
            <p>
              8.2. Continued use of our services constitutes acceptance of
              modified terms.
            </p>

            <h2 className="text-xl font-semibold">
              9. Limitation of Liability
            </h2>
            <p>
              9.1. We are not liable for any indirect, incidental, or
              consequential damages.
            </p>
            <p>
              9.2. Our liability is limited to the amount paid for our services.
            </p>

            <h2 className="text-xl font-semibold">10. Contact</h2>
            <p>
              For questions about these Terms and Conditions, please contact our
              support team.
            </p>
          </div>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
}
