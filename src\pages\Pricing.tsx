import { useState } from "react";
import { Check } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { useNavigate } from "react-router-dom";
import { useAuth } from "@/providers/AuthProvider";
import { toast } from "sonner";
import { PayPalScriptProvider, PayPalButtons } from "@paypal/react-paypal-js";

const tier = {
  name: "Restaurant Plan",
  price: { monthly: 89, yearly: 890 },
  description:
    "Complete restaurant management solution with 2 months free trial",
  features: [
    "2 months free trial period",
    "Complete reservation management system",
    "Menu and inventory management",
    "Table management system",
    "Staff scheduling and management",
    "Customer feedback and reviews",
    "Advanced analytics and reporting",
    "Multi-device access",
    "24/7 customer support",
    "Regular feature updates",
  ],
};

export const Pricing = () => {
  const [isYearly, setIsYearly] = useState(false);
  const { user, userRole } = useAuth();
  const navigate = useNavigate();
  const [showPayPal, setShowPayPal] = useState(false);

  const handleSubscribe = () => {
    if (!user) {
      sessionStorage.setItem("subscriptionIntent", "true");
      navigate("/auth");
      return;
    }

    if (userRole === "client") {
      toast.error("Only restaurants can subscribe to this plan");
      return;
    }

    setShowPayPal(true);
  };

  const amount = isYearly ? tier.price.yearly : tier.price.monthly;

  return (
    <div className="py-12 sm:py-24">
      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        <div className="mx-auto max-w-4xl text-center">
          <h2 className="text-base font-semibold leading-7 text-indigo-600">
            Restaurant Subscription
          </h2>
          <p className="mt-2 text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl">
            Start managing your restaurant efficiently
          </p>
        </div>

        <div className="mt-6 flex justify-center">
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium">Monthly</span>
            <Switch
              checked={isYearly}
              onCheckedChange={setIsYearly}
              className="data-[state=checked]:bg-indigo-600"
            />
            <span className="text-sm font-medium">Yearly (2 months free)</span>
          </div>
        </div>

        <div className="mx-auto mt-16 max-w-lg">
          <div className="flex flex-col justify-between rounded-3xl bg-white p-8 ring-1 ring-gray-200 xl:p-10">
            <div>
              <div className="flex items-center justify-between gap-x-4">
                <h3 className="text-lg font-semibold leading-8 text-gray-900">
                  {tier.name}
                </h3>
              </div>
              <p className="mt-4 text-sm leading-6 text-gray-600">
                {tier.description}
              </p>
              <p className="mt-6 flex items-baseline gap-x-1">
                <span className="text-4xl font-bold tracking-tight text-gray-900">
                  {amount} AZN
                </span>
                <span className="text-sm font-semibold leading-6 text-gray-600">
                  /{isYearly ? "year" : "month"}
                </span>
              </p>
              <ul
                role="list"
                className="mt-8 space-y-3 text-sm leading-6 text-gray-600"
              >
                {tier.features.map((feature) => (
                  <li key={feature} className="flex gap-x-3">
                    <Check className="h-6 w-5 flex-none text-indigo-600" />
                    {feature}
                  </li>
                ))}
              </ul>
            </div>
            {!showPayPal ? (
              <Button
                onClick={handleSubscribe}
                className="mt-8 w-full bg-indigo-600 hover:bg-indigo-500"
              >
                Get started with free trial
              </Button>
            ) : (
              <div className="mt-8">
                <PayPalScriptProvider
                  options={{
                    clientId: import.meta.env.VITE_PAYPAL_CLIENT_ID,
                    currency: "USD",
                  }}
                >
                  <PayPalButtons
                    style={{ layout: "vertical" }}
                    createOrder={(_data, actions) => {
                      if (actions.order) {
                        return actions.order.create({
                          intent: "CAPTURE",
                          purchase_units: [
                            {
                              amount: {
                                currency_code: "USD",
                                value: (amount / 1.7).toFixed(2), // Converting AZN to USD (approximate)
                              },
                            },
                          ],
                        });
                      }
                      throw new Error("Failed to create order");
                    }}
                    onApprove={(_data, actions) => {
                      if (actions.order) {
                        return actions.order.capture().then(() => {
                          toast.success("Payment successful!");
                          navigate("/success");
                        });
                      }
                      throw new Error("Failed to capture order");
                    }}
                    onError={(err) => {
                      console.error("PayPal Checkout onError", err);
                      toast.error("Payment failed. Please try again.");
                      navigate("/failed");
                    }}
                  />
                </PayPalScriptProvider>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
