import React from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

import { Filter, X } from "lucide-react";
import { Reward } from "@/types/rewards";

export interface RewardFilters {
  type?: string[];
  pointsRange?: {
    min: number;
    max: number;
  };
  isGlobal?: boolean;
  hasRestaurant?: boolean;
  isExpiring?: boolean; // Expiring within 7 days
}

interface RewardFiltersProps {
  rewards: Array<
    Reward & { restaurantInfo?: { name: string; imageUrl?: string } }
  >;
  filters: RewardFilters;
  onFiltersChange: (filters: RewardFilters) => void;
}

export const RewardFiltersComponent: React.FC<RewardFiltersProps> = ({
  rewards,
  filters,
  onFiltersChange,
}) => {
  // Get unique reward types from available rewards
  const availableTypes = Array.from(
    new Set(rewards.map((reward) => reward.type).filter(<PERSON><PERSON>an))
  );

  // Get points range from available rewards
  const pointsValues = rewards.map((reward) => reward.pointsCost);
  const minPoints = Math.min(...pointsValues);
  const maxPoints = Math.max(...pointsValues);

  const handleTypeToggle = (type: string) => {
    const currentTypes = filters.type || [];
    const newTypes = currentTypes.includes(type)
      ? currentTypes.filter((t) => t !== type)
      : [...currentTypes, type];

    onFiltersChange({
      ...filters,
      type: newTypes.length > 0 ? newTypes : undefined,
    });
  };

  const handlePointsRangeChange = (min: number, max: number) => {
    // If the same range is clicked, deselect it
    if (filters.pointsRange?.min === min && filters.pointsRange?.max === max) {
      onFiltersChange({
        ...filters,
        pointsRange: undefined,
      });
    } else {
      onFiltersChange({
        ...filters,
        pointsRange: { min, max },
      });
    }
  };

  const handleSourceToggle = (source: "global" | "restaurant") => {
    if (source === "global") {
      onFiltersChange({
        ...filters,
        isGlobal: filters.isGlobal ? undefined : true,
        hasRestaurant: undefined,
      });
    } else {
      onFiltersChange({
        ...filters,
        hasRestaurant: filters.hasRestaurant ? undefined : true,
        isGlobal: undefined,
      });
    }
  };

  const handleExpiringToggle = () => {
    onFiltersChange({
      ...filters,
      isExpiring: filters.isExpiring ? undefined : true,
    });
  };

  const clearAllFilters = () => {
    onFiltersChange({});
  };

  const hasActiveFilters = Object.keys(filters).some(
    (key) => filters[key as keyof RewardFilters] !== undefined
  );

  const getTypeLabel = (type: string) => {
    switch (type) {
      case "discount":
        return "Discount";
      case "freeItem":
        return "Free Item";
      case "freeDelivery":
        return "Free Delivery";
      case "vipAccess":
        return "VIP Access";
      default:
        return type.charAt(0).toUpperCase() + type.slice(1);
    }
  };

  return (
    <Card>
      <CardHeader className="pb-4">
        <CardTitle className="flex items-center justify-between text-base">
          <div className="flex items-center">
            <Filter className="mr-2 h-4 w-4 text-primary" />
            Filters
          </div>
          {hasActiveFilters && (
            <Button
              variant="ghost"
              size="sm"
              onClick={clearAllFilters}
              className="text-xs text-muted-foreground hover:text-foreground h-6 px-2"
            >
              <X className="h-3 w-3 mr-1" />
              Clear All
            </Button>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {/* Reward Type Filter */}
          {availableTypes.length > 0 && (
            <div>
              <h4 className="text-sm font-medium mb-3 text-foreground">
                Reward Type
              </h4>
              <div className="flex flex-wrap gap-2">
                {availableTypes.map((type) => (
                  <Badge
                    key={type}
                    variant={
                      filters.type?.includes(type) ? "default" : "outline"
                    }
                    className="cursor-pointer hover:bg-primary/10 transition-colors text-xs px-3 py-1"
                    onClick={() => handleTypeToggle(type)}
                  >
                    {getTypeLabel(type)}
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {/* Points Range Filter */}
          <div>
            <h4 className="text-sm font-medium mb-3 text-foreground">
              Points Range
            </h4>
            <div className="flex flex-wrap gap-2">
              <Badge
                variant={
                  filters.pointsRange?.min === minPoints &&
                  filters.pointsRange?.max === Math.floor(maxPoints * 0.33)
                    ? "default"
                    : "outline"
                }
                className="cursor-pointer hover:bg-primary/10 transition-colors text-xs px-3 py-1"
                onClick={() =>
                  handlePointsRangeChange(
                    minPoints,
                    Math.floor(maxPoints * 0.33)
                  )
                }
              >
                Low ({minPoints}-{Math.floor(maxPoints * 0.33)} pts)
              </Badge>
              <Badge
                variant={
                  filters.pointsRange?.min === Math.floor(maxPoints * 0.33) &&
                  filters.pointsRange?.max === Math.floor(maxPoints * 0.66)
                    ? "default"
                    : "outline"
                }
                className="cursor-pointer hover:bg-primary/10 transition-colors text-xs px-3 py-1"
                onClick={() =>
                  handlePointsRangeChange(
                    Math.floor(maxPoints * 0.33),
                    Math.floor(maxPoints * 0.66)
                  )
                }
              >
                Medium ({Math.floor(maxPoints * 0.33)}-
                {Math.floor(maxPoints * 0.66)} pts)
              </Badge>
              <Badge
                variant={
                  filters.pointsRange?.min === Math.floor(maxPoints * 0.66) &&
                  filters.pointsRange?.max === maxPoints
                    ? "default"
                    : "outline"
                }
                className="cursor-pointer hover:bg-primary/10 transition-colors text-xs px-3 py-1"
                onClick={() =>
                  handlePointsRangeChange(
                    Math.floor(maxPoints * 0.66),
                    maxPoints
                  )
                }
              >
                High ({Math.floor(maxPoints * 0.66)}-{maxPoints} pts)
              </Badge>
            </div>
          </div>

          {/* Source Filter */}
          <div>
            <h4 className="text-sm font-medium mb-3 text-foreground">Source</h4>
            <div className="flex flex-wrap gap-2">
              <Badge
                variant={filters.isGlobal ? "default" : "outline"}
                className="cursor-pointer hover:bg-primary/10 transition-colors text-xs px-3 py-1"
                onClick={() => handleSourceToggle("global")}
              >
                Platform Rewards
              </Badge>
              <Badge
                variant={filters.hasRestaurant ? "default" : "outline"}
                className="cursor-pointer hover:bg-primary/10 transition-colors text-xs px-3 py-1"
                onClick={() => handleSourceToggle("restaurant")}
              >
                Restaurant Rewards
              </Badge>
            </div>
          </div>

          {/* Expiring Soon Filter */}
          <div>
            <h4 className="text-sm font-medium mb-3 text-foreground">
              Availability
            </h4>
            <Badge
              variant={filters.isExpiring ? "default" : "outline"}
              className="cursor-pointer hover:bg-primary/10 transition-colors text-xs px-3 py-1"
              onClick={handleExpiringToggle}
            >
              Expiring Soon (7 days)
            </Badge>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
