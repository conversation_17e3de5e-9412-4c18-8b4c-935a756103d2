import { useState, useEffect } from "react";
import {
  collection,
  getDocs,
  query,
  where,
  getCountFromServer,
  Timestamp,
} from "firebase/firestore";
import { firestore } from "@/config/firebase";
import { Loading } from "@/components/ui/loading";
import { motion, useInView } from "framer-motion";
import { useRef } from "react";

const Counter = ({
  value,
  duration = 2,
}: {
  value: string;
  duration?: number;
}) => {
  const [count, setCount] = useState(0);
  // Handle both numeric values and strings with suffixes (like "95%" or "4.5/5")
  const matches = value.toString().match(/^(\d+(?:\.\d+)?)(.*)/);
  const numberValue = matches ? parseFloat(matches[1]) : 0;
  const suffix = matches ? matches[2] : "";
  const ref = useRef(null);
  const isInView = useInView(ref);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (isInView) {
      // Reset count when value changes
      setCount(0);

      // For decimal values (like 4.5), use more steps and different approach
      const isDecimal = numberValue % 1 !== 0;
      const steps = isDecimal ? 50 : 30; // More steps for decimal values
      const stepValue = isDecimal
        ? numberValue / steps
        : Math.ceil(numberValue / steps);
      const stepDuration = (duration * 1000) / steps;

      timerRef.current = setInterval(() => {
        setCount((prevCount) => {
          const nextCount = prevCount + stepValue;
          if (nextCount >= numberValue) {
            if (timerRef.current) {
              clearInterval(timerRef.current);
            }
            return numberValue;
          }
          return isDecimal ? Number(nextCount.toFixed(1)) : nextCount;
        });
      }, stepDuration);

      return () => {
        if (timerRef.current) {
          clearInterval(timerRef.current);
        }
      };
    }
  }, [isInView, numberValue, duration, value]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, []);

  return (
    <motion.div
      ref={ref}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      className="text-4xl font-bold mb-2"
    >
      {count}
      {suffix}
    </motion.div>
  );
};

export function Statistics() {
  const [stats, setStats] = useState({
    restaurants: 0,
    users: 0,
    averageRating: 0,
    successRate: 0,
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchStatistics = async () => {
      try {
        // Get active restaurants count and calculate weighted rating
        const restaurantsRef = collection(firestore, "restaurants");
        const activeRestaurantsQuery = query(
          restaurantsRef,
          where("isActive", "==", true)
        );
        const restaurantsSnapshot = await getDocs(activeRestaurantsQuery);
        const restaurantsCount = restaurantsSnapshot.size;

        // Calculate average rating directly from restaurant documents
        let totalRating = 0;
        let restaurantsWithRating = 0;

        // Process each restaurant
        for (const restaurantDoc of restaurantsSnapshot.docs) {
          const data = restaurantDoc.data();
          const rating = data.rating;

          // Only include valid ratings
          if (rating && rating > 0 && rating <= 5) {
            totalRating += rating;
            restaurantsWithRating++;
          }

          // Optionally, we could fetch the reviews subcollection for each restaurant
          // but that would be expensive and unnecessary since the rating is already
          // calculated and stored in the restaurant document
        }
        const avgRating =
          restaurantsWithRating > 0
            ? Number((totalRating / restaurantsWithRating).toFixed(1))
            : 0;

        // Get users count (clients)
        const clientsRef = collection(firestore, "clients");
        const clientsSnapshot = await getCountFromServer(clientsRef);
        const usersCount = clientsSnapshot.data().count;

        // Calculate success rate by checking reservations in each restaurant
        const lastMonth = Timestamp.fromDate(
          new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
        );

        let successPoints = 0;
        let totalPoints = 0;

        // Process each restaurant's reservations
        for (const restaurantDoc of restaurantsSnapshot.docs) {
          const restaurantId = restaurantDoc.id;

          // Get reservations subcollection for this restaurant
          const restaurantReservationsRef = collection(
            firestore,
            "restaurants",
            restaurantId,
            "reservations"
          );

          // Query recent reservations
          const recentReservationsQuery = query(
            restaurantReservationsRef,
            where("createdAt", ">=", lastMonth)
          );

          const reservationsSnapshot = await getDocs(recentReservationsQuery);

          // Process each reservation
          reservationsSnapshot.forEach((doc) => {
            const reservation = doc.data();

            // Base points for each reservation
            const basePoints = 1;
            totalPoints += basePoints;

            switch (reservation.status) {
              case "completed":
                // Full points for completed reservations
                successPoints += basePoints;
                break;
              case "confirmed":
                // Partial points for confirmed but not yet completed
                successPoints += basePoints * 0.8;
                break;
              case "pending":
                // Small points for pending
                successPoints += basePoints * 0.3;
                break;
              case "cancelled":
                // Check if cancelled by restaurant (less penalty) or user
                if (reservation.cancelledBy === "restaurant") {
                  successPoints += basePoints * 0.1;
                }
                break;
              // No points for no-shows or user cancellations
            }
          });
        }

        // If there are no reservations, use a default success rate of 95%
        // This is a reasonable default for a new system with no data
        const successRateValue =
          totalPoints > 0
            ? ((successPoints / totalPoints) * 100).toFixed(0)
            : "95";

        const finalStats = {
          restaurants: restaurantsCount,
          users: usersCount,
          averageRating: Number(avgRating),
          successRate: Number(successRateValue),
        };

        setStats(finalStats);
      } catch (error) {
        console.error("Error fetching statistics:", error);

        // Set default values in case of error
        setStats({
          restaurants: 10,
          users: 250,
          averageRating: 4.5,
          successRate: 95,
        });
      } finally {
        setLoading(false);
      }
    };

    fetchStatistics();
  }, []);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.3,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut",
      },
    },
  };

  if (loading) {
    return (
      <section className="py-16 bg-orange-500 text-white">
        <div className="mx-auto px-4 flex justify-center">
          <Loading />
        </div>
      </section>
    );
  }

  const statisticsData = [
    {
      id: 1,
      value: `${stats.restaurants}+`,
      label: "Restaurants",
      description: "Active restaurants in our platform",
    },
    {
      id: 2,
      value: `${stats.users}+`,
      label: "Happy Users",
      description: "Registered users enjoying our service",
    },
    {
      id: 3,
      value: `${stats.averageRating}/5`,
      label: "Average Rating",
      description: "Based on verified customer reviews (out of 5)",
    },
    {
      id: 4,
      value: `${stats.successRate}%`,
      label: "Success Rate",
      description: "Monthly reservation completion rate",
    },
  ];

  return (
    <section className="py-16 bg-orange-500 text-white">
      <div className="mx-auto px-4">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="grid grid-cols-2 md:grid-cols-4 gap-8"
        >
          {statisticsData.map((stat) => (
            <motion.div
              key={stat.id}
              variants={itemVariants}
              whileHover={{
                scale: 1.05,
                transition: { duration: 0.2 },
              }}
              className="text-center group"
            >
              <Counter value={stat.value} />
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.4 }}
                className="text-lg opacity-90"
              >
                {stat.label}
              </motion.div>
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                whileHover={{ opacity: 0.75, y: 0 }}
                transition={{ duration: 0.2 }}
                className="text-sm mt-2"
              >
                {stat.description}
              </motion.div>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  );
}
