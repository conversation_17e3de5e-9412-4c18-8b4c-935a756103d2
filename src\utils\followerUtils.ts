import { firestore } from "@/config/firebase";
import { collection, query, getCountFromServer } from "firebase/firestore";

/**
 * Fetches the follower count for a restaurant
 * @param restaurantId The ID of the restaurant
 * @returns The number of followers
 */
export const getFollowerCount = async (restaurantId: string): Promise<number> => {
  try {
    const followersRef = collection(firestore, "restaurants", restaurantId, "followers");
    const snapshot = await getCountFromServer(query(followersRef));
    return snapshot.data().count;
  } catch (error) {
    console.error("Error fetching follower count:", error);
    return 0;
  }
};
