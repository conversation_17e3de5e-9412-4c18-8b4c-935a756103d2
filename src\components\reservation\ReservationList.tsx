import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Reservation } from "./types";

interface ReservationListProps {
  reservations: Reservation[];
  user: {
    id: string;
    email: string;
    name?: string;
  } | null;
  handleEditClick: (reservation: Reservation) => void;
  handleCancelReservation: (reservationId: string) => void;
}

const ReservationList = ({
  reservations,
  user,
  handleEditClick,
  handleCancelReservation,
}: ReservationListProps) => {
  if (!user) {
    return <p className="text-muted-foreground">Please login to view your reservations</p>;
  }

  if (reservations.length === 0) {
    return <p className="text-muted-foreground">No reservations found</p>;
  }

  return (
    <div className="space-y-4">
      {reservations.map((reservation) => (
        <Card key={reservation.id}>
          <CardContent className="pt-6">
            <div className="flex justify-between items-start">
              <div>
                <p className="font-medium">
                  {new Date(reservation.date).toLocaleDateString()} at{" "}
                  {reservation.arrivalTime}
                </p>
                <p className="text-sm text-muted-foreground">
                  {reservation.customerName} - {reservation.customerPhone}
                </p>
                <p className="text-sm text-muted-foreground">
                  Table: {reservation.tableName} - Party of {reservation.partySize}
                </p>
              </div>
              <div className="space-y-2">
                <span
                  className={`inline-block px-2 py-1 rounded-full text-xs ${
                    reservation.status === "confirmed"
                      ? "bg-green-100 text-green-800"
                      : reservation.status === "cancelled"
                      ? "bg-red-100 text-red-800"
                      : reservation.status === "active"
                      ? "bg-green-100 text-green-800 animate-pulse"
                      : reservation.status === "completed"
                      ? "bg-blue-100 text-blue-800"
                      : "bg-yellow-100 text-yellow-800"
                  }`}
                >
                  {reservation.status === "active"
                    ? "In Progress"
                    : reservation.status.charAt(0).toUpperCase() +
                      reservation.status.slice(1)}
                </span>
                {(reservation.status === "confirmed" ||
                  reservation.status === "pending") && (
                  <div className="flex flex-col gap-2 mt-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleEditClick(reservation)}
                    >
                      Edit
                    </Button>
                    <Button
                      variant="destructive"
                      size="sm"
                      onClick={() => handleCancelReservation(reservation.id)}
                    >
                      Cancel
                    </Button>
                  </div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

export default ReservationList;