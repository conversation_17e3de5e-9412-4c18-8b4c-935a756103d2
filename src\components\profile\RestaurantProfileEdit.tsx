import { RestaurantDetails } from "@/types";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { MultiSelect } from "@/components/ui/multi-select";
import { RESTAURANT_OPTIONS } from "@/types";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { WorkingHours } from "@/types";
import {
  to24HourFormat,
  to12HourFormat,
  calculateDuration,
} from "@/utils/timeUtils";
import { LANGUAGES } from "@/constants";
import { LocationPicker } from "./LocationPicker";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from "@/components/ui/command";
import { GeocodingResult } from "@/types";

interface RestaurantProfileEditProps {
  editData: RestaurantDetails;
  setEditData: (data: RestaurantDetails) => void;
  addressQuery: string;
  setAddressQuery: (query: string) => void;
  addressResults: GeocodingResult[];
  isLoadingAddress: boolean;
  mapPosition: [number, number] | null;
  setMapPosition: (position: [number, number] | null) => void;
  getAddressFromCoordinates: (
    lat: number,
    lng: number
  ) => Promise<string | null>;
}

export const RestaurantProfileEdit = ({
  editData,
  setEditData,
  addressQuery,
  setAddressQuery,
  addressResults,
  isLoadingAddress,
  mapPosition,
  setMapPosition,
  getAddressFromCoordinates,
}: RestaurantProfileEditProps) => {
  return (
    <div className="grid gap-4">
      <div className="grid grid-cols-4 items-center gap-4">
        <Label htmlFor="username" className="text-right">
          Username
        </Label>
        <Input
          id="username"
          value={editData?.username || ""}
          onChange={(e) =>
            setEditData({
              ...editData,
              username: e.target.value,
            })
          }
          className="col-span-3"
        />
      </div>

      <div className="grid grid-cols-4 items-center gap-4">
        <Label htmlFor="restaurantName" className="text-right">
          Restaurant Name
        </Label>
        <Input
          id="restaurantName"
          value={editData?.restaurantName || ""}
          onChange={(e) =>
            setEditData({
              ...editData,
              restaurantName: e.target.value,
            })
          }
          className="col-span-3"
        />
      </div>

      <div className="grid grid-cols-4 items-center gap-4">
        <Label htmlFor="cuisines" className="text-right">
          Cuisines
        </Label>
        <MultiSelect
          options={[...RESTAURANT_OPTIONS.cuisines].map((cuisine) => ({
            label: cuisine,
            value: cuisine,
          }))}
          selected={editData?.cuisines || []}
          onChange={(value) =>
            setEditData({
              ...editData,
              cuisines: value,
            })
          }
          placeholder="Select cuisines"
          className="col-span-3"
        />
      </div>

      <div className="grid grid-cols-4 items-center gap-4">
        <Label htmlFor="categories" className="text-right">
          Categories
        </Label>
        <MultiSelect
          options={[...RESTAURANT_OPTIONS.categories].map((category) => ({
            label: category,
            value: category,
          }))}
          selected={editData?.categories || []}
          onChange={(value) =>
            setEditData({
              ...editData,
              categories: value,
            })
          }
          placeholder="Select categories"
          className="col-span-3"
        />
      </div>

      <div className="grid grid-cols-4 items-center gap-4">
        <Label htmlFor="phone" className="text-right">
          Phone
        </Label>
        <Input
          id="phone"
          value={editData?.phone || ""}
          onChange={(e) =>
            setEditData({
              ...editData,
              phone: e.target.value,
            })
          }
          className="col-span-3"
        />
      </div>

      <div className="grid grid-cols-4 items-center gap-4">
        <Label htmlFor="description" className="text-right">
          Description
        </Label>
        <Textarea
          id="description"
          value={editData?.description || ""}
          onChange={(e) => {
            const words = e.target.value.trim().split(/\s+/).length;
            if (words <= 100) {
              setEditData({
                ...editData,
                description: e.target.value,
              });
            }
          }}
          placeholder="Describe your restaurant (max 100 words)"
          className="col-span-3 h-32"
        />
        <div className="col-span-4 text-right text-sm text-muted-foreground">
          {(editData?.description || "").trim().split(/\s+/).length}/100 words
        </div>
      </div>

      <div className="grid grid-cols-4 items-center gap-4">
        <Label htmlFor="imageUrl" className="text-right">
          Image URL
        </Label>
        <Input
          id="imageUrl"
          value={editData?.imageUrl || ""}
          onChange={(e) =>
            setEditData({
              ...editData,
              imageUrl: e.target.value,
            })
          }
          className="col-span-3"
        />
      </div>

      <div className="grid grid-cols-4 items-center gap-4">
        <Label htmlFor="address" className="text-right">
          Address
        </Label>
        <div className="col-span-3">
          <Popover>
            <PopoverTrigger asChild>
              <Textarea
                id="address"
                value={addressQuery || editData?.address || ""}
                onChange={(e) => {
                  setAddressQuery(e.target.value);
                  setEditData({
                    ...editData,
                    address: e.target.value,
                  });
                }}
                className="w-full"
                placeholder="Start typing to search address..."
              />
            </PopoverTrigger>
            <PopoverContent className="w-[400px] p-0 z-[9999]" align="start">
              <Command>
                <CommandInput placeholder="Search address..." />
                <CommandEmpty>
                  {isLoadingAddress ? "Searching..." : "No address found."}
                </CommandEmpty>
                <CommandGroup>
                  {addressResults.map((result) => (
                    <CommandItem
                      key={result.place_id}
                      onSelect={() => {
                        setAddressQuery(result.display_name);
                        setEditData({
                          ...editData,
                          address: result.display_name,
                          location: {
                            latitude: parseFloat(result.lat),
                            longitude: parseFloat(result.lon),
                          },
                        });
                      }}
                    >
                      {result.display_name}
                    </CommandItem>
                  ))}
                </CommandGroup>
              </Command>
            </PopoverContent>
          </Popover>
        </div>
      </div>

      <div className="grid grid-cols-4 items-center gap-4">
        <Label htmlFor="location" className="text-right">
          Location
        </Label>
        <div className="col-span-3">
          <p className="text-sm text-muted-foreground mb-2">
            Click on the map to set your restaurant's location, or search for an
            address.
          </p>
          <LocationPicker
            initialLocation={
              mapPosition
                ? {
                    latitude: mapPosition[0],
                    longitude: mapPosition[1],
                  }
                : undefined
            }
            onLocationSelect={async (lat, lng) => {
              setMapPosition([lat, lng]);
              const address = await getAddressFromCoordinates(lat, lng);
              if (address) {
                setAddressQuery(address);
                setEditData({
                  ...editData,
                  location: { latitude: lat, longitude: lng },
                  address: address,
                });
              }
            }}
          />
        </div>
      </div>

      <div className="grid grid-cols-4 items-start gap-4">
        <Label className="text-right mt-2">Working Hours</Label>
        <div className="col-span-3 space-y-4">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="autoUpdateStatus"
              checked={editData?.autoUpdateStatus}
              onCheckedChange={(checked: boolean) =>
                setEditData({
                  ...editData,
                  autoUpdateStatus: checked === true,
                })
              }
            />
            <Label htmlFor="autoUpdateStatus">
              Automatically update open status based on working hours
            </Label>
          </div>

          {[
            "monday",
            "tuesday",
            "wednesday",
            "thursday",
            "friday",
            "saturday",
            "sunday",
          ].map((day) => (
            <div key={day} className="flex items-center space-x-4">
              <div className="w-24 capitalize">{day}</div>
              <Checkbox
                checked={
                  editData?.workingHours?.find(
                    (h: WorkingHours) => h.day === day
                  )?.isOpen
                }
                onCheckedChange={(checked: boolean) => {
                  const workingHours = [...(editData?.workingHours || [])];
                  const dayIndex = workingHours.findIndex((h) => h.day === day);
                  if (dayIndex >= 0) {
                    workingHours[dayIndex] = {
                      ...workingHours[dayIndex],
                      isOpen: checked === true,
                    };
                  } else {
                    workingHours.push({
                      day: day as WorkingHours["day"],
                      isOpen: checked === true,
                      openTime: "09:00",
                      closeTime: "22:00",
                    });
                  }
                  setEditData({
                    ...editData,
                    workingHours,
                  });
                }}
              />
              <div className="flex space-x-2">
                <Input
                  type="time"
                  step="1800"
                  className="w-32"
                  value={
                    editData?.workingHours?.find(
                      (h: WorkingHours) => h.day === day
                    )?.openTime || "09:00"
                  }
                  onChange={(e) => {
                    const workingHours = [...(editData?.workingHours || [])];
                    const dayIndex = workingHours.findIndex(
                      (h) => h.day === day
                    );

                    const formattedTime = to24HourFormat(e.target.value);

                    if (dayIndex >= 0) {
                      workingHours[dayIndex] = {
                        ...workingHours[dayIndex],
                        openTime: formattedTime,
                      };
                    } else {
                      workingHours.push({
                        day: day as WorkingHours["day"],
                        isOpen: true,
                        openTime: formattedTime,
                        closeTime: "22:00",
                      });
                    }

                    setEditData({
                      ...editData,
                      workingHours: workingHours.sort((a, b) => {
                        const days = [
                          "monday",
                          "tuesday",
                          "wednesday",
                          "thursday",
                          "friday",
                          "saturday",
                          "sunday",
                        ];
                        return days.indexOf(a.day) - days.indexOf(b.day);
                      }),
                    });
                  }}
                />
                <span className="py-2">to</span>
                <Input
                  type="time"
                  step="1800"
                  className="w-32"
                  value={
                    editData?.workingHours?.find(
                      (h: WorkingHours) => h.day === day
                    )?.closeTime || "22:00"
                  }
                  onChange={(e) => {
                    const workingHours = [...(editData?.workingHours || [])];
                    const dayIndex = workingHours.findIndex(
                      (h) => h.day === day
                    );

                    const formattedTime = to24HourFormat(e.target.value);

                    if (dayIndex >= 0) {
                      workingHours[dayIndex] = {
                        ...workingHours[dayIndex],
                        closeTime: formattedTime,
                      };
                    } else {
                      workingHours.push({
                        day: day as WorkingHours["day"],
                        isOpen: true,
                        openTime: "09:00",
                        closeTime: formattedTime,
                      });
                    }

                    setEditData({
                      ...editData,
                      workingHours: workingHours.sort((a, b) => {
                        const days = [
                          "monday",
                          "tuesday",
                          "wednesday",
                          "thursday",
                          "friday",
                          "saturday",
                          "sunday",
                        ];
                        return days.indexOf(a.day) - days.indexOf(b.day);
                      }),
                    });
                  }}
                />
                {editData?.workingHours?.find(
                  (h: WorkingHours) => h.day === day
                )?.isOpen && (
                  <span className="py-2 text-sm text-gray-500">
                    (
                    {to12HourFormat(
                      editData?.workingHours?.find(
                        (h: WorkingHours) => h.day === day
                      )?.openTime || "09:00"
                    )}{" "}
                    to{" "}
                    {to12HourFormat(
                      editData?.workingHours?.find(
                        (h: WorkingHours) => h.day === day
                      )?.closeTime || "22:00"
                    )}{" "}
                    -{" "}
                    {calculateDuration(
                      editData?.workingHours?.find(
                        (h: WorkingHours) => h.day === day
                      )?.openTime || "09:00",
                      editData?.workingHours?.find(
                        (h: WorkingHours) => h.day === day
                      )?.closeTime || "22:00"
                    )}{" "}
                    hours)
                  </span>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>

      <div className="grid grid-cols-4 items-center gap-4">
        <Label htmlFor="isOpen">Open Status</Label>
        <Select
          value={editData?.isOpen ? "yes" : "no"}
          onValueChange={(value: string) =>
            setEditData({
              ...editData,
              isOpen: value === "yes",
            })
          }
        >
          <SelectTrigger className="col-span-3">
            <SelectValue placeholder="Is restaurant open?" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="yes">Open</SelectItem>
            <SelectItem value="no">Closed</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="grid grid-cols-4 items-center gap-4">
        <Label>Features</Label>
        <MultiSelect
          options={RESTAURANT_OPTIONS.features.map((feature: string) => ({
            label: feature,
            value: feature,
          }))}
          selected={editData?.features || []}
          onChange={(value) =>
            setEditData({
              ...editData,
              features: value,
            })
          }
          placeholder="Select features"
          className="col-span-3"
        />
      </div>

      <div className="grid grid-cols-4 items-center gap-4">
        <Label>Atmosphere</Label>
        <MultiSelect
          options={RESTAURANT_OPTIONS.atmosphere.map((atm: string) => ({
            label: atm,
            value: atm,
          }))}
          selected={editData?.atmosphere || []}
          onChange={(value) =>
            setEditData({
              ...editData,
              atmosphere: value,
            })
          }
          placeholder="Select atmosphere"
          className="col-span-3"
        />
      </div>

      <div className="grid grid-cols-4 items-center gap-4">
        <Label>Dietary Options</Label>
        <MultiSelect
          options={RESTAURANT_OPTIONS.dietary.map((diet: string) => ({
            label: diet,
            value: diet,
          }))}
          selected={editData?.dietary || []}
          onChange={(value) =>
            setEditData({
              ...editData,
              dietary: value,
            })
          }
          placeholder="Select dietary options"
          className="col-span-3"
        />
      </div>

      <div className="grid grid-cols-4 items-center gap-4">
        <Label>Services</Label>
        <MultiSelect
          options={RESTAURANT_OPTIONS.services.map((service: string) => ({
            label: service,
            value: service,
          }))}
          selected={editData?.services || []}
          onChange={(value) =>
            setEditData({
              ...editData,
              services: value,
            })
          }
          placeholder="Select services"
          className="col-span-3"
        />
      </div>

      <div className="grid grid-cols-4 items-center gap-4">
        <Label>Specialties</Label>
        <MultiSelect
          options={RESTAURANT_OPTIONS.specialties.map((specialty: string) => ({
            label: specialty,
            value: specialty,
          }))}
          selected={editData?.specialties || []}
          onChange={(value) =>
            setEditData({
              ...editData,
              specialties: value,
            })
          }
          placeholder="Select specialties"
          className="col-span-3"
        />
      </div>

      <div className="grid grid-cols-4 items-center gap-4">
        <Label>Payment Methods</Label>
        <MultiSelect
          options={RESTAURANT_OPTIONS.paymentMethods.map((method: string) => ({
            label: method,
            value: method,
          }))}
          selected={editData?.paymentMethods || []}
          onChange={(value) =>
            setEditData({
              ...editData,
              paymentMethods: value,
            })
          }
          placeholder="Select payment methods"
          className="col-span-3"
        />
      </div>

      <div className="grid grid-cols-4 items-center gap-4">
        <Label>Certifications</Label>
        <MultiSelect
          options={RESTAURANT_OPTIONS.certifications.map((cert: string) => ({
            label: cert,
            value: cert,
          }))}
          selected={editData?.certifications || []}
          onChange={(value) =>
            setEditData({
              ...editData,
              certifications: value,
            })
          }
          placeholder="Select certifications"
          className="col-span-3"
        />
      </div>

      <div className="grid grid-cols-4 items-center gap-4">
        <Label htmlFor="languages">Languages</Label>
        <MultiSelect
          options={LANGUAGES.map((lang) => ({
            label: lang,
            value: lang,
          }))}
          selected={editData?.languages || []}
          onChange={(value) =>
            setEditData({
              ...editData,
              languages: value,
            })
          }
          placeholder="Select languages"
          className="col-span-3"
        />
      </div>

      <div className="grid grid-cols-4 items-center gap-4">
        <Label>Noise Level</Label>
        <Select
          value={editData?.noiseLevel}
          onValueChange={(value: "quiet" | "moderate" | "loud") =>
            setEditData({
              ...editData,
              noiseLevel: value,
            })
          }
        >
          <SelectTrigger className="col-span-3">
            <SelectValue placeholder="Select noise level" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="quiet">Quiet</SelectItem>
            <SelectItem value="moderate">Moderate</SelectItem>
            <SelectItem value="loud">Loud</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="grid grid-cols-4 items-center gap-4">
        <Label>Dress Code</Label>
        <Select
          value={editData?.dressCode}
          onValueChange={(value: "casual" | "smart casual" | "formal") =>
            setEditData({
              ...editData,
              dressCode: value,
            })
          }
        >
          <SelectTrigger className="col-span-3">
            <SelectValue placeholder="Select dress code" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="casual">Casual</SelectItem>
            <SelectItem value="smart casual">Smart Casual</SelectItem>
            <SelectItem value="formal">Formal</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="grid grid-cols-4 items-center gap-4">
        <Label>Reservation Policy</Label>
        <Select
          value={editData?.reservationPolicy}
          onValueChange={(value: "required" | "recommended" | "not required") =>
            setEditData({
              ...editData,
              reservationPolicy: value,
            })
          }
        >
          <SelectTrigger className="col-span-3">
            <SelectValue placeholder="Select reservation policy" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="required">Required</SelectItem>
            <SelectItem value="recommended">Recommended</SelectItem>
            <SelectItem value="not required">Not Required</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="grid grid-cols-4 items-center gap-4">
        <Label>Parking Available</Label>
        <div className="col-span-3 flex items-center space-x-2">
          <Switch
            checked={editData?.parkingAvailable || false}
            onCheckedChange={(checked: boolean) =>
              setEditData({
                ...editData,
                parkingAvailable: checked,
              })
            }
          />
        </div>
      </div>

      <div className="space-y-4">
        <div className="grid grid-cols-4 items-center gap-4">
          <Label>Indoor Seating</Label>
          <Switch
            checked={editData?.seating?.indoor}
            onCheckedChange={(checked: boolean) =>
              setEditData({
                ...editData,
                seating: {
                  ...editData.seating,
                  indoor: checked,
                },
              })
            }
          />
        </div>

        <div className="grid grid-cols-4 items-center gap-4">
          <Label>Outdoor Seating</Label>
          <Switch
            checked={editData?.seating?.outdoor}
            onCheckedChange={(checked: boolean) =>
              setEditData({
                ...editData,
                seating: {
                  ...editData.seating,
                  outdoor: checked,
                },
              })
            }
          />
        </div>

        <div className="grid grid-cols-4 items-center gap-4">
          <Label>Total Seating Capacity</Label>
          <Input
            type="number"
            value={editData?.seating?.totalCapacity}
            onChange={(e) =>
              setEditData({
                ...editData,
                seating: {
                  ...editData.seating,
                  totalCapacity: parseInt(e.target.value, 10) || 0,
                },
              })
            }
            min="0"
            className="col-span-3"
          />
        </div>
      </div>
    </div>
  );
};
