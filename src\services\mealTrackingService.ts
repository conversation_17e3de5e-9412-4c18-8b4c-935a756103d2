import { firestore } from "@/config/firebase";
import {
  doc,
  collection,
  addDoc,
  updateDoc,
  getDoc,
  getDocs,
  query,
  where,
  orderBy,
  limit,
  Timestamp,
  serverTimestamp,
  deleteDoc,
  setDoc
} from "firebase/firestore";
import { Meal, MealLog, DietaryGoal } from "@/types";
import { v4 as uuidv4 } from 'uuid';
import { notificationService } from "@/services/NotificationService";

class MealTrackingService {
  // Add a new meal
  async addMeal(userId: string, meal: Omit<Meal, 'id'>): Promise<string | null> {
    try {
      const mealId = uuidv4();
      const mealWithId: Meal = {
        ...meal,
        id: mealId,
        date: meal.date || Timestamp.now()
      };

      // Add to meals collection
      const mealRef = doc(firestore, "clients", userId, "meals", mealId);
      await setDoc(mealRef, mealWithId);

      // Update meal log for the day
      await this.updateMealLog(userId, mealWithId);

      // Update dietary goals progress
      await this.updateGoalsProgress(userId, mealWithId);

      return mealId;
    } catch (error) {
      console.error("Error adding meal:", error);
      return null;
    }
  }

  // Update meal log for a specific day
  private async updateMealLog(userId: string, meal: Meal): Promise<void> {
    try {
      // Convert meal date to start of day for grouping
      const mealDate = meal.date instanceof Timestamp
        ? meal.date.toDate()
        : new Date(meal.date);

      mealDate.setHours(0, 0, 0, 0);
      const dayTimestamp = Timestamp.fromDate(mealDate);

      // Check if log exists for this day
      const logQuery = query(
        collection(firestore, "clients", userId, "mealLogs"),
        where("date", "==", dayTimestamp)
      );

      const logSnapshot = await getDocs(logQuery);

      if (logSnapshot.empty) {
        // Create new log for this day
        const newLog: MealLog = {
          meals: [meal],
          date: dayTimestamp,
          totalCalories: meal.calories || 0,
          totalProtein: meal.protein || 0,
          totalCarbs: meal.carbs || 0,
          totalFat: meal.fat || 0
        };

        await addDoc(collection(firestore, "clients", userId, "mealLogs"), newLog);
      } else {
        // Update existing log
        const logDoc = logSnapshot.docs[0];
        const existingLog = logDoc.data() as MealLog;

        const updatedLog: MealLog = {
          ...existingLog,
          meals: [...(existingLog.meals || []), meal],
          totalCalories: (existingLog.totalCalories || 0) + (meal.calories || 0),
          totalProtein: (existingLog.totalProtein || 0) + (meal.protein || 0),
          totalCarbs: (existingLog.totalCarbs || 0) + (meal.carbs || 0),
          totalFat: (existingLog.totalFat || 0) + (meal.fat || 0)
        };

        await setDoc(logDoc.ref, updatedLog);
      }
    } catch (error) {
      console.error("Error updating meal log:", error);
    }
  }

  // Update dietary goals progress based on a meal
  private async updateGoalsProgress(userId: string, meal: Meal): Promise<void> {
    try {
      // Get user's dietary goals
      const goalsQuery = query(
        collection(firestore, "clients", userId, "dietaryGoals"),
        where("trackingEnabled", "==", true)
      );

      const goalsSnapshot = await getDocs(goalsQuery);

      if (goalsSnapshot.empty) return;

      // Update each relevant goal
      for (const goalDoc of goalsSnapshot.docs) {
        const goal = goalDoc.data() as DietaryGoal;

        // Skip if goal is already completed
        if (goal.completed) continue;

        // Check if meal date is within goal period
        const mealDate = meal.date instanceof Timestamp ? meal.date.toDate() : new Date(meal.date);
        const startDate = goal.startDate instanceof Timestamp ? goal.startDate.toDate() : new Date(goal.startDate);
        const endDate = goal.endDate instanceof Timestamp
          ? goal.endDate.toDate()
          : goal.endDate
            ? new Date(goal.endDate)
            : new Date(8640000000000000); // Far future date if no end date

        if (mealDate < startDate || mealDate > endDate) continue;

        // Update progress based on goal type
        let newProgress = goal.progress;
        let goalAchieved = false;

        switch (goal.type) {
          case "calorie":
            newProgress += meal.calories || 0;
            break;
          case "protein":
            newProgress += meal.protein || 0;
            break;
          case "carbs":
            newProgress += meal.carbs || 0;
            break;
          case "fat":
            newProgress += meal.fat || 0;
            break;
          default:
            continue; // Skip other goal types
        }

        // Check if goal is achieved
        if (goal.target > 0 && newProgress >= goal.target) {
          goalAchieved = true;
        }

        // Update goal in database
        await updateDoc(goalDoc.ref, {
          progress: newProgress,
          completed: goalAchieved,
          lastUpdated: serverTimestamp()
        });

        // Send notification if goal achieved
        if (goalAchieved) {
          await notificationService.createDatabaseNotification(
            userId,
            "client",
            "goalAchieved",
            "Goal Achieved!",
            `Congratulations! You've reached your ${goal.type} goal of ${goal.target} ${goal.unit}.`
          );
        }
      }
    } catch (error) {
      console.error("Error updating goals progress:", error);
    }
  }

  // Get meal logs for a date range
  async getMealLogs(userId: string, startDate: Date, endDate: Date): Promise<MealLog[]> {
    try {
      const startTimestamp = Timestamp.fromDate(startDate);
      const endTimestamp = Timestamp.fromDate(endDate);

      const logsQuery = query(
        collection(firestore, "clients", userId, "mealLogs"),
        where("date", ">=", startTimestamp),
        where("date", "<=", endTimestamp),
        orderBy("date", "desc")
      );

      const logsSnapshot = await getDocs(logsQuery);

      return logsSnapshot.docs.map(doc => {
        const data = doc.data();
        return {
          id: doc.id,
          meals: data.meals || [],
          date: data.date,
          totalCalories: data.totalCalories || 0,
          totalProtein: data.totalProtein || 0,
          totalCarbs: data.totalCarbs || 0,
          totalFat: data.totalFat || 0,
          ...data
        } as MealLog;
      });
    } catch (error) {
      console.error("Error getting meal logs:", error);
      return [];
    }
  }

  // Get recent meals for quick adding
  async getRecentMeals(userId: string, count: number = 10): Promise<Meal[]> {
    try {
      const mealsQuery = query(
        collection(firestore, "clients", userId, "meals"),
        orderBy("date", "desc"),
        limit(count)
      );

      const mealsSnapshot = await getDocs(mealsQuery);

      return mealsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Meal[];
    } catch (error) {
      console.error("Error getting recent meals:", error);
      return [];
    }
  }

  // Delete a meal
  async deleteMeal(userId: string, mealId: string): Promise<boolean> {
    try {
      // Get the meal to delete
      const mealRef = doc(firestore, "clients", userId, "meals", mealId);
      const mealDoc = await getDoc(mealRef);

      if (!mealDoc.exists()) {
        console.error("Meal not found");
        return false;
      }

      const meal = mealDoc.data() as Meal;

      // Delete from meals collection
      await deleteDoc(mealRef);

      // Update meal log for the day
      await this.removeMealFromLog(userId, meal);

      // Update dietary goals progress
      await this.adjustGoalsProgress(userId, meal);

      return true;
    } catch (error) {
      console.error("Error deleting meal:", error);
      return false;
    }
  }

  // Remove meal from log
  private async removeMealFromLog(userId: string, meal: Meal): Promise<void> {
    try {
      // Convert meal date to start of day for grouping
      const mealDate = meal.date instanceof Timestamp
        ? meal.date.toDate()
        : new Date(meal.date);

      mealDate.setHours(0, 0, 0, 0);
      const dayTimestamp = Timestamp.fromDate(mealDate);

      // Find log for this day
      const logQuery = query(
        collection(firestore, "clients", userId, "mealLogs"),
        where("date", "==", dayTimestamp)
      );

      const logSnapshot = await getDocs(logQuery);

      if (!logSnapshot.empty) {
        const logDoc = logSnapshot.docs[0];
        const existingLog = logDoc.data() as MealLog;

        // Remove meal from log
        const updatedMeals = (existingLog.meals || []).filter(m => m.id !== meal.id);

        // Recalculate totals
        const updatedLog: Partial<MealLog> = {
          meals: updatedMeals,
          totalCalories: updatedMeals.reduce((sum, m) => sum + (m.calories || 0), 0),
          totalProtein: updatedMeals.reduce((sum, m) => sum + (m.protein || 0), 0),
          totalCarbs: updatedMeals.reduce((sum, m) => sum + (m.carbs || 0), 0),
          totalFat: updatedMeals.reduce((sum, m) => sum + (m.fat || 0), 0)
        };

        // If no meals left, delete the log, otherwise update it
        if (updatedMeals.length === 0) {
          await deleteDoc(logDoc.ref);
        } else {
          await updateDoc(logDoc.ref, updatedLog);
        }
      }
    } catch (error) {
      console.error("Error removing meal from log:", error);
    }
  }

  // Adjust dietary goals progress after meal deletion
  private async adjustGoalsProgress(userId: string, meal: Meal): Promise<void> {
    try {
      // Get user's dietary goals
      const goalsQuery = query(
        collection(firestore, "clients", userId, "dietaryGoals"),
        where("trackingEnabled", "==", true)
      );

      const goalsSnapshot = await getDocs(goalsQuery);

      if (goalsSnapshot.empty) return;

      // Update each relevant goal
      for (const goalDoc of goalsSnapshot.docs) {
        const goal = goalDoc.data() as DietaryGoal;

        // Check if meal date is within goal period
        const mealDate = meal.date instanceof Timestamp ? meal.date.toDate() : new Date(meal.date);
        const startDate = goal.startDate instanceof Timestamp ? goal.startDate.toDate() : new Date(goal.startDate);
        const endDate = goal.endDate instanceof Timestamp
          ? goal.endDate.toDate()
          : goal.endDate
            ? new Date(goal.endDate)
            : new Date(8640000000000000); // Far future date if no end date

        if (mealDate < startDate || mealDate > endDate) continue;

        // Adjust progress based on goal type
        let newProgress = goal.progress;

        switch (goal.type) {
          case "calorie":
            newProgress -= meal.calories || 0;
            break;
          case "protein":
            newProgress -= meal.protein || 0;
            break;
          case "carbs":
            newProgress -= meal.carbs || 0;
            break;
          case "fat":
            newProgress -= meal.fat || 0;
            break;
          default:
            continue; // Skip other goal types
        }

        // Ensure progress doesn't go below 0
        newProgress = Math.max(0, newProgress);

        // Update goal in database
        await updateDoc(goalDoc.ref, {
          progress: newProgress,
          completed: newProgress >= goal.target,
          lastUpdated: serverTimestamp()
        });
      }
    } catch (error) {
      console.error("Error adjusting goals progress:", error);
    }
  }

  // Create a new dietary goal
  async createDietaryGoal(userId: string, goal: Omit<DietaryGoal, 'id'>): Promise<string | null> {
    try {
      const goalId = uuidv4();
      const goalWithId: DietaryGoal = {
        ...goal,
        id: goalId,
        progress: 0,
        completed: false,
        trackingEnabled: true,
        startDate: goal.startDate || Timestamp.now()
      };

      const goalRef = doc(firestore, "clients", userId, "dietaryGoals", goalId);
      await setDoc(goalRef, goalWithId);

      return goalId;
    } catch (error) {
      console.error("Error creating dietary goal:", error);
      return null;
    }
  }

  // Get all dietary goals
  async getDietaryGoals(userId: string): Promise<DietaryGoal[]> {
    try {
      const goalsQuery = query(
        collection(firestore, "clients", userId, "dietaryGoals"),
        orderBy("startDate", "desc")
      );

      const goalsSnapshot = await getDocs(goalsQuery);

      return goalsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as DietaryGoal[];
    } catch (error) {
      console.error("Error getting dietary goals:", error);
      return [];
    }
  }

  // Update a dietary goal
  async updateDietaryGoal(userId: string, goalId: string, updates: Partial<DietaryGoal>): Promise<boolean> {
    try {
      const goalRef = doc(firestore, "clients", userId, "dietaryGoals", goalId);
      await updateDoc(goalRef, {
        ...updates,
        lastUpdated: serverTimestamp()
      });

      return true;
    } catch (error) {
      console.error("Error updating dietary goal:", error);
      return false;
    }
  }

  // Delete a dietary goal
  async deleteDietaryGoal(userId: string, goalId: string): Promise<boolean> {
    try {
      const goalRef = doc(firestore, "clients", userId, "dietaryGoals", goalId);
      await deleteDoc(goalRef);

      return true;
    } catch (error) {
      console.error("Error deleting dietary goal:", error);
      return false;
    }
  }

  // Get nutrition summary for a date range
  async getNutritionSummary(userId: string, startDate: Date, endDate: Date): Promise<{
    totalCalories: number;
    totalProtein: number;
    totalCarbs: number;
    totalFat: number;
    dailyAverages: {
      calories: number;
      protein: number;
      carbs: number;
      fat: number;
    };
    mealTypeBreakdown: {
      breakfast: number;
      lunch: number;
      dinner: number;
      snack: number;
    };
  }> {
    try {
      const logs = await this.getMealLogs(userId, startDate, endDate);

      // Calculate totals
      const totalCalories = logs.reduce((sum, log) => sum + (log.totalCalories || 0), 0);
      const totalProtein = logs.reduce((sum, log) => sum + (log.totalProtein || 0), 0);
      const totalCarbs = logs.reduce((sum, log) => sum + (log.totalCarbs || 0), 0);
      const totalFat = logs.reduce((sum, log) => sum + (log.totalFat || 0), 0);

      // Calculate daily averages
      const dayCount = Math.max(1, logs.length);
      const dailyAverages = {
        calories: totalCalories / dayCount,
        protein: totalProtein / dayCount,
        carbs: totalCarbs / dayCount,
        fat: totalFat / dayCount
      };

      // Calculate meal type breakdown
      const allMeals = logs.flatMap(log => log.meals || []);
      const mealTypeBreakdown = {
        breakfast: allMeals.filter(meal => meal.mealType === "breakfast").length,
        lunch: allMeals.filter(meal => meal.mealType === "lunch").length,
        dinner: allMeals.filter(meal => meal.mealType === "dinner").length,
        snack: allMeals.filter(meal => meal.mealType === "snack").length
      };

      return {
        totalCalories,
        totalProtein,
        totalCarbs,
        totalFat,
        dailyAverages,
        mealTypeBreakdown
      };
    } catch (error) {
      console.error("Error getting nutrition summary:", error);
      return {
        totalCalories: 0,
        totalProtein: 0,
        totalCarbs: 0,
        totalFat: 0,
        dailyAverages: {
          calories: 0,
          protein: 0,
          carbs: 0,
          fat: 0
        },
        mealTypeBreakdown: {
          breakfast: 0,
          lunch: 0,
          dinner: 0,
          snack: 0
        }
      };
    }
  }
}

// Create and export a singleton instance
export const mealTrackingService = new MealTrackingService();
