import { firestore } from "@/config/firebase";
import {
  collection,
  query,
  where,
  getDocs,
  limit,
  orderBy,
  doc,
  getDoc,
} from "firebase/firestore";
import { Restaurant } from "@/types/restaurant";
import { Order } from "@/types/order";
import { MealPreferences, ClientDetails } from "@/types";
import {
  getCombinedRecommendations,
  Recommendation,
  fetchUserPreferences,
} from "@/utils/recommendationUtils";

/**
 * Service for handling recommendation-related operations
 */
export const recommendationService = {
  /**
   * Fetches a user's order history
   * @param userId The user ID
   * @param maxOrders Maximum number of orders to fetch
   * @returns Array of orders
   */
  async getUserOrderHistory(userId: string, maxOrders: number = 50): Promise<Order[]> {
    try {
      const ordersRef = collection(firestore, "orders");
      const ordersQuery = query(
        ordersRef,
        where("userId", "==", userId),
        orderBy("createdAt", "desc"),
        limit(maxOrders)
      );

      const ordersSnapshot = await getDocs(ordersQuery);

      return ordersSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as Order));
    } catch {
      // Handle error fetching user order history
      return [];
    }
  },

  /**
   * Fetches all active restaurants
   * @returns Array of restaurants
   */
  async getActiveRestaurants(): Promise<Restaurant[]> {
    try {
      const restaurantsRef = collection(firestore, "restaurants");
      const restaurantsQuery = query(
        restaurantsRef,
        where("isActive", "==", true)
      );

      const restaurantsSnapshot = await getDocs(restaurantsQuery);

      return restaurantsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as Restaurant));
    } catch {
      // Handle error fetching active restaurants
      return [];
    }
  },

  /**
   * Fetches a user's preferences
   * @param userId The user ID
   * @returns The user's meal preferences or null if not found
   */
  async getUserPreferences(userId: string): Promise<MealPreferences | null> {
    return fetchUserPreferences(userId);
  },

  /**
   * Fetches a user's profile
   * @param userId The user ID
   * @returns The user's profile or null if not found
   */
  async getUserProfile(userId: string): Promise<ClientDetails | null> {
    try {
      const userDoc = await getDoc(doc(firestore, "clients", userId));

      if (!userDoc.exists()) return null;

      return userDoc.data() as ClientDetails;
    } catch {
      // Handle error fetching user profile
      return null;
    }
  },

  /**
   * Generates personalized recommendations for a user
   * @param userId The user ID
   * @param limit Maximum number of recommendations to return
   * @returns Array of recommendations
   */
  async getPersonalizedRecommendations(userId: string, limit: number = 6): Promise<Recommendation[]> {
    try {
      // 1. Fetch user's order history
      const orders = await this.getUserOrderHistory(userId);

      // 2. Fetch all active restaurants
      const restaurants = await this.getActiveRestaurants();

      // 3. Fetch user's preferences
      const preferences = await this.getUserPreferences(userId);

      // 4. Generate recommendations
      return getCombinedRecommendations(
        userId,
        orders,
        restaurants,
        preferences,
        limit
      );
    } catch {
      // Handle error generating personalized recommendations
      return [];
    }
  }
};
